# ZF (采埃孚) Netty接口对接指南

## 概述

本项目使用Netty框架实现与采埃孚(ZF)接口的高性能对接，支持SSL/TLS双向认证、连接池管理、自动重试等企业级特性。

## 架构设计

### 核心组件

1. **ZfNettyHttpClient** - 基于Netty的HTTP客户端
2. **ZfApiService** - ZF API业务服务层
3. **ZfSslContextBuilder** - SSL上下文构建器
4. **ZfConnectionPoolManager** - 连接池管理器
5. **ZfRetryHandler** - 重试机制处理器

### 技术特性

- ✅ 基于Netty 4.1.106.Final的高性能网络通信
- ✅ 支持SSL/TLS双向认证
- ✅ 连接池管理，提高连接复用率
- ✅ 自动重试机制，支持指数退避
- ✅ 异步非阻塞处理，提高并发性能
- ✅ 完整的错误处理和日志记录
- ✅ Spring Boot集成，支持配置文件管理

## 配置说明

### 1. 应用配置 (application-tlsapi.yml)

```yaml
# ZF (采埃孚) 接口配置
zf:
  netty:
    # 连接配置
    connection:
      host: zf-api.example.com  # ZF服务器地址
      port: 443
      ssl: true
      connect-timeout: 30000
      read-timeout: 60000
      write-timeout: 30000
    # 连接池配置
    pool:
      max-connections: 50
      max-connections-per-route: 20
      connection-idle-timeout: 300000
      keep-alive-timeout: 60000
    # 重试配置
    retry:
      max-attempts: 3
      retry-delay: 1000
      backoff-multiplier: 2.0
  # ZF API接口配置
  api:
    # 认证接口
    auth:
      baseurl: https://zf-api.example.com/auth
      login: /v1/login
      refresh: /v1/refresh
      logout: /v1/logout
    # 数据接口
    data:
      baseurl: https://zf-api.example.com/api
      vehicle-data: /v1/vehicles/{vehicleId}/data
      diagnostic: /v1/vehicles/{vehicleId}/diagnostic
      telemetry: /v1/vehicles/{vehicleId}/telemetry
    # 控制接口
    control:
      baseurl: https://zf-api.example.com/control
      remote-start: /v1/vehicles/{vehicleId}/start
      remote-stop: /v1/vehicles/{vehicleId}/stop
      lock-unlock: /v1/vehicles/{vehicleId}/lock
  # 证书配置
  certificate:
    client-cert-path: /path/to/zf-client-cert.pfx
    client-cert-password: your-cert-password
    trust-store-path: /path/to/zf-truststore.jks
    trust-store-password: your-truststore-password
```

### 2. Maven依赖配置

已在pom.xml中添加必要的Netty依赖：

```xml
<!-- Netty 完整支持 -->
<dependency>
    <groupId>io.netty</groupId>
    <artifactId>netty-all</artifactId>
    <version>${netty.version}</version>
</dependency>
```

## 使用方法

### 1. 基本API调用

```java
@Autowired
private ZfApiService zfApiService;

// 登录
ZfLoginRequest loginRequest = new ZfLoginRequest();
loginRequest.setUsername("your_username");
loginRequest.setPassword("your_password");
loginRequest.setClientId("your_client_id");
loginRequest.setClientSecret("your_client_secret");

CompletableFuture<ZfAuthResponse> loginFuture = zfApiService.login(loginRequest);
ZfAuthResponse authResponse = loginFuture.get();

// 获取车辆数据
if (authResponse.isSuccess()) {
    CompletableFuture<ZfVehicleDataResponse> dataFuture = 
        zfApiService.getVehicleData("VEHICLE_ID_123");
    ZfVehicleDataResponse vehicleData = dataFuture.get();
}
```

### 2. REST API调用

```bash
# 登录
curl -X POST http://localhost:8089/api/zf/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password",
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret"
  }'

# 获取车辆数据
curl -X GET http://localhost:8089/api/zf/vehicles/VEHICLE_ID_123/data

# 远程启动车辆
curl -X POST http://localhost:8089/api/zf/vehicles/VEHICLE_ID_123/start \
  -H "Content-Type: application/json" \
  -d '{
    "duration": 15,
    "climateControl": true,
    "targetTemperature": 22.0,
    "authCode": "AUTH_CODE_123"
  }'
```

## 部署配置

### 1. 证书配置

确保以下证书文件正确配置：

- **客户端证书** (.pfx格式): 用于双向TLS认证
- **信任存储** (.jks格式): 包含ZF服务器的CA证书

### 2. 网络配置

- 确保服务器能够访问ZF API服务器
- 配置防火墙允许HTTPS出站连接
- 如有代理，需要配置代理设置

### 3. 监控配置

建议配置以下监控指标：

- 连接池使用率
- API调用成功率
- 响应时间
- 重试次数

## 错误处理

### 常见错误及解决方案

1. **SSL握手失败**
   - 检查客户端证书是否正确
   - 验证信任存储是否包含正确的CA证书
   - 确认证书未过期

2. **连接超时**
   - 检查网络连接
   - 调整连接超时配置
   - 验证ZF服务器地址和端口

3. **认证失败**
   - 验证用户名密码
   - 检查客户端ID和密钥
   - 确认访问令牌未过期

## 性能优化

### 1. 连接池优化

```yaml
zf:
  netty:
    pool:
      max-connections: 100        # 根据并发需求调整
      max-connections-per-route: 50
      connection-idle-timeout: 600000  # 10分钟
```

### 2. 重试策略优化

```yaml
zf:
  netty:
    retry:
      max-attempts: 5
      retry-delay: 500
      backoff-multiplier: 1.5
```

### 3. 超时配置优化

```yaml
zf:
  netty:
    connection:
      connect-timeout: 15000    # 15秒
      read-timeout: 30000       # 30秒
      write-timeout: 15000      # 15秒
```

## 安全考虑

1. **证书管理**
   - 定期更新客户端证书
   - 安全存储证书密码
   - 监控证书过期时间

2. **访问控制**
   - 限制API访问权限
   - 实施访问日志记录
   - 配置访问频率限制

3. **数据保护**
   - 敏感数据加密传输
   - 避免在日志中记录敏感信息
   - 实施数据脱敏策略

## 测试

运行测试用例：

```bash
mvn test -Dtest=ZfApiServiceTest
```

## 故障排查

### 启用调试日志

```yaml
logging:
  level:
    com.nst.nstsecurity.common.zf: DEBUG
    io.netty: DEBUG
```

### 常用排查命令

```bash
# 检查网络连接
telnet zf-api.example.com 443

# 验证SSL证书
openssl s_client -connect zf-api.example.com:443 -cert client.crt -key client.key

# 查看应用日志
tail -f logs/application.log | grep ZF
```

## 联系支持

如有问题，请联系开发团队或查看项目文档。
