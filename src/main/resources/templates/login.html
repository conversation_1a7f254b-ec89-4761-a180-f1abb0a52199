<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    <title>宏景电子</title>
    <meta name="description" content="RUT">
    <link href="../static/css/login/style.css" th:href="@{/css/login/style.css}" rel="stylesheet"/>
    <link href="../static/css/login/tailwind.min.css" th:href="@{/css/login/tailwind.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/rut/css/rut-ui.css" th:href="@{/rut/css/rut-ui.css?v=4.7.1}" rel="stylesheet"/>
    <!-- 360浏览器急速模式 -->
    <meta name="renderer" content="webkit">
    <!-- 避免IE使用兼容模式 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){alert('未登录或登录超时。请重新登录');window.top.location=window.location};
    </script>
</head>
<body>

<div class="relative min-h-screen flex">
    <div class="flex flex-col sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-auto min-w-0 bg-white">
        <div class="sm:w-1/2 xl:w-3/5 h-full hidden md:flex flex-auto items-center justify-center p-10 overflow-hidden bg-purple-900 text-white bg-no-repeat bg-cover relative backgroundImg">
            <div class="absolute bg-gradient-to-b from-indigo-600 to-blue-500 opacity-75 inset-0 z-0 ">
            </div>
            <div class="w-full max-w-md z-10">
                <div class="sm:text-4xl xl:text-5xl font-bold leading-tight mb-6">宏景电子</div>
                <div class="sm:text-sm xl:text-md text-gray-200 font-normal">
                    成就客户，开拓创新；
                    诚信务实，知行合一。<br/>
                    提升自主品牌汽车零部件行业形象，
                    成为世界一流企业，创百年品牌！
                </div>
            </div>
            <ul class="circles">
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
            </ul>
        </div>

        <div class="md:flex md:items-center md:justify-center w-full sm:w-auto md:h-full w-2/5 xl:w-2/5 p-8 md:p-10 lg:p-14 sm:rounded-lg md:rounded-none bg-white">
            <div class="max-w-md w-full mx-auto space-y-8">
                <div class="text-center">
                    <!--                    <div class="mt-6 text-3xl font-bold text-gray-900">-->
                    <div style="margin-left: 30%;">
                        <img src="data:image/png;base64,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" style="width: 45px;height: 45px;float:left;vertical-align: middle;">
                    </div>
                    <!--                    </div>-->
                    <span class="mt-6 text-3xl font-bold text-gray-900" style="padding-right: 22%; line-height: 2.9rem !important;">宏景电子</span>
                    <!--                    <h2 class="mt-6 text-3xl font-bold text-gray-900">-->
                    <!--                        宏景电子-->
                    <!--                    </h2>-->
                </div>
                <div class="flex items-center justify-center space-x-2">
                    <span class="h-px w-16 bg-gray-200"></span>
                    <span class="text-gray-300 font-normal">共同的宏景&nbsp;&nbsp;&nbsp;&nbsp;共同的梦想</span>
                    <span class="h-px w-16 bg-gray-200"></span>
                </div>
                <form class="mt-8 space-y-6" id="signupForm" autocomplete="off">
                    <div class="mt-8 content-center">
                        <input name="username" class="w-full content-center text-base px-4 py-2 border-b border-gray-300 focus:outline-none rounded-2xl focus:border-indigo-500" type="text" placeholder="请输入用户名" />
                    </div>
                    <div class="mt-8 content-center">
                        <input name="password" class="w-full content-center text-base px-4 py-2 border-b rounded-2xl border-gray-300 focus:outline-none focus:border-indigo-500 " type="password" placeholder="请输入密码"/>
                    </div>
                    <div class="mt-8 content-center" th:if="${captchaEnabled==true}">
                        <input name="validateCode" id="validateCode" class="w-full content-center text-base px-4 py-2 border-b rounded-2xl border-gray-300 focus:outline-none focus:border-indigo-500 " type="text" placeholder="请输入验证码"/>
                        <a href="javascript:void(0);" title="点击更换验证码" style="display: inline-block;float:right;width:120px;height:30px; margin:2px 0px 0px -102px; position: absolute;">
                            <img id="ImgCode" th:src="@{/captcha/captchaImage(type=${captchaType})}" class="border-b rounded-2xl focus:outline-none focus:border-indigo-500" width="85%" />
                        </a>
                    </div>
                    <div class="flex items-center justify-between" th:if="${isRemembered}" th:classappend="${captchaEnabled==false} ? 'm-t'">
                        <div class="flex items-center">
                            <input id="rememberme" name="rememberme" type="checkbox" class="h-4 w-4 bg-blue-500 focus:ring-blue-400 border-gray-300 rounded" />
                            <label for="rememberme" class="ml-2 block text-sm text-gray-900">记住我</label>
                        </div>

                    </div>
                    <div>
                        <button id="btnSubmit" data-loading="正在验证登录，请稍后..." class="w-full flex justify-center bg-gradient-to-r from-indigo-500 to-blue-600 hover:bg-gradient-to-l hover:from-blue-500 hover:to-indigo-600 text-gray-100 p-4 rounded-full tracking-wide font-semibold shadow-lg cursor-pointer transition ease-in duration-500">
                            登 录
                        </button>
                    </div>

                </form>
            </div>

        </div>

    </div>
</div>

<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; </script>
<!--[if lte IE 8]><script>window.location.href=ctx+'html/ie.html';</script><![endif]-->
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/rut/js/rut-ui.js" th:src="@{/rut/js/rut-ui.js?v=4.7.0}"></script>
<script src="../static/rut/login.js" th:src="@{/rut/login.js}"></script>
</body>
</html>
