<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nst.nstsecurity.mapper.UserMapper">

    <resultMap type="UserDomain" id="UserDomainResult">
        <result property="id"    column="id"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="permission"    column="permission"    />
    </resultMap>

    <sql id="selectUserVo">
        select id, username, password, permission from nst_user
    </sql>

    <select id="findByUsername" parameterType="UserDomain" resultMap="UserDomainResult">
        <include refid="selectUserVo"/>
        <where>
            and del_flag = 0
            <if test="username != null  and username != ''"> and username = #{username} </if>
        </where>
    </select>

</mapper>