!function(w){"use strict";function r(e,t){this.setOption(t),this.setLanguage(),this.setCssClass(),this.setProp(),this.setElem(e),this.setButtonAttrDefault(),this.setInitRecord(),this.eDropdownButton(),this.eInput(),this.eWhole()}var n={data:void 0,lang:"cn",multiple:!1,pagination:!0,dropButton:!0,listSize:10,multipleControlbar:!0,maxSelectLimit:0,selectToCloseList:!1,initRecord:void 0,dbTable:"tbl",keyField:"id",showField:"name",searchField:void 0,andOr:"AND",orderBy:!1,pageSize:10,params:void 0,formatItem:void 0,autoFillResult:!1,autoSelectFirst:!1,noResultClean:!0,selectOnly:!1,inputDelay:.5,eSelect:void 0,eOpen:void 0,eAjaxSuccess:void 0,eTagRemove:void 0,eClear:void 0};function o(e){return w(e).closest("div.sp_container").find("input.sp_input")}r.version="2.20",r.dataKey="selectPageObject",r.prototype.setOption=function(e){e.searchField=e.searchField||e.showField,e.andOr=e.andOr.toUpperCase(),"AND"!==e.andOr&&"OR"!==e.andOr&&(e.andOr="AND");for(var t=["searchField"],a=0;a<t.length;a++)e[t[a]]=this.strToArray(e[t[a]]);!1!==e.orderBy&&(e.orderBy=this.setOrderbyOption(e.orderBy,e.showField)),e.multiple&&!e.selectToCloseList&&(e.autoFillResult=!1,e.autoSelectFirst=!1),e.pagination||(e.pageSize=200),("number"!==w.type(e.listSize)||e.listSize<0)&&(e.listSize=10),this.option=e},r.prototype.strToArray=function(e){return e?e.replace(/[\s　]+/g,"").split(","):""},r.prototype.setOrderbyOption=function(e,t){var a=[],o=[];if("object"==typeof e)for(var n=0;n<e.length;n++)(o=w.trim(e[n]).split(" ")).length&&a.push(2===o.length?o.concat():[o[0],"ASC"]);else o=w.trim(e).split(" "),a[0]=2===o.length?o.concat():o[0].toUpperCase().match(/^(ASC|DESC)$/i)?[t,o[0].toUpperCase()]:[o[0],"ASC"];return a},r.prototype.setLanguage=function(){var e,t=this.option;switch(t.lang){case"de":e={add_btn:"Hinzufügen-Button",add_title:"Box hinzufügen",del_btn:"Löschen-Button",del_title:"Box löschen",next:"Nächsten",next_title:"Nächsten"+t.pageSize+" (Pfeil-rechts)",prev:"Vorherigen",prev_title:"Vorherigen"+t.pageSize+" (Pfeil-links)",first_title:"Ersten (Umschalt + Pfeil-links)",last_title:"Letzten (Umschalt + Pfeil-rechts)",get_all_btn:"alle (Pfeil-runter)",get_all_alt:"(Button)",close_btn:"Schließen (Tab)",close_alt:"(Button)",loading:"lade...",loading_alt:"(lade)",page_info:"page_num von page_count",select_ng:"Achtung: Bitte wählen Sie aus der Liste aus.",select_ok:"OK : Richtig ausgewählt.",not_found:"nicht gefunden",ajax_error:"Bei der Verbindung zum Server ist ein Fehler aufgetreten.",clear:"Löschen Sie den Inhalt",select_all:"Wähle diese Seite",unselect_all:"Diese Seite entfernen",clear_all:"Alles löschen",max_selected:"Sie können nur bis zu max_selected_limit Elemente auswählen"};break;case"en":e={add_btn:"Add button",add_title:"add a box",del_btn:"Del button",del_title:"delete a box",next:"Next",next_title:"Next"+t.pageSize+" (Right key)",prev:"Prev",prev_title:"Prev"+t.pageSize+" (Left key)",first_title:"First (Shift + Left key)",last_title:"Last (Shift + Right key)",get_all_btn:"Get All (Down key)",get_all_alt:"(button)",close_btn:"Close (Tab key)",close_alt:"(button)",loading:"loading...",loading_alt:"(loading)",page_info:"Page page_num of page_count",select_ng:"Attention : Please choose from among the list.",select_ok:"OK : Correctly selected.",not_found:"not found",ajax_error:"An error occurred while connecting to server.",clear:"Clear content",select_all:"Select current page",unselect_all:"Clear current page",clear_all:"Clear all selected",max_selected:"You can only select up to max_selected_limit items"};break;case"es":e={add_btn:"Agregar boton",add_title:"Agregar una opcion",del_btn:"Borrar boton",del_title:"Borrar una opcion",next:"Siguiente",next_title:"Proximas "+t.pageSize+" (tecla derecha)",prev:"Anterior",prev_title:"Anteriores "+t.pageSize+" (tecla izquierda)",first_title:"Primera (Shift + Left)",last_title:"Ultima (Shift + Right)",get_all_btn:"Ver todos (tecla abajo)",get_all_alt:"(boton)",close_btn:"Cerrar (tecla TAB)",close_alt:"(boton)",loading:"Cargando...",loading_alt:"(Cargando)",page_info:"page_num de page_count",select_ng:"Atencion: Elija una opcion de la lista.",select_ok:"OK: Correctamente seleccionado.",not_found:"no encuentre",ajax_error:"Un error ocurrió mientras conectando al servidor.",clear:"Borrar el contenido",select_all:"Elija esta página",unselect_all:"Borrar esta página",clear_all:"Borrar todo marcado",max_selected:"Solo puedes seleccionar hasta max_selected_limit elementos"};break;case"pt-br":e={add_btn:"Adicionar botão",add_title:"Adicionar uma caixa",del_btn:"Apagar botão",del_title:"Apagar uma caixa",next:"Próxima",next_title:"Próxima "+t.pageSize+" (tecla direita)",prev:"Anterior",prev_title:"Anterior "+t.pageSize+" (tecla esquerda)",first_title:"Primeira (Shift + Left)",last_title:"Última (Shift + Right)",get_all_btn:"Ver todos (Seta para baixo)",get_all_alt:"(botão)",close_btn:"Fechar (tecla TAB)",close_alt:"(botão)",loading:"Carregando...",loading_alt:"(Carregando)",page_info:"page_num de page_count",select_ng:"Atenção: Escolha uma opção da lista.",select_ok:"OK: Selecionado Corretamente.",not_found:"não encontrado",ajax_error:"Um erro aconteceu enquanto conectando a servidor.",clear:"Limpe o conteúdo",select_all:"Selecione a página atual",unselect_all:"Remova a página atual",clear_all:"Limpar tudo",max_selected:"Você só pode selecionar até max_selected_limit itens"};break;case"ja":e={add_btn:"追加ボタン",add_title:"入力ボックスを追加します",del_btn:"削除ボタン",del_title:"入力ボックスを削除します",next:"次へ",next_title:"次の"+t.pageSize+"件 (右キー)",prev:"前へ",prev_title:"前の"+t.pageSize+"件 (左キー)",first_title:"最初のページへ (Shift + 左キー)",last_title:"最後のページへ (Shift + 右キー)",get_all_btn:"全件取得 (下キー)",get_all_alt:"画像:ボタン",close_btn:"閉じる (Tabキー)",close_alt:"画像:ボタン",loading:"読み込み中...",loading_alt:"画像:読み込み中...",page_info:"page_num 件 (全 page_count 件)",select_ng:"注意 : リストの中から選択してください",select_ok:"OK : 正しく選択されました。",not_found:"(0 件)",ajax_error:"サーバとの通信でエラーが発生しました。",clear:"コンテンツをクリアする",select_all:"当ページを選びます",unselect_all:"移して当ページを割ります",clear_all:"選択した項目をクリアする",max_selected:"最多で max_selected_limit のプロジェクトを選ぶことしかできません"};break;case"cn":default:e={add_btn:"添加按钮",add_title:"添加区域",del_btn:"删除按钮",del_title:"删除区域",next:"下一页",next_title:"下"+t.pageSize+" (→)",prev:"上一页",prev_title:"上"+t.pageSize+" (←)",first_title:"首页 (Shift + ←)",last_title:"尾页 (Shift + →)",get_all_btn:"获得全部 (↓)",get_all_alt:"(按钮)",close_btn:"关闭 (Tab键)",close_alt:"(按钮)",loading:"读取中...",loading_alt:"(读取中)",page_info:"第 page_num 页(共page_count页)",select_ng:"请注意：请从列表中选择.",select_ok:"OK : 已经选择.",not_found:"无查询结果",ajax_error:"连接到服务器时发生错误！",clear:"清除内容",select_all:"选择当前页项目",unselect_all:"取消选择当前页项目",clear_all:"清除全部已选择项目",max_selected:"最多只能选择 max_selected_limit 个项目"}}this.message=e},r.prototype.setCssClass=function(){this.css_class={container:"sp_container",container_open:"sp_container_open",re_area:"sp_result_area",result_open:"sp_result_area_open",control_box:"sp_control_box",element_box:"sp_element_box",navi:"sp_navi",results:"sp_results",re_off:"sp_results_off",select:"sp_over",select_ok:"sp_select_ok",select_ng:"sp_select_ng",selected:"sp_selected",input_off:"sp_input_off",message_box:"sp_message_box",disabled:"sp_disabled",button:"sp_button",caret_open:"sp_caret_open",btn_on:"sp_btn_on",btn_out:"sp_btn_out",input:"sp_input",clear_btn:"sp_clear_btn",align_right:"sp_align_right"}},r.prototype.setProp=function(){this.prop={disabled:!1,current_page:1,max_page:1,is_loading:!1,xhr:!1,key_paging:!1,key_select:!1,prev_value:"",selected_text:"",last_input_time:void 0,init_set:!1},this.template={tag:{content:'<li class="selected_tag" itemvalue="#item_value#">#item_text#<span class="tag_close"><i class="sp-iconfont if-close"></i></span></li>',textKey:"#item_text#",valueKey:"#item_value#"},page:{current:"page_num",total:"page_count"},msg:{maxSelectLimit:"max_selected_limit"}}},r.prototype.elementRealSize=function(e,t){var a,o={absolute:!1,clone:!1,includeMargin:!1,display:"block"},n=e.eq(0),l=[],i="",e=function(){a.each(function(e){var t=w(this),e=l[e];void 0===e?t.removeAttr("style"):t.attr("style",e)})};(function(){a=n.parents().addBack().filter(":hidden"),i+="visibility: hidden !important; display: "+o.display+" !important; ",!0===o.absolute&&(i+="position: absolute !important;"),a.each(function(){var e=w(this),t=e.attr("style");l.push(t),e.attr("style",t?t+";"+i:i)})})();t=/(outer)/.test(t)?n[t](o.includeMargin):n[t]();return e(),t},r.prototype.setElem=function(e){var t={},a=this.option,o=this.css_class,n=this.message,l=w(e),i=l.outerWidth();i<=0&&(i=this.elementRealSize(l,"outerWidth")),i<150&&(i=150),t.combo_input=l.attr({autocomplete:"off"}).addClass(o.input).wrap("<div>"),a.selectOnly&&t.combo_input.prop("readonly",!0),t.container=t.combo_input.parent().addClass(o.container),t.combo_input.prop("disabled")&&(a.multiple?t.container.addClass(o.disabled):t.combo_input.addClass(o.input_off)),t.container.width(i),t.button=w("<div>").addClass(o.button),t.dropdown=w('<span class="sp_caret"></span>'),t.clear_btn=w("<div>").html(w("<i>").addClass("sp-iconfont if-close")).addClass(o.clear_btn).attr("title",n.clear),a.dropButton||t.clear_btn.addClass(o.align_right),t.element_box=w("<ul>").addClass(o.element_box),a.multiple&&a.multipleControlbar&&(t.control=w("<div>").addClass(o.control_box)),t.result_area=w("<div>").addClass(o.re_area),a.pagination&&(t.navi=w("<div>").addClass("sp_pagination").append("<ul>")),t.results=w("<ul>").addClass(o.results);e="_text",l=t.combo_input.attr("id")||t.combo_input.attr("name"),i=t.combo_input.attr("name")||"selectPage",n=i,o=l;t.hidden=w('<input type="hidden" class="sp_hidden" />').attr({name:n,id:o}).val(""),t.combo_input.attr({name:i+e,id:l+e}),t.container.append(t.hidden),a.dropButton&&(t.container.append(t.button),t.button.append(t.dropdown)),w(document.body).append(t.result_area),t.result_area.append(t.results),a.pagination&&t.result_area.append(t.navi),a.multiple&&(a.multipleControlbar&&(t.control.append('<button type="button" class="btn btn-default sp_clear_all" ><i class="sp-iconfont if-clear"></i></button>'),t.control.append('<button type="button" class="btn btn-default sp_unselect_all" ><i class="sp-iconfont if-unselect-all"></i></button>'),t.control.append('<button type="button" class="btn btn-default sp_select_all" ><i class="sp-iconfont if-select-all"></i></button>'),t.control_text=w("<p>"),t.control.append(t.control_text),t.result_area.prepend(t.control)),t.container.addClass("sp_container_combo"),t.combo_input.addClass("sp_combo_input").before(t.element_box),(a=w("<li>").addClass("input_box")).append(t.combo_input),t.element_box.append(a),t.combo_input.attr("placeholder")&&t.combo_input.attr("placeholder_bak",t.combo_input.attr("placeholder"))),this.elem=t},r.prototype.setButtonAttrDefault=function(){this.option.dropButton&&this.elem.button.attr("title",this.message.close_btn)},r.prototype.setInitRecord=function(e){var o,a=this,n=a.option,t=a.elem,l="";"undefined"!=w.type(t.combo_input.data("init"))&&(n.initRecord=String(t.combo_input.data("init"))),e||n.initRecord||!t.combo_input.val()||(n.initRecord=t.combo_input.val()),t.combo_input.val(""),e||t.hidden.val(n.initRecord),(l=e&&t.hidden.val()?t.hidden.val():n.initRecord)&&("object"==typeof n.data?(o=new Array,t=l.split(","),w.each(t,function(e,t){for(var a=0;a<n.data.length;a++)if(n.data[a][n.keyField]==t){o.push(n.data[a]);break}}),!n.multiple&&1<o.length&&(o=[o[0]]),a.afterInit(a,o)):w.ajax({dataType:"json",type:"POST",url:n.data,data:{searchTable:n.dbTable,searchKey:n.keyField,searchValue:l},success:function(e){var t=null;n.eAjaxSuccess&&w.isFunction(n.eAjaxSuccess)&&(t=n.eAjaxSuccess(e)),a.afterInit(a,t.list)},error:function(){a.ajaxErrorNotify(a)}}))},r.prototype.afterInit=function(o,e){var n,t,l;!e||w.isArray(e)&&0===e.length||(w.isArray(e)||(e=[e]),n=o.option,t=o.css_class,l=function(e){var t=e[n.showField];if(n.formatItem&&w.isFunction(n.formatItem))try{t=n.formatItem(e)}catch(e){}return t},n.multiple?(o.prop.init_set=!0,o.clearAll(o),w.each(e,function(e,t){var a={text:l(t),value:t[n.keyField]};o.isAlreadySelected(o,a)||o.addNewTag(o,t,a)}),o.tagValuesSet(o),o.inputResize(o),o.prop.init_set=!1):(e=e[0],o.elem.combo_input.val(l(e)),o.elem.hidden.val(e[n.keyField]),o.prop.prev_value=l(e),o.prop.selected_text=l(e),n.selectOnly&&o.elem.combo_input.attr("title",o.message.select_ok).removeClass(t.select_ng).addClass(t.select_ok),o.putClearButton()))},r.prototype.eDropdownButton=function(){var t=this;t.option.dropButton&&t.elem.button.mouseup(function(e){e.stopPropagation(),t.elem.result_area.is(":hidden")&&!t.elem.combo_input.prop("disabled")?t.elem.combo_input.focus():t.hideResults(t)})},r.prototype.eInput=function(){function a(){o.prop.page_move=!1,o.suggest(o),o.setCssFocusedInput(o)}var o=this,n=o.option,t=o.elem,e=o.message;t.combo_input.keyup(function(e){o.processKey(o,e)}).keydown(function(e){o.processControl(o,e)}).focus(function(e){t.result_area.is(":hidden")&&(e.stopPropagation(),o.prop.first_show=!0,a())}),t.container.on("click.SelectPage","div."+o.css_class.clear_btn,function(e){e.stopPropagation(),o.disabled(o)||(o.clearAll(o,!0),n.eClear&&w.isFunction(n.eClear)&&n.eClear(o))}),t.result_area.on("mousedown.SelectPage",function(e){e.stopPropagation()}),n.multiple&&(n.multipleControlbar&&(t.control.find(".sp_select_all").on("click.SelectPage",function(){o.selectAllLine(o)}).hover(function(){t.control_text.html(e.select_all)},function(){t.control_text.html("")}),t.control.find(".sp_unselect_all").on("click.SelectPage",function(){o.unSelectAllLine(o)}).hover(function(){t.control_text.html(e.unselect_all)},function(){t.control_text.html("")}),t.control.find(".sp_clear_all").on("click.SelectPage",function(){o.clearAll(o,!0)}).hover(function(){t.control_text.html(e.clear_all)},function(){t.control_text.html("")})),t.element_box.on("click.SelectPage",function(e){e=e.target||e.srcElement;w(e).is("ul")&&t.combo_input.focus()}),t.element_box.on("click.SelectPage","span.tag_close",function(){var e=w(this).closest("li"),t=e.data("dataObj");o.removeTag(o,e),a(),n.eTagRemove&&w.isFunction(n.eTagRemove)&&n.eTagRemove([t])}),o.inputResize(o))},r.prototype.eWhole=function(){function a(e){e.elem.combo_input.val(""),e.option.multiple||e.elem.hidden.val(""),e.prop.selected_text=""}var o=this.css_class;w(document.body).off("mousedown.selectPage").on("mousedown.selectPage",function(e){var e=e.target||e.srcElement,t=w(e).closest("div."+o.container);w("div."+o.container+"."+o.container_open).each(function(){if(this!=t[0]){var e=w(this).find("input."+o.input).data(r.dataKey);if(!e.elem.combo_input.val()&&e.elem.hidden.val()&&!e.option.multiple)return e.prop.current_page=1,a(e),e.hideResults(e),!0;e.elem.results.find("li").not("."+o.message_box).length?!e.option.autoFillResult||e.elem.hidden.val()?e.hideResults(e):e.elem.results.find("li.sp_over").length?e.selectCurrentLine(e):e.option.autoSelectFirst?(e.nextLine(e),e.selectCurrentLine(e)):e.hideResults(e):(e.option.noResultClean?a(e):e.option.multiple||e.elem.hidden.val(""),e.hideResults(e))}})})},r.prototype.eResultList=function(){var t=this,a=this.css_class;t.elem.results.children("li").hover(function(){t.prop.key_select?t.prop.key_select=!1:w(this).hasClass(a.selected)||w(this).hasClass(a.message_box)||(w(this).addClass(a.select),t.setCssFocusedResults(t))},function(){w(this).removeClass(a.select)}).click(function(e){t.prop.key_select?t.prop.key_select=!1:(e.preventDefault(),e.stopPropagation(),w(this).hasClass(a.selected)||t.selectCurrentLine(t))})},r.prototype.eScroll=function(){var s=this.css_class;w(window).on("scroll.SelectPage",function(){w("div."+s.container+"."+s.container_open).each(function(){var e=w(this).find("input."+s.input).data(r.dataKey),t=e.elem.result_area.offset(),a=w(window).scrollTop(),o=w(document).height(),n=w(window).height(),l=e.elem.result_area.outerHeight(),i=t.top+l,l=n<o,o=e.elem.result_area.hasClass("shadowDown");l&&(o?n+a<i&&e.calcResultsSize(e):t.top<a&&e.calcResultsSize(e))})})},r.prototype.ePaging=function(){var t=this;t.option.pagination&&(t.elem.navi.find("li.csFirstPage").off("click").on("click",function(e){e.preventDefault(),t.firstPage(t)}),t.elem.navi.find("li.csPreviousPage").off("click").on("click",function(e){e.preventDefault(),t.prevPage(t)}),t.elem.navi.find("li.csNextPage").off("click").on("click",function(e){e.preventDefault(),t.nextPage(t)}),t.elem.navi.find("li.csLastPage").off("click").on("click",function(e){e.preventDefault(),t.lastPage(t)}))},r.prototype.ajaxErrorNotify=function(e){e.showMessage(e.message.ajax_error)},r.prototype.showMessage=function(e,t){t&&(t='<li class="'+e.css_class.message_box+'"><i class="sp-iconfont if-warning"></i> '+t+"</li>",e.elem.results.empty().append(t).show(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.elem.control.hide(),e.option.pagination&&e.elem.navi.hide())},r.prototype.scrollWindow=function(e,t){var a,o,n=e.getCurrentLine(e),l=(n&&!t?n:e.elem.container).offset().top;e.prop.size_li=e.elem.results.children("li:first").outerHeight(),a=e.prop.size_li;var i=w(window).height(),t=w(window).scrollTop(),e=t+i-a;if(n.length)if(l<t||i<a)o=l-t;else{if(!(e<l))return;o=l-e}else l<t&&(o=l-t);window.scrollBy(0,o)},r.prototype.setOpenStatus=function(e,t){var a=e.elem,e=e.css_class;t?(a.container.addClass(e.container_open),a.result_area.addClass(e.result_open)):(a.container.removeClass(e.container_open),a.result_area.removeClass(e.result_open))},r.prototype.setCssFocusedInput=function(e){},r.prototype.setCssFocusedResults=function(e){},r.prototype.checkValue=function(e){var t=e.elem.combo_input.val();t!=e.prop.prev_value&&(e.prop.prev_value=t,e.prop.first_show=!1,e.option.selectOnly&&e.setButtonAttrDefault(),e.option.multiple||t||(e.elem.combo_input.val(""),e.elem.hidden.val(""),e.elem.clear_btn.remove()),e.suggest(e))},r.prototype.processKey=function(e,t){-1===w.inArray(t.keyCode,[37,38,39,40,27,9,13])&&(16!=t.keyCode&&e.setCssFocusedInput(e),e.inputResize(e),"string"===w.type(e.option.data)?(e.prop.last_input_time=t.timeStamp,setTimeout(function(){t.timeStamp-e.prop.last_input_time==0&&e.checkValue(e)},1e3*e.option.inputDelay)):e.checkValue(e))},r.prototype.processControl=function(e,t){if(-1<w.inArray(t.keyCode,[37,38,39,40,27,9])&&e.elem.result_area.is(":visible")||-1<w.inArray(t.keyCode,[13,9])&&e.getCurrentLine(e))switch(t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0,t.returnValue=!1,t.keyCode){case 37:t.shiftKey?e.firstPage(e):e.prevPage(e);break;case 38:e.prop.key_select=!0,e.prevLine(e);break;case 39:t.shiftKey?e.lastPage(e):e.nextPage(e);break;case 40:e.elem.results.children("li").length?(e.prop.key_select=!0,e.nextLine(e)):e.suggest(e);break;case 9:e.prop.key_paging=!0,e.selectCurrentLine(e);break;case 13:e.selectCurrentLine(e);break;case 27:e.prop.key_paging=!0,e.hideResults(e)}},r.prototype.abortAjax=function(e){e.prop.xhr&&(e.prop.xhr.abort(),e.prop.xhr=!1)},r.prototype.suggest=function(e){var t=w.trim(e.elem.combo_input.val()),a=!e.option.multiple&&t&&t===e.prop.selected_text?"":t;a=a.split(/[\s　]+/),e.option.eOpen&&w.isFunction(e.option.eOpen)&&e.option.eOpen.call(e),e.abortAjax(e);t=e.prop.current_page||1;"object"==typeof e.option.data?e.searchForJson(e,a,t):e.searchForDb(e,a,t)},r.prototype.setLoading=function(e){""===e.elem.results.html()&&e.setOpenStatus(e,!0)},r.prototype.searchForDb=function(l,i,s){var r=l.option;r.eAjaxSuccess&&w.isFunction(r.eAjaxSuccess)||l.hideResults(l);var e=r.params,t={},a=r.searchField;i.length&&i[0]&&i[0]!==l.prop.prev_value&&(s=1);var o,n={q_word:i,pageNumber:s,pageSize:r.pageSize,andOr:r.andOr,searchTable:r.dbTable};!1!==r.orderBy&&(n.orderBy=r.orderBy),n[a]=i[0],t=e&&w.isFunction(e)&&(o=e.call(l))&&w.isPlainObject(o)?w.extend({},n,o):n,l.prop.xhr=w.ajax({dataType:"json",url:r.data,type:"POST",data:t,success:function(e){if(!e||!w.isPlainObject(e))return l.hideResults(l),void l.ajaxErrorNotify(l);var t,a={};try{t=r.eAjaxSuccess(e),a.originalResult=t.list,a.cnt_whole=t.totalRow}catch(e){return void l.showMessage(l,l.message.ajax_error)}if(a.candidate=[],a.keyField=[],"object"!=typeof a.originalResult)return l.prop.xhr=null,void l.notFoundSearch(l);a.cnt_page=a.originalResult.length;for(var o=0;o<a.cnt_page;o++)for(var n in a.originalResult[o])n==r.keyField&&a.keyField.push(a.originalResult[o][n]),n==r.showField&&a.candidate.push(a.originalResult[o][n]);l.prepareResults(l,a,i,s)},error:function(e,t){"abort"!=t&&(l.hideResults(l),l.ajaxErrorNotify(l))},complete:function(){l.prop.xhr=null}})},r.prototype.searchForJson=function(e,t,a){for(var o,n,l=e.option,i=[],s=[],r=[],c={},p=0,u=[];s[p]=t[p].replace(/\W/g,"\\$&").toString(),u[p]=new RegExp(s[p],"gi"),p++,p<t.length;);for(p=0;p<l.data.length;p++){for(var d,_=!1,m=l.data[p],g=0;g<u.length;g++)if(d=m[l.searchField],l.formatItem&&w.isFunction(l.formatItem)&&(d=l.formatItem(m)),d.match(u[g])){if(_=!0,"OR"==l.andOr)break}else if(_=!1,"AND"==l.andOr)break;_&&i.push(m)}if(!1===l.orderBy)r=i.concat();else{for(var f=new RegExp("^"+s[0]+"$","gi"),h=new RegExp("^"+s[0],"gi"),v=[],b=[],y=[],p=0;p<i.length;p++){var x=l.orderBy[0][0],x=String(i[p][x]);(x.match(f)?v:x.match(h)?b:y).push(i[p])}y=l.orderBy[0][1].match(/^asc$/i)?(v=e.sortAsc(e,v),b=e.sortAsc(e,b),e.sortAsc(e,y)):(v=e.sortDesc(e,v),b=e.sortDesc(e,b),e.sortDesc(e,y)),r=r.concat(v).concat(b).concat(y)}c.cnt_whole=r.length,e.prop.page_move?r.length<=(a-1)*l.pageSize&&(a=1,e.prop.current_page=1):l.multiple||(o=e.elem.hidden.val(),"undefined"!==w.type(o)&&""!==w.trim(o)&&(n=0,w.each(r,function(e,t){if(t[l.keyField]==o)return n=e+1,!1}),(a=Math.ceil(n/l.pageSize))<1&&(a=1),e.prop.current_page=a));var C=(a-1)*l.pageSize,S=C+l.pageSize;for(c.originalResult=[],p=C;p<S&&void 0!==r[p];p++)for(var k in c.originalResult.push(r[p]),r[p])k==l.keyField&&(void 0===c.keyField&&(c.keyField=[]),c.keyField.push(r[p][k])),k==l.showField&&(void 0===c.candidate&&(c.candidate=[]),c.candidate.push(r[p][k]));void 0===c.candidate&&(c.candidate=[]),c.cnt_page=c.candidate.length,e.prepareResults(e,c,t,a)},r.prototype.sortAsc=function(a,e){return e.sort(function(e,t){e=e[a.option.orderBy[0][0]],t=t[a.option.orderBy[0][0]];return"number"===w.type(e)?e-t:String(e).localeCompare(String(t))}),e},r.prototype.sortDesc=function(a,e){return e.sort(function(e,t){e=e[a.option.orderBy[0][0]],t=t[a.option.orderBy[0][0]];return"number"===w.type(e)?t-e:String(t).localeCompare(String(e))}),e},r.prototype.notFoundSearch=function(e){e.elem.results.empty(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.setCssFocusedInput(e)},r.prototype.prepareResults=function(e,t,a,o){e.option.pagination&&e.setNavi(e,t.cnt_whole,t.cnt_page,o),t.keyField||(t.keyField=!1),e.option.selectOnly&&1===t.candidate.length&&t.candidate[0]==a[0]&&(e.elem.hidden.val(t.keyField[0]),this.setButtonAttrDefault());o=!1;a&&a.length&&a[0]&&(o=!0),e.displayResults(e,t,o)},r.prototype.setNavi=function(e,t,a,o){var n,l,i,s,r=e.message,c=e.elem.navi.find("ul"),p=Math.ceil(t/e.option.pageSize);function u(){return r.page_info.replace(n.template.page.current,i).replace(n.template.page.total,s)}0===p?o=0:p<o?o=p:0===o&&(o=1),e.prop.current_page=o,e.prop.max_page=p,n=e,i=o,s=p,0===(l=c).find("li").length?(l.hide().empty(),l.append('<li class="csFirstPage" title="'+r.first_title+'" ><a href="javascript:void(0);"> <i class="sp-iconfont if-first"></i> </a></li>'),l.append('<li class="csPreviousPage" title="'+r.prev_title+'" ><a href="javascript:void(0);"><i class="sp-iconfont if-previous"></i></a></li>'),l.append('<li class="pageInfoBox"><a href="javascript:void(0);"> '+u()+" </a></li>"),l.append('<li class="csNextPage" title="'+r.next_title+'" ><a href="javascript:void(0);"><i class="sp-iconfont if-next"></i></a></li>'),l.append('<li class="csLastPage" title="'+r.last_title+'" ><a href="javascript:void(0);"> <i class="sp-iconfont if-last"></i> </a></li>'),l.show()):l.find("li.pageInfoBox a").html(u());var d="disabled",_=c.find("li.csFirstPage"),m=c.find("li.csPreviousPage"),g=c.find("li.csNextPage"),t=c.find("li.csLastPage");1===o||0===o?(_.hasClass(d)||_.addClass(d),m.hasClass(d)||m.addClass(d)):(_.hasClass(d)&&_.removeClass(d),m.hasClass(d)&&m.removeClass(d)),o===p||0===p?(g.hasClass(d)||g.addClass(d),t.hasClass(d)||t.addClass(d)):(g.hasClass(d)&&g.removeClass(d),t.hasClass(d)&&t.removeClass(d)),1<p&&e.ePaging()},r.prototype.displayResults=function(e,t,a){var o=e.option,n=e.elem;if(n.results.hide().empty(),o.multiple&&"number"===w.type(o.maxSelectLimit)&&0<o.maxSelectLimit){var l=n.element_box.find("li.selected_tag").length;if(0<l&&l>=o.maxSelectLimit){l=e.message.max_selected;return void e.showMessage(e,l.replace(e.template.msg.maxSelectLimit,o.maxSelectLimit))}}if(t.candidate.length)for(var i=t.candidate,s=t.keyField,r=n.hidden.val(),c=r?r.split(","):new Array,p="",u=0;u<i.length;u++){if(o.formatItem&&w.isFunction(o.formatItem))try{p=o.formatItem(t.originalResult[u])}catch(e){console.error("formatItem 内容格式化函数内容设置不正确！"),p=i[u]}else p=i[u];var d=w("<li>").html(p).attr({pkey:s[u]});o.formatItem||d.attr("title",p),-1!==w.inArray(s[u].toString(),c)&&d.addClass(e.css_class.selected),d.data("dataObj",t.originalResult[u]),n.results.append(d)}else{r='<li class="'+e.css_class.message_box+'"><i class="sp-iconfont if-warning"></i> '+e.message.not_found+"</li>";n.results.append(r)}n.results.show(),o.multiple&&o.multipleControlbar&&n.control.show(),o.pagination&&n.navi.show(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.eResultList(),e.eScroll(),a&&t.candidate.length&&o.autoSelectFirst&&e.nextLine(e)},r.prototype.calcResultsSize=function(e){function t(){if("static"!==u.container.css("position")){var e;p.pagination||(e=u.results.find("li:first").outerHeight(!0)*p.listSize,u.results.css({"max-height":e,"overflow-y":"auto"}));var t=w(document).width(),a=w(document).height(),o=w(window).height(),n=u.container.offset(),l=w(window).scrollTop(),i=u.result_area.outerWidth(),s=n.left,r=u.container.outerHeight(),c=n.left+i>t?s-(i-u.container.outerWidth()):s,t=n.top,i=0,s=t+r+e+5,a=o<a;return(e=u.result_area.outerHeight())<t-l-5&&a&&o+l<s||!a&&o<s?(i=n.top-e-5,u.result_area.removeClass("shadowUp shadowDown").addClass("shadowUp")):(i=n.top+(p.multiple?u.container.outerHeight():r),u.result_area.removeClass("shadowUp shadowDown").addClass("shadowDown"),i+=5),{top:i+"px",left:c+"px"}}c=u.combo_input.offset(),u.result_area.css({top:c.top+u.combo_input.outerHeight()+"px",left:c.left+"px"})}var a,p=e.option,u=e.elem;u.result_area.is(":visible")?u.result_area.css(t()):(a=t(),u.result_area.css(a).show(1,function(){var e=t();a.top===e.top&&a.left===e.left||u.result_area.css(e)}))},r.prototype.hideResults=function(e){e.prop.key_paging&&(e.scrollWindow(e,!0),e.prop.key_paging=!1),e.setCssFocusedInput(e),e.option.autoFillResult,e.elem.results.empty(),e.elem.result_area.hide(),e.setOpenStatus(e,!1),w(window).off("scroll.SelectPage"),e.abortAjax(e),e.setButtonAttrDefault()},r.prototype.disabled=function(e,t){var a=e.elem;if("undefined"===w.type(t))return a.combo_input.prop("disabled");"boolean"===w.type(t)&&(a.combo_input.prop("disabled",t),t?a.container.addClass(e.css_class.disabled):a.container.removeClass(e.css_class.disabled))},r.prototype.firstPage=function(e){1<e.prop.current_page&&(e.prop.current_page=1,e.prop.page_move=!0,e.suggest(e))},r.prototype.prevPage=function(e){1<e.prop.current_page&&(e.prop.current_page--,e.prop.page_move=!0,e.suggest(e))},r.prototype.nextPage=function(e){e.prop.current_page<e.prop.max_page&&(e.prop.current_page++,e.prop.page_move=!0,e.suggest(e))},r.prototype.lastPage=function(e){e.prop.current_page<e.prop.max_page&&(e.prop.current_page=e.prop.max_page,e.prop.page_move=!0,e.suggest(e))},r.prototype.afterAction=function(e,t){e.inputResize(e),e.elem.combo_input.change(),e.setCssFocusedInput(e),e.prop.init_set||(e.option.multiple?(e.option.selectToCloseList&&(e.hideResults(e),e.elem.combo_input.blur()),!e.option.selectToCloseList&&t&&(e.suggest(e),e.elem.combo_input.focus())):(e.hideResults(e),e.elem.combo_input.blur()))},r.prototype.selectCurrentLine=function(e){e.scrollWindow(e,!0);var t,a,o=e.option,n=e.getCurrentLine(e);n&&(t=n.data("dataObj"),o.multiple?(e.elem.combo_input.val(""),a={text:n.text(),value:n.attr("pkey")},e.isAlreadySelected(e,a)||(e.addNewTag(e,t,a),e.tagValuesSet(e))):(e.elem.combo_input.val(n.text()),e.elem.combo_input.data("dataObj",t),e.elem.hidden.val(n.attr("pkey"))),o.selectOnly&&e.setButtonAttrDefault(),o.eSelect&&w.isFunction(o.eSelect)&&o.eSelect(t,e),e.prop.prev_value=e.elem.combo_input.val(),e.prop.selected_text=e.elem.combo_input.val(),e.putClearButton()),e.afterAction(e,!0)},r.prototype.putClearButton=function(){this.option.multiple||this.elem.combo_input.prop("disabled")||this.elem.container.append(this.elem.clear_btn)},r.prototype.selectAllLine=function(o){var n=o.option,l=new Array;o.elem.results.find("li").each(function(e,t){var a=w(t),t=a.data("dataObj"),a={text:a.text(),value:a.attr("pkey")};if(o.isAlreadySelected(o,a)||(o.addNewTag(o,t,a),o.tagValuesSet(o)),l.push(t),"number"===w.type(n.maxSelectLimit)&&0<n.maxSelectLimit&&n.maxSelectLimit===o.elem.element_box.find("li.selected_tag").length)return!1}),n.eSelect&&w.isFunction(n.eSelect)&&n.eSelect(l,o),o.afterAction(o,!0)},r.prototype.unSelectAllLine=function(a){var e=a.option,o=[];a.elem.results.find("li").each(function(e,t){t=w(t).attr("pkey"),t=a.elem.element_box.find('li.selected_tag[itemvalue="'+t+'"]');t.length&&o.push(t.data("dataObj")),a.removeTag(a,t)}),a.afterAction(a,!0),e.eTagRemove&&w.isFunction(e.eTagRemove)&&e.eTagRemove(o)},r.prototype.clearAll=function(e,t){var a=e.option,o=[];a.multiple?(e.elem.element_box.find("li.selected_tag").each(function(e,t){o.push(w(t).data("dataObj")),t.remove()}),e.elem.element_box.find("li.selected_tag").remove()):w(e.elem.combo_input).removeData("dataObj"),e.reset(e),e.afterAction(e,t),a.multiple?a.eTagRemove&&w.isFunction(a.eTagRemove)&&a.eTagRemove(o):e.elem.clear_btn.remove()},r.prototype.reset=function(e){e.elem.combo_input.val(""),e.elem.hidden.val(""),e.prop.prev_value="",e.prop.selected_text="",e.prop.current_page=1},r.prototype.getCurrentLine=function(e){if(e.elem.result_area.is(":hidden"))return!1;e=e.elem.results.find("li."+e.css_class.select);return!!e.length&&e},r.prototype.isAlreadySelected=function(e,t){var a=!1;return t.value&&(!(e=e.elem.hidden.val())||(e=e.split(","))&&e.length&&-1!=w.inArray(t.value,e)&&(a=!0)),a},r.prototype.addNewTag=function(e,t,a){var o;e.option.multiple&&t&&a&&(o=(o=(o=e.template.tag.content).replace(e.template.tag.textKey,a.text)).replace(e.template.tag.valueKey,a.value),(o=w(o)).data("dataObj",t),e.elem.combo_input.prop("disabled")&&o.find("span.tag_close").hide(),e.elem.combo_input.closest("li").before(o))},r.prototype.removeTag=function(e,t){var a=w(t).attr("itemvalue"),o=e.elem.hidden.val();"undefined"!=w.type(a)&&o&&(o=o.split(","),-1!=(a=w.inArray(a.toString(),o))&&(o.splice(a,1),e.elem.hidden.val(o.toString()))),w(t).remove(),e.inputResize(e)},r.prototype.tagValuesSet=function(e){var t,a;!e.option.multiple||(t=e.elem.element_box.find("li.selected_tag"))&&t.length&&(a=new Array,w.each(t,function(e,t){t=w(t).attr("itemvalue");"undefined"!==w.type(t)&&a.push(t)}),a.length&&e.elem.hidden.val(a.join(",")))},r.prototype.inputResize=function(e){var t,a;e.option.multiple&&(t=e.elem.combo_input.closest("li"),a=function(e,t){t.removeClass("full_width");t=.75*(e.elem.combo_input.val().length+1)+"em";e.elem.combo_input.css("width",t).removeAttr("placeholder")},0===e.elem.element_box.find("li.selected_tag").length&&e.elem.combo_input.attr("placeholder_bak")?(t.hasClass("full_width")||t.addClass("full_width"),e.elem.combo_input.attr("placeholder",e.elem.combo_input.attr("placeholder_bak")).removeAttr("style")):a(e,t))},r.prototype.nextLine=function(e){var t,a=e.getCurrentLine(e);a?(t=e.elem.results.children("li").index(a),a.removeClass(e.css_class.select)):t=-1,++t<e.elem.results.children("li").length?(e.elem.results.children("li").eq(t).addClass(e.css_class.select),e.setCssFocusedResults(e)):e.setCssFocusedInput(e),e.scrollWindow(e,!1)},r.prototype.prevLine=function(e){var t,a=e.getCurrentLine(e);a?(t=e.elem.results.children("li").index(a),a.removeClass(e.css_class.select)):t=e.elem.results.children("li").length,-1<--t?(e.elem.results.children("li").eq(t).addClass(e.css_class.select),e.setCssFocusedResults(e)):e.setCssFocusedInput(e),e.scrollWindow(e,!1)};var e=w.fn.selectPage;w.fn.selectPage=function(o){return this.each(function(){var e=w(this),t=e.data(r.dataKey),a=w.extend({},n,e.data(),t&&t.option,"object"==typeof o&&o);t||e.data(r.dataKey,t=new r(this,a))})},w.fn.selectPage.Constructor=r,w.fn.selectPageClear=function(){return this.each(function(){var e=o(this).data(r.dataKey);e&&(e.prop.init_set=!0,e.clearAll(e),e.prop.init_set=!1)})},w.fn.selectPageRefresh=function(){return this.each(function(){var e=o(this).data(r.dataKey);e&&e.elem.hidden.val()&&e.setInitRecord(!0)})},w.fn.selectPageData=function(t){return this.each(function(){var e;t&&w.isArray(t)&&((e=o(this).data(r.dataKey))&&(e.clearAll(e),e.option.data=t))})},w.fn.selectPageDisabled=function(t){var a=!1;return this.each(function(){var e=o(this).data(r.dataKey);e&&("undefined"!==w.type(t)?e.disabled(e,t):a=e.disabled(e))}),a},w.fn.selectPageText=function(){var t="";return this.each(function(){var a,e=o(this).data(r.dataKey);e&&(e.option.multiple?(a=[],e.elem.element_box.find("li.selected_tag").each(function(e,t){a.push(w(t).text())}),t+=a.toString()):t+=e.elem.combo_input.val())}),t},w.fn.selectPageSelectedData=function(){var a=[];return this.each(function(){var e=o(this).data(r.dataKey);e&&(e.option.multiple?e.elem.element_box.find("li.selected_tag").each(function(e,t){a.push(w(t).data("dataObj"))}):(e=e.elem.combo_input.data("dataObj"))&&a.push(e))}),a},w.fn.selectPage.noConflict=function(){return w.fn.selectPage=e,this}}(window.jQuery);
