/*!
 * bootstrap-fileinput v5.2.8
 * http://plugins.krajee.com/file-input
 *
 * Krajee Explorer Font Awesome 5.x theme style for bootstrap-fileinput. Load this theme file after loading
 * font awesome 4.x CSS and `fileinput.css`.
 *
 * Author: <PERSON><PERSON><PERSON>
 * Copyright: 2014 - 2022, <PERSON><PERSON><PERSON>, Krajee.com
 *
 * Licensed under the BSD-3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */.theme-explorer-fa .file-preview-frame{border:1px solid #ddd;margin:2px 0;width:100%;display:flex;justify-content:space-between;align-items:center}.explorer-frame .file-preview-other,.theme-explorer-fa .explorer-frame .kv-file-content,.theme-explorer-fa .file-actions,.theme-explorer-fa .file-drag-handle,.theme-explorer-fa .file-upload-indicator{text-align:center}.theme-explorer-fa .file-drag-handle,.theme-explorer-fa .file-upload-indicator{position:absolute;display:inline-block;bottom:8px;right:4px;width:16px;height:16px;font-size:16px}.theme-explorer-fa .explorer-caption,.theme-explorer-fa .file-thumb-progress .progress{display:block;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.theme-explorer-fa .file-thumb-progress .progress{margin-top:5px}.theme-explorer-fa .explorer-caption,.theme-explorer-fa .file-footer-buttons{padding:5px}.theme-explorer-fa .file-footer-buttons{text-align:right}.theme-explorer-fa .explorer-caption{color:#777;padding-top:5px}.theme-explorer-fa .kvsortable-ghost{opacity:.6;background:#e1edf7;border:2px solid #a1abff}.theme-explorer-fa .file-preview .table{margin:0}.theme-explorer-fa .file-error-message ul{padding:5px 0 0 20px}.explorer-frame .file-preview-text{display:inline-block;color:#428bca;border:1px solid #ddd;font-family:Menlo,Monaco,Consolas,"Courier New",monospace;outline:0;padding:8px;resize:none}.explorer-frame .file-preview-html{display:inline-block;border:1px solid #ddd;padding:8px;overflow:auto}.explorer-frame .file-other-icon{font-size:2.6em}.explorer-frame:not(.kv-zoom-body):hover{background-color:#f5f5f5}.theme-explorer-fa .file-preview-frame samp{font-size:.9rem}.theme-explorer-fa .explorer-frame .kv-file-content{width:160px;height:80px;padding:5px;text-align:left}.theme-explorer-fa .file-details-cell{width:60%;font-size:.95rem;text-align:left;margin-right:auto}.theme-explorer-fa .file-actions-cell{position:relative;height:80px;width:200px}.file-zoom-dialog .explorer-frame .file-other-icon{font-size:22em;font-size:50vmin}@media only screen and (max-width:1249px){.theme-explorer-fa .file-preview-frame .file-details-cell{width:40%}}@media only screen and (max-width:1023px){.theme-explorer-fa .file-preview-frame .file-details-cell{width:30%}}@media only screen and (max-width:767px){.theme-explorer-fa .file-preview-frame .file-details-cell{width:200px}}@media only screen and (max-width:575px){.theme-explorer-fa .file-preview-frame{flex-direction:column}.theme-explorer-fa .file-preview-frame .kv-file-content{text-align:center}.theme-explorer-fa .file-details-cell{text-align:center;margin-right:0}.theme-explorer-fa .file-actions-cell,.theme-explorer-fa .file-details-cell,.theme-explorer-fa .file-preview-frame .kv-file-content{width:100%}.theme-explorer-fa .file-actions-cell{height:auto}.theme-explorer-fa .file-footer-buttons{text-align:left}}