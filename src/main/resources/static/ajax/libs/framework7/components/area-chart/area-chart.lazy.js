(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var a=e.$,s=e.utils,l=(e.getDevice,e.getSupport,e.Class),n=(e.<PERSON>,e.ConstructorMethods),o=(e.ModalMethods,s.extend),r=s.deleteProps;class i extends l{constructor(e,t){void 0===t&&(t={}),super(t,[e]);const s=this,l=o({},e.params.areaChart);s.useModulesParams(l),s.params=o(l,t);const{el:n}=s.params;if(!n)return s;const r=a(n);return 0===r.length?s:r[0].f7AreaChart?r[0].f7AreaChart:(o(s,{app:e,$el:r,el:r&&r[0],currentIndex:null,hiddenDatasets:[],f7Tooltip:null,linesOffsets:null}),r[0].f7AreaChart=s,s.useModules(),s.onMouseEnter=s.onMouseEnter.bind(s),s.onMouseMove=s.onMouseMove.bind(s),s.onMouseLeave=s.onMouseLeave.bind(s),s.onLegendClick=s.onLegendClick.bind(s),s.init(),s)}getVisibleLabels(){const{maxAxisLabels:e,axisLabels:t}=this.params;if(!e||t.length<=e)return t;const a=Math.ceil(t.length/e);return t.filter(((e,t)=>t%a==0))}getSummValues(){const{datasets:e}=this.params,{hiddenDatasets:t}=this,a=[];return e.filter(((e,a)=>!t.includes(a))).forEach((e=>{let{values:t}=e;t.forEach(((e,t)=>{a[t]||(a[t]=0),a[t]+=e}))})),a}getChartData(){const{datasets:e,lineChart:t,width:a,height:s}=this.params,{hiddenDatasets:l}=this,n=[];if(!e.length)return n;const o=e[0].values.map((()=>0));let r=0;return t?e.filter(((e,t)=>!l.includes(t))).forEach((e=>{let{values:t}=e;const a=Math.max(...t);a>r&&(r=a)})):r=Math.max(...this.getSummValues()),e.filter(((e,t)=>!l.includes(t))).forEach((e=>{let{label:l,values:i,color:d}=e;const h=i.map(((e,l)=>{o[l]+=e;const n=t?e:o[l],d=l/(i.length-1)*a,h=s-n/r*s;return t?`${0===l?"M":"L"}${d},${h}`:`${d} ${h}`}));t||h.push(`${a} ${s} 0 ${s}`),n.push({label:l,points:h.join(" "),color:d})})),n.reverse()}getVerticalLines(){const{datasets:e,width:t}=this.params,a=[];if(!e.length)return a;const s=e[0].values;return s.forEach(((e,l)=>{const n=l/(s.length-1)*t;a.push(n)})),a}toggleDataset(e){const{hiddenDatasets:t,params:{toggleDatasets:a}}=this;a&&(t.includes(e)?t.splice(t.indexOf(e),1):t.push(e),this.$legendEl&&(this.$legendEl.find(".area-chart-legend-item").removeClass("area-chart-legend-item-hidden"),t.forEach((e=>{this.$legendEl.find(`.area-chart-legend-item[data-index="${e}"]`).addClass("area-chart-legend-item-hidden")}))),this.update({},!0))}formatAxisLabel(e){const{formatAxisLabel:t}=this.params;return t?t.call(this,e):e}formatLegendLabel(e){const{formatLegendLabel:t}=this.params;return t?t.call(this,e):e}calcLinesOffsets(){const e=this.svgEl.querySelectorAll("line");this.linesOffsets=[];for(let t=0;t<e.length;t+=1)this.linesOffsets.push(e[t].getBoundingClientRect().left)}formatTooltip(){const e=this,{currentIndex:t,hiddenDatasets:a,params:{datasets:s,axisLabels:l,formatTooltip:n,formatTooltipTotal:o,formatTooltipAxisLabel:r,formatTooltipDataset:i}}=e;if(null===t)return"";let d=0;const h=s.filter(((e,t)=>!a.includes(t))).map((e=>({color:e.color,label:e.label,value:e.values[t]})));if(h.forEach((e=>{d+=e.value})),n)return n({index:t,total:d,datasets:h});let c=r?r.call(e,l[t]):this.formatAxisLabel(l[t]);c||(c="");return`\n        <div class="area-chart-tooltip-label">${c}</div>\n        <div class="area-chart-tooltip-total">${o?o.call(e,d):d}</div>\n        ${h.length>0?`\n        <ul class="area-chart-tooltip-list">\n          ${h.map((t=>{let{label:a,color:s,value:l}=t;return`\n                <li><span style="background-color: ${s};"></span>${i?i.call(e,a,l,s):`${a?`${a}: `:""}${l}`}</li>\n              `})).join("")}\n        </ul>`:""}\n      `}setTooltip(){const e=this,{app:t,el:a,svgEl:s,hiddenDatasets:l,currentIndex:n,params:{tooltip:o,datasets:r}}=e;if(!o)return;if(r.filter(((e,t)=>!l.includes(t))).length>0)return null===n||e.f7Tooltip?void(e.f7Tooltip&&e.f7Tooltip.hide&&e.f7Tooltip.show&&(null!==n?(e.f7Tooltip.setText(e.formatTooltip()),e.f7Tooltip.setTargetEl(s.querySelector(`line[data-index="${n}"]`)),e.f7Tooltip.show()):e.f7Tooltip.hide())):(e.f7Tooltip=t.tooltip.create({trigger:"manual",containerEl:a,targetEl:s.querySelector(`line[data-index="${n}"]`),text:e.formatTooltip(),cssClass:"area-chart-tooltip"}),void(e.f7Tooltip&&e.f7Tooltip.show&&e.f7Tooltip.show()));e.f7Tooltip&&e.f7Tooltip.hide&&e.f7Tooltip.hide()}setCurrentIndex(e){e!==this.currentIndex&&(this.currentIndex=e,this.$el.trigger("areachart:select",{index:e}),this.emit("local::select areaChartSelect",this,e),this.$svgEl.find("line").removeClass("area-chart-current-line"),this.$svgEl.find(`line[data-index="${e}"]`).addClass("area-chart-current-line"),this.setTooltip())}onLegendClick(e){const t=parseInt(a(e.target).closest(".area-chart-legend-item").attr("data-index"),10);this.toggleDataset(t)}onMouseEnter(){this.calcLinesOffsets()}onMouseMove(e){const t=this;t.linesOffsets||t.calcLinesOffsets();let a=e.pageX;void 0===a&&(a=0);const s=t.linesOffsets.map((e=>Math.abs(a-e))),l=Math.min(...s),n=s.indexOf(l);t.setCurrentIndex(n)}onMouseLeave(){this.setCurrentIndex(null)}attachEvents(){const{svgEl:e,$el:t}=this;e&&(e.addEventListener("mouseenter",this.onMouseEnter),e.addEventListener("mousemove",this.onMouseMove),e.addEventListener("mouseleave",this.onMouseLeave),t.on("click",".area-chart-legend-item",this.onLegendClick))}detachEvents(){const{svgEl:e,$el:t}=this;e&&(e.removeEventListener("mouseenter",this.onMouseEnter),e.removeEventListener("mousemove",this.onMouseMove),e.removeEventListener("mouseleave",this.onMouseLeave),t.off("click",".area-chart-legend-item",this.onLegendClick))}render(){const e=this,{lineChart:t,toggleDatasets:a,width:s,height:l,axis:n,axisLabels:o,legend:r,datasets:i}=e.params,d=e.getChartData(),h=e.getVerticalLines(),c=e.getVisibleLabels(),u=a?"button":"span";return $jsx("div",null,$jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:s,height:l,viewBox:`0 0 ${s} ${l}`,preserveAspectRatio:"none"},d.map((e=>t?$jsx("path",{stroke:e.color,"fill-rule":"evenodd",d:e.points}):$jsx("polygon",{fill:e.color,"fill-rule":"evenodd",points:e.points}))),h.map(((e,t)=>$jsx("line",{"data-index":t,fill:"#000",x1:e,y1:0,x2:e,y2:l})))),n&&$jsx("div",{class:"area-chart-axis"},o.map((t=>$jsx("span",null,c.includes(t)&&$jsx("span",null,e.formatAxisLabel(t)))))),r&&$jsx("div",{class:"area-chart-legend"},i.map(((t,s)=>$jsx(u,{"data-index":s,class:"area-chart-legend-item "+(a?"area-chart-legend-button":""),_type:a?"button":void 0},$jsx("span",{style:`background-color: ${t.color}`}),e.formatLegendLabel(t.label))))))}update(e,t){void 0===e&&(e={}),void 0===t&&(t=!1);const s=this,{params:l}=s;if(Object.keys(e).forEach((t=>{void 0!==e[t]&&(l[t]=e[t])})),0===s.$svgEl.length)return s;s.detachEvents(),s.$svgEl.remove(),t||(s.$axisEl.remove(),s.$legendEl.remove());const n=a(s.render()),r=n.find("svg");if(o(s,{svgEl:r&&r[0],$svgEl:r}),!t){const e=n.find(".area-chart-axis"),t=n.find(".area-chart-legend");o(s,{$axisEl:e,$legendEl:t}),s.$el.append(e),s.$el.append(t)}return s.$el.prepend(r),s.attachEvents(),s}init(){const e=this,t=a(e.render()),s=t.find("svg"),l=t.find(".area-chart-axis"),n=t.find(".area-chart-legend");return o(e,{svgEl:s&&s[0],$svgEl:s,$axisEl:l,$legendEl:n}),e.$el.append(s),e.$el.append(l),e.$el.append(n),e.attachEvents(),e}destroy(){const e=this;e.$el&&!e.destroyed&&(e.$el.trigger("piechart:beforedestroy"),e.emit("local::beforeDestroy areaChartBeforeDestroy",e),e.detachEvents(),e.$svgEl.remove(),e.$axisEl.remove(),e.$legendEl.remove(),e.f7Tooltip&&e.f7Tooltip.destroy&&e.f7Tooltip.destroy(),delete e.$el[0].f7AreaChart,r(e),e.destroyed=!0)}}var d={name:"areaChart",params:{areaChart:{el:null,lineChart:!1,datasets:[],axis:!1,axisLabels:[],tooltip:!1,legend:!1,toggleDatasets:!1,width:640,height:320,maxAxisLabels:8,formatAxisLabel:null,formatLegendLabel:null,formatTooltip:null,formatTooltipAxisLabel:null,formatTooltipTotal:null,formatTooltipDataset:null}},create(){const e=this;e.areaChart=n({defaultSelector:".area-chart",constructor:i,app:e,domProp:"f7AreaChart"}),e.areaChart.update=function(t,s){if(0===a(t).length)return;const l=e.areaChart.get(t);return l?(l.update(s),l):void 0}}};if(t){if(e.prototype.modules&&e.prototype.modules[d.name])return;e.use(d),e.instance&&(e.instance.useModuleParams(d,e.instance.params),e.instance.useModule(d))}return d}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
