:root{--f7-actions-grid-button-font-size:12px;--f7-actions-grid-button-text-color:#757575;--f7-actions-grid-button-icon-size:48px}:root .dark,:root.dark{--f7-actions-label-text-color:rgba(255, 255, 255, 0.55)}.ios{--f7-actions-border-radius:13px;--f7-actions-button-padding:0px;--f7-actions-button-text-align:center;--f7-actions-button-height:57px;--f7-actions-button-height-landscape:44px;--f7-actions-button-font-size:20px;--f7-actions-button-icon-size:28px;--f7-actions-button-justify-content:center;--f7-actions-label-padding:8px 10px;--f7-actions-label-font-size:13px;--f7-actions-label-justify-content:center;--f7-actions-group-border-color:transparent;--f7-actions-group-margin:8px;--f7-actions-bg-color:rgba(255, 255, 255, 0.95);--f7-actions-bg-color-rgb:255,255,255;--f7-actions-button-border-color:rgba(0, 0, 0, 0.2);--f7-actions-button-pressed-bg-color:rgba(230, 230, 230, 0.9);--f7-actions-button-pressed-bg-color-rgb:230,230,230;--f7-actions-label-text-color:#8a8a8a}.ios .dark,.ios.dark{--f7-actions-bg-color:rgba(45, 45, 45, 0.95);--f7-actions-bg-color-rgb:45,45,45;--f7-actions-button-border-color:rgba(255, 255, 255, 0.15);--f7-actions-button-pressed-bg-color:rgba(50, 50, 50, 0.9);--f7-actions-button-pressed-bg-color-rgb:50,50,50}.md{--f7-actions-border-radius:0px;--f7-actions-button-border-color:transparent;--f7-actions-button-padding:0 16px;--f7-actions-button-text-align:left;--f7-actions-button-height:48px;--f7-actions-button-height-landscape:48px;--f7-actions-button-font-size:16px;--f7-actions-button-icon-size:24px;--f7-actions-button-justify-content:space-between;--f7-actions-label-padding:12px 16px;--f7-actions-label-font-size:16px;--f7-actions-label-justify-content:flex-start;--f7-actions-group-margin:0px;--f7-actions-bg-color:#fff;--f7-actions-button-pressed-bg-color:#e5e5e5;--f7-actions-label-text-color:rgba(0, 0, 0, 0.54);--f7-actions-group-border-color:rgba(0, 0, 0, 0.12)}.md .dark,.md.dark{--f7-actions-bg-color:#202020;--f7-actions-button-pressed-bg-color:#2e2e2e;--f7-actions-group-border-color:rgba(255, 255, 255, 0.15)}.aurora{--f7-actions-border-radius:8px;--f7-actions-button-padding:0 16px;--f7-actions-button-text-align:center;--f7-actions-button-height:48px;--f7-actions-button-height-landscape:48px;--f7-actions-button-font-size:16px;--f7-actions-button-icon-size:24px;--f7-actions-button-justify-content:space-between;--f7-actions-label-padding:10px 16px;--f7-actions-label-font-size:14px;--f7-actions-label-justify-content:center;--f7-actions-group-margin:16px;--f7-actions-bg-color:#fff;--f7-actions-button-border-color:rgba(0, 0, 0, 0.12);--f7-actions-button-pressed-bg-color:#e5e5e5;--f7-actions-label-text-color:rgba(0, 0, 0, 0.5);--f7-actions-group-border-color:rgba(0, 0, 0, 0.1)}.aurora .dark,.aurora.dark{--f7-actions-bg-color:#202020;--f7-actions-button-border-color:rgba(255, 255, 255, 0.15);--f7-actions-button-pressed-bg-color:#2e2e2e;--f7-actions-group-border-color:rgba(255, 255, 255, 0.15)}.actions-backdrop-unique{z-index:13500}.actions-modal{position:absolute;left:0;bottom:0;z-index:13500;width:100%;transform:translate3d(0,100%,0);display:none;max-height:100%;overflow:auto;-webkit-overflow-scrolling:touch;transition-property:transform;will-change:transform}.actions-modal.modal-in,.actions-modal.modal-out{transition-duration:.3s}.actions-modal.not-animated{transition-duration:0s}.actions-modal.modal-in{transform:translate3d(0,calc(-1 * var(--f7-safe-area-bottom)),0)}.actions-modal.modal-out{z-index:13499;transform:translate3d(0,100%,0)}@media (min-width:496px){.actions-modal{width:480px;left:50%;margin-left:-240px}}@media (orientation:landscape){.actions-modal{--f7-actions-button-height:var(--f7-actions-button-height-landscape)}}.actions-group{overflow:hidden;position:relative;margin:var(--f7-actions-group-margin);border-radius:var(--f7-actions-border-radius);transform:translate3d(0,0,0)}.actions-group:after{content:'';position:absolute;background-color:var(--f7-actions-group-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.actions-group:last-child:after{display:none!important}.actions-button,.actions-label{width:100%;font-weight:400;margin:0;box-sizing:border-box;display:block;position:relative;overflow:hidden;text-align:var(--f7-actions-button-text-align);background:var(--f7-actions-bg-color)}@supports ((-webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))){.ios-translucent-modals .actions-button,.ios-translucent-modals .actions-label{background-color:rgba(var(--f7-actions-bg-color-rgb),.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.actions-button:after,.actions-label:after{content:'';position:absolute;background-color:var(--f7-actions-button-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.actions-button:first-child,.actions-label:first-child{border-radius:var(--f7-actions-border-radius) var(--f7-actions-border-radius) 0 0}.actions-button:last-child,.actions-label:last-child{border-radius:0 0 var(--f7-actions-border-radius) var(--f7-actions-border-radius)}.actions-button:last-child:after,.actions-label:last-child:after{display:none!important}.actions-button:first-child:last-child,.actions-label:first-child:last-child{border-radius:var(--f7-actions-border-radius)}.actions-button a,.actions-label a{text-decoration:none;color:inherit;display:block}.actions-button b,.actions-button.actions-button-bold,.actions-label b,.actions-label.actions-button-bold{font-weight:600}.actions-button{cursor:pointer;display:flex;color:var(--f7-actions-button-text-color,var(--f7-theme-color));font-size:var(--f7-actions-button-font-size);height:var(--f7-actions-button-height);line-height:var(--f7-actions-button-height);padding:var(--f7-actions-button-padding);justify-content:var(--f7-actions-button-justify-content);z-index:10}.actions-button.active-state{background-color:var(--f7-actions-button-pressed-bg-color)!important}.actions-button[class*=color-]{color:var(--f7-theme-color)}@supports ((-webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))){.ios-translucent-modals .actions-button.active-state{background-color:rgba(var(--f7-actions-button-pressed-bg-color-rgb),.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.actions-button-media{flex-shrink:0;display:flex;align-items:center}.actions-button-media i.icon{width:var(--f7-actions-button-icon-size);height:var(--f7-actions-button-icon-size);font-size:var(--f7-actions-button-icon-size)}.actions-button a,.actions-button-text{position:relative;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.actions-button-text{width:100%;flex-shrink:1;text-align:var(--f7-actions-button-text-align)}.actions-label{line-height:1.3;display:flex;align-items:center;font-size:var(--f7-actions-label-font-size);color:var(--f7-actions-label-text-color);padding:var(--f7-actions-label-padding);justify-content:var(--f7-actions-label-justify-content);min-height:var(--f7-actions-label-min-height,var(--f7-actions-button-height))}.actions-label[class*=' color-']{--f7-actions-label-text-color:var(--f7-theme-color)}.actions-grid .actions-group{display:flex;flex-wrap:wrap;justify-content:flex-start;border-radius:0;background:var(--f7-actions-bg-color);margin-top:0}.actions-grid .actions-group:first-child{border-radius:var(--f7-actions-border-radius) var(--f7-actions-border-radius) 0 0}.actions-grid .actions-group:last-child{border-radius:0 0 var(--f7-actions-border-radius) var(--f7-actions-border-radius)}.actions-grid .actions-group:first-child:last-child{border-radius:var(--f7-actions-border-radius)}.actions-grid .actions-group:not(:last-child){margin-bottom:0}.actions-grid .actions-button,.actions-grid .actions-label{border-radius:0!important;background:0 0}.actions-grid .actions-button{width:33.33333333%;display:block;color:var(--f7-actions-grid-button-text-color);height:auto;line-height:1;padding:16px}.actions-grid .actions-button:after{display:none!important}.actions-grid .actions-button-media{margin-left:auto!important;margin-right:auto!important;width:var(--f7-actions-grid-button-icon-size);height:var(--f7-actions-grid-button-icon-size)}.actions-grid .actions-button-media i.icon{width:var(--f7-actions-grid-button-icon-size);height:var(--f7-actions-grid-button-icon-size);font-size:var(--f7-actions-grid-button-icon-size)}.actions-grid .actions-button-text{margin-left:0!important;text-align:center!important;margin-top:8px;line-height:1.33em;height:1.33em;font-size:var(--f7-actions-grid-button-font-size)}@supports ((-webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))){.ios-translucent-modals .actions-grid .actions-group{background-color:rgba(var(--f7-actions-bg-color-rgb),.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.ios .actions-button-media{margin-left:16px}.ios .actions-button-media+.actions-button-text{text-align:left;margin-left:16px}.md .actions-button{transition-duration:.3s}.md .actions-button-media{min-width:40px}.md .actions-button-media+.actions-button-text{margin-left:16px}.aurora .actions-button-media{margin-left:16px}.aurora .actions-button-media+.actions-button-text{text-align:left;margin-left:16px}