.ios {
  .item-label,
  .item-floating-label {
    + .item-input-wrap {
      margin-top: 0;
    }
  }
  .item-input-focused .item-floating-label {
    color: var(--f7-label-text-color);
  }
  .item-input {
    .item-media {
      align-self: flex-start;
    }
  }
  .item-input-wrap {
    margin-top: calc(-1 * var(--f7-list-item-padding-vertical));
    margin-bottom: calc(-1 * var(--f7-list-item-padding-vertical));
  }
  .inline-labels,
  .inline-label {
    .item-label,
    .item-floating-label {
      + .item-input-wrap {
        margin-top: calc(-1 * var(--f7-list-item-padding-vertical));
      }
    }
    .item-input-wrap {
      margin-top: calc(-1 * var(--f7-list-item-padding-vertical));
    }
  }
  .item-input-error-message,
  .item-input-info,
  .input-error-message,
  .input-info {
    position: relative;
    margin-bottom: 6px;
    margin-top: -8px;
  }

  .item-input-focused {
    .item-label,
    .item-floating-label {
      color: var(--f7-label-focused-text-color, var(--f7-label-text-color));
    }
    .item-inner:after {
      background: var(--f7-input-focused-border-color, var(--f7-list-item-border-color));
    }
  }
  .item-input-invalid {
    .item-label,
    .item-floating-label {
      color: var(--f7-label-invalid-text-color, var(--f7-label-text-color));
    }
    .item-inner:after {
      background: var(--f7-input-invalid-border-color, var(--f7-list-item-border-color));
    }
  }
  .item-input-invalid,
  .input-invalid {
    input,
    select,
    textarea {
      color: var(--f7-input-invalid-text-color, var(--f7-input-error-text-color));
    }
  }

  .input-clear-button {
    &:after {
      content: 'delete_round_ios';
      font-size: calc(var(--f7-input-clear-button-size) / (14 / 10));
      line-height: 1.4;
    }
    &:before {
      width: 44px;
      height: 44px;
      margin-left: -22px;
      margin-top: -22px;
    }
  }

  .item-input-outline,
  .input-outline {
    .item-input-wrap {
      margin-top: 0;
      margin-bottom: 0;
    }
    .item-input-error-message,
    .item-input-info,
    .input-error-message,
    .input-info {
      margin-top: 0;
      white-space: normal;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .item-input-info,
    .input-info {
      margin-bottom: calc(-1 * var(--f7-input-info-font-size) * var(--f7-input-info-line-height));
    }
    .item-input-error-message,
    .input-error-message {
      margin-bottom: calc(-1 * var(--f7-input-error-font-size) * var(--f7-input-error-line-height));
    }
    &.item-input-with-info,
    &.input-with-info {
      .item-input-wrap {
        margin-bottom: calc(var(--f7-input-info-font-size) * var(--f7-input-info-line-height));
      }
    }
    &.item-input-with-error-message,
    &.input-with-error-message {
      .item-input-wrap {
        margin-bottom: calc(var(--f7-input-error-font-size) * var(--f7-input-error-line-height));
      }
    }
  }
}
