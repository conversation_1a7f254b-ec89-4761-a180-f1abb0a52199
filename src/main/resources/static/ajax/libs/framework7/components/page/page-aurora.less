.aurora {
  .page-next {
    opacity: 0;
    pointer-events: none;
    transform: translate3d(0, 0px, 0);
    &.page-next-on-right {
      .ltr({
        transform: translate3d(100%, 0, 0);
      });
      .rtl({
        transform: translate3d(-100%, 0, 0);
      });
    }
  }

  // Animations
  .router-transition-forward {
    .page-next {
      animation: aurora-page-next-to-current var(--f7-page-transition-duration) forwards;
    }
    .page-current {
      animation: none;
    }
  }
  .router-transition-backward {
    .page-current {
      animation: aurora-page-current-to-next var(--f7-page-transition-duration) forwards;
    }
    .page-previous {
      animation: none;
    }
  }
}
@keyframes aurora-page-next-to-current {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes aurora-page-current-to-next {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
