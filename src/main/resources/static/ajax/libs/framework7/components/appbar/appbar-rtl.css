:root{--f7-appbar-shadow-image:none}.ios{--f7-appbar-height:44px;--f7-appbar-inner-padding-left:8px;--f7-appbar-inner-padding-right:8px}.md{--f7-appbar-height:48px;--f7-appbar-inner-padding-left:16px;--f7-appbar-inner-padding-right:16px}.aurora{--f7-appbar-height:64px;--f7-appbar-inner-padding-left:16px;--f7-appbar-inner-padding-right:16px}.appbar{position:relative;left:0;top:0;width:100%;z-index:500;-webkit-backface-visibility:hidden;backface-visibility:hidden;box-sizing:border-box;margin:0;transform:translate3d(0,0,0);height:calc(var(--f7-appbar-height) + var(--f7-safe-area-top));background-image:var(--f7-appbar-bg-image,var(--f7-bars-bg-image));background-color:var(--f7-appbar-bg-color,var(--f7-bars-bg-color));color:var(--f7-appbar-text-color,var(--f7-bars-text-color));font-size:var(--f7-appbar-font-size);z-index:7000}@supports ((-webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))){.ios-translucent-bars .appbar{background-color:rgba(var(--f7-appbar-bg-color-rgb,var(--f7-bars-bg-color-rgb)),var(--f7-bars-translucent-opacity));-webkit-backdrop-filter:saturate(180%) blur(var(--f7-bars-translucent-blur));backdrop-filter:saturate(180%) blur(var(--f7-bars-translucent-blur))}}.appbar .panel~.appbar{z-index:5500}.appbar a{color:var(--f7-appbar-link-color,var(--f7-bars-link-color,var(--f7-theme-color)))}.appbar a.link{display:flex;justify-content:flex-start;line-height:var(--f7-appbar-link-line-height, var(--f7-appbar-height));height:var(--f7-appbar-link-height,var(--f7-appbar-height))}.appbar .center,.appbar .left,.appbar .right{display:flex;align-items:center}.appbar.no-border:after,.appbar.no-hairline:after{display:none!important}.appbar.no-border .title-large:after,.appbar.no-hairline .title-large:after{display:none!important}.appbar.no-shadow:before{display:none!important}.appbar:after,.appbar:before{-webkit-backface-visibility:hidden;backface-visibility:hidden}.appbar:after{content:'';position:absolute;background-color:var(--f7-appbar-border-color,var(--f7-bars-border-color));display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.appbar:before{content:'';position:absolute;right:0;width:100%;top:100%;bottom:auto;height:8px;pointer-events:none;background:var(--f7-appbar-shadow-image)}.appbar:after{z-index:1}.appbar~*{--f7-appbar-app-offset:calc(var(--f7-appbar-height) + var(--f7-appbar-extra-offset, 0px) + var(--f7-safe-area-top))}.appbar~.appbar,.appbar~.view,.appbar~.views{--f7-safe-area-top:0px}.appbar~.panel .page,.appbar~.panel .page-content,.appbar~.panel .view{--f7-safe-area-top:0px}.appbar-inner{position:absolute;left:0;top:var(--f7-safe-area-top);width:100%;height:var(--f7-appbar-height);display:flex;align-items:center;justify-content:space-between;box-sizing:border-box;padding:0 calc(var(--f7-appbar-inner-padding-right) + var(--f7-safe-area-right)) 0 calc(var(--f7-appbar-inner-padding-left) + var(--f7-safe-area-left))}.appbar-inner.stacked{display:none}