(function framework7ComponentLoader(p,o){void 0===o&&(o=!0);var e=p.$,s=p.utils,t=p.getDevice,a=p.getSupport,r=(p.Class,p.Modal),l=(p.ConstructorMethods,p.ModalMethods),n=s.extend,i=s.now,c=s.nextTick;class u extends r{constructor(p,o){const s=n({on:{}},p.params.popup,o);super(p,s);const r=this,l=getWindow(),u=getDocument(),d=a(),m=t();let h,f,g,v;if(r.params=s,h=r.params.el?e(r.params.el).eq(0):e(r.params.content).filter((p=>1===p.nodeType)).eq(0),h&&h.length>0&&h[0].f7Modal)return h[0].f7Modal;if(0===h.length)return r.destroy();function y(p){const o=p.target,s=e(o);if(!(!m.desktop&&m.cordova&&(l.Keyboard&&l.Keyboard.isVisible||l.cordova.plugins&&l.cordova.plugins.Keyboard&&l.cordova.plugins.Keyboard.isVisible))&&0===s.closest(r.el).length&&r.params&&r.params.closeByBackdropClick&&r.params.backdrop&&r.backdropEl&&r.backdropEl===o){let p=!0;r.$el.nextAll(".popup.modal-in").each((o=>{const e=o.f7Modal;e&&e.params.closeByBackdropClick&&e.params.backdrop&&e.backdropEl===r.backdropEl&&(p=!1)})),p&&r.close()}}function b(p){27===p.keyCode&&r.params.closeOnEscape&&r.close()}function w(o){return(p.height-2*o)/p.height}r.params.backdrop&&r.params.backdropEl?f=e(r.params.backdropEl):r.params.backdrop&&(r.params.backdropUnique?(f=e('<div class="popup-backdrop popup-backdrop-unique"></div>'),r.$containerEl.append(f)):f=r.$containerEl.children(".popup-backdrop"),0===f.length&&(f=e('<div class="popup-backdrop"></div>'),r.$containerEl.append(f))),n(r,{app:p,push:h.hasClass("popup-push")||r.params.push,$el:h,el:h[0],$backdropEl:f,backdropEl:f&&f[0],type:"popup",$htmlEl:e("html")}),r.params.push&&h.addClass("popup-push");let k,C,E,$,M,T,x,B,P,S,q,H=!0,A=!1,D=!1;function K(p){!A&&H&&r.params.swipeToClose&&(r.params.swipeHandler&&0===e(p.target).closest(r.params.swipeHandler).length||(A=!0,D=!1,k={x:"touchstart"===p.type?p.targetTouches[0].pageX:p.pageX,y:"touchstart"===p.type?p.targetTouches[0].pageY:p.pageY},$=i(),E=void 0,r.params.swipeHandler||"touchstart"!==p.type||(T=e(p.target).closest(".page-content")[0])))}function O(o){if(!A)return;if(C={x:"touchmove"===o.type?o.targetTouches[0].pageX:o.pageX,y:"touchmove"===o.type?o.targetTouches[0].pageY:o.pageY},void 0===E&&(E=!!(E||Math.abs(C.x-k.x)>Math.abs(C.y-k.y))),E)return A=!1,void(D=!1);M=k.y-C.y,v&&g&&M>0&&(M=0);const e=M<0?"to-bottom":"to-top";if(h.transition(0),"string"==typeof r.params.swipeToClose&&e!==r.params.swipeToClose)return h.transform(""),void h.transition("");if(D)r.emit("local::swipeMove popupSwipeMove",r),r.$el.trigger("popup:swipemove");else{if(v&&g&&(S=h[0].offsetHeight,q=h.prevAll(".popup.modal-in").eq(0),0===q.length&&(q=p.$el.children(".view, .views"))),T&&(x=T.scrollTop,P=T.scrollHeight,B=T.offsetHeight,!(P===B||"to-bottom"===e&&0===x||"to-top"===e&&x===P-B)))return h.transform(""),h.transition(""),A=!1,void(D=!1);D=!0,r.emit("local::swipeStart popupSwipeStart",r),r.$el.trigger("popup:swipestart")}if(o.preventDefault(),v&&g){const p=1-Math.abs(M/S),o=1-(1-w(g))*p;q.hasClass("popup")?q.hasClass("popup-push")?q.transition(0).forEach((e=>{e.style.setProperty("transform",`translate3d(0, calc(-1 * ${p} * (var(--f7-popup-push-offset) + 10px)) , 0px) scale(${o})`,"important")})):q.transition(0).forEach((p=>{p.style.setProperty("transform",`translate3d(0, 0px , 0px) scale(${o})`,"important")})):q.transition(0).forEach((p=>{p.style.setProperty("transform",`translate3d(0,0,0) scale(${o})`,"important")}))}h.transition(0).transform(`translate3d(0,${-M}px,0)`)}function X(){if(A=!1,!D)return;r.emit("local::swipeEnd popupSwipeEnd",r),r.$el.trigger("popup:swipeend"),D=!1,H=!1,h.transition(""),v&&g&&q.transition("").transform("");const p=M<=0?"to-bottom":"to-top";if("string"==typeof r.params.swipeToClose&&p!==r.params.swipeToClose)return h.transform(""),void(H=!0);const o=Math.abs(M),e=(new Date).getTime()-$;e<300&&o>20||e>=300&&o>100?c((()=>{"to-bottom"===p?h.addClass("swipe-close-to-bottom"):h.addClass("swipe-close-to-top"),h.transform(""),r.emit("local::swipeclose popupSwipeClose",r),r.$el.trigger("popup:swipeclose"),r.close(),H=!0})):(H=!0,h.transform(""))}const Y=!!d.passiveListener&&{passive:!0};let z;r.params.swipeToClose&&(h.on(p.touchEvents.start,K,Y),p.on("touchmove",O),p.on("touchend:passive",X),r.once("popupDestroy",(()=>{h.off(p.touchEvents.start,K,Y),p.off("touchmove",O),p.off("touchend:passive",X)})));const N=()=>{const o=v;r.push&&(v=r.push&&(p.width<630||p.height<630||h.hasClass("popup-tablet-fullscreen"))),v&&!o?L():v&&o?r.$htmlEl[0].style.setProperty("--f7-popup-push-scale",w(g)):!v&&o&&(r.$htmlEl.removeClass("with-modal-popup-push"),r.$htmlEl[0].style.removeProperty("--f7-popup-push-scale"))},L=()=>{p.off("resize",N),r.push&&(v=r.push&&(p.width<630||p.height<630||h.hasClass("popup-tablet-fullscreen"))),v&&(g=parseInt(h.css("--f7-popup-push-offset"),10),Number.isNaN(g)&&(g=0),g&&(h.addClass("popup-push"),r.$htmlEl.addClass("with-modal-popup-push"),r.$htmlEl[0].style.setProperty("--f7-popup-push-scale",w(g)))),p.on("resize",N)};return r.on("open",(()=>{z=!1,r.params.closeOnEscape&&e(u).on("keydown",b),h.prevAll(".popup.modal-in").addClass("popup-behind"),L()})),r.on("opened",(()=>{h.removeClass("swipe-close-to-bottom swipe-close-to-top"),r.params.closeByBackdropClick&&p.on("click",y)})),r.on("close",(()=>{z=r.$el.prevAll(".popup-push.modal-in").length>0,r.params.closeOnEscape&&e(u).off("keydown",b),r.params.closeByBackdropClick&&p.off("click",y),h.prevAll(".popup.modal-in").eq(0).removeClass("popup-behind"),v&&g&&!z&&(r.$htmlEl.removeClass("with-modal-popup-push"),r.$htmlEl.addClass("with-modal-popup-push-closing")),p.off("resize",N)})),r.on("closed",(()=>{h.removeClass("popup-behind"),v&&g&&!z&&(r.$htmlEl.removeClass("with-modal-popup-push-closing"),r.$htmlEl[0].style.removeProperty("--f7-popup-push-scale"))})),h[0].f7Modal=r,r}}var d={name:"popup",params:{popup:{backdrop:!0,backdropEl:void 0,backdropUnique:!1,closeByBackdropClick:!0,closeOnEscape:!1,swipeToClose:!1,swipeHandler:null,push:!1,containerEl:null}},static:{Popup:u},create(){this.popup=l({app:this,constructor:u,defaultSelector:".popup.modal-in",parentSelector:".popup"})},clicks:{".popup-open":function(p,o){void 0===o&&(o={});this.popup.open(o.popup,o.animate,p)},".popup-close":function(p,o){void 0===o&&(o={});this.popup.close(o.popup,o.animate,p)}}};if(o){if(p.prototype.modules&&p.prototype.modules[d.name])return;p.use(d),p.instance&&(p.instance.useModuleParams(d,p.instance.params),p.instance.useModule(d))}return d}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
