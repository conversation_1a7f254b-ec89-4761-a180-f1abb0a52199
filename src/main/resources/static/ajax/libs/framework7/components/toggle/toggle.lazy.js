(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var o=e.$,s=e.utils,a=(e.getDevice,e.getSupport),n=e.Class,l=(e.<PERSON>,e.ConstructorMethods),i=(e.<PERSON>dal<PERSON>eth<PERSON>,s.extend),c=s.now,g=s.nextTick,r=s.deleteProps;class d extends n{constructor(e,t){void 0===t&&(t={}),super(t,[e]);const s=this,n=a(),l={};s.useModulesParams(l),s.params=i(l,t);const r=s.params.el;if(!r)return s;const d=o(r);if(0===d.length)return s;if(d[0].f7Toggle)return d[0].f7Toggle;const h=d.children('input[type="checkbox"]');let u;i(s,{app:e,$el:d,el:d[0],$inputEl:h,inputEl:h[0],disabled:d.hasClass("disabled")||h.hasClass("disabled")||h.attr("disabled")||h[0].disabled}),Object.defineProperty(s,"checked",{enumerable:!0,configurable:!0,set(e){s&&void 0!==s.$inputEl&&s.checked!==e&&(h[0].checked=e,s.$inputEl.trigger("change"))},get:()=>h[0].checked}),d[0].f7Toggle=s;const f={};let p,v,m,b,y;function T(e){u||s.disabled||(f.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,f.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,v=0,u=!0,p=void 0,b=c(),y=s.checked,m=d[0].offsetWidth,g((()=>{u&&d.addClass("toggle-active-state")})))}function M(t){if(!u||s.disabled)return;const o="touchmove"===t.type?t.targetTouches[0].pageX:t.pageX,a="touchmove"===t.type?t.targetTouches[0].pageY:t.pageY,n=e.rtl?-1:1;if(void 0===p&&(p=!!(p||Math.abs(a-f.y)>Math.abs(o-f.x))),p)return void(u=!1);let l;t.preventDefault(),v=o-f.x,v*n<0&&Math.abs(v)>m/3&&y&&(l=!0),v*n>0&&Math.abs(v)>m/3&&!y&&(l=!0),l&&(f.x=o,s.checked=!y,y=!y)}function k(){if(!u||s.disabled)return p&&d.removeClass("toggle-active-state"),void(u=!1);const t=e.rtl?-1:1;let o;u=!1,d.removeClass("toggle-active-state"),c()-b<300&&(v*t<0&&y&&(o=!0),v*t>0&&!y&&(o=!0),o&&(s.checked=!y))}function E(){s.$el.trigger("toggle:change"),s.emit("local::change toggleChange",s)}s.attachEvents=function(){const t=!!n.passiveListener&&{passive:!0};d.on(e.touchEvents.start,T,t),e.on("touchmove",M),e.on("touchend:passive",k),s.$inputEl.on("change",E)},s.detachEvents=function(){const t=!!n.passiveListener&&{passive:!0};d.off(e.touchEvents.start,T,t),e.off("touchmove",M),e.off("touchend:passive",k),s.$inputEl.off("change",E)},s.useModules(),s.init()}toggle(){this.checked=!this.checked}init(){this.attachEvents()}destroy(){let e=this;e.$el.trigger("toggle:beforedestroy"),e.emit("local::beforeDestroy toggleBeforeDestroy",e),delete e.$el[0].f7Toggle,e.detachEvents(),r(e),e=null}}var h={name:"toggle",create(){this.toggle=l({defaultSelector:".toggle",constructor:d,app:this,domProp:"f7Toggle"})},static:{Toggle:d},on:{tabMounted(e){const t=this;o(e).find(".toggle-init").each((e=>t.toggle.create({el:e})))},tabBeforeRemove(e){o(e).find(".toggle-init").each((e=>{e.f7Toggle&&e.f7Toggle.destroy()}))},pageInit(e){const t=this;e.$el.find(".toggle-init").each((e=>t.toggle.create({el:e})))},pageBeforeRemove(e){e.$el.find(".toggle-init").each((e=>{e.f7Toggle&&e.f7Toggle.destroy()}))}},vnode:{"toggle-init":{insert(e){const t=e.elm;this.toggle.create({el:t})},destroy(e){const t=e.elm;t.f7Toggle&&t.f7Toggle.destroy()}}}};if(t){if(e.prototype.modules&&e.prototype.modules[h.name])return;e.use(h),e.instance&&(e.instance.useModuleParams(h,e.instance.params),e.instance.useModule(h))}return h}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
