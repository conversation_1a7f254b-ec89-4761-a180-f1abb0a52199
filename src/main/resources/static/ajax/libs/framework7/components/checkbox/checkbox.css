:root{--f7-checkbox-icon-color:#fff;--f7-checkbox-extra-margin:0px}:root .dark,:root.dark{--f7-checkbox-inactive-color:rgba(255, 255, 255, 0.3);--f7-checkbox-icon-color:#000}.ios{--f7-checkbox-size:22px;--f7-checkbox-border-radius:50%;--f7-checkbox-border-width:1px;--f7-checkbox-inactive-color:#c7c7cc}.md{--f7-checkbox-size:18px;--f7-checkbox-border-radius:2px;--f7-checkbox-border-width:2px;--f7-checkbox-inactive-color:#6d6d6d}.aurora{--f7-checkbox-size:16px;--f7-checkbox-border-radius:4px;--f7-checkbox-border-width:1px;--f7-checkbox-inactive-color:rgba(0, 0, 0, 0.25)}.checkbox{position:relative;display:inline-block;vertical-align:middle;z-index:1;background-color:transparent;--f7-touch-ripple-color:rgba(var(--f7-theme-color-rgb), 0.5)}.checkbox i,.icon-checkbox{flex-shrink:0;border:var(--f7-checkbox-border-width) solid var(--f7-checkbox-inactive-color);width:var(--f7-checkbox-size);height:var(--f7-checkbox-size);border-radius:var(--f7-checkbox-border-radius);box-sizing:border-box;position:relative;display:block}.checkbox i:after,.icon-checkbox:after{font-family:framework7-core-icons;font-weight:400;font-style:normal;line-height:1;letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:'liga';text-align:center;display:block;width:100%;height:100%;font-size:20px;width:var(--f7-checkbox-size);height:var(--f7-checkbox-size);line-height:var(--f7-checkbox-size);top:calc(0px - var(--f7-checkbox-border-width));opacity:0;color:var(--f7-checkbox-icon-color);position:relative;transition-property:opacity;left:calc(0px - var(--f7-checkbox-border-width))}.checkbox input[type=checkbox]:not(:checked)~i:after,label.item-checkbox input[type=checkbox]:not(:checked)~* .icon-checkbox:after,label.item-checkbox input[type=checkbox]:not(:checked)~.icon-checkbox:after{font-size:0}.checkbox input[type=checkbox]:checked~i,.checkbox input[type=checkbox]:indeterminate~i,label.item-checkbox input[type=checkbox]:checked~* .icon-checkbox,label.item-checkbox input[type=checkbox]:checked~.icon-checkbox,label.item-checkbox input[type=checkbox]:indeterminate~* .icon-checkbox,label.item-checkbox input[type=checkbox]:indeterminate~.icon-checkbox{border-color:var(--f7-checkbox-active-color,var(--f7-theme-color));background-color:var(--f7-checkbox-active-color,var(--f7-theme-color))}.checkbox input[type=checkbox]:checked~i:after,.checkbox input[type=checkbox]:indeterminate~i:after,label.item-checkbox input[type=checkbox]:checked~* .icon-checkbox:after,label.item-checkbox input[type=checkbox]:checked~.icon-checkbox:after,label.item-checkbox input[type=checkbox]:indeterminate~* .icon-checkbox:after,label.item-checkbox input[type=checkbox]:indeterminate~.icon-checkbox:after{opacity:1}.checkbox input[type=checkbox]:indeterminate~i:after,label.item-checkbox input[type=checkbox]:indeterminate~* .icon-checkbox:after,label.item-checkbox input[type=checkbox]:indeterminate~.icon-checkbox:after{font-size:0;content:'';position:absolute;top:50%;width:70%;background:var(--f7-checkbox-icon-color);height:2px;border-radius:2px;margin-top:-1px;transition:0s;left:15%}.checkbox,label.item-checkbox{cursor:pointer}.checkbox input[type=checkbox],.checkbox input[type=radio],label.item-checkbox input[type=checkbox],label.item-checkbox input[type=radio]{display:none}label.item-checkbox{transition-duration:.3s}label.item-checkbox .item-content .item-media,label.item-checkbox.item-content .item-media{align-self:center}label.item-checkbox>.icon-checkbox{margin-right:calc(var(--f7-list-item-media-margin) + var(--f7-checkbox-extra-margin))}label.item-checkbox.active-state{background-color:var(--f7-list-link-pressed-bg-color)}label.item-checkbox.active-state:after{background-color:transparent}.disabled label.item-checkbox,label.item-checkbox.disabled{opacity:.55;pointer-events:none}.ios .checkbox i:after,.ios .icon-checkbox:after{content:'checkbox_ios';font-size:21px}.ios label.item-checkbox.active-state{transition-duration:0s}.ios .checkbox input[type=checkbox]:indeterminate~i:after,.ios label.item-checkbox input[type=checkbox]:indeterminate~* .icon-checkbox:after,.ios label.item-checkbox input[type=checkbox]:indeterminate~.icon-checkbox:after{height:2px;margin-top:-1px}.md .checkbox i,.md .icon-checkbox{transition-duration:.2s}.md .checkbox i:after,.md .icon-checkbox:after{content:'checkbox_md';transition-duration:.2s;font-size:15px}.md label.item-checkbox{position:relative;overflow:hidden;z-index:0}.aurora .checkbox i,.aurora .icon-checkbox{transition-duration:150ms}.aurora .checkbox i:after,.aurora .icon-checkbox:after{content:'checkbox_aurora';transition-duration:150ms;font-size:21px}.aurora .checkbox i:before,.aurora .icon-checkbox:before{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background:rgba(0,0,0,.1);opacity:0;transition-duration:150ms}.aurora .checkbox.active-state i:before{opacity:1}.aurora label.item-checkbox{position:relative;overflow:hidden;z-index:0}.aurora.device-desktop label.item-checkbox:hover:not(.active-state):not(.no-hover){background-color:var(--f7-list-link-hover-bg-color)}