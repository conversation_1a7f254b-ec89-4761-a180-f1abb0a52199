(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var r=e.$,s=e.utils,i=(e.getDevice,e.getSupport,e.<PERSON>,e.<PERSON>,e.<PERSON>structor<PERSON>,e.<PERSON>,s.extend);function a(e,t){let r=t.css(`min-${e}`);return"auto"===r||"none"===r?r=0:r.indexOf("px")>=0?r=parseFloat(r):r.indexOf("%")>=0&&(r=t.parent()[0]["height"===e?"offsetHeight":"offsetWidth"]*parseFloat(r)/100),r}function o(e,t){let r=t.css(`max-${e}`);return"auto"===r||"none"===r?r=null:r.indexOf("px")>=0?r=parseFloat(r):r.indexOf("%")>=0&&(r=t.parent()[0]["height"===e?"offsetHeight":"offsetWidth"]*parseFloat(r)/100),r}const n={init(){const e=this;let t,s,i,n,l,g,h,c,d,p,f,u,v,z,m,x,b;const w=getDocument();r(w).on(e.touchEvents.start,".col > .resize-handler, .row > .resize-handler",(function(e){t||s||(l=r(e.target).closest(".resize-handler"),i="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,n="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,t=!0,g=void 0,h=void 0,b=void 0)})),e.on("touchmove",(function(r){if(!t)return;const w=1===l.parent(".row").length,$=w?"height":"width",y=w?"offsetHeight":"offsetWidth";s||(g=l.parent(w?".row":".col"),!g.length||g.hasClass("resizable")&&!g.hasClass("resizable-fixed")||(g=g.prevAll(".resizable:not(.resizable-fixed)").eq(0)),h=g.next(w?".row":".col"),!h.length||h.hasClass("resizable")&&!h.hasClass("resizable-fixed")||(h=h.nextAll(".resizable:not(.resizable-fixed)").eq(0)),g.length&&(c=g[0][y],d=a($,g),p=o($,g),z=g.parent()[0][y],m=g.parent().children(w?".row":'[class*="col-"], .col').length,x=parseFloat(g.css(w?"--f7-grid-row-gap":"--f7-grid-gap"))),h.length&&(f=h[0][y],u=a($,h),v=o($,h),g.length||(z=h.parent()[0][y],m=h.parent().children(w?".row":'[class*="col-"], .col').length,x=parseFloat(h.css(w?"--f7-grid-row-gap":"--f7-grid-gap"))))),s=!0;const C="touchmove"===r.type?r.targetTouches[0].pageX:r.pageX,M="touchmove"===r.type?r.targetTouches[0].pageY:r.pageY;if(void 0!==b||w||(b=!!(b||Math.abs(M-n)>Math.abs(C-i))),b)return t=!1,void(s=!1);const F=g.hasClass("resizable-absolute")||h.hasClass("resizable-absolute"),O=!w||w&&!F;if(O&&!h.length||!g.length)return t=!1,void(s=!1);r.preventDefault();let R,T,X=w?M-n:C-i;if(g.length&&(R=c+X,R<d&&(R=d,X=R-c),p&&R>p&&(R=p,X=R-c)),h.length&&O&&(T=f-X,T<u&&(T=u,X=f-T,R=c+X),v&&T>v&&(T=v,X=f-T,R=c+X)),F)return g[0].style[$]=`${R}px`,O&&(h[0].style[$]=`${T}px`),g.trigger("grid:resize"),h.trigger("grid:resize"),e.emit("gridResize",g[0]),void e.emit("gridResize",h[0]);const Y=(m-1)*x/m,D=w?`${m-1} * var(--f7-grid-row-gap) / ${m}`:"(var(--f7-cols-per-row) - 1) * var(--f7-grid-gap) / var(--f7-cols-per-row)",H=R+Y,W=T+Y;g[0].style[$]=`calc(${H/z*100}% - ${D})`,h[0].style[$]=`calc(${W/z*100}% - ${D})`,g.trigger("grid:resize"),h.trigger("grid:resize"),e.emit("gridResize",g[0]),e.emit("gridResize",h[0])})),e.on("touchend",(function(){t&&(s||(t=!1),t=!1,s=!1)}))}};var l={name:"grid",create(){i(this,{grid:{init:n.init.bind(this)}})},on:{init(){this.grid.init()}}};if(t){if(e.prototype.modules&&e.prototype.modules[l.name])return;e.use(l),e.instance&&(e.instance.useModuleParams(l,e.instance.params),e.instance.useModule(l))}return l}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
