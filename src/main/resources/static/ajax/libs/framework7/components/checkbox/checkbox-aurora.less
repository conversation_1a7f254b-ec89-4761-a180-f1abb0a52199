.aurora {
  .icon-checkbox,
  .checkbox i {
    transition-duration: 150ms;
    &:after {
      content: 'checkbox_aurora';
      transition-duration: 150ms;
      font-size: 21px;
    }
    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.1);
      opacity: 0;
      transition-duration: 150ms;
    }
  }
  .checkbox.active-state i:before {
    opacity: 1;
  }

  label.item-checkbox {
    position: relative;
    overflow: hidden;
    z-index: 0;
  }

  &.device-desktop {
    label.item-checkbox {
      &:hover:not(.active-state):not(.no-hover) {
        background-color: var(--f7-list-link-hover-bg-color);
      }
    }
  }
}
