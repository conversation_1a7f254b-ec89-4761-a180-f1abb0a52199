(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var a=e.$,s=e.utils,r=(e.getDevice,e.getSupport,e.Class),n=(e.<PERSON>,e.ConstructorMethods),i=(e.<PERSON>dal<PERSON>ethods,s.extend),l=s.deleteProps;class c extends r{constructor(e,t){void 0===t&&(t={}),super(t,[e]);const s=this,r={top:!1,topOffset:0,bottomOffset:0,attachments:[],renderAttachments:void 0,renderAttachment:void 0,maxHeight:null,resizePage:!0};s.useModulesParams(r),s.params=i(r,t);const n=a(s.params.el);if(0===n.length)return s;if(n[0].f7Messagebar)return n[0].f7Messagebar;n[0].f7Messagebar=s;const l=n.parents(".page").eq(0),c=l.find(".page-content").eq(0),h=n.find(".messagebar-area");let o;o=s.params.textareaEl?a(s.params.textareaEl):n.find("textarea");const m=n.find(".messagebar-attachments"),g=n.find(".messagebar-sheet");function d(){s.params.resizePage&&s.resizePage()}function b(e){e.preventDefault()}function u(e){const t=a(this).index();a(e.target).closest(".messagebar-attachment-delete").length?(a(this).trigger("messagebar:attachmentdelete",t),s.emit("local::attachmentDelete messagebarAttachmentDelete",s,this,t)):(a(this).trigger("messagebar:attachmentclick",t),s.emit("local::attachmentClick messagebarAttachmentClick",s,this,t))}function f(){s.checkEmptyState(),s.$el.trigger("messagebar:change"),s.emit("local::change messagebarChange",s)}function p(){s.sheetHide(),s.$el.addClass("messagebar-focused"),s.$el.trigger("messagebar:focus"),s.emit("local::focus messagebarFocus",s)}function $(){s.$el.removeClass("messagebar-focused"),s.$el.trigger("messagebar:blur"),s.emit("local::blur messagebarBlur",s)}return s.params.top&&n.addClass("messagebar-top"),i(s,{$el:n,el:n[0],$areaEl:h,areaEl:h[0],$textareaEl:o,textareaEl:o[0],$attachmentsEl:m,attachmentsEl:m[0],attachmentsVisible:m.hasClass("messagebar-attachments-visible"),$sheetEl:g,sheetEl:g[0],sheetVisible:g.hasClass("messagebar-sheet-visible"),$pageEl:l,pageEl:l[0],$pageContentEl:c,pageContentEl:c,top:n.hasClass("messagebar-top")||s.params.top,attachments:[]}),s.attachEvents=function(){n.on("textarea:resize",d),n.on("submit",b),n.on("click",".messagebar-attachment",u),o.on("change input",f),o.on("focus",p),o.on("blur",$),e.on("resize",d)},s.detachEvents=function(){n.off("textarea:resize",d),n.off("submit",b),n.off("click",".messagebar-attachment",u),o.off("change input",f),o.off("focus",p),o.off("blur",$),e.off("resize",d)},s.useModules(),s.init(),s}focus(){return this.$textareaEl.focus(),this}blur(){return this.$textareaEl.blur(),this}clear(){return this.$textareaEl.val("").trigger("change"),this}getValue(){return this.$textareaEl.val().trim()}setValue(e){return this.$textareaEl.val(e).trigger("change"),this}setPlaceholder(e){return this.$textareaEl.attr("placeholder",e),this}resizePage(){const e=this,{params:t,$el:a,top:s,$pageEl:r,$pageContentEl:n,$areaEl:i,$textareaEl:l,$sheetEl:c,$attachmentsEl:h}=e,o=a[0].offsetHeight;let m=t.maxHeight;if(s);else{const s=parseInt(n.css("padding-bottom"),10),g=o+t.bottomOffset;if(g!==s&&n.length){const t=parseInt(n.css("padding-top"),10),s=n[0].scrollHeight,o=n[0].offsetHeight,d=n[0].scrollTop===s-o;m||(m=r[0].offsetHeight-t-c.outerHeight()-h.outerHeight()-parseInt(i.css("margin-top"),10)-parseInt(i.css("margin-bottom"),10)),l.css("max-height",`${m}px`),n.css("padding-bottom",`${g}px`),d&&n.scrollTop(n[0].scrollHeight-o),a.trigger("messagebar:resizepage"),e.emit("local::resizePage messagebarResizePage",e)}}}checkEmptyState(){const{$el:e,$textareaEl:t}=this,a=t.val().trim();a&&a.length?e.addClass("messagebar-with-value"):e.removeClass("messagebar-with-value")}attachmentsCreate(e){void 0===e&&(e="");const t=this,s=a(`<div class="messagebar-attachments">${e}</div>`);return s.insertBefore(t.$textareaEl),i(t,{$attachmentsEl:s,attachmentsEl:s[0]}),t}attachmentsShow(e){void 0===e&&(e="");const t=this;return t.$attachmentsEl=t.$el.find(".messagebar-attachments"),0===t.$attachmentsEl.length&&t.attachmentsCreate(e),t.$el.addClass("messagebar-attachments-visible"),t.attachmentsVisible=!0,t.params.resizePage&&t.resizePage(),t}attachmentsHide(){const e=this;return e.$el.removeClass("messagebar-attachments-visible"),e.attachmentsVisible=!1,e.params.resizePage&&e.resizePage(),e}attachmentsToggle(){const e=this;return e.attachmentsVisible?e.attachmentsHide():e.attachmentsShow(),e}renderAttachment(e){const t=this;return t.params.renderAttachment?t.params.renderAttachment.call(t,e):`\n        <div class="messagebar-attachment">\n          <img src="${e}">\n          <span class="messagebar-attachment-delete"></span>\n        </div>\n      `}renderAttachments(){const e=this;let t;t=e.params.renderAttachments?e.params.renderAttachments.call(e,e.attachments):`${e.attachments.map((t=>e.renderAttachment(t))).join("")}`,0===e.$attachmentsEl.length?e.attachmentsCreate(t):e.$attachmentsEl.html(t)}sheetCreate(e){void 0===e&&(e="");const t=this,s=a(`<div class="messagebar-sheet">${e}</div>`);return t.$el.append(s),i(t,{$sheetEl:s,sheetEl:s[0]}),t}sheetShow(e){void 0===e&&(e="");const t=this;return t.$sheetEl=t.$el.find(".messagebar-sheet"),0===t.$sheetEl.length&&t.sheetCreate(e),t.$el.addClass("messagebar-sheet-visible"),t.sheetVisible=!0,t.params.resizePage&&t.resizePage(),t}sheetHide(){const e=this;return e.$el.removeClass("messagebar-sheet-visible"),e.sheetVisible=!1,e.params.resizePage&&e.resizePage(),e}sheetToggle(){const e=this;return e.sheetVisible?e.sheetHide():e.sheetShow(),e}init(){const e=this;return e.attachEvents(),e.checkEmptyState(),e}destroy(){const e=this;e.emit("local::beforeDestroy messagebarBeforeDestroy",e),e.$el.trigger("messagebar:beforedestroy"),e.detachEvents(),e.$el[0]&&(e.$el[0].f7Messagebar=null,delete e.$el[0].f7Messagebar),l(e)}}var h={name:"messagebar",static:{Messagebar:c},create(){this.messagebar=n({defaultSelector:".messagebar",constructor:c,app:this,domProp:"f7Messagebar",addMethods:"clear getValue setValue setPlaceholder resizePage focus blur attachmentsCreate attachmentsShow attachmentsHide attachmentsToggle renderAttachments sheetCreate sheetShow sheetHide sheetToggle".split(" ")})},on:{tabBeforeRemove(e){const t=this;a(e).find(".messagebar-init").each((e=>{t.messagebar.destroy(e)}))},tabMounted(e){const t=this;a(e).find(".messagebar-init").each((e=>{t.messagebar.create(i({el:e},a(e).dataset()))}))},pageBeforeRemove(e){const t=this;e.$el.find(".messagebar-init").each((e=>{t.messagebar.destroy(e)}))},pageInit(e){const t=this;e.$el.find(".messagebar-init").each((e=>{t.messagebar.create(i({el:e},a(e).dataset()))}))}},vnode:{"messagebar-init":{insert(e){const t=e.elm;this.messagebar.create(i({el:t},a(t).dataset()))},destroy(e){const t=e.elm;this.messagebar.destroy(t)}}}};if(t){if(e.prototype.modules&&e.prototype.modules[h.name])return;e.use(h),e.instance&&(e.instance.useModuleParams(h,e.instance.params),e.instance.useModule(h))}return h}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
