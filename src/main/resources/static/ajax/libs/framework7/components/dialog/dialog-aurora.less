.aurora {
  .dialog {
    &.modal-out {
      transform: translate3d(0, -50%, 0) scale(0.815);
    }
  }
  .dialog-title {
    + .dialog-text {
      margin-top: 16px;
    }
  }
  .dialog-text {
    line-height: 1.5;
  }
  .dialog-buttons {
    padding: var(--f7-dialog-inner-padding);
    padding-top: 0;
    overflow: hidden;
    box-sizing: border-box;
    justify-content: flex-end;
  }

  .dialog-button {
    border-radius: 8px;
    min-width: 64px;
    padding: 0 10px;
    border: none;
    transition-duration: 300ms;
    transform: translate3d(0, 0, 0);
    background: var(--f7-theme-color);
    &.dialog-button-bold {
      font-weight: 600;
    }
    + .dialog-button {
      margin-left: 16px;
    }
    &.active-state {
      background-color: var(--f7-dialog-button-pressed-bg-color, var(--f7-theme-color-shade));
    }
  }
  &.device-desktop {
    .dialog-button:not(.active-state):not(.no-hover):hover {
      background-color: var(--f7-dialog-button-hover-bg-color, var(--f7-theme-color-tint));
    }
  }

  .dialog-buttons-vertical {
    .dialog-buttons {
      display: flex;
      flex-direction: column;
      align-items: stretch;
    }
    .dialog-button {
      margin-left: 0;
      flex-shrink: 0;
      + .dialog-button {
        margin-top: 8px;
      }
    }
  }

  // Inputs
  div.dialog-input-field {
    margin-top: 16px;
    input.dialog-input {
      margin-top: 0;
    }
    &.input:after {
      display: none !important;
    }
  }
  .dialog-input {
    padding: 0 8px;
    transition-duration: 200ms;
    position: relative;
    + .dialog-input {
      margin-top: 8px;
    }
  }
  .dialog-input-double {
    + .dialog-input-double {
      margin-top: 0;
      .dialog-input {
        border-top: 0;
        margin-top: 0;
      }
    }
  }

  // Preloader
  .dialog-preloader,
  .dialog-progress {
    .dialog-title,
    .dialog-inner {
      text-align: center;
    }
  }
  .dialog-preloader {
    .dialog-title ~ .preloader,
    .dialog-text ~ .preloader {
      margin-top: 16px;
    }
  }
  .dialog-progress {
    .dialog-title ~ .progressbar,
    .dialog-text ~ .progressbar,
    .dialog-title ~ .progressbar-infinite,
    .dialog-text ~ .progressbar-infinite {
      margin-top: 16px;
    }
  }
}
