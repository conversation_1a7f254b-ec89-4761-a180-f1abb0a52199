(function framework7ComponentLoader(e,o){void 0===o&&(o=!0);var t=e.$,a=e.utils,r=e.getDevice,n=(e.getSupport,e.Class),l=(e.<PERSON>,e.ConstructorMethods),p=(e.Mo<PERSON>ethods,a.extend),s=a.id,d=a.nextTick,i=a.iosPreloaderContent,c=a.mdPreloaderContent,u=a.auroraPreloaderContent,m=a.deleteProps;class h extends n{constructor(e,o){void 0===o&&(o={}),super(o,[e]);const a=this;a.app=e;const n=r(),l=p({on:{}},e.params.autocomplete);let i,c;void 0===l.searchbarDisableButton&&(l.searchbarDisableButton="aurora"!==e.theme),a.useModulesParams(l),a.params=p(l,o),a.params.openerEl&&(i=t(a.params.openerEl),i.length&&(i[0].f7Autocomplete=a)),a.params.inputEl&&(c=t(a.params.inputEl),c.length&&(c[0].f7Autocomplete=a));const u=s();let m=o.url;!m&&i&&i.length&&(i.attr("href")?m=i.attr("href"):i.find("a").length>0&&(m=i.find("a").attr("href"))),m&&"#"!==m&&""!==m||(m=a.params.url);const h=a.params.multiple?"checkbox":"radio";p(a,{$openerEl:i,openerEl:i&&i[0],$inputEl:c,inputEl:c&&c[0],id:u,url:m,value:a.params.value||[],inputType:h,inputName:`${h}-${u}`,$modalEl:void 0,$dropdownEl:void 0});let v="";function f(){let e=a.$inputEl.val().trim();a.params.source&&a.params.source.call(a,e,(o=>{let t="";const r=a.params.limit?Math.min(a.params.limit,o.length):o.length;let n,l,p;a.items=o,a.params.highlightMatches&&(e=e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),n=new RegExp(`(${e})`,"i"));for(let e=0;e<r;e+=1){const r="object"==typeof o[e]?o[e][a.params.valueProperty]:o[e],s="object"==typeof o[e]?o[e][a.params.textProperty]:o[e];0===e&&(l=r,p=a.items[e]),t+=a.renderItem({value:r,text:a.params.highlightMatches?s.replace(n,"<b>$1</b>"):s},e)}if(""===t&&""===e&&a.params.dropdownPlaceholderText&&(t+=a.renderItem({placeholder:!0,text:a.params.dropdownPlaceholderText})),a.$dropdownEl.find("ul").html(t),a.params.typeahead){if(!l||!p)return;if(0!==l.toLowerCase().indexOf(e.toLowerCase()))return;if(v.toLowerCase()===e.toLowerCase())return void(a.value=[]);if(0===v.toLowerCase().indexOf(e.toLowerCase()))return v=e,void(a.value=[]);c.val(l),c[0].setSelectionRange(e.length,l.length);const o="object"==typeof a.value[0]?a.value[0][a.params.valueProperty]:a.value[0];o&&l.toLowerCase()===o.toLowerCase()||(a.value=[p],a.emit("local::change autocompleteChange",[p]))}v=e}))}function $(){const e=this,o=e.value;let r,n,l;if(t(e).parents(".autocomplete-values").length>0){if("checkbox"===a.inputType&&!e.checked){for(let e=0;e<a.value.length;e+=1)l="string"==typeof a.value[e]?a.value[e]:a.value[e][a.params.valueProperty],l!==o&&1*l!=1*o||a.value.splice(e,1);a.updateValues(),a.emit("local::change autocompleteChange",a.value)}}else{for(let e=0;e<a.items.length;e+=1)n="object"==typeof a.items[e]?a.items[e][a.params.valueProperty]:a.items[e],n!==o&&1*n!=1*o||(r=a.items[e]);if("radio"===a.inputType)a.value=[r];else if(e.checked)a.value.push(r);else for(let e=0;e<a.value.length;e+=1)l="object"==typeof a.value[e]?a.value[e][a.params.valueProperty]:a.value[e],l!==o&&1*l!=1*o||a.value.splice(e,1);a.updateValues(),("radio"===a.inputType&&e.checked||"checkbox"===a.inputType)&&a.emit("local::change autocompleteChange",a.value)}}function g(e){const o=t(e.target);o.is(a.$inputEl[0])||a.$dropdownEl&&o.closest(a.$dropdownEl[0]).length||a.close()}function b(){a.open()}function w(){a.open()}function x(){a.$dropdownEl.find("label.active-state").length>0||setTimeout((()=>{a.close()}),0)}function E(){a.positionDropdown()}function C(e){if(!a.opened)return;if(27===e.keyCode)return e.preventDefault(),void a.$inputEl.blur();if(13===e.keyCode){const o=a.$dropdownEl.find(".autocomplete-dropdown-selected label");return o.length?(e.preventDefault(),o.trigger("click"),void a.$inputEl.blur()):void(a.params.typeahead&&(e.preventDefault(),a.$inputEl.blur()))}if(40!==e.keyCode&&38!==e.keyCode)return;e.preventDefault();const o=a.$dropdownEl.find(".autocomplete-dropdown-selected");let t;o.length?(t=o[40===e.keyCode?"next":"prev"]("li"),t.length||(t=a.$dropdownEl.find("li").eq(40===e.keyCode?0:a.$dropdownEl.find("li").length-1))):t=a.$dropdownEl.find("li").eq(40===e.keyCode?0:a.$dropdownEl.find("li").length-1),t.hasClass("autocomplete-dropdown-placeholder")||(o.removeClass("autocomplete-dropdown-selected"),t.addClass("autocomplete-dropdown-selected"))}function y(){const e=t(this);let o;for(let t=0;t<a.items.length;t+=1){const r="object"==typeof a.items[t]?a.items[t][a.params.valueProperty]:a.items[t],n=e.attr("data-value");r!==n&&1*r!=1*n||(o=a.items[t])}a.params.updateInputValueOnSelect&&(a.$inputEl.val("object"==typeof o?o[a.params.valueProperty]:o),a.$inputEl.trigger("input change")),a.value=[o],a.emit("local::change autocompleteChange",[o]),a.close()}return a.attachEvents=function(){"dropdown"!==a.params.openIn&&a.$openerEl&&a.$openerEl.on("click",b),"dropdown"===a.params.openIn&&a.$inputEl&&(a.$inputEl.on("focus",w),a.$inputEl.on(a.params.inputEvents,f),n.android?t("html").on("click",g):a.$inputEl.on("blur",x),a.$inputEl.on("keydown",C))},a.detachEvents=function(){"dropdown"!==a.params.openIn&&a.$openerEl&&a.$openerEl.off("click",b),"dropdown"===a.params.openIn&&a.$inputEl&&(a.$inputEl.off("focus",w),a.$inputEl.off(a.params.inputEvents,f),n.android?t("html").off("click",g):a.$inputEl.off("blur",x),a.$inputEl.off("keydown",C))},a.attachDropdownEvents=function(){a.$dropdownEl.on("click","label",y),e.on("resize",E)},a.detachDropdownEvents=function(){a.$dropdownEl.off("click","label",y),e.off("resize",E)},a.attachPageEvents=function(){a.$el.on("change",'input[type="radio"], input[type="checkbox"]',$),a.params.closeOnSelect&&!a.params.multiple&&a.$el.once("click",".list label",(()=>{d((()=>{a.close()}))}))},a.detachPageEvents=function(){a.$el.off("change",'input[type="radio"], input[type="checkbox"]',$)},a.useModules(),a.init(),a}get view(){const e=this,{$openerEl:o,$inputEl:t,app:a}=e;let r;if(e.params.view)r=e.params.view;else if(o||t){const e=o||t;r=e.closest(".view").length&&e.closest(".view")[0].f7View}return r||(r=a.views.main),r}positionDropdown(){const e=this,{$inputEl:o,app:a,$dropdownEl:r}=e,n=o.parents(".page-content");if(0===n.length)return;const l=o.offset(),p=o[0].offsetWidth,s=o[0].offsetHeight,d=o.parents(".list");let i;d.parents().each((e=>{if(i)return;const o=t(e);o.parent(n).length&&(i=o)}));const c=d.offset(),u=parseInt(n.css("padding-bottom"),10),m=d.length>0?c.left-n.offset().left:0,h=l.left-(d.length>0?c.left:0)-(a.rtl,0),v=l.top-(n.offset().top-n[0].scrollTop),f=n[0].scrollHeight-u-(v+n[0].scrollTop)-o[0].offsetHeight,$=a.rtl?"padding-right":"padding-left";let g;d.length&&!e.params.expandInput&&(g=(a.rtl?d[0].offsetWidth-h-p:h)-("md"===a.theme?16:15)),r.css({left:`${d.length>0?m:h}px`,top:`${v+n[0].scrollTop+s}px`,width:`${d.length>0?d[0].offsetWidth:p}px`}),r.children(".autocomplete-dropdown-inner").css({maxHeight:`${f}px`,[$]:d.length>0&&!e.params.expandInput?`${g}px`:""})}focus(){this.$el.find("input[type=search]").focus()}source(e){const o=this;if(!o.params.source)return;const{$el:t}=o;o.params.source.call(o,e,(a=>{let r="";const n=o.params.limit?Math.min(o.params.limit,a.length):a.length;o.items=a;for(let e=0;e<n;e+=1){let t=!1;const n="object"==typeof a[e]?a[e][o.params.valueProperty]:a[e];for(let e=0;e<o.value.length;e+=1){const a="object"==typeof o.value[e]?o.value[e][o.params.valueProperty]:o.value[e];a!==n&&1*a!=1*n||(t=!0)}r+=o.renderItem({value:n,text:"object"==typeof a[e]?a[e][o.params.textProperty]:a[e],inputType:o.inputType,id:o.id,inputName:o.inputName,selected:t},e)}t.find(".autocomplete-found ul").html(r),0===a.length?0!==e.length?(t.find(".autocomplete-not-found").show(),t.find(".autocomplete-found, .autocomplete-values").hide()):(t.find(".autocomplete-values").show(),t.find(".autocomplete-found, .autocomplete-not-found").hide()):(t.find(".autocomplete-found").show(),t.find(".autocomplete-not-found, .autocomplete-values").hide())}))}updateValues(){const e=this;let o="";for(let t=0;t<e.value.length;t+=1)o+=e.renderItem({value:"object"==typeof e.value[t]?e.value[t][e.params.valueProperty]:e.value[t],text:"object"==typeof e.value[t]?e.value[t][e.params.textProperty]:e.value[t],inputType:e.inputType,id:e.id,inputName:`${e.inputName}-checked}`,selected:!0},t);e.$el.find(".autocomplete-values ul").html(o)}preloaderHide(){const e=this;"dropdown"===e.params.openIn&&e.$dropdownEl?e.$dropdownEl.find(".autocomplete-preloader").removeClass("autocomplete-preloader-visible"):t(".autocomplete-preloader").removeClass("autocomplete-preloader-visible")}preloaderShow(){const e=this;"dropdown"===e.params.openIn&&e.$dropdownEl?e.$dropdownEl.find(".autocomplete-preloader").addClass("autocomplete-preloader-visible"):t(".autocomplete-preloader").addClass("autocomplete-preloader-visible")}renderPreloader(){const e=this,o={iosPreloaderContent:i,mdPreloaderContent:c,auroraPreloaderContent:u};return $jsx("div",{class:"autocomplete-preloader preloader "+(e.params.preloaderColor?`color-${e.params.preloaderColor}`:"")},o[`${e.app.theme}PreloaderContent`]||"")}renderSearchbar(){const e=this;return e.params.renderSearchbar?e.params.renderSearchbar.call(e):$jsx("form",{class:"searchbar"},$jsx("div",{class:"searchbar-inner"},$jsx("div",{class:"searchbar-input-wrap"},$jsx("input",{type:"search",spellcheck:e.params.searchbarSpellcheck||"false",placeholder:e.params.searchbarPlaceholder}),$jsx("i",{class:"searchbar-icon"}),$jsx("span",{class:"input-clear-button"})),e.params.searchbarDisableButton&&$jsx("span",{class:"searchbar-disable-button"},e.params.searchbarDisableText)))}renderItem(e,o){const t=this;if(t.params.renderItem)return t.params.renderItem.call(t,e,o);const a=e.value&&"string"==typeof e.value?e.value.replace(/"/g,"&quot;"):e.value;return"dropdown"!==t.params.openIn?$jsx("li",null,$jsx("label",{class:`item-${e.inputType} item-content`},$jsx("input",{type:e.inputType,name:e.inputName,value:a,_checked:e.selected}),$jsx("i",{class:`icon icon-${e.inputType}`}),$jsx("div",{class:"item-inner"},$jsx("div",{class:"item-title"},e.text)))):e.placeholder?$jsx("li",{class:"autocomplete-dropdown-placeholder"},$jsx("label",{class:"item-content"},$jsx("div",{class:"item-inner"},$jsx("div",{class:"item-title"},e.text)))):$jsx("li",null,$jsx("label",{class:"item-radio item-content","data-value":a},$jsx("div",{class:"item-inner"},$jsx("div",{class:"item-title"},e.text))))}renderNavbar(){const e=this;if(e.params.renderNavbar)return e.params.renderNavbar.call(e);let o=e.params.pageTitle;void 0===o&&e.$openerEl&&e.$openerEl.length&&(o=e.$openerEl.find(".item-title").text().trim());const t="popup"===e.params.openIn,a=t?e.params.preloader&&$jsx("div",{class:"left"},e.renderPreloader()):$jsx("div",{class:"left sliding"},$jsx("a",{class:"link back"},$jsx("i",{class:"icon icon-back"}),$jsx("span",{class:"if-not-md"},e.params.pageBackLinkText))),r=t?$jsx("div",{class:"right"},$jsx("a",{class:"link popup-close","data-popup":".autocomplete-popup"},e.params.popupCloseLinkText)):e.params.preloader&&$jsx("div",{class:"right"},e.renderPreloader());return $jsx("div",{class:"navbar "+(e.params.navbarColorTheme?`color-${e.params.navbarColorTheme}`:"")},$jsx("div",{class:"navbar-bg"}),$jsx("div",{class:"navbar-inner "+(e.params.navbarColorTheme?`color-${e.params.navbarColorTheme}`:"")},a,o&&$jsx("div",{class:"title sliding"},o),r,$jsx("div",{class:"subnavbar sliding"},e.renderSearchbar())))}renderDropdown(){const e=this;return e.params.renderDropdown?e.params.renderDropdown.call(e,e.items):$jsx("div",{class:"autocomplete-dropdown"},$jsx("div",{class:"autocomplete-dropdown-inner"},$jsx("div",{class:"list "+(e.params.expandInput?"":"no-safe-areas")},$jsx("ul",null))),e.params.preloader&&e.renderPreloader())}renderPage(e){const o=this;return o.params.renderPage?o.params.renderPage.call(o,o.items):$jsx("div",{class:"page page-with-subnavbar autocomplete-page","data-name":"autocomplete-page"},o.renderNavbar(e),$jsx("div",{class:"searchbar-backdrop"}),$jsx("div",{class:"page-content"},$jsx("div",{class:`list autocomplete-list autocomplete-found autocomplete-list-${o.id} ${o.params.formColorTheme?`color-${o.params.formColorTheme}`:""}`},$jsx("ul",null)),$jsx("div",{class:"list autocomplete-not-found"},$jsx("ul",null,$jsx("li",{class:"item-content"},$jsx("div",{class:"item-inner"},$jsx("div",{class:"item-title"},o.params.notFoundText))))),$jsx("div",{class:"list autocomplete-values"},$jsx("ul",null))))}renderPopup(){const e=this;return e.params.renderPopup?e.params.renderPopup.call(e,e.items):$jsx("div",{class:"popup autocomplete-popup"},$jsx("div",{class:"view"},e.renderPage(!0),";"))}onOpen(e,o){const a=this,r=a.app,n=t(o);if(a.$el=n,a.el=n[0],a.openedIn=e,a.opened=!0,"dropdown"===a.params.openIn)a.attachDropdownEvents(),a.$dropdownEl.addClass("autocomplete-dropdown-in"),a.$inputEl.trigger("input");else{let e=n.find(".searchbar");"page"===a.params.openIn&&"ios"===r.theme&&0===e.length&&(e=t(r.navbar.getElByPage(n)).find(".searchbar")),a.searchbar=r.searchbar.create({el:e,backdropEl:n.find(".searchbar-backdrop"),customSearch:!0,on:{search(e,o){0===o.length&&a.searchbar.enabled?a.searchbar.backdropShow():a.searchbar.backdropHide(),a.source(o)}}}),a.attachPageEvents(),a.updateValues(),a.params.requestSourceOnOpen&&a.source("")}a.emit("local::open autocompleteOpen",a)}autoFocus(){const e=this;return e.searchbar&&e.searchbar.$inputEl&&e.searchbar.$inputEl.focus(),e}onOpened(){const e=this;"dropdown"!==e.params.openIn&&e.params.autoFocus&&e.autoFocus(),e.emit("local::opened autocompleteOpened",e)}onClose(){const e=this;e.destroyed||(e.searchbar&&e.searchbar.destroy&&(e.searchbar.destroy(),e.searchbar=null,delete e.searchbar),"dropdown"===e.params.openIn?(e.detachDropdownEvents(),e.$dropdownEl.removeClass("autocomplete-dropdown-in").remove(),e.$inputEl.parents(".item-content-dropdown-expanded").removeClass("item-content-dropdown-expanded")):e.detachPageEvents(),e.emit("local::close autocompleteClose",e))}onClosed(){const e=this;e.destroyed||(e.opened=!1,e.$el=null,e.el=null,delete e.$el,delete e.el,e.emit("local::closed autocompleteClosed",e))}openPage(){const e=this;if(e.opened)return e;const o=e.renderPage();return e.view.router.navigate({url:e.url,route:{content:o,path:e.url,on:{pageBeforeIn(o,t){e.onOpen("page",t.el)},pageAfterIn(o,t){e.onOpened("page",t.el)},pageBeforeOut(o,t){e.onClose("page",t.el)},pageAfterOut(o,t){e.onClosed("page",t.el)}},options:{animate:e.params.animate}}}),e}openPopup(){const e=this;if(e.opened)return e;const o={content:e.renderPopup(),animate:e.params.animate,push:e.params.popupPush,swipeToClose:e.params.popupSwipeToClose,on:{popupOpen(o){e.onOpen("popup",o.el)},popupOpened(o){e.onOpened("popup",o.el)},popupClose(o){e.onClose("popup",o.el)},popupClosed(o){e.onClosed("popup",o.el)}}};return e.params.routableModals&&e.view?e.view.router.navigate({url:e.url,route:{path:e.url,popup:o}}):e.modal=e.app.popup.create(o).open(e.params.animate),e}openDropdown(){const e=this;e.$dropdownEl||(e.$dropdownEl=t(e.renderDropdown()));e.$inputEl.parents(".list").length&&e.$inputEl.parents(".item-content").length>0&&e.params.expandInput&&e.$inputEl.parents(".item-content").addClass("item-content-dropdown-expanded");const o=e.$inputEl.parents(".page-content");e.params.dropdownContainerEl?t(e.params.dropdownContainerEl).append(e.$dropdownEl):0===o.length?e.$dropdownEl.insertAfter(e.$inputEl):(e.positionDropdown(),o.append(e.$dropdownEl)),e.onOpen("dropdown",e.$dropdownEl),e.onOpened("dropdown",e.$dropdownEl)}open(){const e=this;if(e.opened)return e;return e[`open${e.params.openIn.split("").map(((e,o)=>0===o?e.toUpperCase():e)).join("")}`](),e}close(){const e=this;return e.opened?("dropdown"===e.params.openIn?(e.onClose(),e.onClosed()):e.params.routableModals&&e.view||"page"===e.openedIn?e.view.router.back({animate:e.params.animate}):(e.modal.once("modalClosed",(()=>{d((()=>{e.destroyed||(e.modal.destroy(),delete e.modal)}))})),e.modal.close()),e):e}init(){this.attachEvents()}destroy(){const e=this;e.emit("local::beforeDestroy autocompleteBeforeDestroy",e),e.detachEvents(),e.$inputEl&&e.$inputEl[0]&&delete e.$inputEl[0].f7Autocomplete,e.$openerEl&&e.$openerEl[0]&&delete e.$openerEl[0].f7Autocomplete,m(e),e.destroyed=!0}}var v={name:"autocomplete",params:{autocomplete:{openerEl:void 0,inputEl:void 0,view:void 0,dropdownContainerEl:void 0,dropdownPlaceholderText:void 0,typeahead:!1,highlightMatches:!0,expandInput:!1,updateInputValueOnSelect:!0,inputEvents:"input",value:void 0,multiple:!1,source:void 0,limit:void 0,valueProperty:"id",textProperty:"text",openIn:"page",pageBackLinkText:"Back",popupCloseLinkText:"Close",pageTitle:void 0,searchbarPlaceholder:"Search...",searchbarDisableText:"Cancel",searchbarDisableButton:void 0,searchbarSpellcheck:!1,popupPush:!1,popupSwipeToClose:void 0,animate:!0,autoFocus:!1,closeOnSelect:!1,notFoundText:"Nothing found",requestSourceOnOpen:!1,preloaderColor:void 0,preloader:!1,formColorTheme:void 0,navbarColorTheme:void 0,routableModals:!1,url:"select/",renderDropdown:void 0,renderPage:void 0,renderPopup:void 0,renderItem:void 0,renderSearchbar:void 0,renderNavbar:void 0}},static:{Autocomplete:h},create(){const e=this;e.autocomplete=p(l({defaultSelector:void 0,constructor:h,app:e,domProp:"f7Autocomplete"}),{open(o){const t=e.autocomplete.get(o);if(t&&t.open)return t.open()},close(o){const t=e.autocomplete.get(o);if(t&&t.close)return t.close()}})}};if(o){if(e.prototype.modules&&e.prototype.modules[v.name])return;e.use(v),e.instance&&(e.instance.useModuleParams(v,e.instance.params),e.instance.useModule(v))}return v}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
