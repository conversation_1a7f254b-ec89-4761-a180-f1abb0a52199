(function framework7ComponentLoader(t,e){void 0===e&&(e=!0);var s=t.$,o=t.utils,a=(t.getDevice,t.getSupport),l=(t.<PERSON>,t.<PERSON>,t.<PERSON><PERSON><PERSON>,t.<PERSON>,o.bindMethods);const r={init(){const t=this,e=getDocument();let o,l,r,n,i,d,p,b,c,u,g,v,f,h,m,x,w,C,M,T,I;function L(e,a){l=!1,o=!0,I=!1,r="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,i=s(e.target).closest("li").eq(0),f=i.index(),p=i.parents(".sortable");const n=i.parents(".list-group");n.length&&n.parents(p).length&&(p=n),d=p.children("ul").children("li:not(.disallow-sorting):not(.no-sorting)"),t.panel&&(t.panel.allowOpen=!1),t.swipeout&&(t.swipeout.allow=!1),a&&(i.addClass("sorting"),p.addClass("sortable-sorting"),I=!0)}const E=!!a().passiveListener&&{passive:!1,capture:!1};s(e).on(t.touchEvents.start,".list.sortable .sortable-handler",L,E),t.on("touchmove:active",(function(e){if(!o||!i)return;const a="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY;if(!l){h=i.parents(".page"),m=i.parents(".page-content");const t=parseInt(m.css("padding-top"),10),e=parseInt(m.css("padding-bottom"),10);T=m[0].scrollTop,w=h.offset().top+t,x=h.height()-t-e,i.addClass("sorting"),p.addClass("sortable-sorting"),C=i[0].offsetTop,c=i[0].offsetTop,u=i.parent().height()-C-i.height(),b=i[0].offsetHeight,M=i.offset().top}l=!0,e.preventDefault(),e.f7PreventSwipePanel=!0,n=a-r;const f=m[0].scrollTop-T,I=Math.min(Math.max(n+f,-c),u);i.transform(`translate3d(0,${I}px,0)`);const L=44;let E,y=!0;n+f+L<-c&&(y=!1),n+f-L>u&&(y=!1),v=void 0,g=void 0,y&&(M+n+b+L>w+x&&(E=M+n+b+L-(w+x)),M+n<w+L&&(E=M+n-w-L),E&&(m[0].scrollTop+=E)),d.each((e=>{const o=s(e);if(o[0]===i[0])return;const a=o[0].offsetTop,l=o.height(),r=C+I;let n;const d=o[0].f7Translate;r>=a-l/2&&i.index()<o.index()?(n=-b,o.transform(`translate3d(0, ${n}px,0)`),g=o,v=void 0):r<=a+l/2&&i.index()>o.index()?(n=b,o[0].f7Translate=n,o.transform(`translate3d(0, ${n}px,0)`),g=void 0,v||(v=o)):(n=void 0,o.transform("translate3d(0, 0%,0)")),d!==n&&(o.trigger("sortable:move"),t.emit("sortableMove",o[0],p[0])),o[0].f7Translate=n}))})),t.on("touchend:passive",(function(){if(!o||!l)return o&&!l&&(t.panel&&(t.panel.allowOpen=!0),t.swipeout&&(t.swipeout.allow=!0),I&&(i.removeClass("sorting"),p.removeClass("sortable-sorting"))),o=!1,void(l=!1);let e;t.panel&&(t.panel.allowOpen=!0),t.swipeout&&(t.swipeout.allow=!0),d.transform(""),i.removeClass("sorting"),p.removeClass("sortable-sorting"),g?e=g.index():v&&(e=v.index());let s=p.dataset().sortableMoveElements;if(void 0===s&&(s=t.params.sortable.moveElements),s&&(g&&i.insertAfter(g),v&&i.insertBefore(v)),(g||v)&&p.hasClass("virtual-list")){f=i[0].f7VirtualListIndex,void 0===f&&(f=i.attr("data-virtual-list-index")),v?(e=v[0].f7VirtualListIndex,void 0===e&&(e=v.attr("data-virtual-list-index"))):(e=g[0].f7VirtualListIndex,void 0===e&&(e=g.attr("data-virtual-list-index"))),e=null!==e?parseInt(e,10):void 0;const t=p[0].f7VirtualList;f&&(f=parseInt(f,10)),e&&(e=parseInt(e,10)),t&&t.moveItem(f,e)}void 0===e||Number.isNaN(e)||e===f||(i.trigger("sortable:sort",{from:f,to:e}),t.emit("sortableSort",i[0],{from:f,to:e,el:i[0]},p[0])),v=void 0,g=void 0,o=!1,l=!1})),s(e).on("taphold",".sortable-tap-hold",((t,e)=>{L(e,!0)}))},enable(t){void 0===t&&(t=".list.sortable");const e=s(t);0!==e.length&&(e.addClass("sortable-enabled"),e.trigger("sortable:enable"),this.emit("sortableEnable",e[0]))},disable(t){void 0===t&&(t=".list.sortable");const e=s(t);0!==e.length&&(e.removeClass("sortable-enabled"),e.trigger("sortable:disable"),this.emit("sortableDisable",e[0]))},toggle(t){void 0===t&&(t=".list.sortable");const e=this,o=s(t);0!==o.length&&(o.hasClass("sortable-enabled")?e.sortable.disable(o):e.sortable.enable(o))}};var n={name:"sortable",params:{sortable:{moveElements:!0}},create(){l(this,{sortable:r})},on:{init(){this.params.sortable&&this.sortable.init()}},clicks:{".sortable-enable":function(t,e){void 0===e&&(e={});this.sortable.enable(e.sortable)},".sortable-disable":function(t,e){void 0===e&&(e={});this.sortable.disable(e.sortable)},".sortable-toggle":function(t,e){void 0===e&&(e={});this.sortable.toggle(e.sortable)}}};if(e){if(t.prototype.modules&&t.prototype.modules[n.name])return;t.use(n),t.instance&&(t.instance.useModuleParams(n,t.instance.params),t.instance.useModule(n))}return n}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
