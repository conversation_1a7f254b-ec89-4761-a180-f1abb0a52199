(function framework7ComponentLoader(e,a){void 0===a&&(a=!0);var r=e.$,s=e.utils,t=e.getDevice,l=(e.getSupport,e.Class),n=(e.<PERSON>,e.ConstructorMethods),i=(e.<PERSON>,s.extend),o=s.nextTick,b=s.deleteProps;const c=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"OE",letters:"Œ"},{base:"oe",letters:"œ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],d={};for(let e=0;e<c.length;e+=1){const a=c[e].letters;for(let r=0;r<a.length;r+=1)d[a[r]]=c[e].base}function h(e){return e.replace(/[^\u0000-\u007E]/g,(e=>d[e]||e))}class p extends l{constructor(e,a){void 0===a&&(a={}),super(a,[e]);const s=this,t={el:void 0,inputEl:void 0,inputEvents:"change input compositionend",disableButton:!0,disableButtonEl:void 0,backdropEl:void 0,searchContainer:void 0,searchItem:"li",searchIn:void 0,searchGroup:".list-group",searchGroupTitle:".item-divider, .list-group-title",ignore:".searchbar-ignore",foundEl:".searchbar-found",notFoundEl:".searchbar-not-found",hideOnEnableEl:".searchbar-hide-on-enable",hideOnSearchEl:".searchbar-hide-on-search",backdrop:void 0,removeDiacritics:!0,customSearch:!1,hideDividers:!0,hideGroups:!0,disableOnBackdropClick:!0,expandable:!1,inline:!1};s.useModulesParams(t),s.params=i(t,a);const l=r(s.params.el);if(0===l.length)return s;if(l[0].f7Searchbar)return l[0].f7Searchbar;let n;l[0].f7Searchbar=s;const o=l.parents(".navbar");if(l.parents(".page").length>0)n=l.parents(".page");else if(o.length>0&&(n=r(e.navbar.getPageByEl(o[0])),!n.length)){const e=l.parents(".view").find(".page-current");e[0]&&e[0].f7Page&&e[0].f7Page.navbarEl===o[0]&&(n=e)}let b,c,d,h;a.foundEl?b=r(a.foundEl):"string"==typeof s.params.foundEl&&n&&(b=n.find(s.params.foundEl)),a.notFoundEl?c=r(a.notFoundEl):"string"==typeof s.params.notFoundEl&&n&&(c=n.find(s.params.notFoundEl)),a.hideOnEnableEl?d=r(a.hideOnEnableEl):"string"==typeof s.params.hideOnEnableEl&&n&&(d=n.find(s.params.hideOnEnableEl)),a.hideOnSearchEl?h=r(a.hideOnSearchEl):"string"==typeof s.params.hideOnSearchEl&&n&&(h=n.find(s.params.hideOnSearchEl));const p=s.params.expandable||l.hasClass("searchbar-expandable"),u=s.params.inline||l.hasClass("searchbar-inline");let f,m,g,E;function v(e){e.preventDefault()}function $(e){s.enable(e),s.$el.addClass("searchbar-focused")}function C(){s.$el.removeClass("searchbar-focused"),"aurora"!==e.theme||E&&E.length&&s.params.disableButton||s.query||s.disable()}function y(){const e=s.$inputEl.val().trim();(s.$searchContainer&&s.$searchContainer.length>0&&(s.params.searchIn||s.isVirtualList||s.params.searchIn===s.params.searchItem)||s.params.customSearch)&&s.search(e,!0)}function k(e,a){s.$el.trigger("searchbar:clear",a),s.emit("local::clear searchbarClear",s,a)}function B(e){s.disable(e)}function x(){!s||s&&!s.$el||s.enabled&&(s.$el.removeClass("searchbar-enabled"),s.expandable&&s.$el.parents(".navbar").removeClass("with-searchbar-expandable-enabled with-searchbar-expandable-enabled-no-transition"))}function S(){!s||s&&!s.$el||s.enabled&&(s.$el.addClass("searchbar-enabled"),s.expandable&&s.$el.parents(".navbar").addClass("with-searchbar-expandable-enabled-no-transition"))}return void 0===s.params.backdrop&&(s.params.backdrop=!u&&"aurora"!==e.theme),s.params.backdrop&&(f=s.params.backdropEl?r(s.params.backdropEl):n&&n.length>0?n.find(".searchbar-backdrop"):l.siblings(".searchbar-backdrop"),0===f.length&&(f=r('<div class="searchbar-backdrop"></div>'),n&&n.length?l.parents(n).length>0&&o&&0===l.parents(o).length?f.insertBefore(l):f.insertBefore(n.find(".page-content").eq(0)):f.insertBefore(l))),s.params.searchContainer&&(m=r(s.params.searchContainer)),g=s.params.inputEl?r(s.params.inputEl):l.find('input[type="search"]').eq(0),s.params.disableButton&&(E=s.params.disableButtonEl?r(s.params.disableButtonEl):l.find(".searchbar-disable-button")),i(s,{app:e,view:e.views.get(l.parents(".view")),$el:l,el:l[0],$backdropEl:f,backdropEl:f&&f[0],$searchContainer:m,searchContainer:m&&m[0],$inputEl:g,inputEl:g[0],$disableButtonEl:E,disableButtonEl:E&&E[0],disableButtonHasMargin:!1,$pageEl:n,pageEl:n&&n[0],$navbarEl:o,navbarEl:o&&o[0],$foundEl:b,foundEl:b&&b[0],$notFoundEl:c,notFoundEl:c&&c[0],$hideOnEnableEl:d,hideOnEnableEl:d&&d[0],$hideOnSearchEl:h,hideOnSearchEl:h&&h[0],previousQuery:"",query:"",isVirtualList:m&&m.hasClass("virtual-list"),virtualList:void 0,enabled:!1,expandable:p,inline:u}),s.attachEvents=function(){l.on("submit",v),s.params.disableButton&&s.$disableButtonEl.on("click",B),s.params.disableOnBackdropClick&&s.$backdropEl&&s.$backdropEl.on("click",B),s.expandable&&"ios"===e.theme&&s.view&&o.length&&s.$pageEl&&(s.$pageEl.on("page:beforeout",x),s.$pageEl.on("page:beforein",S)),s.$inputEl.on("focus",$),s.$inputEl.on("blur",C),s.$inputEl.on(s.params.inputEvents,y),s.$inputEl.on("input:clear",k)},s.detachEvents=function(){l.off("submit",v),s.params.disableButton&&s.$disableButtonEl.off("click",B),s.params.disableOnBackdropClick&&s.$backdropEl&&s.$backdropEl.off("click",B),s.expandable&&"ios"===e.theme&&s.view&&o.length&&s.$pageEl&&(s.$pageEl.off("page:beforeout",x),s.$pageEl.off("page:beforein",S)),s.$inputEl.off("focus",$),s.$inputEl.off("blur",C),s.$inputEl.off(s.params.inputEvents,y),s.$inputEl.off("input:clear",k)},s.useModules(),s.init(),s}clear(e){const a=this;if(!a.query&&e&&r(e.target).hasClass("searchbar-clear"))return a.disable(),a;const s=a.value;return a.$inputEl.val("").trigger("change").focus(),a.$el.trigger("searchbar:clear",s),a.emit("local::clear searchbarClear",a,s),a}setDisableButtonMargin(){const e=this;if(e.expandable)return;const a=e.app;e.$disableButtonEl.transition(0).show(),e.$disableButtonEl.css("margin-"+(a.rtl?"left":"right"),-e.disableButtonEl.offsetWidth+"px"),e._clientLeft=e.$disableButtonEl[0].clientLeft,e.$disableButtonEl.transition(""),e.disableButtonHasMargin=!0}enable(e){const a=this;if(a.enabled)return a;const r=a.app,s=getDocument(),l=t();function n(){if(a.$backdropEl&&(a.$searchContainer&&a.$searchContainer.length||a.params.customSearch)&&!a.$el.hasClass("searchbar-enabled")&&!a.query&&a.backdropShow(),a.$el.addClass("searchbar-enabled"),(!a.$disableButtonEl||a.$disableButtonEl&&0===a.$disableButtonEl.length)&&a.$el.addClass("searchbar-enabled-no-disable-button"),!a.expandable&&a.$disableButtonEl&&a.$disableButtonEl.length>0&&"md"!==r.theme&&(a.disableButtonHasMargin||a.setDisableButtonMargin(),a.$disableButtonEl.css("margin-"+(r.rtl?"left":"right"),"0px")),a.expandable){const e=a.$el.parents(".navbar");if(e.hasClass("navbar-large")&&a.$pageEl){const r=a.$pageEl.find(".page-content"),s=e.find(".title-large");r.addClass("with-searchbar-expandable-enabled"),e.hasClass("navbar-large")&&e.hasClass("navbar-large-collapsed")&&s.length&&r.length&&(r.transition(0),r[0].scrollTop-=s[0].offsetHeight,setTimeout((()=>{r.transition("")}),200))}"md"===r.theme&&e.length?e.addClass("with-searchbar-expandable-enabled"):(e.addClass("with-searchbar-expandable-enabled"),e.hasClass("navbar-large")&&e.addClass("navbar-large-collapsed"))}a.$hideOnEnableEl&&a.$hideOnEnableEl.addClass("hidden-by-searchbar"),a.$el.trigger("searchbar:enable"),a.emit("local::enable searchbarEnable",a)}a.enabled=!0;let i=!1;!0===e&&s.activeElement!==a.inputEl&&(i=!0);return l.ios&&"ios"===r.theme?a.expandable?(i&&a.$inputEl.focus(),n()):(i&&a.$inputEl.focus(),!e||"focus"!==e.type&&!0!==e?n():o((()=>{n()}),400)):(i&&a.$inputEl.focus(),"md"===r.theme&&a.expandable&&a.$el.parents(".page, .view, .navbar-inner, .navbar").scrollLeft(r.rtl?100:0),n()),a}disable(){const e=this;if(!e.enabled)return e;const a=e.app;if(e.$inputEl.val("").trigger("change"),e.$el.removeClass("searchbar-enabled searchbar-focused searchbar-enabled-no-disable-button"),e.expandable){const r=e.$el.parents(".navbar"),s=e.$pageEl&&e.$pageEl.find(".page-content");if(r.hasClass("navbar-large")&&s.length){const a=r.find(".title-large");if(e.$el.transitionEnd((()=>{s.removeClass("with-searchbar-expandable-closing")})),r.hasClass("navbar-large")&&r.hasClass("navbar-large-collapsed")&&a.length){const e=s[0].scrollTop,r=a[0].offsetHeight;e>r&&(s.transition(0),s[0].scrollTop=e+r,setTimeout((()=>{s.transition("")}),200))}s.removeClass("with-searchbar-expandable-enabled").addClass("with-searchbar-expandable-closing")}"md"===a.theme&&r.length?(r.removeClass("with-searchbar-expandable-enabled with-searchbar-expandable-enabled-no-transition").addClass("with-searchbar-expandable-closing"),e.$el.transitionEnd((()=>{r.removeClass("with-searchbar-expandable-closing")}))):(r.removeClass("with-searchbar-expandable-enabled with-searchbar-expandable-enabled-no-transition").addClass("with-searchbar-expandable-closing"),e.$el.transitionEnd((()=>{r.removeClass("with-searchbar-expandable-closing")})),e.$pageEl&&e.$pageEl.find(".page-content").trigger("scroll"))}return!e.expandable&&e.$disableButtonEl&&e.$disableButtonEl.length>0&&"md"!==a.theme&&e.$disableButtonEl.css("margin-"+(a.rtl?"left":"right"),-e.disableButtonEl.offsetWidth+"px"),e.$backdropEl&&(e.$searchContainer&&e.$searchContainer.length||e.params.customSearch)&&e.backdropHide(),e.enabled=!1,e.$inputEl.blur(),e.$hideOnEnableEl&&e.$hideOnEnableEl.removeClass("hidden-by-searchbar"),e.$el.trigger("searchbar:disable"),e.emit("local::disable searchbarDisable",e),e}toggle(){const e=this;return e.enabled?e.disable():e.enable(!0),e}backdropShow(){const e=this;return e.$backdropEl&&e.$backdropEl.addClass("searchbar-backdrop-in"),e}backdropHide(){const e=this;return e.$backdropEl&&e.$backdropEl.removeClass("searchbar-backdrop-in"),e}search(e,a){const s=this;if(s.previousQuery=s.query||"",e===s.previousQuery)return s;a||(s.enabled||s.enable(),s.$inputEl.val(e),s.$inputEl.trigger("input")),s.query=e,s.value=e;const{$searchContainer:t,$el:l,$foundEl:n,$notFoundEl:i,$hideOnSearchEl:o,isVirtualList:b}=s;if(e.length>0&&o?o.addClass("hidden-by-searchbar"):o&&o.removeClass("hidden-by-searchbar"),(t&&t.length&&l.hasClass("searchbar-enabled")||s.params.customSearch&&l.hasClass("searchbar-enabled"))&&(0===e.length?s.backdropShow():s.backdropHide()),s.params.customSearch)return l.trigger("searchbar:search",{query:e,previousQuery:s.previousQuery}),s.emit("local::search searchbarSearch",s,e,s.previousQuery),s;let c,d=[];if(b){if(s.virtualList=t[0].f7VirtualList,""===e.trim())return s.virtualList.resetFilter(),i&&i.hide(),n&&n.show(),l.trigger("searchbar:search",{query:e,previousQuery:s.previousQuery}),s.emit("local::search searchbarSearch",s,e,s.previousQuery),s;if(c=s.params.removeDiacritics?h(e):e,s.virtualList.params.searchAll)d=s.virtualList.params.searchAll(c,s.virtualList.items)||[];else if(s.virtualList.params.searchByItem)for(let e=0;e<s.virtualList.items.length;e+=1)s.virtualList.params.searchByItem(c,s.virtualList.items[e],e)&&d.push(e)}else{let a;a=s.params.removeDiacritics?h(e.trim().toLowerCase()).split(" "):e.trim().toLowerCase().split(" "),t.find(s.params.searchItem).removeClass("hidden-by-searchbar").each((e=>{const t=r(e);let l=[],n=s.params.searchIn?t.find(s.params.searchIn):t;s.params.searchIn===s.params.searchItem&&(n=t),n.each((e=>{let a=r(e).text().trim().toLowerCase();s.params.removeDiacritics&&(a=h(a)),l.push(a)})),l=l.join(" ");let i=0;for(let e=0;e<a.length;e+=1)l.indexOf(a[e])>=0&&(i+=1);i===a.length||s.params.ignore&&t.is(s.params.ignore)?d.push(t[0]):t.addClass("hidden-by-searchbar")})),s.params.hideDividers&&t.find(s.params.searchGroupTitle).each((e=>{const a=r(e),t=a.nextAll(s.params.searchItem);let l=!0;for(let e=0;e<t.length;e+=1){const a=t.eq(e);if(a.is(s.params.searchGroupTitle))break;a.hasClass("hidden-by-searchbar")||(l=!1)}const n=s.params.ignore&&a.is(s.params.ignore);l&&!n?a.addClass("hidden-by-searchbar"):a.removeClass("hidden-by-searchbar")})),s.params.hideGroups&&t.find(s.params.searchGroup).each((e=>{const a=r(e),t=s.params.ignore&&a.is(s.params.ignore);0!==a.find(s.params.searchItem).filter((e=>!r(e).hasClass("hidden-by-searchbar"))).length||t?a.removeClass("hidden-by-searchbar"):a.addClass("hidden-by-searchbar")}))}return 0===d.length?(i&&i.show(),n&&n.hide()):(i&&i.hide(),n&&n.show()),b&&s.virtualList&&s.virtualList.filterItems(d),l.trigger("searchbar:search",{query:e,previousQuery:s.previousQuery,foundItems:d}),s.emit("local::search searchbarSearch",s,e,s.previousQuery,d),s}init(){const e=this;e.expandable&&e.$el&&e.$el.addClass("searchbar-expandable"),e.inline&&e.$el&&e.$el.addClass("searchbar-inline"),e.attachEvents()}destroy(){const e=this;e.emit("local::beforeDestroy searchbarBeforeDestroy",e),e.$el.trigger("searchbar:beforedestroy"),e.detachEvents(),e.$el[0]&&(e.$el[0].f7Searchbar=null,delete e.$el[0].f7Searchbar),b(e)}}var u={name:"searchbar",static:{Searchbar:p},create(){this.searchbar=n({defaultSelector:".searchbar",constructor:p,app:this,domProp:"f7Searchbar",addMethods:"clear enable disable toggle search".split(" ")})},on:{tabMounted(e){const a=this;r(e).find(".searchbar-init").each((e=>{const s=r(e);a.searchbar.create(i(s.dataset(),{el:e}))}))},tabBeforeRemove(e){r(e).find(".searchbar-init").each((e=>{e.f7Searchbar&&e.f7Searchbar.destroy&&e.f7Searchbar.destroy()}))},pageInit(e){const a=this;e.$el.find(".searchbar-init").each((e=>{const s=r(e);a.searchbar.create(i(s.dataset(),{el:e}))})),"ios"===a.theme&&e.view&&e.view.router.dynamicNavbar&&e.$navbarEl&&e.$navbarEl.length>0&&e.$navbarEl.find(".searchbar-init").each((e=>{const s=r(e);a.searchbar.create(i(s.dataset(),{el:e}))}))},pageBeforeRemove(e){e.$el.find(".searchbar-init").each((e=>{e.f7Searchbar&&e.f7Searchbar.destroy&&e.f7Searchbar.destroy()})),"ios"===this.theme&&e.view&&e.view.router.dynamicNavbar&&e.$navbarEl&&e.$navbarEl.length>0&&e.$navbarEl.find(".searchbar-init").each((e=>{e.f7Searchbar&&e.f7Searchbar.destroy&&e.f7Searchbar.destroy()}))}},clicks:{".searchbar-clear":function(e,a){void 0===a&&(a={});const r=this.searchbar.get(a.searchbar);r&&r.clear()},".searchbar-enable":function(e,a){void 0===a&&(a={});const r=this.searchbar.get(a.searchbar);r&&r.enable(!0)},".searchbar-disable":function(e,a){void 0===a&&(a={});const r=this.searchbar.get(a.searchbar);r&&r.disable()},".searchbar-toggle":function(e,a){void 0===a&&(a={});const r=this.searchbar.get(a.searchbar);r&&r.toggle()}},vnode:{"searchbar-init":{insert(e){const a=e.elm,s=r(a);this.searchbar.create(i(s.dataset(),{el:a}))},destroy(e){const a=e.elm;a.f7Searchbar&&a.f7Searchbar.destroy&&a.f7Searchbar.destroy()}}}};if(a){if(e.prototype.modules&&e.prototype.modules[u.name])return;e.use(u),e.instance&&(e.instance.useModuleParams(u,e.instance.params),e.instance.useModule(u))}return u}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
