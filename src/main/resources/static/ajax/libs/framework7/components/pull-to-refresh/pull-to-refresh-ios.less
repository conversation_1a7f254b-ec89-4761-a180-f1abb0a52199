.ios {
  .ptr-preloader {
    margin-bottom: calc(-1 * var(--f7-ptr-size));
    width: 100%;
    left: 0;
    top: 0;
    position: relative;
    .preloader {
      visibility: visible;
    }
  }

  .ptr-content:not(.ptr-refreshing):not(.ptr-pull-up) {
    .ptr-preloader .preloader,
    .ptr-preloader .preloader-inner {
      animation: none;
    }
  }
  .ptr-content:not(.ptr-refreshing):not(.ptr-pull-up):not(.ptr-pull-down),
  .ptr-content.ptr-closing {
    .ptr-preloader .preloader-inner-line {
      opacity: 0 !important;
    }
  }

  .ptr-transitioning,
  .ptr-refreshing {
    > * {
      transition-duration: 200ms;
      transition-property: transform;
    }
  }

  .ptr-transitioning {
    .ptr-preloader .preloader-inner-line {
      transition-duration: 200ms;
    }
  }

  .ptr-pull-up {
    .ptr-preloader .preloader {
      animation: ios-ptr-preloader-spin 1s ease-out forwards;
    }
  }

  .ptr-refreshing:not(.ptr-bottom) {
    transform: none;
    > * {
      transform: translate3d(0, var(--f7-ptr-size), 0);
    }
    > .ptr-preloader {
      transform: translate3d(0, 0, 0);
    }
  }

  .ptr-bottom {
    .ptr-preloader {
      margin-bottom: 0;
      margin-top: calc(-1 * var(--f7-ptr-size));
      position: relative;
    }
    &.ptr-transitioning,
    &.ptr-refreshing {
      > * {
        transition-duration: 300ms;
        transition-property: transform;
      }
    }
    &.ptr-refreshing {
      transform: none;
      > * {
        transform: translate3d(0, calc(-1 * var(--f7-ptr-size)), 0);
      }
      > .ptr-preloader {
        transform: translate3d(0, 0, 0);
      }
    }
  }
}

@keyframes ios-ptr-preloader-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(135deg);
  }
}
