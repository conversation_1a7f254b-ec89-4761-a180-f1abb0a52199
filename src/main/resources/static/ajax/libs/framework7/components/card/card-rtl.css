:root{--f7-card-margin-horizontal:16px;--f7-card-margin-vertical:16px;--f7-card-content-padding-horizontal:16px;--f7-card-content-padding-vertical:16px;--f7-card-border-radius:4px;--f7-card-font-size:inherit;--f7-card-header-text-color:inherit;--f7-card-header-font-weight:400;--f7-card-header-padding-horizontal:16px;--f7-card-footer-font-weight:400;--f7-card-footer-font-size:inherit;--f7-card-footer-padding-horizontal:16px;--f7-card-expandable-font-size:16px;--f7-card-expandable-tablet-width:670px;--f7-card-expandable-tablet-height:670px;--f7-card-bg-color:#fff;--f7-card-outline-border-color:rgba(0, 0, 0, 0.12);--f7-card-header-border-color:rgba(0, 0, 0, 0.1);--f7-card-footer-border-color:rgba(0, 0, 0, 0.1);--f7-card-expandable-bg-color:#fff}:root .dark,:root.dark{--f7-card-bg-color:#1c1c1d;--f7-card-expandable-bg-color:#1c1c1d;--f7-card-outline-border-color:rgba(255, 255, 255, 0.15);--f7-card-header-border-color:rgba(255, 255, 255, 0.15);--f7-card-footer-border-color:rgba(255, 255, 255, 0.15);--f7-card-footer-text-color:rgba(255, 255, 255, 0.55)}.ios{--f7-card-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2);--f7-card-header-font-size:17px;--f7-card-header-padding-vertical:10px;--f7-card-header-min-height:44px;--f7-card-footer-text-color:rgba(0, 0, 0, 0.45);--f7-card-footer-padding-vertical:10px;--f7-card-footer-min-height:44px;--f7-card-expandable-margin-horizontal:20px;--f7-card-expandable-margin-vertical:30px;--f7-card-expandable-box-shadow:0px 20px 40px rgba(0, 0, 0, 0.3);--f7-card-expandable-border-radius:15px;--f7-card-expandable-tablet-border-radius:5px;--f7-card-expandable-header-font-size:27px;--f7-card-expandable-header-font-weight:bold}.md{--f7-card-box-shadow:var(--f7-elevation-1);--f7-card-header-font-size:16px;--f7-card-header-padding-vertical:4px;--f7-card-header-min-height:48px;--f7-card-footer-text-color:rgba(0, 0, 0, 0.54);--f7-card-footer-padding-vertical:4px;--f7-card-footer-min-height:48px;--f7-card-expandable-margin-horizontal:12px;--f7-card-expandable-margin-vertical:24px;--f7-card-expandable-box-shadow:var(--f7-elevation-10);--f7-card-expandable-border-radius:8px;--f7-card-expandable-tablet-border-radius:4px;--f7-card-expandable-header-font-size:24px;--f7-card-expandable-header-font-weight:500}.aurora{--f7-card-border-radius:8px;--f7-card-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.15);--f7-card-header-font-size:16px;--f7-card-header-font-weight:bold;--f7-card-header-padding-vertical:8px;--f7-card-header-min-height:48px;--f7-card-footer-text-color:rgba(0, 0, 0, 0.6);--f7-card-footer-padding-vertical:8px;--f7-card-footer-min-height:48px;--f7-card-expandable-margin-horizontal:10px;--f7-card-expandable-margin-vertical:20px;--f7-card-expandable-box-shadow:0px 10px 20px rgba(0, 0, 0, 0.2);--f7-card-expandable-border-radius:8px;--f7-card-expandable-tablet-border-radius:4px;--f7-card-expandable-header-font-size:24px;--f7-card-expandable-header-font-weight:bold}.card .list>ul:after,.card .list>ul:before,.cards-list>ul:after,.cards-list>ul:before{display:none!important}.card .list ul,.cards-list ul{background:0 0}.card{background:var(--f7-card-bg-color);position:relative;border-radius:var(--f7-card-border-radius);font-size:var(--f7-card-font-size);margin-top:var(--f7-card-margin-vertical);margin-bottom:var(--f7-card-margin-vertical);margin-left:calc(var(--f7-card-margin-horizontal) + var(--f7-safe-area-left));margin-right:calc(var(--f7-card-margin-horizontal) + var(--f7-safe-area-right));box-shadow:var(--f7-card-box-shadow)}.card .block,.card .list{margin:0}.row:not(.no-gap) .col>.card{margin-left:0;margin-right:0}.card.no-shadow{box-shadow:none}.aurora .card-outline-aurora,.card-outline,.ios .card-outline-ios,.md .card-outline-md{box-shadow:none;border:1px solid var(--f7-card-outline-border-color)}.aurora .card-outline-aurora.no-border,.aurora .card-outline-aurora.no-hairlines,.card-outline.no-border,.card-outline.no-hairlines,.ios .card-outline-ios.no-border,.ios .card-outline-ios.no-hairlines,.md .card-outline-md.no-border,.md .card-outline-md.no-hairlines{border:none}.card-content{position:relative}.card-content-padding{position:relative;padding:var(--f7-card-content-padding-vertical) var(--f7-card-content-padding-horizontal)}.card-content-padding>.block,.card-content-padding>.list{margin:calc(-1 * var(--f7-card-content-padding-vertical)) calc(-1 * var(--f7-card-content-padding-horizontal))}.card-content-padding>p:first-child{margin-top:0}.card-content-padding>p:last-child{margin-bottom:0}.card-header{min-height:var(--f7-card-header-min-height);color:var(--f7-card-header-text-color);font-size:var(--f7-card-header-font-size);font-weight:var(--f7-card-header-font-weight);padding:var(--f7-card-header-padding-vertical) var(--f7-card-header-padding-horizontal)}.card-footer{min-height:var(--f7-card-footer-min-height);color:var(--f7-card-footer-text-color);font-size:var(--f7-card-footer-font-size);font-weight:var(--f7-card-footer-font-weight);padding:var(--f7-card-footer-padding-vertical) var(--f7-card-footer-padding-horizontal)}.card-footer a.link{overflow:hidden}.card-footer,.card-header{position:relative;box-sizing:border-box;display:flex;justify-content:space-between;align-items:center}.card-footer[valign=top],.card-header[valign=top]{align-items:flex-start}.card-footer[valign=bottom],.card-header[valign=bottom]{align-items:flex-end}.card-footer a.link,.card-header a.link{position:relative}.card-footer a.link i.icon,.card-header a.link i.icon{display:block}.card-footer a.icon-only,.card-header a.icon-only{display:flex;justify-content:center;align-items:center;margin:0}.card-header{border-radius:var(--f7-card-border-radius) var(--f7-card-border-radius) 0 0}.card-header:after{content:'';position:absolute;background-color:var(--f7-card-header-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.card-header.no-hairline:after{display:none!important}.card-footer{border-radius:0 0 var(--f7-card-border-radius) var(--f7-card-border-radius)}.card-footer:before{content:'';position:absolute;background-color:var(--f7-card-footer-border-color);display:block;z-index:15;top:0;right:auto;bottom:auto;left:0;height:1px;width:100%;transform-origin:50% 0%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.card-footer.no-hairline:before{display:none!important}.card-expandable{overflow:hidden;height:300px;background:var(--f7-card-expandable-bg-color);position:relative;transform-origin:center center;transition-property:transform,border-radius;border-radius:var(--f7-card-expandable-border-radius);z-index:2;transition-duration:.2s;margin-left:calc(var(--f7-card-expandable-margin-horizontal) + var(--f7-safe-area-left));margin-right:calc(var(--f7-card-expandable-margin-horizontal) + var(--f7-safe-area-right));margin-top:var(--f7-card-expandable-margin-vertical);margin-bottom:var(--f7-card-expandable-margin-vertical);box-shadow:var(--f7-card-expandable-box-shadow);font-size:var(--f7-card-expandable-font-size)}.card-expandable:not(.card-opened){-webkit-user-select:none;user-select:none}.card-expandable.card-no-transition{transition-duration:0s}.card-expandable.card-expandable-animate-width .card-content{transition-property:width,transform;width:100%}.card-expandable.active-state{transform:scale(.97) translate3d(0,0,0)}.card-expandable .card-opened-fade-in,.card-expandable .card-opened-fade-out{transition-duration:.4s}.card-expandable .card-opened-fade-in{opacity:0;pointer-events:none}.card-expandable .card-content{position:absolute;top:0;width:100vw;height:100vh;transform-origin:center top;overflow:hidden;transition-property:transform;box-sizing:border-box;pointer-events:none;right:0}.card-expandable .card-content .card-content-padding{padding-left:calc(var(--f7-safe-area-left) + var(--f7-card-content-padding-horizontal));padding-right:calc(var(--f7-safe-area-right) + var(--f7-card-content-padding-horizontal))}.card-expandable.card-opened{transition-duration:0s}.card-expandable.card-closing,.card-expandable.card-opening,.card-expandable.card-transitioning{transition-duration:.4s}.card-expandable.card-opening .card-content{transition-duration:.3s}.card-expandable.card-closing .card-content{transition-duration:.5s}.card-expandable.card-closing,.card-expandable.card-opened,.card-expandable.card-opening{z-index:300}.card-expandable.card-opened,.card-expandable.card-opening{border-radius:0}.card-expandable.card-opened .card-opened-fade-in,.card-expandable.card-opening .card-opened-fade-in{opacity:1;pointer-events:auto}.card-expandable.card-opened .card-opened-fade-out,.card-expandable.card-opening .card-opened-fade-out{opacity:0;pointer-events:none}.card-expandable.card-opened .card-content{overflow:auto;-webkit-overflow-scrolling:touch;pointer-events:auto}.card-expandable .card-header{font-size:var(--f7-card-expandable-header-font-size);font-weight:var(--f7-card-expandable-header-font-weight)}.card-expandable .card-header:after{display:none!important}.card-prevent-open{pointer-events:auto}.card-expandable-size{width:0;height:0;position:absolute;left:0;top:0;opacity:0;pointer-events:none;visibility:hidden}@media (min-width:768px) and (min-height:670px){.card-expandable:not(.card-tablet-fullscreen){max-width:var(--f7-card-expandable-tablet-width)}.card-expandable:not(.card-tablet-fullscreen).card-opened,.card-expandable:not(.card-tablet-fullscreen).card-opening{border-radius:var(--f7-card-expandable-tablet-border-radius)}.card-expandable:not(.card-tablet-fullscreen):not(.card-expandable-animate-width) .card-content{width:var(--f7-card-expandable-tablet-width)}.card-expandable:not(.card-tablet-fullscreen) .card-expandable-size{width:var(--f7-card-expandable-tablet-width);height:var(--f7-card-expandable-tablet-height)}}.page.page-with-card-opened .page-content{overflow:hidden}.card-backdrop{position:fixed;left:0;top:0;width:100%;height:100%;z-index:299;pointer-events:none;background:rgba(0,0,0,.2);opacity:0}.card-backdrop-in{animation:card-backdrop-fade-in .4s forwards;pointer-events:auto}.card-backdrop-out{animation:card-backdrop-fade-out .4s forwards}@supports ((-webkit-backdrop-filter:blur(15px)) or (backdrop-filter:blur(15px))){.card-backdrop{background:0 0;-webkit-backdrop-filter:blur(15px);backdrop-filter:blur(15px)}}@keyframes card-backdrop-fade-in{from{opacity:0}to{opacity:1}}@keyframes card-backdrop-fade-out{from{opacity:1}to{opacity:0}}