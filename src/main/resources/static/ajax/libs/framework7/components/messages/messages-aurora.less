.aurora {
  .messages-title,
  .message-header,
  .message-footer,
  .message-name {
    b {
      font-weight: 500;
    }
  }
  .message-header,
  .message-name {
    margin-bottom: 2px;
  }
  .message-footer {
    margin-top: 2px;
  }
  .message-text-header {
    margin-bottom: 2px;
  }
  .message-text-footer {
    margin-top: 2px;
  }
  .message-received,
  .message-sent {
    &.message-tail .message-bubble {
      &:before {
        position: absolute;
        content: '';
        bottom: 0;
        width: 0;
        height: 0;
      }
    }
  }
  .message-image {
    margin: var(--f7-message-bubble-padding-vertical)
      calc(-1 * var(--f7-message-bubble-padding-horizontal) + 4px);
    &:first-child {
      margin-top: calc(-1 * var(--f7-message-bubble-padding-vertical) + 4px);
      img {
        border-top-left-radius: var(--f7-message-bubble-border-radius);
        border-top-right-radius: var(--f7-message-bubble-border-radius);
      }
    }
    &:last-child {
      margin-bottom: calc(-1 * var(--f7-message-bubble-padding-vertical) + 4px);
      img {
        border-bottom-left-radius: var(--f7-message-bubble-border-radius);
        border-bottom-right-radius: var(--f7-message-bubble-border-radius);
      }
    }
  }
  .message-received {
    margin-left: calc(16px + var(--f7-safe-area-left));
    .message-avatar + .message-content {
      margin-left: 5px;
    }
    .message-header,
    .message-footer,
    .message-name {
      margin-left: var(--f7-message-bubble-padding-horizontal);
    }
    &.message-tail .message-bubble {
      &:before {
        border-left: 6px solid transparent;
        border-right: 0 solid transparent;
        border-bottom: 6px solid var(--f7-message-received-bg-color);
        right: 100%;
        transform: translate(2px, 0px) rotate(-15deg);
        transform-origin: right bottom;
      }
    }
  }
  .message-sent {
    margin-right: calc(16px + var(--f7-safe-area-right));
    .message-avatar + .message-content {
      margin-right: 5px;
    }
    .message-header,
    .message-footer,
    .message-name {
      margin-right: var(--f7-message-bubble-padding-horizontal);
    }
    &.message-tail .message-bubble {
      &:before {
        border-left: 0 solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid var(--f7-message-sent-bg-color, var(--f7-theme-color));
        left: 100%;
        transform: translate(-2px, 0px) rotate(15deg);
        transform-origin: left bottom;
      }
    }
  }

  // Rules
  .message + .message:not(.message-first) {
    margin-top: 5px;
  }

  // Typing
  .message-typing-indicator {
    > div {
      width: 6px;
      height: 6px;
      + div {
        .ltr({
          margin-left: 4px;
        });
        .rtl({
          margin-right: 4px;
        });
      }
    }
    > div:nth-child(1) {
      animation: aurora-message-typing-indicator 900ms infinite;
    }
    > div:nth-child(2) {
      animation: aurora-message-typing-indicator 900ms 150ms infinite;
    }
    > div:nth-child(3) {
      animation: aurora-message-typing-indicator 900ms 300ms infinite;
    }
  }
}

@keyframes aurora-message-typing-indicator {
  0% {
    transform: translateY(0%);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0%);
  }
}
