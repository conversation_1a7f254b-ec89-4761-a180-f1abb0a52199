(function framework7ComponentLoader(t,e){void 0===e&&(e=!0);var a=t.$,i=t.utils,n=t.getDevice,s=(t.getSupport,t.Class,t.<PERSON>,t.<PERSON>structor<PERSON>,t.<PERSON>,i.bindMethods);const r={ignoreTypes:["checkbox","button","submit","range","radio","image"],createTextareaResizableShadow(){const t=getDocument(),e=a(t.createElement("textarea"));e.addClass("textarea-resizable-shadow"),e.prop({disabled:!0,readonly:!0}),r.textareaResizableShadow=e},textareaResizableShadow:void 0,resizeTextarea(t){const e=this,i=getWindow(),n=a(t);r.textareaResizableShadow||r.createTextareaResizableShadow();const s=r.textareaResizableShadow;if(!n.length)return;if(!n.hasClass("resizable"))return;0===r.textareaResizableShadow.parents().length&&e.$el.append(s);const l=i.getComputedStyle(n[0]);"padding-top padding-bottom padding-left padding-right margin-left margin-right margin-top margin-bottom width font-size font-family font-style font-weight line-height font-variant text-transform letter-spacing border box-sizing display".split(" ").forEach((t=>{let e=l[t];"font-size line-height letter-spacing width".split(" ").indexOf(t)>=0&&(e=e.replace(",",".")),s.css(t,e)}));const o=n[0].clientHeight;s.val("");const p=s[0].scrollHeight;s.val(n.val()),s.css("height",0);const u=s[0].scrollHeight;o!==u&&(u>p?n.css("height",`${u}px`):u<o&&n.css("height",""),(u>p||u<o)&&(n.trigger("textarea:resize",{initialHeight:p,currentHeight:o,scrollHeight:u}),e.emit("textareaResize",{initialHeight:p,currentHeight:o,scrollHeight:u})))},validate(t){const e=a(t);if(!e.length)return!0;const i=e.parents(".item-input"),n=e.parents(".input");function s(){e[0].f7ValidateReadonly&&(e[0].readOnly=!0)}e[0].f7ValidateReadonly&&(e[0].readOnly=!1);const r=e[0].validity,l=e.dataset().errorMessage||e[0].validationMessage||"";if(!r)return s(),!0;if(!r.valid){let t=e.nextAll(".item-input-error-message, .input-error-message");return l&&(0===t.length&&(t=a(`<div class="${n.length?"input-error-message":"item-input-error-message"}"></div>`),t.insertAfter(e)),t.text(l)),t.length>0&&(i.addClass("item-input-with-error-message"),n.addClass("input-with-error-message")),i.addClass("item-input-invalid"),n.addClass("input-invalid"),e.addClass("input-invalid"),s(),!1}return i.removeClass("item-input-invalid item-input-with-error-message"),n.removeClass("input-invalid input-with-error-message"),e.removeClass("input-invalid"),s(),!0},validateInputs(t){const e=this;return a(t).find("input, textarea, select").map((t=>e.input.validate(t))).indexOf(!1)<0},focus(t){const e=a(t),i=e.attr("type");r.ignoreTypes.indexOf(i)>=0||(e.parents(".item-input").addClass("item-input-focused"),e.parents(".input").addClass("input-focused"),e.addClass("input-focused"))},blur(t){const e=a(t);e.parents(".item-input").removeClass("item-input-focused"),e.parents(".input").removeClass("input-focused"),e.removeClass("input-focused")},checkEmptyState(t){const e=this;let i=a(t);if(i.is("input, select, textarea, .item-input [contenteditable]")||(i=i.find("input, select, textarea, .item-input [contenteditable]").eq(0)),!i.length)return;let n;n=i[0].hasAttribute("contenteditable")?i.find(".text-editor-placeholder").length?"":i.html():i.val();const s=i.parents(".item-input"),r=i.parents(".input");n&&"string"==typeof n&&""!==n.trim()||Array.isArray(n)&&n.length>0?(s.addClass("item-input-with-value"),r.addClass("input-with-value"),i.addClass("input-with-value"),i.trigger("input:notempty"),e.emit("inputNotEmpty",i[0])):(s.removeClass("item-input-with-value"),r.removeClass("input-with-value"),i.removeClass("input-with-value"),i.trigger("input:empty"),e.emit("inputEmpty",i[0]))},scrollIntoView(t,e,i,n){void 0===e&&(e=0);const s=a(t),r=s.parents(".page-content, .panel, .card-expandable .card-content").eq(0);if(!r.length)return!1;const l=r[0].offsetHeight,o=r[0].scrollTop,p=parseInt(r.css("padding-top"),10),u=parseInt(r.css("padding-bottom"),10),d=r.offset().top-o,c=s.offset().top-d,h=c+o-p,m=c+o-l+u+s[0].offsetHeight,g=h+(m-h)/2;return o>h?(r.scrollTop(i?g:h,e),!0):o<m?(r.scrollTop(i?g:m,e),!0):(n&&r.scrollTop(i?g:m,e),!1)},init(){const t=this,e=n(),i=getWindow(),s=getDocument();r.createTextareaResizableShadow(),a(s).on("click",".input-clear-button",(function(){const e=a(this).siblings("input, textarea").eq(0),i=e.val();e.val("").trigger("input change").focus().trigger("input:clear",i),t.emit("inputClear",i)})),a(s).on("mousedown",".input-clear-button",(function(t){t.preventDefault()})),a(s).on("change input","input, textarea, select, .item-input [contenteditable]",(function(){const e=a(this),i=e.attr("type"),n=e[0].nodeName.toLowerCase(),s=e[0].hasAttribute("contenteditable");r.ignoreTypes.indexOf(i)>=0||(t.input.checkEmptyState(e),s||(null!==e.attr("data-validate-on-blur")||!e.dataset().validate&&null===e.attr("validate")||t.input.validate(e),"textarea"===n&&e.hasClass("resizable")&&t.input.resizeTextarea(e)))}),!0),a(s).on("focus","input, textarea, select, .item-input [contenteditable]",(function(){const n=this;t.params.input.scrollIntoViewOnFocus&&(e.android?a(i).once("resize",(()=>{s&&s.activeElement===n&&t.input.scrollIntoView(n,t.params.input.scrollIntoViewDuration,t.params.input.scrollIntoViewCentered,t.params.input.scrollIntoViewAlways)})):t.input.scrollIntoView(n,t.params.input.scrollIntoViewDuration,t.params.input.scrollIntoViewCentered,t.params.input.scrollIntoViewAlways)),t.input.focus(n)}),!0),a(s).on("blur","input, textarea, select, .item-input [contenteditable]",(function(){const e=a(this),i=e[0].nodeName.toLowerCase();t.input.blur(e),(e.dataset().validate||null!==e.attr("validate")||null!==e.attr("data-validate-on-blur"))&&t.input.validate(e),"textarea"===i&&e.hasClass("resizable")&&r.textareaResizableShadow&&r.textareaResizableShadow.remove()}),!0),a(s).on("invalid","input, textarea, select",(function(e){const i=a(this);null!==i.attr("data-validate-on-blur")||!i.dataset().validate&&null===i.attr("validate")||(e.preventDefault(),t.input.validate(i))}),!0)}};var l={name:"input",params:{input:{scrollIntoViewOnFocus:void 0,scrollIntoViewCentered:!1,scrollIntoViewDuration:0,scrollIntoViewAlways:!1}},create(){const t=this;void 0===t.params.input.scrollIntoViewOnFocus&&(t.params.input.scrollIntoViewOnFocus=n().android),s(t,{input:r})},on:{init(){this.input.init()},tabMounted(t){const e=this,i=a(t);i.find(".item-input, .input").each((t=>{a(t).find("input, select, textarea, [contenteditable]").each((t=>{const i=a(t);r.ignoreTypes.indexOf(i.attr("type"))>=0||e.input.checkEmptyState(i)}))})),i.find("textarea.resizable").each((t=>{e.input.resizeTextarea(t)}))},pageInit(t){const e=this,i=t.$el;i.find(".item-input, .input").each((t=>{a(t).find("input, select, textarea, [contenteditable]").each((t=>{const i=a(t);r.ignoreTypes.indexOf(i.attr("type"))>=0||e.input.checkEmptyState(i)}))})),i.find("textarea.resizable").each((t=>{e.input.resizeTextarea(t)}))},"panelBreakpoint panelCollapsedBreakpoint panelResize panelOpen panelSwipeOpen resize viewMasterDetailBreakpoint":function(t){const e=this;t&&t.$el?t.$el.find("textarea.resizable").each((t=>{e.input.resizeTextarea(t)})):a("textarea.resizable").each((t=>{e.input.resizeTextarea(t)}))}}};if(e){if(t.prototype.modules&&t.prototype.modules[l.name])return;t.use(l),t.instance&&(t.instance.useModuleParams(l,t.instance.params),t.instance.useModule(l))}return l}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
