:root{--f7-input-bg-color:transparent;--f7-label-font-weight:400;--f7-label-line-height:1.3;--f7-input-padding-left:0px;--f7-input-padding-right:0px;--f7-input-error-text-color:#ff3b30;--f7-input-error-font-size:12px;--f7-input-error-line-height:1.4;--f7-input-error-font-weight:400;--f7-input-info-font-size:12px;--f7-input-info-line-height:1.4;--f7-input-outline-height:40px;--f7-input-outline-border-radius:4px;--f7-input-outline-padding-horizontal:12px;--f7-textarea-height:100px;--f7-input-outline-border-color:#bbb}:root .dark,:root.dark{--f7-input-outline-border-color:#444}.ios{--f7-input-height:44px;--f7-input-font-size:17px;--f7-input-placeholder-color:#a9a9a9;--f7-textarea-padding-vertical:11px;--f7-label-font-size:12px;--f7-label-text-color:inherit;--f7-floating-label-scale:calc(17 / 12);--f7-inline-label-font-size:17px;--f7-inline-label-line-height:1.4;--f7-inline-label-padding-top:3px;--f7-input-clear-button-size:14px;--f7-input-text-color:#000000;--f7-input-info-text-color:rgba(0, 0, 0, 0.45);--f7-input-clear-button-color:rgba(0, 0, 0, 0.45)}.ios .dark,.ios.dark{--f7-input-text-color:#fff;--f7-input-info-text-color:rgba(255, 255, 255, 0.55);--f7-input-clear-button-color:rgba(255, 255, 255, 0.5)}.md{--f7-input-height:36px;--f7-input-font-size:16px;--f7-textarea-padding-vertical:7px;--f7-label-font-size:12px;--f7-floating-label-scale:calc(16 / 12);--f7-inline-label-font-size:16px;--f7-inline-label-line-height:1.5;--f7-inline-label-padding-top:7px;--f7-input-clear-button-size:18px;--f7-input-clear-button-color:#aaa;--f7-input-text-color:#212121;--f7-input-placeholder-color:rgba(0, 0, 0, 0.35);--f7-label-text-color:rgba(0, 0, 0, 0.65);--f7-input-info-text-color:rgba(0, 0, 0, 0.45)}.md .dark,.md.dark{--f7-input-text-color:rgba(255, 255, 255, 0.87);--f7-input-placeholder-color:rgba(255, 255, 255, 0.35);--f7-label-text-color:rgba(255, 255, 255, 0.54);--f7-input-info-text-color:rgba(255, 255, 255, 0.45)}.aurora{--f7-input-height:32px;--f7-input-font-size:16px;--f7-textarea-padding-vertical:4px;--f7-label-font-size:12px;--f7-label-text-color:inherit;--f7-floating-label-scale:calc(16 / 12);--f7-inline-label-font-size:16px;--f7-inline-label-line-height:1.5;--f7-inline-label-padding-top:4px;--f7-input-clear-button-size:14px;--f7-input-outline-border-radius:8px;--f7-input-text-color:#000000;--f7-input-placeholder-color:rgba(0, 0, 0, 0.32);--f7-input-clear-button-color:rgba(0, 0, 0, 0.45);--f7-input-info-text-color:rgba(0, 0, 0, 0.5)}.aurora .dark,.aurora.dark{--f7-input-text-color:#fff;--f7-input-clear-button-color:rgba(255, 255, 255, 0.5);--f7-input-placeholder-color:rgba(255, 255, 255, 0.35);--f7-input-info-text-color:rgba(255, 255, 255, 0.45)}input[type=date],input[type=datetime-local],input[type=email],input[type=month],input[type=number],input[type=password],input[type=search],input[type=tel],input[type=text],input[type=time],input[type=url],select,textarea{box-sizing:border-box;-webkit-appearance:none;appearance:none;border:none;box-shadow:none;border-radius:0;outline:0;display:block;padding:0;margin:0;font-family:inherit;background:0 0;resize:none;font-size:inherit;color:inherit}.textarea-resizable-shadow{opacity:0;position:absolute;z-index:-1000;pointer-events:none;left:-1000px;top:-1000px;visibility:hidden}.list input[type=date],.list input[type=datetime-local],.list input[type=email],.list input[type=month],.list input[type=number],.list input[type=password],.list input[type=search],.list input[type=tel],.list input[type=text],.list input[type=time],.list input[type=url],.list select{width:100%;height:var(--f7-input-height);color:var(--f7-input-text-color);font-size:var(--f7-input-font-size);background-color:var(--f7-input-bg-color,transparent);padding-left:var(--f7-input-padding-left);padding-right:var(--f7-input-padding-right)}.list input[type=date]::placeholder,.list input[type=datetime-local]::placeholder,.list input[type=email]::placeholder,.list input[type=month]::placeholder,.list input[type=number]::placeholder,.list input[type=password]::placeholder,.list input[type=search]::placeholder,.list input[type=tel]::placeholder,.list input[type=text]::placeholder,.list input[type=time]::placeholder,.list input[type=url]::placeholder,.list select::placeholder{color:var(--f7-input-placeholder-color)}.list textarea{width:100%;color:var(--f7-input-text-color);font-size:var(--f7-input-font-size);resize:none;line-height:1.4;height:var(--f7-textarea-height);background-color:var(--f7-input-bg-color,transparent);padding-top:var(--f7-textarea-padding-vertical);padding-bottom:var(--f7-textarea-padding-vertical);padding-left:var(--f7-input-padding-left);padding-right:var(--f7-input-padding-right)}.list textarea::placeholder{color:var(--f7-input-placeholder-color)}.list textarea.resizable{height:var(--f7-input-height)}.list input[type=datetime-local]{max-width:50vw}.list input[type=date],.list input[type=datetime-local],.list input[type=month],.list input[type=time]{line-height:var(--f7-input-height)}.list input[type=date],.list input[type=datetime-local],.list input[type=month]{text-align:right;flex-direction:row-reverse;width:auto}.list .item-floating-label,.list .item-label{width:100%;vertical-align:top;flex-shrink:0;font-size:var(--f7-label-font-size);font-weight:var(--f7-label-font-weight);line-height:var(--f7-label-line-height);color:var(--f7-label-text-color);transition-duration:.2s;transition-property:transform,color}.list .item-floating-label{--label-height:calc(var(--f7-label-font-size) * var(--f7-label-line-height));transform:scale(var(--f7-floating-label-scale)) translateY(calc((var(--f7-input-height)/ 2 + 50%)/ var(--f7-floating-label-scale)));color:var(--f7-input-placeholder-color);width:auto;max-width:calc(100% / var(--f7-floating-label-scale));pointer-events:none;right:var(--f7-input-padding-right);transform-origin:right center}.list .item-floating-label~.item-input-wrap input::placeholder,.list .item-floating-label~.item-input-wrap textarea::placeholder{opacity:0;transition-duration:.1s}.list .item-floating-label~.item-input-wrap input.input-focused::placeholder,.list .item-floating-label~.item-input-wrap textarea.input-focused::placeholder{opacity:1;transition-duration:.3s}.list .item-input-with-value .item-floating-label{color:var(--f7-label-text-color)}.list .item-input-focused .item-floating-label,.list .item-input-with-value .item-floating-label{transform:scale(1) translateY(0)}.list .item-input-wrap{width:100%;flex-shrink:1;position:relative}.item-input .item-inner{display:flex;flex-direction:column;align-items:flex-start}.input-error-message,.item-input-error-message{font-size:var(--f7-input-error-font-size);line-height:var(--f7-input-error-line-height);color:var(--f7-input-error-text-color);font-weight:var(--f7-input-error-font-weight);display:none;box-sizing:border-box}.input-info,.item-input-info{font-size:var(--f7-input-info-font-size);line-height:var(--f7-input-info-line-height);color:var(--f7-input-info-text-color)}.input-invalid .input-error-message,.input-invalid .item-input-error-message,.item-input-invalid .input-error-message,.item-input-invalid .item-input-error-message{display:block}.input-invalid .input-info,.input-invalid .item-input-info,.item-input-invalid .input-info,.item-input-invalid .item-input-info{display:none}.inline-label .item-inner,.inline-labels .item-inner{display:flex;align-items:center;flex-direction:row}.inline-label .item-floating-label,.inline-label .item-label,.inline-labels .item-floating-label,.inline-labels .item-label{padding-top:var(--f7-inline-label-padding-top);align-self:flex-start;width:35%;font-size:var(--f7-inline-label-font-size);line-height:var(--f7-inline-label-line-height)}.inline-label .item-floating-label+.item-input-wrap,.inline-label .item-label+.item-input-wrap,.inline-labels .item-floating-label+.item-input-wrap,.inline-labels .item-label+.item-input-wrap{margin-right:8px}.input{position:relative}.input input,.input select,.input textarea{width:100%}.input-clear-button{opacity:0;pointer-events:none;visibility:hidden;transition-duration:.1s;position:absolute;top:50%;border:none;padding:0;margin:0;outline:0;z-index:1;cursor:pointer;background:0 0;width:var(--f7-input-clear-button-size);height:var(--f7-input-clear-button-size);margin-top:calc(-1 * var(--f7-input-clear-button-size)/ 2);color:var(--f7-input-clear-button-color);left:0}.input-clear-button:after{font-family:framework7-core-icons;font-weight:400;font-style:normal;line-height:1;letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:'liga';text-align:center;display:block;width:100%;height:100%;font-size:20px}.input-clear-button:before{position:absolute;content:'';left:50%;top:50%}.item-input-wrap .input-clear-button{top:calc(var(--f7-input-height)/ 2)}.input-clear-button.active-state{opacity:.75!important}.input-with-value .input-clear-button,.input-with-value~.input-clear-button,.item-input-with-value .input-clear-button{opacity:1;pointer-events:auto;visibility:visible}.input-dropdown,.input-dropdown-wrap{position:relative}.input-dropdown-wrap:before,.input-dropdown:before{content:'';pointer-events:none;position:absolute;top:50%;margin-top:-2px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:5px solid #727272;left:6px}.input-dropdown input,.input-dropdown select,.input-dropdown textarea,.input-dropdown-wrap input,.input-dropdown-wrap select,.input-dropdown-wrap textarea{padding-left:calc(20px + var(--f7-input-padding-left))}.input-outline:after,.item-input-outline .item-input-wrap:after{content:'';position:absolute;left:0;top:0;width:100%;height:100%;box-sizing:border-box;border:1px solid var(--f7-input-outline-border-color);border-radius:var(--f7-input-outline-border-radius);transition-duration:.2s;pointer-events:none}.input-outline.input-focused:after,.item-input-outline.item-input-focused .item-input-wrap:after{border-width:2px;border-color:var(--f7-input-outline-focused-border-color,var(--f7-theme-color))}.input-outline.input-invalid:after,.item-input-outline.item-input-invalid .item-input-wrap:after{border-width:2px;border-color:var(--f7-input-outline-invalid-border-color,var(--f7-input-error-text-color))}.input-outline input,.input-outline select,.input-outline textarea,.item-input-outline input,.item-input-outline select,.item-input-outline textarea,.list .item-input-outline input,.list .item-input-outline select,.list .item-input-outline textarea{border-radius:var(--f7-input-outline-border-radius);padding-left:var(--f7-input-outline-padding-horizontal);padding-right:var(--f7-input-outline-padding-horizontal)}.input-outline.input-dropdown:before,.item-input-outline .input-dropdown-wrap:before{left:8px}.input-outline.input-dropdown input,.input-outline.input-dropdown select,.input-outline.input-dropdown textarea,.item-input-outline .input-dropdown-wrap input,.item-input-outline .input-dropdown-wrap select,.item-input-outline .input-dropdown-wrap textarea{padding-left:20px}.input-outline .input-clear-button,.item-input-outline .input-clear-button{left:8px}.item-input-outline{--f7-input-height:var(--f7-input-outline-height)}.item-input-outline .item-inner:after{display:none!important}.item-input-outline .item-label{right:var(--f7-input-outline-padding-horizontal)}.inline-label .item-input-outline .item-label,.inline-labels .item-input-outline .item-label,.item-input-outline .inline-label .item-label,.item-input-outline .inline-label.item-label{right:0}.item-input-outline .item-floating-label{right:calc(var(--f7-input-outline-padding-horizontal) - 4px);padding-left:4px;padding-right:4px;background:var(--f7-page-bg-color);z-index:10;margin-top:calc(-.5 * (var(--f7-label-font-size) * var(--f7-label-line-height)))}.item-input-outline.item-input-focused .item-floating-label,.item-input-outline.item-input-with-value .item-floating-label{transform:scale(1) translateY(50%)}.item-input-outline .item-input-error-message,.item-input-outline .item-input-info{padding-right:var(--f7-input-outline-padding-horizontal)}.block-strong .item-input-outline .item-floating-label{background:var(--f7-block-strong-bg-color)}.list .item-input-outline .item-floating-label{background:var(--f7-list-bg-color)}.dark option{background-color:var(--f7-page-bg-color)}.ios .item-floating-label+.item-input-wrap,.ios .item-label+.item-input-wrap{margin-top:0}.ios .item-input-focused .item-floating-label{color:var(--f7-label-text-color)}.ios .item-input .item-media{align-self:flex-start}.ios .item-input-wrap{margin-top:calc(-1 * var(--f7-list-item-padding-vertical));margin-bottom:calc(-1 * var(--f7-list-item-padding-vertical))}.ios .inline-label .item-floating-label+.item-input-wrap,.ios .inline-label .item-label+.item-input-wrap,.ios .inline-labels .item-floating-label+.item-input-wrap,.ios .inline-labels .item-label+.item-input-wrap{margin-top:calc(-1 * var(--f7-list-item-padding-vertical))}.ios .inline-label .item-input-wrap,.ios .inline-labels .item-input-wrap{margin-top:calc(-1 * var(--f7-list-item-padding-vertical))}.ios .input-error-message,.ios .input-info,.ios .item-input-error-message,.ios .item-input-info{position:relative;margin-bottom:6px;margin-top:-8px}.ios .item-input-focused .item-floating-label,.ios .item-input-focused .item-label{color:var(--f7-label-focused-text-color,var(--f7-label-text-color))}.ios .item-input-focused .item-inner:after{background:var(--f7-input-focused-border-color,var(--f7-list-item-border-color))}.ios .item-input-invalid .item-floating-label,.ios .item-input-invalid .item-label{color:var(--f7-label-invalid-text-color,var(--f7-label-text-color))}.ios .item-input-invalid .item-inner:after{background:var(--f7-input-invalid-border-color,var(--f7-list-item-border-color))}.ios .input-invalid input,.ios .input-invalid select,.ios .input-invalid textarea,.ios .item-input-invalid input,.ios .item-input-invalid select,.ios .item-input-invalid textarea{color:var(--f7-input-invalid-text-color,var(--f7-input-error-text-color))}.ios .input-clear-button:after{content:'delete_round_ios';font-size:calc(var(--f7-input-clear-button-size) / (14 / 10));line-height:1.4}.ios .input-clear-button:before{width:44px;height:44px;margin-left:-22px;margin-top:-22px}.ios .input-outline .item-input-wrap,.ios .item-input-outline .item-input-wrap{margin-top:0;margin-bottom:0}.ios .input-outline .input-error-message,.ios .input-outline .input-info,.ios .input-outline .item-input-error-message,.ios .input-outline .item-input-info,.ios .item-input-outline .input-error-message,.ios .item-input-outline .input-info,.ios .item-input-outline .item-input-error-message,.ios .item-input-outline .item-input-info{margin-top:0;white-space:normal;overflow:hidden;text-overflow:ellipsis}.ios .input-outline .input-info,.ios .input-outline .item-input-info,.ios .item-input-outline .input-info,.ios .item-input-outline .item-input-info{margin-bottom:calc(-1 * var(--f7-input-info-font-size) * var(--f7-input-info-line-height))}.ios .input-outline .input-error-message,.ios .input-outline .item-input-error-message,.ios .item-input-outline .input-error-message,.ios .item-input-outline .item-input-error-message{margin-bottom:calc(-1 * var(--f7-input-error-font-size) * var(--f7-input-error-line-height))}.ios .input-outline.input-with-info .item-input-wrap,.ios .input-outline.item-input-with-info .item-input-wrap,.ios .item-input-outline.input-with-info .item-input-wrap,.ios .item-input-outline.item-input-with-info .item-input-wrap{margin-bottom:calc(var(--f7-input-info-font-size) * var(--f7-input-info-line-height))}.ios .input-outline.input-with-error-message .item-input-wrap,.ios .input-outline.item-input-with-error-message .item-input-wrap,.ios .item-input-outline.input-with-error-message .item-input-wrap,.ios .item-input-outline.item-input-with-error-message .item-input-wrap{margin-bottom:calc(var(--f7-input-error-font-size) * var(--f7-input-error-line-height))}.md .input:not(.input-outline):after,.md .item-input:not(.item-input-outline) .item-input-wrap:after{content:'';position:absolute;background-color:var(--f7-list-item-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.md .input:not(.input-outline):after,.md .item-input:not(.item-input-outline) .item-input-wrap:after{transition-duration:.2s}.md .item-input-wrap{min-height:var(--f7-input-height)}.md .item-input .item-media{align-self:flex-end}.md .item-input .item-inner:after{display:none!important}.md .inline-label .item-media,.md .inline-labels .item-media{align-self:flex-start;padding-top:14px}.md .input-with-error-message,.md .input-with-info,.md .item-input-with-error-message,.md .item-input-with-info{padding-bottom:20px}.md .input-error-message,.md .input-info,.md .item-input-error-message,.md .item-input-info{position:absolute;top:100%;margin-top:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%;right:0}.md .item-input-focused .item-floating-label,.md .item-input-focused .item-label{color:var(--f7-label-focused-text-color,var(--f7-theme-color))}.md .input-focused:not(.input-outline):after,.md .item-input-focused:not(.item-input-outline) .item-input-wrap:after{background:var(--f7-input-focused-border-color,var(--f7-theme-color))}.md .input-focused:not(.input-outline):after,.md .input-invalid:not(.input-outline):after,.md .item-input-focused:not(.item-input-outline) .item-input-wrap:after,.md .item-input-invalid:not(.item-input-outline) .item-input-wrap:after{transform:scaleY(2)!important}.md .input-invalid:not(.input-outline):after,.md .item-input-invalid:not(.item-input-outline) .item-input-wrap:after{background:var(--f7-input-invalid-border-color,var(--f7-input-error-text-color))}.md .item-input-invalid .item-floating-label,.md .item-input-invalid .item-label{color:var(--f7-label-invalid-text-color,var(--f7-input-error-text-color))}.md .input-invalid input,.md .input-invalid select,.md .input-invalid textarea,.md .item-input-invalid input,.md .item-input-invalid select,.md .item-input-invalid textarea{color:var(--f7-input-invalid-text-color,var(--f7-input-text-color))}.md .input-clear-button:after{font-size:calc(var(--f7-input-clear-button-size) / (24 / 20));content:'delete_round_md';line-height:1.2}.md .input-clear-button:before{width:48px;height:48px;margin-left:-24px;margin-top:-24px}.aurora .item-floating-label+.item-input-wrap,.aurora .item-label+.item-input-wrap{margin-top:0}.aurora .input:not(.input-outline):after{content:'';position:absolute;background-color:var(--f7-list-item-border-color);display:block;z-index:15;top:auto;right:auto;bottom:0;left:0;height:1px;width:100%;transform-origin:50% 100%;transform:scaleY(calc(1 / var(--f7-device-pixel-ratio)))}.aurora .input:not(.input-outline):after{transition-duration:.2s}.aurora .item-input-focused .item-floating-label{color:var(--f7-label-text-color)}.aurora .inline-label .item-media,.aurora .inline-labels .item-media{align-self:flex-start;padding-top:12px}.aurora .input-error-message,.aurora .input-info,.aurora .item-input-error-message,.aurora .item-input-info{position:relative}.aurora .input-focused:not(.input-outline):after,.aurora .input-invalid:not(.input-outline):after,.aurora .item-input-focused:not(.item-input-outline) .item-inner:after,.aurora .item-input-invalid:not(.item-input-outline) .item-inner:after{transform:scaleY(2)!important;display:block!important}.aurora .item-input-focused .item-floating-label,.aurora .item-input-focused .item-label{color:var(--f7-label-focused-text-color,var(--f7-theme-color))}.aurora .input-focused:not(.input-outline):after,.aurora .item-input-focused:not(.item-input-outline) .item-inner:after{background:var(--f7-input-focused-border-color,var(--f7-theme-color))}.aurora .item-input-invalid .item-floating-label,.aurora .item-input-invalid .item-label{color:var(--f7-label-invalid-text-color,var(--f7-input-error-text-color))}.aurora .input-invalid:not(.input-outline):after,.aurora .item-input-invalid:not(.item-input-outline) .item-inner:after{background:var(--f7-input-invalid-border-color,var(--f7-input-error-text-color))}.aurora .input-invalid input,.aurora .input-invalid select,.aurora .input-invalid textarea,.aurora .item-input-invalid input,.aurora .item-input-invalid select,.aurora .item-input-invalid textarea{color:var(--f7-input-invalid-text-color,var(--f7-input-text-color))}.aurora .input-clear-button:after{content:'delete_round_ios';font-size:calc(var(--f7-input-clear-button-size) / (14 / 10));line-height:1.4}.aurora .input-clear-button:before{width:44px;height:44px;margin-left:-22px;margin-top:-22px}.aurora .input-outline .item-input-wrap,.aurora .item-input-outline .item-input-wrap{margin-top:0;margin-bottom:0}.aurora .input-outline .input-error-message,.aurora .input-outline .input-info,.aurora .input-outline .item-input-error-message,.aurora .input-outline .item-input-info,.aurora .item-input-outline .input-error-message,.aurora .item-input-outline .input-info,.aurora .item-input-outline .item-input-error-message,.aurora .item-input-outline .item-input-info{margin-top:0;white-space:normal;overflow:hidden;text-overflow:ellipsis}.aurora .input-outline .input-info,.aurora .input-outline .item-input-info,.aurora .item-input-outline .input-info,.aurora .item-input-outline .item-input-info{margin-bottom:calc(-1 * var(--f7-input-info-font-size) * var(--f7-input-info-line-height))}.aurora .input-outline .input-error-message,.aurora .input-outline .item-input-error-message,.aurora .item-input-outline .input-error-message,.aurora .item-input-outline .item-input-error-message{margin-bottom:calc(-1 * var(--f7-input-error-font-size) * var(--f7-input-error-line-height))}.aurora .input-outline.input-with-info .item-input-wrap,.aurora .input-outline.item-input-with-info .item-input-wrap,.aurora .item-input-outline.input-with-info .item-input-wrap,.aurora .item-input-outline.item-input-with-info .item-input-wrap{margin-bottom:calc(var(--f7-input-info-font-size) * var(--f7-input-info-line-height))}.aurora .input-outline.input-with-error-message .item-input-wrap,.aurora .input-outline.item-input-with-error-message .item-input-wrap,.aurora .item-input-outline.input-with-error-message .item-input-wrap,.aurora .item-input-outline.item-input-with-error-message .item-input-wrap{margin-bottom:calc(var(--f7-input-error-font-size) * var(--f7-input-error-line-height))}