.ios {
  .toolbar {
    a.icon-only {
      min-height: var(--f7-toolbar-height);
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      min-width: 44px;
    }
  }
  .tabbar-labels {
    .tab-link,
    .link {
      padding-top: 4px;
      padding-bottom: 4px;
      i + span {
        margin: 0;
      }
    }
  }
  @media (min-width: 768px) and (min-height: 600px) {
    .tabbar,
    .tabbar-labels {
      .tab-link,
      .link {
        justify-content: center;
        flex-direction: row;
        i + span {
          margin-left: 5px;
        }
      }
    }
  }

  // Scrollable
  .tabbar-scrollable {
    .toolbar-inner {
      justify-content: flex-start;
    }
    .tab-link,
    .link {
      padding: 0 8px;
    }
  }
}
