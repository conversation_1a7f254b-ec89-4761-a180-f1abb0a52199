@import './app-vars.less';
// Core Icons
@font-face {
  @fontBase64: '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***************************+UZBO6dsX13XJ5PhSf0Fa2zs/bGtyYGByYHFN8OTl2k0ZnVfHeRIMxdcWZRcjqwYT7gmItTgWoOGYpGwZj9JDaQJ18/pTb1U0p38WxEKeEoshQEyMjkn+xJI92koaNfe3bWhFisUGmUIYWenBju4fg5s5EkBPyQNAjKHFOdILFQqeqPg9XMDikHN3hdOhAOIAXgVhxvFGuCJw9BnJAxMU7x7NOzVM8nvQ5lwKGTfj8ZiYft+CMOhbOhd4SSWTMbOIlU+zvBZIYy5kEkr6zq6DHfwBucu52rov+S80yybtCwukyaXRVl1VQSORnpRxDzfHyaBUAQdhahch38p/iWfv41dD0g+xTv4sR+7/fLPrWxaCngF3ysHyoW3iL8yBr1A+sICTlFTLNZ1UpsSqkIKWrlOKAYmD0JnInwui49Wbx0d3VpdLeVKVMr8+xx2HR3Zvxzh7bJVKlll53sxT50z49m4UeX5ad/D3gqu8G7GyVMP908wyT+eJxQ3JetmCq+CeVZ1K7GWWXTKRU5lV448P93Hn4S4xNiS6EoQX3+M038AL0/eSgAAeJxjYGRgYADi8z/9HsTz23xl4GZiAIG7GupfofS3/z/+9bKkMX0GcjkYwNIAehAN0wAAAHicY2BkYGD6/K+XQY8l7f8PBgaWNAagCApwBwCRUwXDeJxj2M0gyAACqxgGNWAMAGIdID4A5OwD0rOA+BBI7P9PhuNAMSBmSYOKWwCxExCfBYqf/f+D0Q3IdoGKAdlMIHVAzPTm/3+G3UA2w/8fIDOZzkL0MxkD9QHFAYb+GDQAAAAAKgAqACoANAA+AEgAUgBcAGYAcAB6AIQAjgCYAKIArAC2AMAAygDUAN4A6ADyAPwBBgEQARoBJAEuATgBQgFMAVYBYAFqAXQBfgGIAZIBnAGmAbIB3AH+AigCOgJQAmICggKSAqIC1ALqAwoDKgM8A1oDbAOQA64DvgPkA/4ELgROBG4EgASoBL4E2AT4eJxjYGRgYHBnkGZgYQABJiBmZACJOTDogQQADZsA2gB4nK2QvW7CMBSFj/mT2qHqUHaPgEjkZEEwFomFpeqQPQ0pWJAYmUgI9WW6devYrU/RpVP3vkVPjCt1YOhApCt/OT7XPtcArvAKgePXw71ngS5ePDdwiQ/PTUTi1nMLXfHsuY1r8em5g26jS6doXfDvznXVLDDAk+cGbvDuuYkHfHluYSAqz21I8ea5Q/0bUxhscYCFxhIrVJDMnKHPNYZiRRiSE2yQYkFXwbJU5nSnpFoveIZ2u5ia7cHq5aqSvawvYxVHQ5ls0oUutJXzVWo3abHVCzof2Vy35tgzhMUaIwS8uuacpB2X2NFq0yLfG7seBZmxeaAzU1I+6f1rmDmxcqvlfLmbKuRcEhPW/yMc/THGVGvP79tgZspqZuwyl3Go5ESejEo9HgejoH6Ocwye0Ge5r51PMotiltCt9WxIcrvTppRKRaFSSp7h0h8wQYYLAAAAeJxtUIlOwkAUnAGVW4rch36M8b7v2zSlLdIIXbJdxL/HhbaBJm6yyb6ZebPzHlJYnvkcxH/nWF8ihTQMVFFDHQ000UIbHXTRwz4OcIgjrTvBKc5wjgtc4grXuMEt7nCPBzziCc94wSve8I4PfDLFNDe4yS1mmGWOeRZYZInbLNNghTusssY6G2yyxTY77LLHXe7lA9eS9tD0RFCxh+6PFL4pva+hWiC5iBw75ZgbuQOl65IlpZjFVUGz9ndf/Oq3kVBqk1LCtrhON3yhvIFnW8rTiD0SgbvoKIfmfaGUGGvLnLQcTyyYatztiJlvWlMppJUJhFRRxDBEiOdj7XRSiuYIicqKiJDium3ZcUeuck0ppr6z+D2q9dATJc0w23qW5aArozCDFhjJferVJJy1Yjs0i/nschKN15Kdofcf+xC0ZgAA';
  font-family: 'framework7-core-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, @{fontBase64}') format('woff');
  font-weight: 400;
  font-style: normal;
}

.rtl({
  html {
    direction: rtl;
  }
});
html,
body,
.framework7-root {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}
body {
  margin: 0;
  padding: 0;
  width: 100%;
  background: #fff;
  overflow: hidden;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  font-family: var(--f7-font-family);
  font-size: var(--f7-font-size);
  line-height: var(--f7-line-height);
  color: var(--f7-text-color);
}
.dark body,
body.dark {
  background: #000;
}
.dark {
  color: var(--f7-text-color);
}
.framework7-root {
  overflow: hidden;
  box-sizing: border-box;
}
.framework7-initializing * {
  &,
  &:before,
  &:after {
    transition-duration: 0ms !important;
  }
}
.device-ios,
.device-android {
  cursor: pointer;
}
.device-ios {
  touch-action: manipulation;
}

// Fix for iPad in Safari in Lanscape mode
@media (width: 1024px) and (height: 691px) and (orientation: landscape) {
  html,
  body,
  .framework7-root {
    height: 671px;
  }
}
@media (width: 1024px) and (height: 692px) and (orientation: landscape) {
  html,
  body,
  .framework7-root {
    height: 672px;
  }
}
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-touch-callout: none;
}
a,
input,
textarea,
select {
  outline: 0;
}
a {
  cursor: pointer;
  text-decoration: none;
  color: var(--f7-theme-color);
}
.link,
.item-link {
  cursor: pointer;
}
p {
  margin: 1em 0;
}
// Disabled
.disabled {
  .disabled() !important;
}
// Full viewport
html.device-full-viewport {
  &,
  & body {
    height: 100vh;
  }
}

.if-ios-theme({
  @import './app-ios.less';
});
.if-md-theme({
  @import './app-md.less';
});
.if-aurora-theme({
  @import './app-aurora.less';
});
