.aurora {
  .ptr-preloader {
    margin-top: calc(-1 * var(--f7-ptr-size));
    width: 100%;
    left: 0;
  }
  .ptr-arrow {
    position: absolute;
    left: 50%;
    top: 50%;
    background: no-repeat center;
    z-index: 10;
    transform: rotate(0deg) translate3d(0, 0, 0);
    transition-duration: 300ms;
    transition-property: transform, opacity;
    width: 12px;
    height: 20px;
    margin-left: -6px;
    margin-top: -10px;
    visibility: visible;
    color: var(--f7-preloader-color);
    &:after {
      .core-icons-font();
      width: 12px;
      height: 20px;
      line-height: 20px;
      font-size: 8px;
      content: 'ptr_arrow_ios';
    }
  }
  .ptr-content:not(.ptr-refreshing) {
    .ptr-preloader .preloader,
    .ptr-preloader .preloader-inner {
      animation: none;
    }
  }
  .ptr-transitioning,
  .ptr-refreshing {
    transition-duration: 300ms;
    transition-property: transform;
  }
  .ptr-refreshing {
    transform: translate3d(0, var(--f7-ptr-size), 0);
    .ptr-arrow {
      visibility: hidden;
    }
    .ptr-preloader .preloader {
      visibility: visible;
    }
  }
  .ptr-pull-up {
    .ptr-arrow {
      transform: rotate(180deg) translate3d(0, 0, 0);
    }
  }
  .ptr-no-navbar,
  .ptr-with-navbar-large-transparent,
  .ptr-with-navbar-transparent {
    margin-top: calc(-1 * var(--f7-ptr-size));
    height: calc(100% + var(--f7-ptr-size));
    .ptr-preloader {
      margin-top: 0;
    }
  }
  .ptr-bottom {
    .ptr-preloader {
      margin-top: 0;
      margin-bottom: calc(-1 * var(--f7-ptr-size));
    }
    &.ptr-transitioning,
    &.ptr-refreshing {
      > * {
        transition-duration: 300ms;
        transition-property: transform;
      }
    }
    &.ptr-refreshing {
      transform: none;
      > * {
        transform: translate3d(0, calc(-1 * var(--f7-ptr-size)), 0);
      }
    }
    .ptr-arrow {
      transform: rotate(180deg) translate3d(0, 0, 0);
    }
    &.ptr-pull-up {
      .ptr-arrow {
        transform: rotate(0deg) translate3d(0, 0, 0);
      }
    }
  }
  .ptr-with-navbar-large-transparent,
  .ptr-with-navbar-transparent {
    .ptr-preloader {
      .preloader,
      .ptr-arrow {
        opacity: 0;
        transition-duration: 300ms;
        transition-property: transform, opacity;
      }
    }
    &.ptr-pull-down,
    &.ptr-pull-up,
    &.ptr-refreshing {
      .ptr-preloader {
        .preloader,
        .ptr-arrow {
          opacity: 1;
        }
      }
    }
    &.ptr-closing {
      .ptr-preloader {
        .preloader,
        .ptr-arrow {
          opacity: 0;
          transition-duration: 300ms;
        }
      }
    }
  }
}
