(function framework7ComponentLoader(t,e){void 0===e&&(e=!0);var r=t.$,o=t.utils,s=t.getDevice,a=t.getSupport,n=t.Class,l=(t.<PERSON>,t.ConstructorMethods),i=(t.Mo<PERSON>Methods,o.deleteProps),p=o.extend;class c extends n{constructor(t,e){super({},[t]);const o=this,n=s(),l=a(),i=r(e),p=i.find(".ptr-preloader");o.$el=i,o.el=i[0],o.app=t,o.bottom=o.$el.hasClass("ptr-bottom"),o.useModulesParams({});const c="md"===t.theme,d="ios"===t.theme,h="aurora"===t.theme;let f,u,g;o.done=function(){const t=c?p:i,e=s=>{r(s.target).closest(p).length||(i.removeClass("ptr-transitioning ptr-pull-up ptr-pull-down ptr-closing"),i.trigger("ptr:done"),o.emit("local::done ptrDone",i[0]),t.off("transitionend",e))};return t.on("transitionend",e),i.removeClass("ptr-refreshing").addClass("ptr-transitioning ptr-closing"),o},o.refresh=function(){return i.hasClass("ptr-refreshing")||(i.addClass("ptr-transitioning ptr-refreshing"),i.trigger("ptr:refresh",o.done),o.emit("local::refresh ptrRefresh",i[0],o.done)),o},o.mousewheel="true"===i.attr("data-ptr-mousewheel");const m={};let v,b,C,w,M,T,y,x,$,P,E,H=!1,D=!1,R=!1,S=0,X=!1;const Y=i.parents(".page");if((Y.find(".navbar").length>0||Y.parents(".view").children(".navbars").length>0)&&(X=!0),Y.hasClass("no-navbar")&&(X=!1),!o.bottom){const e=t.navbar.getElByPage(Y[0]);if(e){const t=r(e),o=t.hasClass("navbar-large-transparent")||t.hasClass("navbar-large")&&t.hasClass("navbar-transparent"),s=t.hasClass("navbar-transparent")&&!t.hasClass("navbar-large");o?i.addClass("ptr-with-navbar-large-transparent"):s&&i.addClass("ptr-with-navbar-transparent")}}function B(t){void 0===t&&(t=0);const e=p.find(".preloader-inner-line"),r=1/e.length;e.forEach(((e,o)=>{const s=(t-o*r)/r;e.style.opacity=.27*Math.max(Math.min(s,1),0)}))}function I(){p.find(".preloader-inner-line").css("opacity","")}function k(t){if(u){if("android"!==n.os)return;if("targetTouches"in t&&t.targetTouches.length>1)return}i.hasClass("ptr-refreshing")||r(t.target).closest(".sortable-handler, .ptr-ignore, .card-expandable.card-opened").length||(g=!1,x=!1,u=!0,v=void 0,M=void 0,"touchstart"===t.type&&(f=t.targetTouches[0].identifier),m.x="touchstart"===t.type?t.targetTouches[0].pageX:t.pageX,m.y="touchstart"===t.type?t.targetTouches[0].pageY:t.pageY)}function L(t){if(!u)return;let s,a,l;if("touchmove"===t.type){if(f&&t.touches)for(let e=0;e<t.touches.length;e+=1)t.touches[e].identifier===f&&(l=t.touches[e]);l||(l=t.targetTouches[0]),s=l.pageX,a=l.pageY}else s=t.pageX,a=t.pageY;if(!s||!a)return;if(void 0===v&&(v=!!(v||Math.abs(a-m.y)>Math.abs(s-m.x))),!v)return void(u=!1);if(w=i[0].scrollTop,!g){let s;if(i.removeClass("ptr-transitioning"),d&&B(0),$=i[0].scrollHeight,P=i[0].offsetHeight,o.bottom&&(E=$-P),w>$)return void(u=!1);const a=r(t.target).closest(".ptr-watch-scroll");if(a.length&&a.each((t=>{t!==e&&t.scrollHeight>t.offsetHeight&&"auto"===r(t).css("overflow")&&(!o.bottom&&t.scrollTop>0||o.bottom&&t.scrollTop<t.scrollHeight-t.offsetHeight)&&(s=!0)})),s)return void(u=!1);y&&(T=i.attr("data-ptr-distance"),T.indexOf("%")>=0&&(T=$*parseInt(T,10)/100)),S=i.hasClass("ptr-refreshing")?T:0,D=!($!==P&&"ios"===n.os&&!c),R=!1}g=!0,b=a-m.y,void 0===M&&(o.bottom?w!==E:0!==w)&&(M=!0);if(o.bottom?b<0&&w>=E||w>E:b>0&&w<=0||w<0){let e;"ios"===n.os&&parseInt(n.osVersion.split(".")[0],10)>7&&(o.bottom||0!==w||M||(D=!0),o.bottom&&w===E&&!M&&(D=!0)),D||!o.bottom||c||(i.css("-webkit-overflow-scrolling","auto"),i.scrollTop(E),R=!0),D||R?(t.cancelable&&t.preventDefault(),C=(o.bottom?-1*Math.abs(b)**.85:b**.85)+S,c?p.transform(`translate3d(0,${C}px,0)`).find(".ptr-arrow").transform(`rotate(${Math.abs(b)/66*180+100}deg)`):(o.bottom||d?i.children().transform(`translate3d(0,${C}px,0)`):i.transform(`translate3d(0,${C}px,0)`),d&&p.transform("translate3d(0,0px,0)"))):d&&!o.bottom&&p.transform(`translate3d(0,${w}px,0)`),d&&!H&&(e=D||R?Math.abs(b)**.85/T:Math.abs(b)/(2*T),B(e)),(D||R)&&Math.abs(b)**.85>T||!D&&Math.abs(b)>=2*T?(H=!0,i.addClass("ptr-pull-up").removeClass("ptr-pull-down"),I()):(H=!1,i.removeClass("ptr-pull-up").addClass("ptr-pull-down")),x||(i.trigger("ptr:pullstart"),o.emit("local::pullStart ptrPullStart",i[0]),x=!0),i.trigger("ptr:pullmove",{event:t,scrollTop:w,translate:C,touchesDiff:b}),o.emit("local::pullMove ptrPullMove",i[0],{event:t,scrollTop:w,translate:C,touchesDiff:b})}else x=!1,i.removeClass("ptr-pull-up ptr-pull-down"),H=!1}function O(t){return"touchend"===t.type&&t.changedTouches&&t.changedTouches.length>0&&f&&t.changedTouches[0].identifier!==f?(u=!1,v=!1,g=!1,void(f=null)):u&&g?(C&&(i.addClass("ptr-transitioning"),C=0),c?p.transform("").find(".ptr-arrow").transform(""):(p.transform(""),o.bottom||d?i.children().transform(""):i.transform("")),D||!o.bottom||c||i.css("-webkit-overflow-scrolling",""),H?(i.addClass("ptr-refreshing"),i.trigger("ptr:refresh",o.done),o.emit("local::refresh ptrRefresh",i[0],o.done)):i.removeClass("ptr-pull-down"),u=!1,g=!1,void(x&&(i.trigger("ptr:pullend"),o.emit("local::pullEnd ptrPullEnd",i[0])))):(u=!1,void(g=!1))}let V,j;X||o.bottom||i.addClass("ptr-no-navbar"),i.attr("data-ptr-distance")?y=!0:c?T=66:d?T=44:h&&(T=38);let q=!0,z=0;function A(){q=!0,j=!1,z=0,C&&(i.addClass("ptr-transitioning"),C=0),c?p.transform("").find(".ptr-arrow").transform(""):(p.transform(""),o.bottom?i.children().transform(""):i.transform("")),H?(i.addClass("ptr-refreshing"),i.trigger("ptr:refresh",o.done),o.emit("local::refresh ptrRefresh",i[0],o.done)):i.removeClass("ptr-pull-down"),x&&(i.trigger("ptr:pullend"),o.emit("local::pullEnd ptrPullEnd",i[0]))}function F(t){if(!q)return;const{deltaX:s,deltaY:a}=t;if(Math.abs(s)>Math.abs(a))return;if(i.hasClass("ptr-refreshing"))return;if(r(t.target).closest(".sortable-handler, .ptr-ignore, .card-expandable.card-opened").length)return;if(clearTimeout(V),w=i[0].scrollTop,!j){let s;if(i.removeClass("ptr-transitioning"),d&&B(0),$=i[0].scrollHeight,P=i[0].offsetHeight,o.bottom&&(E=$-P),w>$)return void(q=!1);const a=r(t.target).closest(".ptr-watch-scroll");if(a.length&&a.each((t=>{t!==e&&t.scrollHeight>t.offsetHeight&&"auto"===r(t).css("overflow")&&(!o.bottom&&t.scrollTop>0||o.bottom&&t.scrollTop<t.scrollHeight-t.offsetHeight)&&(s=!0)})),s)return void(q=!1);y&&(T=i.attr("data-ptr-distance"),T.indexOf("%")>=0&&(T=$*parseInt(T,10)/100))}g=!0,z-=a,b=z,void 0===M&&(o.bottom?w!==E:0!==w)&&(M=!0);if(o.bottom?b<0&&w>=E||w>E:b>0&&w<=0||w<0){let e;t.cancelable&&t.preventDefault(),C=b,Math.abs(C)>T&&(C=T+(Math.abs(C)-T)**.7,o.bottom&&(C=-C)),c?p.transform(`translate3d(0,${C}px,0)`).find(".ptr-arrow").transform(`rotate(${Math.abs(b)/66*180+100}deg)`):o.bottom?i.children().transform(`translate3d(0,${C}px,0)`):(i.transform(`translate3d(0,${C}px,0)`),d&&p.transform(`translate3d(0,${-C}px,0)`)),d&&!H&&(e=Math.abs(C)/T,B(e)),Math.abs(C)>T?(H=!0,i.addClass("ptr-pull-up").removeClass("ptr-pull-down"),I()):(H=!1,i.removeClass("ptr-pull-up").addClass("ptr-pull-down")),x||(i.trigger("ptr:pullstart"),o.emit("local::pullStart ptrPullStart",i[0]),x=!0),i.trigger("ptr:pullmove",{event:t,scrollTop:w,translate:C,touchesDiff:b}),o.emit("local::pullMove ptrPullMove",i[0],{event:t,scrollTop:w,translate:C,touchesDiff:b})}else x=!1,i.removeClass("ptr-pull-up ptr-pull-down"),H=!1;V=setTimeout(A,300)}return Y.length&&i.length?(i[0].f7PullToRefresh=o,o.attachEvents=function(){const e=!!l.passiveListener&&{passive:!0};i.on(t.touchEvents.start,k,e),t.on("touchmove:active",L),t.on("touchend:passive",O),o.mousewheel&&!o.bottom&&i.on("wheel",F)},o.detachEvents=function(){const e=!!l.passiveListener&&{passive:!0};i.off(t.touchEvents.start,k,e),t.off("touchmove:active",L),t.off("touchend:passive",O),o.mousewheel&&!o.bottom&&i.off("wheel",F)},o.useModules(),o.init(),o):o}init(){this.attachEvents()}destroy(){let t=this;t.emit("local::beforeDestroy ptrBeforeDestroy",t),t.$el.trigger("ptr:beforedestroy"),delete t.el.f7PullToRefresh,t.detachEvents(),i(t),t=null}}var d={name:"pullToRefresh",create(){const t=this;t.ptr=p(l({defaultSelector:".ptr-content",constructor:c,app:t,domProp:"f7PullToRefresh"}),{done(e){const r=t.ptr.get(e);if(r)return r.done()},refresh(e){const r=t.ptr.get(e);if(r)return r.refresh()}})},static:{PullToRefresh:c},on:{tabMounted(t){const e=this,o=r(t),s=o.find(".ptr-content");o.is(".ptr-content")&&s.add(o),s.each((t=>{e.ptr.create(t)}))},tabBeforeRemove(t){const e=r(t),o=this,s=e.find(".ptr-content");e.is(".ptr-content")&&s.add(e),s.each((t=>{o.ptr.destroy(t)}))},pageInit(t){const e=this;t.$el.find(".ptr-content").each((t=>{e.ptr.create(t)}))},pageBeforeRemove(t){const e=this;t.$el.find(".ptr-content").each((t=>{e.ptr.destroy(t)}))}}};if(e){if(t.prototype.modules&&t.prototype.modules[d.name])return;t.use(d),t.instance&&(t.instance.useModuleParams(d,t.instance.params),t.instance.useModule(d))}return d}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
