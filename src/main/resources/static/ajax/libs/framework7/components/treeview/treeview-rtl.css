:root{--f7-treeview-item-height:34px;--f7-treeview-item-padding-left:16px;--f7-treeview-item-padding-right:16px;--f7-treeview-toggle-size:24px;--f7-treeview-children-offset:29px;--f7-treeview-label-font-weight:400;--f7-treeview-label-text-color:inherit;--f7-treeview-icon-size:24px;--f7-treeview-toggle-color:rgba(0, 0, 0, 0.5);--f7-treeview-toggle-hover-bg-color:rgba(0, 0, 0, 0.1);--f7-treeview-toggle-pressed-bg-color:rgba(0, 0, 0, 0.15);--f7-treeview-icon-color:rgba(0, 0, 0, 0.5);--f7-treeview-selectable-hover-bg-color:rgba(0, 0, 0, 0.1);--f7-treeview-link-hover-bg-color:rgba(0, 0, 0, 0.1);--f7-treeview-link-pressed-bg-color:rgba(0, 0, 0, 0.15)}:root .dark,:root.dark{--f7-treeview-toggle-color:rgba(255, 255, 255, 0.5);--f7-treeview-toggle-hover-bg-color:rgba(255, 255, 255, 0.03);--f7-treeview-toggle-pressed-bg-color:rgba(255, 255, 255, 0.1);--f7-treeview-icon-color:rgba(255, 255, 255, 0.75);--f7-treeview-selectable-hover-bg-color:rgba(255, 255, 255, 0.03);--f7-treeview-link-hover-bg-color:rgba(255, 255, 255, 0.03);--f7-treeview-link-pressed-bg-color:rgba(255, 255, 255, 0.11)}.ios{--f7-treeview-label-font-size:17px}.md{--f7-treeview-label-font-size:16px}.aurora{--f7-treeview-label-font-size:16px}.treeview-item-root{padding-right:var(--f7-treeview-item-padding-left);padding-left:var(--f7-treeview-item-padding-right);min-height:var(--f7-treeview-item-height);display:flex;align-items:center;justify-content:flex-start}.treeview-item-content{display:flex;justify-content:flex-start;align-items:center}.treeview-item-content>.f7-icons,.treeview-item-content>.material-icons,.treeview-item-content>i{font-size:var(--f7-treeview-icon-size);color:var(--f7-treeview-icon-color)}.treeview-item-content:first-child{margin-right:calc(var(--f7-treeview-toggle-size) + 5px)}.treeview-item-content>*+*{margin-right:5px}.treeview-item-label{font-size:var(--f7-treeview-label-font-size);font-weight:var(--f7-treeview-label-font-weight);color:var(--f7-treeview-label-text-color)}.treeview-toggle{width:var(--f7-treeview-toggle-size);height:var(--f7-treeview-toggle-size);cursor:pointer;border-radius:4px;background-color:rgba(0,0,0,0);transition-duration:.2s;position:relative;margin-left:5px}.treeview-toggle.active-state{background-color:var(--f7-treeview-toggle-pressed-bg-color)}.treeview-toggle:after{transition-duration:.2s;content:'';position:absolute;left:50%;top:50%;width:0;height:0;border-top:5px solid transparent;border-bottom:5px solid transparent;transform:translate(-50%,-50%);border-right:6px solid var(--f7-treeview-toggle-color)}.treeview-toggle-hidden{opacity:0;pointer-events:none;visibility:hidden}.treeview-preloader{--f7-preloader-size:var(--f7-treeview-toggle-size);margin-left:calc(-1 * var(--f7-treeview-toggle-size))}.treeview-item-children{display:none}.treeview-item-opened>.treeview-item-children{display:block}.treeview-item-opened>.treeview-item-root .treeview-toggle:after{transform:translate(-50%,-50%) rotate(-90deg)}a.treeview-item-root{color:var(--f7-treeview-label-text-color)}.treeview-item-selectable.treeview-item-root,.treeview-item-selectable>.treeview-item-root{cursor:pointer;transition-duration:150ms}a.treeview-item-root{transition-duration:150ms}a.treeview-item-root.active-state{background:var(--f7-treeview-link-pressed-bg-color)}.treeview-item-toggle.treeview-item-root,.treeview-item-toggle>.treeview-item-root{cursor:pointer}.treeview-item-selected.treeview-item-root,.treeview-item-selected>.treeview-item-root{background:var(--f7-treeview-selectable-selected-bg-color,rgba(var(--f7-theme-color-rgb),.2))}.treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 1)}.treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 2)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 3)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 4)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 5)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 6)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 7)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 8)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 9)}.treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item .treeview-item-root{padding-right:calc(var(--f7-treeview-item-padding-left) + var(--f7-treeview-children-offset) * 10)}.aurora .treeview-toggle:hover{background-color:var(--f7-treeview-toggle-hover-bg-color)}.aurora .treeview-toggle.active-state{background-color:var(--f7-treeview-toggle-pressed-bg-color)}.aurora .treeview-item-selectable.treeview-item-root:hover,.aurora .treeview-item-selectable>.treeview-item-root:hover{background:var(--f7-treeview-selectable-hover-bg-color)}.aurora a.treeview-item-root:hover{background:var(--f7-treeview-link-hover-bg-color)}.aurora a.treeview-item-root.active-state{background:var(--f7-treeview-link-pressed-bg-color)}.aurora .treeview-item-selected.treeview-item-root:hover,.aurora .treeview-item-selected>.treeview-item-root:hover{background:var(--f7-treeview-selectable-selected-bg-color,rgba(var(--f7-theme-color-rgb),.2))}