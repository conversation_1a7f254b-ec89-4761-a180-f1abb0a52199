(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var i=e.$,n=e.utils,o=(e.getDevice,e.getSupport,e.<PERSON>,e.<PERSON>,e.Constructor<PERSON>,e.<PERSON>,n.bindMethods);const l={handle(e,t){const n=this,o=i(e),l=o[0].scrollTop,r=o[0].scrollHeight,s=o[0].offsetHeight;let c=o[0].getAttribute("data-infinite-distance");const a=o.find(".virtual-list");let f;const d=o.hasClass("infinite-scroll-top");if(c||(c=50),"string"==typeof c&&c.indexOf("%")>=0&&(c=parseInt(c,10)/100*s),c>s&&(c=s),d)l<c&&(o.trigger("infinite",t),n.emit("infinite",o[0],t));else if(l+s>=r-c){if(a.length>0&&(f=a.eq(-1)[0].f7VirtualList,f&&!f.reachEnd&&!f.params.updatableScroll))return;o.trigger("infinite",t),n.emit("infinite",o[0],t)}},create(e){const t=i(e),n=this;function o(e){n.infiniteScroll.handle(this,e)}t.each((e=>{e.f7InfiniteScrollHandler=o,e.addEventListener("scroll",e.f7InfiniteScrollHandler)}))},destroy(e){i(e).each((e=>{e.removeEventListener("scroll",e.f7InfiniteScrollHandler),delete e.f7InfiniteScrollHandler}))}};var r={name:"infiniteScroll",create(){o(this,{infiniteScroll:l})},on:{tabMounted(e){const t=this,n=i(e),o=n.find(".infinite-scroll-content");n.is(".infinite-scroll-content")&&o.add(n),o.each((e=>{t.infiniteScroll.create(e)}))},tabBeforeRemove(e){const t=i(e),n=this,o=t.find(".infinite-scroll-content");t.is(".infinite-scroll-content")&&o.add(t),o.each((e=>{n.infiniteScroll.destroy(e)}))},pageInit(e){const t=this;e.$el.find(".infinite-scroll-content").each((e=>{t.infiniteScroll.create(e)}))},pageBeforeRemove(e){const t=this;e.$el.find(".infinite-scroll-content").each((e=>{t.infiniteScroll.destroy(e)}))}}};if(t){if(e.prototype.modules&&e.prototype.modules[r.name])return;e.use(r),e.instance&&(e.instance.useModuleParams(r,e.instance.params),e.instance.useModule(r))}return r}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
