(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var a=e.$,n=e.utils,r=(e.getDevice,e.getSupport,e.Class),l=(e.<PERSON>,e.ConstructorMethods),u=(e.<PERSON>ethods,n.extend),p=n.deleteProps;class s extends r{constructor(e,t){super(t,[e]);const n=this,r={el:null,inputEl:null,valueEl:null,value:0,formatValue:null,step:1,min:0,max:100,watchInput:!0,autorepeat:!1,autorepeatDynamic:!1,wraps:!1,manualInputMode:!1,decimalPoint:4,buttonsEndInputMode:!0};n.useModulesParams(r),n.params=u(r,t),n.params.value<n.params.min&&(n.params.value=n.params.min),n.params.value>n.params.max&&(n.params.value=n.params.max);const l=n.params.el;if(!l)return n;const p=a(l);if(0===p.length)return n;if(p[0].f7Stepper)return p[0].f7Stepper;let s,i;if(n.params.inputEl?s=a(n.params.inputEl):p.find(".stepper-input-wrap").find("input, textarea").length&&(s=p.find(".stepper-input-wrap").find("input, textarea").eq(0)),s&&s.length){"step min max".split(" ").forEach((e=>{!t[e]&&s.attr(e)&&(n.params[e]=parseFloat(s.attr(e)))}));const e=parseInt(n.params.decimalPoint,10);Number.isNaN(e)?n.params.decimalPoint=0:n.params.decimalPoint=e;const a=parseFloat(s.val());void 0!==t.value||Number.isNaN(a)||!a&&0!==a||(n.params.value=a)}n.params.valueEl?i=a(n.params.valueEl):p.find(".stepper-value").length&&(i=p.find(".stepper-value").eq(0));const o=p.find(".stepper-button-plus"),c=p.find(".stepper-button-minus"),{step:m,min:d,max:f,value:v,decimalPoint:h}=n.params;u(n,{app:e,$el:p,el:p[0],$buttonPlusEl:o,buttonPlusEl:o[0],$buttonMinusEl:c,buttonMinusEl:c[0],$inputEl:s,inputEl:s?s[0]:void 0,$valueEl:i,valueEl:i?i[0]:void 0,step:m,min:d,max:f,value:v,decimalPoint:h,typeModeChanged:!1}),p[0].f7Stepper=n;const g={};let E,y,$,M,b,x=null,S=!1,I=!1;function V(e,t,a,n,r,l){clearTimeout(b),b=setTimeout((()=>{1===e&&($=!0,S=!0),clearInterval(M),l(),M=setInterval((()=>{l()}),r),e<t&&V(e+1,t,a,n,r/2,l)}),1===e?a:n)}function N(e){if(E)return;if(I)return;if(a(e.target).closest(o).length?x="increment":a(e.target).closest(c).length&&(x="decrement"),!x)return;g.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,g.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,E=!0,y=void 0;V(1,n.params.autorepeatDynamic?4:1,500,1e3,300,(()=>{n[x]()}))}function P(e){if(!E)return;if(I)return;const t="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,a="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY;void 0!==y||S||(y=!!(y||Math.abs(a-g.y)>Math.abs(t-g.x)));const n=((t-g.x)**2+(a-g.y)**2)**.5;(y||n>20)&&(E=!1,clearTimeout(b),clearInterval(M))}function T(){clearTimeout(b),clearInterval(M),x=null,S=!1,E=!1}function k(){I?n.params.buttonsEndInputMode&&(I=!1,n.endTypeMode(!0)):$?$=!1:n.decrement(!0)}function C(){I?n.params.buttonsEndInputMode&&(I=!1,n.endTypeMode(!0)):$?$=!1:n.increment(!0)}function w(e){!e.target.readOnly&&n.params.manualInputMode&&(I=!0,"number"==typeof e.target.selectionStart&&(e.target.selectionStart=e.target.value.length,e.target.selectionEnd=e.target.value.length))}function F(e){13!==e.keyCode&&13!==e.which||(e.preventDefault(),I=!1,n.endTypeMode())}function O(){I=!1,n.endTypeMode(!0)}function B(e){I?n.typeValue(e.target.value):e.detail&&e.detail.sentByF7Stepper||n.setValue(e.target.value,!0)}return n.attachEvents=function(){c.on("click",k),o.on("click",C),n.params.watchInput&&s&&s.length&&(s.on("input",B),s.on("click",w),s.on("blur",O),s.on("keyup",F)),n.params.autorepeat&&(e.on("touchstart:passive",N),e.on("touchmove:active",P),e.on("touchend:passive",T))},n.detachEvents=function(){c.off("click",k),o.off("click",C),n.params.watchInput&&s&&s.length&&(s.off("input",B),s.off("click",w),s.off("blur",O),s.off("keyup",F))},n.useModules(),n.init(),n}minus(){return this.decrement()}plus(){return this.increment()}decrement(){const e=this;return e.setValue(e.value-e.step,!1,!0)}increment(){const e=this;return e.setValue(e.value+e.step,!1,!0)}setValue(e,t,a){const n=this,{step:r,min:l,max:u}=n,p=n.value;let s=Math.round(e/r)*r;n.params.wraps&&a?(s>u&&(s=l),s<l&&(s=u)):s=Math.max(Math.min(s,u),l),Number.isNaN(s)&&(s=p),n.value=s;if(!(p!==s)&&!t)return n;n.$el.trigger("stepper:change",n.value);const i=n.formatValue(n.value);return n.$inputEl&&n.$inputEl.length&&(n.$inputEl.val(i),n.$inputEl.trigger("input change",{sentByF7Stepper:!0})),n.$valueEl&&n.$valueEl.length&&n.$valueEl.html(i),n.emit("local::change stepperChange",n,n.value),n}endTypeMode(e){const t=this,{min:a,max:n}=t;let r=parseFloat(t.value);if(Number.isNaN(r)&&(r=0),r=Math.max(Math.min(r,n),a),t.value=r,!t.typeModeChanged)return t.$inputEl&&t.$inputEl.length&&!e&&t.$inputEl.blur(),t;t.typeModeChanged=!1,t.$el.trigger("stepper:change",t.value);const l=t.formatValue(t.value);return t.$inputEl&&t.$inputEl.length&&(t.$inputEl.val(l),t.$inputEl.trigger("input change",{sentByF7Stepper:!0}),e||t.$inputEl.blur()),t.$valueEl&&t.$valueEl.length&&t.$valueEl.html(l),t.emit("local::change stepperChange",t,t.value),t}typeValue(e){const t=this;t.typeModeChanged=!0;let a=String(e);if(1===a.length&&"-"===a)return t;if(a.lastIndexOf(".")+1!==a.length&&a.lastIndexOf(",")+1!==a.length){let e=parseFloat(a.replace(",","."));if(0===e)return t.value=a.replace(",","."),t.$inputEl.val(t.value),t;if(Number.isNaN(e))return t.value=0,t.$inputEl.val(t.value),t;const n=10**t.params.decimalPoint;return e=Math.round(e*n).toFixed(t.params.decimalPoint+1)/n,t.value=parseFloat(String(e).replace(",",".")),t.$inputEl.val(t.value),t}return a.lastIndexOf(".")!==a.indexOf(".")||a.lastIndexOf(",")!==a.indexOf(",")?(a=a.slice(0,-1),t.value=a,t.$inputEl.val(t.value),t):(t.value=a,t.$inputEl.val(a),t)}getValue(){return this.value}formatValue(e){const t=this;return t.params.formatValue?t.params.formatValue.call(t,e):e}init(){const e=this;if(e.attachEvents(),e.$valueEl&&e.$valueEl.length){const t=e.formatValue(e.value);e.$valueEl.html(t)}return e}destroy(){let e=this;e.$el.trigger("stepper:beforedestroy"),e.emit("local::beforeDestroy stepperBeforeDestroy",e),delete e.$el[0].f7Stepper,e.detachEvents(),p(e),e=null}}var i={name:"stepper",create(){const e=this;e.stepper=u(l({defaultSelector:".stepper",constructor:s,app:e,domProp:"f7Stepper"}),{getValue(t){void 0===t&&(t=".stepper");const a=e.stepper.get(t);if(a)return a.getValue()},setValue(t,a){void 0===t&&(t=".stepper");const n=e.stepper.get(t);if(n)return n.setValue(a)}})},static:{Stepper:s},on:{tabMounted(e){const t=this;a(e).find(".stepper-init").each((e=>{const n=a(e).dataset();t.stepper.create(u({el:e},n||{}))}))},tabBeforeRemove(e){a(e).find(".stepper-init").each((e=>{e.f7Stepper&&e.f7Stepper.destroy()}))},pageInit(e){const t=this;e.$el.find(".stepper-init").each((e=>{const n=a(e).dataset();t.stepper.create(u({el:e},n||{}))}))},pageBeforeRemove(e){e.$el.find(".stepper-init").each((e=>{e.f7Stepper&&e.f7Stepper.destroy()}))}},vnode:{"stepper-init":{insert(e){const t=e.elm,n=a(t).dataset();this.stepper.create(u({el:t},n||{}))},destroy(e){const t=e.elm;t.f7Stepper&&t.f7Stepper.destroy()}}}};if(t){if(e.prototype.modules&&e.prototype.modules[i.name])return;e.use(i),e.instance&&(e.instance.useModuleParams(i,e.instance.params),e.instance.useModule(i))}return i}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
