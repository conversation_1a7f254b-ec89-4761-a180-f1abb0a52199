(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var a=e.$,n=e.utils,r=e.getDevice,o=e.getSupport,l=e.Class,s=(e.<PERSON>dal,e.ConstructorMethods),i=(e.ModalMethods,n.extend),c=n.nextTick,d=n.deleteProps;class m extends l{constructor(e,t){void 0===t&&(t={}),super(t,[e]);const n=this;let r,l;if(n.params=i({},e.params.calendar,t),n.params.containerEl&&(r=a(n.params.containerEl),0===r.length))return n;n.params.inputEl&&(l=a(n.params.inputEl));const s="horizontal"===n.params.direction;let c=1;s&&(c=e.rtl?-1:1),i(n,{app:e,$containerEl:r,containerEl:r&&r[0],inline:r&&r.length>0,$inputEl:l,inputEl:l&&l[0],initialized:!1,opened:!1,url:n.params.url,isHorizontal:s,inverter:c,animating:!1,allowTouchMove:!0,hasTimePicker:n.params.timePicker&&!n.params.rangePicker&&!n.params.multiple}),n.dayFormatter=e=>new Intl.DateTimeFormat(n.params.locale,{day:"numeric"}).format(e).replace(/日/,""),n.monthFormatter=e=>new Intl.DateTimeFormat(n.params.locale,{month:"long"}).format(e),n.yearFormatter=e=>new Intl.DateTimeFormat(n.params.locale,{year:"numeric"}).format(e),n.timeSelectorFormatter=e=>new Intl.DateTimeFormat(n.params.locale,n.params.timePickerFormat).format(e);const d=n.timeSelectorFormatter(new Date).toLowerCase();n.is12HoursFormat=d.indexOf("pm")>=0||d.indexOf("am")>=0;let{monthNames:m,monthNamesShort:h,dayNames:p,dayNamesShort:u}=n.params;const{monthNamesIntl:v,monthNamesShortIntl:g,dayNamesIntl:f,dayNamesShortIntl:k}=n.getIntlNames();function y(){n.open()}function $(e){e.preventDefault()}function P(){n.setValue([]),n.opened&&n.update()}function M(e){const t=a(e.target);!n.destroyed&&n.params&&(n.isPopover()||n.opened&&!n.closing&&(t.closest('[class*="backdrop"]').length||n.monthPickerPopover||n.yearPickerPopover||n.timePickerPopover||(l&&l.length>0?t[0]!==l[0]&&0===t.closest(".sheet-modal, .calendar-modal").length&&n.close():0===a(e.target).closest(".sheet-modal, .calendar-modal").length&&n.close())))}return"auto"===m&&(m=v),"auto"===h&&(h=g),"auto"===p&&(p=f),"auto"===u&&(u=k),i(n,{monthNames:m,monthNamesShort:h,dayNames:p,dayNamesShort:u}),i(n,{attachInputEvents(){n.$inputEl.on("click",y),n.$inputEl.on("input:clear",P),n.params.inputReadOnly&&(n.$inputEl.on("focus mousedown",$),n.$inputEl[0]&&(n.$inputEl[0].f7ValidateReadonly=!0))},detachInputEvents(){n.$inputEl.off("click",y),n.$inputEl.off("input:clear",P),n.params.inputReadOnly&&(n.$inputEl.off("focus mousedown",$),n.$inputEl[0]&&delete n.$inputEl[0].f7ValidateReadonly)},attachHtmlEvents(){e.on("click",M)},detachHtmlEvents(){e.off("click",M)}}),n.attachCalendarEvents=function(){let t,r,l,s,i,c,d,m,h,p,u,v,g,f,k=!0;const{$el:y,$wrapperEl:$}=n;function P(e){r||t||(t=!0,l="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,i=l,s="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,c=s,d=(new Date).getTime(),v=0,k=!0,f=void 0,h=n.monthsTranslate)}function M(e){if(!t)return;const{isHorizontal:a}=n;i="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,c="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,void 0===f&&(f=!!(f||Math.abs(c-s)>Math.abs(i-l))),a&&f||!n.allowTouchMove?t=!1:(e.preventDefault(),n.animating?t=!1:(k=!1,r||(r=!0,p=$[0].offsetWidth,u=$[0].offsetHeight,$.transition(0)),g=a?i-l:c-s,v=g/(a?p:u),h=100*(n.monthsTranslate*n.inverter+v),$.transform(`translate3d(${a?h:0}%, ${a?0:h}%, 0)`)))}function D(){if(!t||!r)return t=!1,void(r=!1);t=!1,r=!1,m=(new Date).getTime(),m-d<300?Math.abs(g)<10?n.resetMonth():g>=10?e.rtl?n.nextMonth():n.prevMonth():e.rtl?n.prevMonth():n.nextMonth():v<=-.5?e.rtl?n.prevMonth():n.nextMonth():v>=.5?e.rtl?n.nextMonth():n.prevMonth():n.resetMonth(),setTimeout((()=>{k=!0}),100)}function w(e){if(!k)return;let t=a(e.target).parents(".calendar-day");if(0===t.length&&a(e.target).hasClass("calendar-day")&&(t=a(e.target)),0===t.length)return;if(t.hasClass("calendar-day-disabled"))return;n.params.rangePicker||(t.hasClass("calendar-day-next")&&n.nextMonth(),t.hasClass("calendar-day-prev")&&n.prevMonth());const r=parseInt(t.attr("data-year"),10),o=parseInt(t.attr("data-month"),10),l=parseInt(t.attr("data-day"),10);if(n.emit("local::dayClick calendarDayClick",n,t[0],r,o,l),!t.hasClass("calendar-day-selected")||n.params.multiple||n.params.rangePicker){const e=new Date(r,o,l,0,0,0);n.hasTimePicker&&(n.value&&n.value[0]?e.setHours(n.value[0].getHours(),n.value[0].getMinutes()):e.setHours((new Date).getHours(),(new Date).getMinutes())),n.addValue(e)}n.params.closeOnSelect&&(n.params.rangePicker&&2===n.value.length||!n.params.rangePicker)&&n.close()}function T(){n.nextMonth()}function x(){n.prevMonth()}function C(){n.nextYear()}function E(){n.prevYear()}function I(){n.openMonthPicker()}function b(){n.openYearPicker()}function Y(){n.openTimePicker()}const F=!("touchstart"!==e.touchEvents.start||!o().passiveListener)&&{passive:!0,capture:!1};y.find(".calendar-prev-month-button").on("click",x),y.find(".calendar-next-month-button").on("click",T),y.find(".calendar-prev-year-button").on("click",E),y.find(".calendar-next-year-button").on("click",C),n.params.monthPicker&&y.find(".current-month-value").on("click",I),n.params.yearPicker&&y.find(".current-year-value").on("click",b),n.hasTimePicker&&y.find(".calendar-time-selector a").on("click",Y),$.on("click",w),n.params.touchMove&&($.on(e.touchEvents.start,P,F),e.on("touchmove:active",M),e.on("touchend:passive",D)),n.detachCalendarEvents=function(){y.find(".calendar-prev-month-button").off("click",x),y.find(".calendar-next-month-button").off("click",T),y.find(".calendar-prev-year-button").off("click",E),y.find(".calendar-next-year-button").off("click",C),n.params.monthPicker&&y.find(".current-month-value").off("click",I),n.params.yearPicker&&y.find(".current-year-value").off("click",b),n.hasTimePicker&&y.find(".calendar-time-selector a").off("click",Y),$.off("click",w),n.params.touchMove&&($.off(e.touchEvents.start,P,F),e.off("touchmove:active",M),e.off("touchend:passive",D))}},n.init(),n}get view(){const{$inputEl:e,app:t,params:a}=this;let n;return a.view?n=a.view:e&&(n=e.parents(".view").length&&e.parents(".view")[0].f7View),n||(n=t.views.main),n}getIntlNames(){const e=this,t=e.params.locale,a=[],n=[],r=[],o=[],l=new Intl.DateTimeFormat(t,{month:"long"}),s=new Intl.DateTimeFormat(t,{month:"short"}),i=new Intl.DateTimeFormat(t,{weekday:"long"}),c=new Intl.DateTimeFormat(t,{weekday:"short"});let d,m,h;for(let t=0;t<24;t+=1){const r=(new Date).setMonth(t,1),o=e.yearFormatter(r);d&&o!==d&&(m&&(h=!0),m=!0,d=o),d||(d=o),m&&d===o&&!h&&(a.push(l.format(r)),n.push(s.format(r)))}const p=(new Date).getDay();for(let e=0;e<7;e+=1){const t=(new Date).getTime()+24*(e-p)*60*60*1e3;r.push(i.format(t)),o.push(c.format(t))}return{monthNamesIntl:a,monthNamesShortIntl:n,dayNamesIntl:r,dayNamesShortIntl:o}}normalizeDate(e){const t=new Date(e);return this.hasTimePicker?new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes()):new Date(t.getFullYear(),t.getMonth(),t.getDate())}normalizeValues(e){const t=this;let a=[];return e&&Array.isArray(e)&&(a=e.map((e=>t.normalizeDate(e)))),a}initInput(){const e=this;e.$inputEl&&e.params.inputReadOnly&&e.$inputEl.prop("readOnly",!0)}isPopover(){const e=this,{app:t,modal:a,params:n}=e,o=r();if("sheet"===n.openIn)return!1;if(a&&"popover"!==a.type)return!1;if(!e.inline&&e.inputEl){if("popover"===n.openIn)return!0;if(o.ios)return!!o.ipad;if(t.width>=768)return!0;if(o.desktop&&"aurora"===t.theme)return!0}return!1}formatDate(e){const t=this,a=new Date(e),n=a.getFullYear(),r=a.getMonth(),o=r+1,l=a.getDate(),s=a.getDay(),{monthNames:i,monthNamesShort:c,dayNames:d,dayNamesShort:m}=t,{dateFormat:h,locale:p}=t.params;function u(e){return e<10?`0${e}`:e}if("string"==typeof h){const e={yyyy:n,yy:String(n).substring(2),mm:u(o),m:o,MM:i[r],M:c[r],dd:u(l),d:l,DD:d[s],D:m[s]};if(t.params.timePicker){const t=a.getHours(),n=a.getMinutes(),r=a.getSeconds();let o=t;t>12&&(o=t-12),0===t&&(o=12);const l=t>=12&&0!==t?"pm":"am";Object.assign(e,{HH:u(t),H:t,hh:u(o),h:o,ss:u(r),s:r,":mm":u(n),":m":n,a:l,A:l.toUpperCase()})}const p=new RegExp(Object.keys(e).map((e=>`(${e})`)).join("|"),"g");return h.replace(p,(t=>t in e?e[t]:t))}if("function"==typeof h)return h(a);return new Intl.DateTimeFormat(p,h).format(a)}formatValue(){const e=this,{value:t}=e;return e.params.formatValue?e.params.formatValue.call(e,t):t.map((t=>e.formatDate(t))).join(e.params.rangePicker?" - ":", ")}addValue(e){const t=this,{multiple:a,rangePicker:n,rangePickerMinDays:r,rangePickerMaxDays:o}=t.params;if(a){let a;t.value||(t.value=[]);for(let n=0;n<t.value.length;n+=1)new Date(e).getTime()===new Date(t.value[n]).getTime()&&(a=n);void 0===a?t.value.push(e):t.value.splice(a,1),t.updateValue()}else n?(t.value||(t.value=[]),2!==t.value.length&&0!==t.value.length||(t.value=[]),0===t.value.length||Math.abs(t.value[0].getTime()-e.getTime())>=60*(r-1)*60*24*1e3&&(0===o||Math.abs(t.value[0].getTime()-e.getTime())<=60*(o-1)*60*24*1e3)?t.value.push(e):t.value=[],t.value.sort(((e,t)=>e-t)),t.updateValue()):(t.value=[e],t.updateValue())}setValue(e){const t=this,a=t.value;if(Array.isArray(a)&&Array.isArray(e)&&a.length===e.length){let t=!0;if(a.forEach(((a,n)=>{a!==e[n]&&(t=!1)})),t)return}t.value=e,t.updateValue()}getValue(){return this.value}updateValue(e){const t=this,{$el:a,$wrapperEl:n,$inputEl:r,value:o,params:l}=t;let s;if(a&&a.length>0){let e;if(n.find(".calendar-day-selected").removeClass("calendar-day-selected calendar-day-selected-range calendar-day-selected-left calendar-day-selected-right"),l.rangePicker&&2===o.length){const t=new Date(o[0]).getTime(),a=new Date(o[1]).getTime();for(s=t;s<=a;s+=864e5){e=new Date(s);let r="calendar-day-selected";t!==a&&(s!==t&&s!==a&&(r+=" calendar-day-selected-range"),s===t&&(r+=" calendar-day-selected-left"),s===a&&(r+=" calendar-day-selected-right")),n.find(`.calendar-day[data-date="${e.getFullYear()}-${e.getMonth()}-${e.getDate()}"]`).addClass(r)}e=new Date(t),n.find(`.calendar-day[data-date="${e.getFullYear()}-${e.getMonth()}-${e.getDate()}"]`).removeClass("calendar-day-selected-range").addClass("calendar-day-selected calendar-day-selected-left"),e=new Date(a),n.find(`.calendar-day[data-date="${e.getFullYear()}-${e.getMonth()}-${e.getDate()}"]`).removeClass("calendar-day-selected-range").addClass("calendar-day-selected calendar-day-selected-right")}else for(s=0;s<t.value.length;s+=1)e=new Date(o[s]),n.find(`.calendar-day[data-date="${e.getFullYear()}-${e.getMonth()}-${e.getDate()}"]`).addClass("calendar-day-selected")}if(e||t.emit("local::change calendarChange",t,o),a&&a.length>0&&t.hasTimePicker&&a.find(".calendar-time-selector a").text(o&&o.length?t.timeSelectorFormatter(o[0]):t.params.timePickerPlaceholder),r&&r.length||l.header){const n=t.formatValue(o);l.header&&a&&a.length&&a.find(".calendar-selected-date").text(n),r&&r.length&&!e&&(r.val(n),r.trigger("change"))}}updateCurrentMonthYear(e){const t=this,{$months:a,$el:n,monthNames:r}=t;let o,l;void 0===e?(t.currentMonth=parseInt(a.eq(1).attr("data-month"),10),t.currentYear=parseInt(a.eq(1).attr("data-year"),10),o=a.eq(1).attr("data-locale-month"),l=a.eq(1).attr("data-locale-year")):(t.currentMonth=parseInt(a.eq("next"===e?a.length-1:0).attr("data-month"),10),t.currentYear=parseInt(a.eq("next"===e?a.length-1:0).attr("data-year"),10),o=a.eq("next"===e?a.length-1:0).attr("data-locale-month"),l=a.eq("next"===e?a.length-1:0).attr("data-locale-year")),n.find(".current-month-value").text(r[o]),n.find(".current-year-value").text(l)}update(){const e=this,{currentYear:t,currentMonth:a,$wrapperEl:n}=e,r=new Date(t,a),o=e.renderMonth(r,"prev"),l=e.renderMonth(r),s=e.renderMonth(r,"next");n.transition(0).html(`${o}${l}${s}`).transform("translate3d(0,0,0)"),e.$months=n.find(".calendar-month"),e.monthsTranslate=0,e.setMonthsTranslate(),e.$months.each((t=>{e.emit("local::monthAdd calendarMonthAdd",t)}))}onMonthChangeStart(e){const t=this,{$months:a,currentYear:n,currentMonth:r}=t;t.updateCurrentMonthYear(e),a.removeClass("calendar-month-current calendar-month-prev calendar-month-next");const o="next"===e?a.length-1:0;a.eq(o).addClass("calendar-month-current"),a.eq("next"===e?o-1:o+1).addClass("next"===e?"calendar-month-prev":"calendar-month-next"),t.emit("local::monthYearChangeStart calendarMonthYearChangeStart",t,n,r)}onMonthChangeEnd(e,t){const a=this,{currentYear:n,currentMonth:r,$wrapperEl:o,monthsTranslate:l}=a;let s,i,c;a.animating=!1,o.find(".calendar-month:not(.calendar-month-prev):not(.calendar-month-current):not(.calendar-month-next)").remove(),void 0===e&&(e="next",t=!0),t?(o.find(".calendar-month-next, .calendar-month-prev").remove(),i=a.renderMonth(new Date(n,r),"prev"),s=a.renderMonth(new Date(n,r),"next")):c=a.renderMonth(new Date(n,r),e),("next"===e||t)&&o.append(c||s),("prev"===e||t)&&o.prepend(c||i);const d=o.find(".calendar-month");a.$months=d,a.setMonthsTranslate(l),a.emit("local::monthAdd calendarMonthAdd",a,"next"===e?d.eq(d.length-1)[0]:d.eq(0)[0]),a.emit("local::monthYearChangeEnd calendarMonthYearChangeEnd",a,n,r)}setMonthsTranslate(e){const t=this,{$months:a,isHorizontal:n,inverter:r}=t;e=e||t.monthsTranslate||0,void 0===t.monthsTranslate&&(t.monthsTranslate=e),a.removeClass("calendar-month-current calendar-month-prev calendar-month-next");const o=100*-(e+1)*r,l=100*-e*r,s=100*-(e-1)*r;a.eq(0).transform(`translate3d(${n?o:0}%, ${n?0:o}%, 0)`).addClass("calendar-month-prev"),a.eq(1).transform(`translate3d(${n?l:0}%, ${n?0:l}%, 0)`).addClass("calendar-month-current"),a.eq(2).transform(`translate3d(${n?s:0}%, ${n?0:s}%, 0)`).addClass("calendar-month-next")}nextMonth(e){const t=this,{params:n,$wrapperEl:r,inverter:o,isHorizontal:l}=t;void 0!==e&&"object"!=typeof e||(e="",n.animate||(e=0));const s=parseInt(t.$months.eq(t.$months.length-1).attr("data-month"),10),i=parseInt(t.$months.eq(t.$months.length-1).attr("data-year"),10),c=new Date(i,s).getTime(),d=!t.animating;if(n.maxDate&&c>new Date(n.maxDate).getTime())return void t.resetMonth();if(t.monthsTranslate-=1,s===t.currentMonth){const e=100*-t.monthsTranslate*o,n=a(t.renderMonth(c,"next")).transform(`translate3d(${l?e:0}%, ${l?0:e}%, 0)`).addClass("calendar-month-next");r.append(n[0]),t.$months=r.find(".calendar-month"),t.emit("local::monthAdd calendarMonthAdd",t.$months.eq(t.$months.length-1)[0])}t.animating=!0,t.onMonthChangeStart("next");const m=100*t.monthsTranslate*o;r.transition(e).transform(`translate3d(${l?m:0}%, ${l?0:m}%, 0)`),d&&r.transitionEnd((()=>{t.onMonthChangeEnd("next")})),n.animate||t.onMonthChangeEnd("next")}prevMonth(e){const t=this,{params:n,$wrapperEl:r,inverter:o,isHorizontal:l}=t;void 0!==e&&"object"!=typeof e||(e="",n.animate||(e=0));const s=parseInt(t.$months.eq(0).attr("data-month"),10),i=parseInt(t.$months.eq(0).attr("data-year"),10),c=new Date(i,s+1,-1).getTime(),d=!t.animating;if(n.minDate){let e=new Date(n.minDate);if(e=new Date(e.getFullYear(),e.getMonth(),1),c<e.getTime())return void t.resetMonth()}if(t.monthsTranslate+=1,s===t.currentMonth){const e=100*-t.monthsTranslate*o,n=a(t.renderMonth(c,"prev")).transform(`translate3d(${l?e:0}%, ${l?0:e}%, 0)`).addClass("calendar-month-prev");r.prepend(n[0]),t.$months=r.find(".calendar-month"),t.emit("local::monthAdd calendarMonthAdd",t.$months.eq(0)[0])}t.animating=!0,t.onMonthChangeStart("prev");const m=100*t.monthsTranslate*o;r.transition(e).transform(`translate3d(${l?m:0}%, ${l?0:m}%, 0)`),d&&r.transitionEnd((()=>{t.onMonthChangeEnd("prev")})),n.animate||t.onMonthChangeEnd("prev")}resetMonth(e){void 0===e&&(e="");const{$wrapperEl:t,inverter:a,isHorizontal:n,monthsTranslate:r}=this,o=100*r*a;t.transition(e).transform(`translate3d(${n?o:0}%, ${n?0:o}%, 0)`)}setYearMonth(e,t,a){const n=this,{params:r,isHorizontal:o,$wrapperEl:l,inverter:s}=n;let i;if(void 0===e&&(e=n.currentYear),void 0===t&&(t=n.currentMonth),void 0!==a&&"object"!=typeof a||(a="",r.animate||(a=0)),i=e<n.currentYear?new Date(e,t+1,-1).getTime():new Date(e,t).getTime(),r.maxDate&&i>new Date(r.maxDate).getTime())return!1;if(r.minDate){let e=new Date(r.minDate);if(e=new Date(e.getFullYear(),e.getMonth(),1),i<e.getTime())return!1}const c=new Date(n.currentYear,n.currentMonth).getTime(),d=i>c?"next":"prev",m=n.renderMonth(new Date(e,t));n.monthsTranslate=n.monthsTranslate||0;const h=n.monthsTranslate;let p;const u=!n.animating&&0!==a;i>c?(n.monthsTranslate-=1,n.animating||n.$months.eq(n.$months.length-1).remove(),l.append(m),n.$months=l.find(".calendar-month"),p=100*-(h-1)*s,n.$months.eq(n.$months.length-1).transform(`translate3d(${o?p:0}%, ${o?0:p}%, 0)`).addClass("calendar-month-next")):(n.monthsTranslate+=1,n.animating||n.$months.eq(0).remove(),l.prepend(m),n.$months=l.find(".calendar-month"),p=100*-(h+1)*s,n.$months.eq(0).transform(`translate3d(${o?p:0}%, ${o?0:p}%, 0)`).addClass("calendar-month-prev")),n.emit("local::monthAdd calendarMonthAdd","next"===d?n.$months.eq(n.$months.length-1)[0]:n.$months.eq(0)[0]),n.animating=!0,n.onMonthChangeStart(d);const v=100*n.monthsTranslate*s;l.transition(a).transform(`translate3d(${o?v:0}%, ${o?0:v}%, 0)`),u&&l.transitionEnd((()=>{n.onMonthChangeEnd(d,!0)})),r.animate&&0!==a||n.onMonthChangeEnd(d,!0)}nextYear(){this.setYearMonth(this.currentYear+1)}prevYear(){this.setYearMonth(this.currentYear-1)}dateInRange(e,t){let a,n=!1;if(!t)return!1;if(Array.isArray(t))for(a=0;a<t.length;a+=1)t[a].from||t[a].to?t[a].from&&t[a].to?e<=new Date(t[a].to).getTime()&&e>=new Date(t[a].from).getTime()&&(n=!0):t[a].from?e>=new Date(t[a].from).getTime()&&(n=!0):t[a].to&&e<=new Date(t[a].to).getTime()&&(n=!0):t[a].date?e===new Date(t[a].date).getTime()&&(n=!0):e===new Date(t[a]).getTime()&&(n=!0);else t.from||t.to?t.from&&t.to?e<=new Date(t.to).getTime()&&e>=new Date(t.from).getTime()&&(n=!0):t.from?e>=new Date(t.from).getTime()&&(n=!0):t.to&&e<=new Date(t.to).getTime()&&(n=!0):t.date?n=e===new Date(t.date).getTime():"function"==typeof t&&(n=t(new Date(e)));return n}daysInMonth(e){const t=new Date(e);return new Date(t.getFullYear(),t.getMonth()+1,0).getDate()}renderMonths(e){const t=this;return t.params.renderMonths?t.params.renderMonths.call(t,e):$jsx("div",{class:"calendar-months-wrapper"},t.renderMonth(e,"prev"),t.renderMonth(e),t.renderMonth(e,"next"))}renderMonth(e,t){const a=this,{params:n,value:r}=a;if(n.renderMonth)return n.renderMonth.call(a,e,t);let o=new Date(e),l=o.getFullYear(),s=o.getMonth(),i=a.monthNames.indexOf(a.monthFormatter(o));i<0&&(i=s);let c=a.yearFormatter(o);"next"===t&&(o=11===s?new Date(l+1,0):new Date(l,s+1,1)),"prev"===t&&(o=0===s?new Date(l-1,11):new Date(l,s-1,1)),"next"!==t&&"prev"!==t||(s=o.getMonth(),l=o.getFullYear(),i=a.monthNames.indexOf(a.monthFormatter(o)),i<0&&(i=s),c=a.yearFormatter(o));const d=[],m=(new Date).setHours(0,0,0,0),h=n.minDate?new Date(n.minDate).getTime():null,p=n.maxDate?new Date(n.maxDate).getTime():null,u=a.daysInMonth(new Date(o.getFullYear(),o.getMonth()).getTime()-864e6),v=a.daysInMonth(o),g=6===n.firstDay?0:1;let f,k,y="",$=n.firstDay-1+0,P=new Date(o.getFullYear(),o.getMonth()).getDay();if(0===P&&(P=7),r&&r.length)for(let e=0;e<r.length;e+=1)d.push(new Date(r[e]).setHours(0,0,0,0));for(let e=1;e<=6;e+=1){let t="";for(let r=1;r<=7;r+=1){let o;$+=1;let i=$-P,c="";1===e&&1===r&&i>g&&1!==n.firstDay&&($-=7,i=$-P);const y=r-1+n.firstDay>6?r-1-7+n.firstDay:r-1+n.firstDay;i<0?(i=u+i+1,c+=" calendar-day-prev",o=new Date(s-1<0?l-1:l,s-1<0?11:s-1,i).getTime()):(i+=1,i>v?(i-=v,c+=" calendar-day-next",o=new Date(s+1>11?l+1:l,s+1>11?0:s+1,i).getTime()):o=new Date(l,s,i).getTime()),o===m&&(c+=" calendar-day-today"),n.rangePicker&&2===d.length?(o>=d[0]&&o<=d[1]&&(c+=" calendar-day-selected"),d[0]!==d[1]&&(o>d[0]&&o<d[1]&&(c+=" calendar-day-selected-range"),o===d[0]&&(c+=" calendar-day-selected-left"),o===d[1]&&(c+=" calendar-day-selected-right"))):d.indexOf(o)>=0&&(c+=" calendar-day-selected"),n.weekendDays.indexOf(y)>=0&&(c+=" calendar-day-weekend");let M="";if(k=!1,n.events&&a.dateInRange(o,n.events)&&(k=!0),k&&(c+=" calendar-day-has-events",M='\n              <span class="calendar-day-events">\n                <span class="calendar-day-event"></span>\n              </span>\n            ',Array.isArray(n.events))){const e=[];n.events.forEach((t=>{const n=t.color||"";e.indexOf(n)<0&&a.dateInRange(o,t)&&e.push(n)})),M=`\n                <span class="calendar-day-events">\n                  ${e.map((e=>`\n                    <span class="calendar-day-event" style="${e?`background-color: ${e}`:""}"></span>\n                  `.trim())).join("")}\n                </span>\n              `}if(n.rangesClasses)for(let e=0;e<n.rangesClasses.length;e+=1)a.dateInRange(o,n.rangesClasses[e].range)&&(c+=` ${n.rangesClasses[e].cssClass}`);f=!1,(h&&o<h||p&&o>p)&&(f=!0),n.disabled&&a.dateInRange(o,n.disabled)&&(f=!0),f&&(c+=" calendar-day-disabled"),o=new Date(o);const D=o.getFullYear(),w=o.getMonth();t+=`\n            <div data-year="${D}" data-month="${w}" data-day="${i}" class="calendar-day${c}" data-date="${D}-${w}-${i}">\n              <span class="calendar-day-number">${a.dayFormatter(o)}${M}</span>\n            </div>`.trim()}y+=`<div class="calendar-row">${t}</div>`}return y=`<div class="calendar-month" data-year="${l}" data-month="${s}" data-locale-year="${c}" data-locale-month="${i}">${y}</div>`,y}renderWeekHeader(){const e=this;if(e.params.renderWeekHeader)return e.params.renderWeekHeader.call(e);const{params:t}=e;let a="";for(let n=0;n<7;n+=1){const r=n+t.firstDay>6?n-7+t.firstDay:n+t.firstDay;a+=`<div class="calendar-week-day">${e.dayNamesShort[r]}</div>`}return $jsx("div",{class:"calendar-week-header"},a)}renderMonthSelector(){const e=this;return e.params.renderMonthSelector?e.params.renderMonthSelector.call(e):$jsx("div",{class:"calendar-month-selector"},$jsx("a",{class:"link icon-only calendar-prev-month-button"},$jsx("i",{class:"icon icon-prev"})),e.params.monthPicker?$jsx("a",{class:"current-month-value link"}):$jsx("span",{class:"current-month-value"}),$jsx("a",{class:"link icon-only calendar-next-month-button"},$jsx("i",{class:"icon icon-next"})))}renderYearSelector(){const e=this;return e.params.renderYearSelector?e.params.renderYearSelector.call(e):$jsx("div",{class:"calendar-year-selector"},$jsx("a",{class:"link icon-only calendar-prev-year-button"},$jsx("i",{class:"icon icon-prev"})),e.params.yearPicker?$jsx("a",{class:"current-year-value link"}):$jsx("span",{class:"current-year-value"}),$jsx("a",{class:"link icon-only calendar-next-year-button"},$jsx("i",{class:"icon icon-next"})))}renderTimeSelector(){const e=this,t=e.value&&e.value[0];let a;return t&&(a=e.timeSelectorFormatter(t)),$jsx("div",{class:"calendar-time-selector"},$jsx("span",null,e.params.timePickerLabel),$jsx("a",{class:"link"},a||e.params.timePickerPlaceholder))}renderHeader(){const e=this;return e.params.renderHeader?e.params.renderHeader.call(e):$jsx("div",{class:"calendar-header"},$jsx("div",{class:"calendar-selected-date"},e.params.headerPlaceholder))}renderFooter(){const e=this,t=e.app;return e.params.renderFooter?e.params.renderFooter.call(e):$jsx("div",{class:"calendar-footer"},$jsx("a",{class:("md"===t.theme?"button":"link")+" calendar-close sheet-close popover-close"},e.params.toolbarCloseText))}renderToolbar(){const e=this;return e.params.renderToolbar?e.params.renderToolbar.call(e,e):$jsx("div",{class:"toolbar toolbar-top no-shadow"},$jsx("div",{class:"toolbar-inner"},e.params.monthSelector?e.renderMonthSelector():"",e.params.yearSelector?e.renderYearSelector():""))}renderInline(){const e=this,{cssClass:t,toolbar:a,header:n,footer:r,rangePicker:o,weekHeader:l}=e.params,{value:s,hasTimePicker:i}=e,c=s&&s.length?s[0]:(new Date).setHours(0,0,0);return $jsx("div",{class:`calendar calendar-inline ${o?"calendar-range":""} ${t||""}`},n&&e.renderHeader(),a&&e.renderToolbar(),l&&e.renderWeekHeader(),$jsx("div",{class:"calendar-months"},e.renderMonths(c)),i&&e.renderTimeSelector(),r&&e.renderFooter())}renderCustomModal(){const e=this,{cssClass:t,toolbar:a,header:n,footer:r,rangePicker:o,weekHeader:l}=e.params,{value:s,hasTimePicker:i}=e,c=s&&s.length?s[0]:(new Date).setHours(0,0,0);return $jsx("div",{class:`calendar calendar-modal ${o?"calendar-range":""} ${t||""}`},n&&e.renderHeader(),a&&e.renderToolbar(),l&&e.renderWeekHeader(),$jsx("div",{class:"calendar-months"},e.renderMonths(c)),i&&e.renderTimeSelector(),r&&e.renderFooter())}renderSheet(){const e=this,{cssClass:t,toolbar:a,header:n,footer:r,rangePicker:o,weekHeader:l}=e.params,{value:s,hasTimePicker:i}=e,c=s&&s.length?s[0]:(new Date).setHours(0,0,0);return $jsx("div",{class:`sheet-modal calendar calendar-sheet ${o?"calendar-range":""} ${t||""}`},n&&e.renderHeader(),a&&e.renderToolbar(),l&&e.renderWeekHeader(),$jsx("div",{class:"sheet-modal-inner calendar-months"},e.renderMonths(c)),i&&e.renderTimeSelector(),r&&e.renderFooter())}renderPopover(){const e=this,{cssClass:t,toolbar:a,header:n,footer:r,rangePicker:o,weekHeader:l}=e.params,{value:s,hasTimePicker:i}=e,c=s&&s.length?s[0]:(new Date).setHours(0,0,0);return $jsx("div",{class:"popover calendar-popover"},$jsx("div",{class:"popover-inner"},$jsx("div",{class:`calendar ${o?"calendar-range":""} ${t||""}`},n&&e.renderHeader(),a&&e.renderToolbar(),l&&e.renderWeekHeader(),$jsx("div",{class:"calendar-months"},e.renderMonths(c)),i&&e.renderTimeSelector(),r&&e.renderFooter())))}render(){const e=this,{params:t}=e;if(t.render)return t.render.call(e);if(!e.inline){let a=t.openIn;return"auto"===a&&(a=e.isPopover()?"popover":"sheet"),"popover"===a?e.renderPopover():"sheet"===a?e.renderSheet():e.renderCustomModal()}return e.renderInline()}openMonthPicker(){const e=this,{$el:t,app:a}=e;if(!t||!t.length)return;t.append('<div class="popover calendar-popover calendar-month-picker-popover"><div class="popover-inner"><div class="calendar-month-picker"></div></div></div>'),e.monthPickerPopover=a.popover.create({el:t.find(".calendar-month-picker-popover"),targetEl:t.find(".calendar-month-selector"),backdrop:!0,backdropUnique:!0,on:{close(){e.closeMonthPicker()},closed(){e.monthPickerPopover.$el&&e.monthPickerPopover.$el.remove(),e.monthPickerPopover.destroy(),e.monthPickerInstance&&(e.monthPickerInstance.close(),e.monthPickerInstance.destroy()),delete e.monthPickerInstance,delete e.monthPickerPopover}}}),e.monthPickerPopover.open();const n=parseInt(e.$el.find(".calendar-month-current").attr("data-locale-month"),10),r=[],o=[];e.monthNames.forEach(((e,t)=>{r.push(t),o.push(e)})),e.monthPickerInstance=a.picker.create({containerEl:e.monthPickerPopover.$el.find(".calendar-month-picker"),value:[n],toolbar:!1,rotateEffect:!1,toolbarCloseText:e.params.toolbarCloseText,cols:[{values:r,displayValues:o}]})}closeMonthPicker(){const e=this;e.monthPickerPopover&&e.monthPickerPopover.opened&&e.monthPickerPopover.close();const t=e.monthPickerInstance.value[0]-(parseInt(e.$el.find(".calendar-month-current").attr("data-locale-month"),10)-e.currentMonth);e.setYearMonth(e.currentYear,t,0)}openYearPicker(){const e=this,{$el:t,app:a}=e;if(!t||!t.length)return;t.append('<div class="popover calendar-popover calendar-year-picker-popover"><div class="popover-inner"><div class="calendar-year-picker"></div></div></div>'),e.yearPickerPopover=a.popover.create({el:t.find(".calendar-year-picker-popover"),targetEl:t.find(".calendar-year-selector"),backdrop:!0,backdropUnique:!0,on:{close(){e.closeYearPicker()},closed(){e.yearPickerPopover.$el&&e.yearPickerPopover.$el.remove(),e.yearPickerPopover.destroy(),e.yearPickerInstance&&(e.yearPickerInstance.close(),e.yearPickerInstance.destroy()),delete e.yearPickerInstance,delete e.yearPickerPopover}}}),e.yearPickerPopover.open();const n=e.currentYear;let r=e.params.yearPickerMin||(new Date).getFullYear()-100;e.params.minDate&&(r=Math.max(r,new Date(e.params.minDate).getFullYear()));let o=e.params.yearPickerMax||(new Date).getFullYear()+100;e.params.maxDate&&(o=Math.min(o,new Date(e.params.maxDate).getFullYear()));const l=[];for(let e=r;e<=o;e+=1)l.push(e);e.yearPickerInstance=a.picker.create({containerEl:e.yearPickerPopover.$el.find(".calendar-year-picker"),value:[n],toolbar:!1,rotateEffect:!1,toolbarCloseText:e.params.toolbarCloseText,cols:[{values:l}]})}closeYearPicker(){const e=this;e.yearPickerPopover&&e.yearPickerPopover.opened&&e.yearPickerPopover.close(),e.setYearMonth(e.yearPickerInstance.value[0],void 0,0)}openTimePicker(){const e=this,{$el:t,app:a,is12HoursFormat:n}=e;if(!t||!t.length)return;t.append('<div class="popover calendar-popover calendar-time-picker-popover"><div class="popover-inner"><div class="calendar-time-picker"></div></div></div>');const r=[],o=[],l=n?12:23;for(let e=n?1:0;e<=l;e+=1)r.push(e);for(let e=0;e<=59;e+=1)o.push(e);let s;s=e.value&&e.value.length?[e.value[0].getHours(),e.value[0].getMinutes()]:[(new Date).getHours(),(new Date).getMinutes()],n&&(s.push(s[0]<12?"AM":"PM"),s[0]>12&&(s[0]-=12),0===s[0]&&(s[0]=12)),e.timePickerPopover=a.popover.create({el:t.find(".calendar-time-picker-popover"),targetEl:t.find(".calendar-time-selector .link"),backdrop:!0,backdropUnique:!0,on:{close(){e.closeTimePicker()},closed(){e.timePickerPopover.$el&&e.timePickerPopover.$el.remove(),e.timePickerPopover.destroy(),e.timePickerInstance&&(e.timePickerInstance.close(),e.timePickerInstance.destroy()),delete e.timePickerInstance,delete e.timePickerPopover}}}),e.timePickerPopover.open(),e.timePickerInstance=a.picker.create({containerEl:e.timePickerPopover.$el.find(".calendar-time-picker"),value:s,toolbar:!1,rotateEffect:!1,toolbarCloseText:e.params.toolbarCloseText,cols:[{values:r},{divider:!0,content:":"},{values:o,displayValues:o.map((e=>e<10?`0${e}`:e))},...n?[{values:["AM","PM"]}]:[]]})}closeTimePicker(){const e=this,{is12HoursFormat:t}=e;if(e.timePickerInstance){const a=e.timePickerInstance.value;let n=parseInt(a[0],10);const r=parseInt(a[1],10),o=e.timePickerInstance.value[2];t&&("AM"===o&&12===n?n=0:"PM"===o&&12!==n&&(n+=12));let l=e.value&&e.value.length&&e.value[0];l?(l=new Date(l),l.setHours(n,r)):(l=new Date,l.setHours(n,r,0,0)),e.setValue([l]),e.timePickerPopover&&e.timePickerPopover.opened&&e.timePickerPopover.close()}}onOpen(){const e=this,{initialized:t,$el:a,app:n,$inputEl:r,inline:o,value:l,params:s}=e;e.closing=!1,e.opened=!0,e.opening=!0,e.attachCalendarEvents();const i=!l&&s.value;t?l&&e.setValue(l,0):l?e.setValue(l,0):s.value&&e.setValue(e.normalizeValues(s.value),0),e.updateCurrentMonthYear(),e.monthsTranslate=0,e.setMonthsTranslate(),i?e.updateValue():s.header&&l&&e.updateValue(!0),!o&&r&&r.length&&"md"===n.theme&&r.trigger("focus"),e.initialized=!0,e.$months.each((t=>{e.emit("local::monthAdd calendarMonthAdd",t)})),a&&a.trigger("calendar:open"),r&&r.trigger("calendar:open"),e.emit("local::open calendarOpen",e)}onOpened(){const e=this;e.opening=!1,e.$el&&e.$el.trigger("calendar:opened"),e.$inputEl&&e.$inputEl.trigger("calendar:opened"),e.emit("local::opened calendarOpened",e)}onClose(){const e=this,t=e.app;if(e.opening=!1,e.closing=!0,e.$inputEl)if("md"===t.theme)e.$inputEl.trigger("blur");else{const a=e.$inputEl.attr("validate"),n=e.$inputEl.attr("required");a&&n&&t.input.validate(e.$inputEl)}e.detachCalendarEvents&&e.detachCalendarEvents(),e.$el&&e.$el.trigger("calendar:close"),e.$inputEl&&e.$inputEl.trigger("calendar:close"),e.emit("local::close calendarClose",e)}onClosed(){const e=this;e.opened=!1,e.closing=!1,e.inline||c((()=>{e.modal&&e.modal.el&&e.modal.destroy&&(e.params.routableModals||e.modal.destroy()),delete e.modal})),e.timePickerInstance&&(e.timePickerInstance.destroy&&e.timePickerInstance.destroy(),delete e.timePickerInstance),e.$el&&e.$el.trigger("calendar:closed"),e.$inputEl&&e.$inputEl.trigger("calendar:closed"),e.emit("local::closed calendarClosed",e)}open(){const e=this,{app:t,opened:n,inline:r,$inputEl:o,params:l}=e;if(n)return;if(r)return e.$el=a(e.render()),e.$el[0].f7Calendar=e,e.$wrapperEl=e.$el.find(".calendar-months-wrapper"),e.$months=e.$wrapperEl.find(".calendar-month"),e.$containerEl.append(e.$el),e.onOpen(),void e.onOpened();let s=l.openIn;"auto"===s&&(s=e.isPopover()?"popover":"sheet");const i=e.render(),c={targetEl:o,scrollToEl:l.scrollToInput?o:void 0,content:i,backdrop:!0===l.backdrop||"popover"===s&&!1!==t.params.popover.backdrop&&!1!==l.backdrop,closeByBackdropClick:l.closeByBackdropClick,on:{open(){const t=this;e.modal=t,e.$el="popover"===s?t.$el.find(".calendar"):t.$el,e.$wrapperEl=e.$el.find(".calendar-months-wrapper"),e.$months=e.$wrapperEl.find(".calendar-month"),e.$el[0].f7Calendar=e,"customModal"===s&&a(e.$el).find(".calendar-close").once("click",(()=>{e.close()})),e.onOpen()},opened(){e.onOpened()},close(){e.onClose()},closed(){e.onClosed()}}};"sheet"===s&&(c.push=l.sheetPush,c.swipeToClose=l.sheetSwipeToClose),l.routableModals&&e.view?e.view.router.navigate({url:e.url,route:{path:e.url,[s]:c}}):(e.modal=t[s].create(c),e.modal.open())}close(){const e=this,{opened:t,inline:a}=e;if(t)return a?(e.onClose(),void e.onClosed()):void(e.params.routableModals&&e.view?e.view.router.back():e.modal.close())}init(){const e=this;if(e.initInput(),e.inline)return e.open(),void e.emit("local::init calendarInit",e);!e.initialized&&e.params.value&&e.setValue(e.normalizeValues(e.params.value)),e.$inputEl&&e.attachInputEvents(),e.params.closeByOutsideClick&&e.attachHtmlEvents(),e.emit("local::init calendarInit",e)}destroy(){const e=this;if(e.destroyed)return;const{$el:t}=e;e.emit("local::beforeDestroy calendarBeforeDestroy",e),t&&t.trigger("calendar:beforedestroy"),e.close(),e.$inputEl&&e.detachInputEvents(),e.params.closeByOutsideClick&&e.detachHtmlEvents(),e.timePickerInstance&&(e.timePickerInstance.destroy&&e.timePickerInstance.destroy(),delete e.timePickerInstance),t&&t.length&&delete e.$el[0].f7Calendar,d(e),e.destroyed=!0}}var h={name:"calendar",static:{Calendar:m},create(){const e=this;e.calendar=s({defaultSelector:".calendar",constructor:m,app:e,domProp:"f7Calendar"}),e.calendar.close=function(e){void 0===e&&(e=".calendar");const t=a(e);if(0===t.length)return;const n=t[0].f7Calendar;!n||n&&!n.opened||n.close()}},params:{calendar:{dateFormat:void 0,monthNames:"auto",monthNamesShort:"auto",dayNames:"auto",dayNamesShort:"auto",locale:void 0,firstDay:1,weekendDays:[0,6],multiple:!1,rangePicker:!1,rangePickerMinDays:1,rangePickerMaxDays:0,direction:"horizontal",minDate:null,maxDate:null,disabled:null,events:null,rangesClasses:null,touchMove:!0,animate:!0,closeOnSelect:!1,monthSelector:!0,monthPicker:!0,yearSelector:!0,yearPicker:!0,yearPickerMin:void 0,yearPickerMax:void 0,timePicker:!1,timePickerLabel:"Time",timePickerFormat:{hour:"numeric",minute:"numeric"},timePickerPlaceholder:"Select time",weekHeader:!0,value:null,containerEl:null,openIn:"auto",sheetPush:!1,sheetSwipeToClose:void 0,formatValue:null,inputEl:null,inputReadOnly:!0,closeByOutsideClick:!0,scrollToInput:!0,header:!1,headerPlaceholder:"Select date",toolbar:!0,toolbarCloseText:"Done",footer:!1,cssClass:null,routableModals:!1,view:null,url:"date/",backdrop:null,closeByBackdropClick:!0,renderWeekHeader:null,renderMonths:null,renderMonth:null,renderMonthSelector:null,renderYearSelector:null,renderHeader:null,renderFooter:null,renderToolbar:null,renderInline:null,renderPopover:null,renderSheet:null,render:null}}};if(t){if(e.prototype.modules&&e.prototype.modules[h.name])return;e.use(h),e.instance&&(e.instance.useModuleParams(h,e.instance.params),e.instance.useModule(h))}return h}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
