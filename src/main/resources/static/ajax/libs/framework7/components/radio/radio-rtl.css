:root{--f7-radio-border-radius:50%;--f7-radio-extra-margin:0px}:root .dark,:root.dark{--f7-radio-inactive-color:rgba(255, 255, 255, 0.3)}.ios{--f7-radio-size:22px;--f7-radio-border-width:1px;--f7-radio-inactive-color:#c7c7cc}.md{--f7-radio-size:20px;--f7-radio-border-width:2px;--f7-radio-inactive-color:#6d6d6d}.aurora{--f7-radio-size:16px;--f7-radio-border-width:1px;--f7-radio-inactive-color:rgba(0, 0, 0, 0.25)}.radio{position:relative;display:inline-block;vertical-align:middle;z-index:1;--f7-touch-ripple-color:rgba(var(--f7-theme-color-rgb), 0.5)}.icon-radio{width:var(--f7-radio-size);height:var(--f7-radio-size);border-radius:var(--f7-radio-border-radius);position:relative;box-sizing:border-box;display:block;flex-shrink:0}.aurora .icon-radio,.md .icon-radio,.radio .icon-radio{border:var(--f7-radio-border-width) solid var(--f7-radio-inactive-color)}.radio,label.item-radio{cursor:pointer}.radio input[type=checkbox],.radio input[type=radio],label.item-radio input[type=checkbox],label.item-radio input[type=radio]{display:none}label.item-radio{transition-duration:.3s}label.item-radio .item-content .item-media,label.item-radio.item-content .item-media{align-self:center}label.item-radio.active-state{background-color:var(--f7-list-link-pressed-bg-color)}label.item-radio.active-state:after{background-color:transparent}.disabled label.item-radio,label.item-radio.disabled{opacity:.55;pointer-events:none}.ios .icon-radio:after{font-family:framework7-core-icons;font-weight:400;font-style:normal;line-height:1;letter-spacing:normal;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-feature-settings:'liga';text-align:center;display:block;width:100%;height:100%;width:calc(var(--f7-radio-size) - var(--f7-radio-border-width) * 2);height:calc(var(--f7-radio-size) - var(--f7-radio-border-width) * 2);line-height:calc(var(--f7-radio-size) - var(--f7-radio-border-width) * 2 + 1px);font-size:20px;content:'radio_ios';color:var(--f7-radio-active-color,var(--f7-theme-color));opacity:0}.ios .radio input[type=radio]:checked~.icon-radio:after,.ios label.item-radio input[type=radio]:checked~* .icon-radio:after,.ios label.item-radio input[type=radio]:checked~.icon-radio:after{opacity:1}.ios .radio input[type=radio]:checked~.icon-radio{border-color:var(--f7-radio-active-color,var(--f7-theme-color))}.ios label.item-radio:not(.item-radio-icon-start) input[type=radio]~.icon-radio{position:absolute;top:50%;margin-top:-11px;left:calc(var(--f7-safe-area-left) + 10px)}.ios label.item-radio:not(.item-radio-icon-start) .item-inner{padding-left:calc(var(--f7-safe-area-left) + 36px)}.ios label.item-radio-icon-start>.icon-radio{margin-left:calc(var(--f7-list-item-media-margin) + var(--f7-checkbox-extra-margin))}.ios label.item-radio.active-state{transition-duration:0s}.md .icon-radio{transition-duration:.2s}.md .icon-radio:after{content:'';position:absolute;width:10px;height:10px;left:50%;top:50%;margin-left:-5px;margin-top:-5px;background-color:var(--f7-radio-active-color,var(--f7-theme-color));border-radius:50%;transform:scale(0);transition-duration:.2s}.md .radio input[type=radio]:checked~.icon-radio,.md label.item-radio input[type=radio]:checked~* .icon-radio,.md label.item-radio input[type=radio]:checked~.icon-radio{border-color:var(--f7-radio-active-color,var(--f7-theme-color))}.md .radio input[type=radio]:checked~.icon-radio:after,.md label.item-radio input[type=radio]:checked~* .icon-radio:after,.md label.item-radio input[type=radio]:checked~.icon-radio:after{background-color:var(--f7-radio-active-color,var(--f7-theme-color));transform:scale(1)}.md label.item-radio{position:relative;overflow:hidden;z-index:0}.md label.item-radio:not(.item-radio-icon-end)>.icon-radio{margin-left:calc(var(--f7-list-item-media-margin) + var(--f7-radio-extra-margin))}.md label.item-radio-icon-end input[type=radio]~.icon-radio{position:absolute;top:50%;margin-top:-10px;left:calc(var(--f7-safe-area-left) + 16px)}.md label.item-radio-icon-end .item-inner{padding-left:calc(var(--f7-safe-area-left) + 52px)}.aurora .icon-radio{transition-duration:150ms;overflow:hidden}.aurora .icon-radio:after{content:'';position:absolute;width:6px;height:6px;left:50%;top:50%;margin-left:-3px;margin-top:-3px;background-color:#fff;border-radius:50%;transform:scale(0);transition-duration:150ms}.aurora .icon-radio:before{content:'';position:absolute;left:0;top:0;width:100%;height:100%;background:rgba(0,0,0,.1);opacity:0;transition-duration:150ms}.aurora .radio.active-state i:before{opacity:1}.aurora .radio input[type=radio]:checked~.icon-radio,.aurora label.item-radio input[type=radio]:checked~* .icon-radio,.aurora label.item-radio input[type=radio]:checked~.icon-radio{border-color:var(--f7-radio-active-color,var(--f7-theme-color));background-color:var(--f7-radio-active-color,var(--f7-theme-color))}.aurora .radio input[type=radio]:checked~.icon-radio:after,.aurora label.item-radio input[type=radio]:checked~* .icon-radio:after,.aurora label.item-radio input[type=radio]:checked~.icon-radio:after{transform:scale(1)}.aurora label.item-radio{position:relative;overflow:hidden;z-index:0}.aurora label.item-radio:not(.item-radio-icon-end)>.icon-radio{margin-left:calc(var(--f7-list-item-media-margin) + var(--f7-radio-extra-margin))}.aurora label.item-radio-icon-end input[type=radio]~.icon-radio{position:absolute;top:50%;margin-top:-8px;left:calc(var(--f7-safe-area-left) + 16px)}.aurora label.item-radio-icon-end .item-inner{padding-left:calc(var(--f7-safe-area-left) + 48px)}.aurora.device-desktop label.item-radio:hover:not(.active-state):not(.no-hover){background-color:var(--f7-list-link-hover-bg-color)}