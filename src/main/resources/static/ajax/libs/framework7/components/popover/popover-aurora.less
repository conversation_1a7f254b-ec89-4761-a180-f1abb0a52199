.aurora {
  .popover {
    transform: none;
    transition-property: opacity;
  }
  .popover-angle {
    width: 24px;
    height: 24px;
    position: absolute;
    left: -24px;
    top: 0;
    z-index: 100;
    overflow: hidden;
    &:after {
      content: '';
      background: var(--f7-popover-bg-color);
      width: 24px;
      height: 24px;
      position: absolute;
      left: 0;
      top: 0;
      border-radius: 4px;
      transform: rotate(45deg);
    }
    &.on-left {
      left: -24px;
      &:after {
        left: 17px;
        top: 0;
      }
    }
    &.on-right {
      left: 100%;
      &:after {
        left: -17px;
        top: 0;
      }
    }
    &.on-top {
      left: 0;
      top: -24px;
      &:after {
        left: 0;
        top: 17px;
      }
    }
    &.on-bottom {
      left: 0;
      top: 100%;
      &:after {
        left: 0;
        top: -17px;
      }
    }
  }
}
