(function framework7ComponentLoader(e,t){void 0===t&&(t=!0);var s=e.$,a=e.utils,l=e.getDevice,r=(e.getSupport,e.Class),i=(e.<PERSON>,e.ConstructorMethods),n=(e.<PERSON>,a.extend),o=a.deleteProps;class h extends r{constructor(e,t){void 0===t&&(t={}),super(t,[e]);const a=this,r=l(),i=getDocument();let o;"md"===e.theme?o=48:"ios"===e.theme?o=44:"aurora"===e.theme&&(o=38);const h={cols:1,height:o,cache:!0,dynamicHeightBufferSize:1,showFilteredItemsOnly:!1,renderExternal:void 0,setListHeight:!0,searchByItem:void 0,searchAll:void 0,ul:null,createUl:!0,scrollableParentEl:void 0,renderItem:e=>`\n            <li>\n              <div class="item-content">\n                <div class="item-inner">\n                  <div class="item-title">${e}</div>\n                </div>\n              </div>\n            </li>\n          `.trim(),on:{}};if(a.useModulesParams(h),a.params=n(h,t),void 0!==a.params.height&&a.params.height||(a.params.height=o),a.$el=s(t.el),a.el=a.$el[0],0===a.$el.length)return;a.$el[0].f7VirtualList=a,a.items=a.params.items,a.params.showFilteredItemsOnly&&(a.filteredItems=[]),a.params.renderItem&&(a.renderItem=a.params.renderItem),a.$pageContentEl=a.$el.parents(".page-content"),a.pageContentEl=a.$pageContentEl[0],a.$scrollableParentEl=a.params.scrollableParentEl?s(a.params.scrollableParentEl).eq(0):a.$pageContentEl,!a.$scrollableParentEl.length&&a.$pageContentEl.length&&(a.$scrollableParentEl=a.$pageContentEl),a.scrollableParentEl=a.$scrollableParentEl[0],void 0!==a.params.updatableScroll?a.updatableScroll=a.params.updatableScroll:(a.updatableScroll=!0,r.ios&&r.osVersion.split(".")[0]<8&&(a.updatableScroll=!1));const m=a.params.ul;let c;a.$ul=m?s(a.params.ul):a.$el.children("ul"),0===a.$ul.length&&a.params.createUl&&(a.$el.append("<ul></ul>"),a.$ul=a.$el.children("ul")),a.ul=a.$ul[0],c=a.ul||a.params.createUl?a.$ul:a.$el,n(a,{$itemsWrapEl:c,itemsWrapEl:c[0],domCache:{},displayDomCache:{},tempDomElement:i.createElement("ul"),lastRepaintY:null,fragment:i.createDocumentFragment(),pageHeight:void 0,rowsPerScreen:void 0,rowsBefore:void 0,rowsAfter:void 0,rowsToRender:void 0,maxBufferHeight:0,listHeight:void 0,dynamicHeight:"function"==typeof a.params.height,autoHeight:"auto"===a.params.height}),a.useModules();const p=a.handleScroll.bind(a),d=a.handleResize.bind(a);let g,f,u,I;return a.attachEvents=function(){g=a.$el.parents(".page").eq(0),f=a.$el.parents(".tab").filter((e=>0===s(e).parent(".tabs").parent(".tabs-animated-wrap, .tabs-swipeable-wrap").length)).eq(0),u=a.$el.parents(".panel").eq(0),I=a.$el.parents(".popup").eq(0),a.$scrollableParentEl.on("scroll",p),g.length&&g.on("page:reinit",d),f.length&&f.on("tab:show",d),u.length&&u.on("panel:open",d),I.length&&I.on("popup:open",d),e.on("resize",d)},a.detachEvents=function(){a.$scrollableParentEl.off("scroll",p),g.length&&g.off("page:reinit",d),f.length&&f.off("tab:show",d),u.length&&u.off("panel:open",d),I.length&&I.off("popup:open",d),e.off("resize",d)},a.init(),a}setListSize(e){const t=this,s=t.filteredItems||t.items;if(e||(t.pageHeight=t.$scrollableParentEl[0].offsetHeight),t.dynamicHeight){t.listHeight=0,t.heights=[];for(let e=0;e<s.length;e+=1){const a=t.params.height(s[e]);t.listHeight+=a,t.heights.push(a)}}else if(t.autoHeight){t.listHeight=0,t.heights||(t.heights=[]),t.heightsCalculated||(t.heightsCalculated=[]);const e={};t.$itemsWrapEl.find("[data-virtual-list-index]").forEach((t=>{e[parseInt(t.getAttribute("data-virtual-list-index"),10)]=t}));for(let a=0;a<s.length;a+=1){const s=e[a];s&&(t.heightsCalculated.includes(a)||(t.heights[a]=s.offsetHeight,t.heightsCalculated.push(a))),void 0===t.heights[a]&&(t.heights[a]=40),t.listHeight+=t.heights[a]}}else t.listHeight=Math.ceil(s.length/t.params.cols)*t.params.height,t.rowsPerScreen=Math.ceil(t.pageHeight/t.params.height),t.rowsBefore=t.params.rowsBefore||2*t.rowsPerScreen,t.rowsAfter=t.params.rowsAfter||t.rowsPerScreen,t.rowsToRender=t.rowsPerScreen+t.rowsBefore+t.rowsAfter,t.maxBufferHeight=t.rowsBefore/2*t.params.height;(t.updatableScroll||t.params.setListHeight)&&t.$itemsWrapEl.css({height:`${t.listHeight}px`})}render(e,t){const s=this;e&&(s.lastRepaintY=null);let a=-(s.$el[0].getBoundingClientRect().top-s.$scrollableParentEl[0].getBoundingClientRect().top);if(void 0!==t&&(a=t),!(null===s.lastRepaintY||Math.abs(a-s.lastRepaintY)>s.maxBufferHeight||!s.updatableScroll&&s.$scrollableParentEl[0].scrollTop+s.pageHeight>=s.$scrollableParentEl[0].scrollHeight))return;s.lastRepaintY=a;const l=s.filteredItems||s.items;let r,i,n,o=0,h=0;if(s.dynamicHeight||s.autoHeight){let e,t=0;s.maxBufferHeight=s.pageHeight;for(let l=0;l<s.heights.length;l+=1)e=s.heights[l],void 0===r&&(t+e>=a-2*s.pageHeight*s.params.dynamicHeightBufferSize?r=l:o+=e),void 0===i&&((t+e>=a+2*s.pageHeight*s.params.dynamicHeightBufferSize||l===s.heights.length-1)&&(i=l+1),h+=e),t+=e;i=Math.min(i,l.length)}else r=(parseInt(a/s.params.height,10)-s.rowsBefore)*s.params.cols,r<0&&(r=0),i=Math.min(r+s.rowsToRender*s.params.cols,l.length);const m=[];let c;for(s.reachEnd=!1,c=r;c<i;c+=1){let e;const t=s.items.indexOf(l[c]);c===r&&(s.currentFromIndex=t),c===i-1&&(s.currentToIndex=t),s.filteredItems?s.items[t]===s.filteredItems[s.filteredItems.length-1]&&(s.reachEnd=!0):t===s.items.length-1&&(s.reachEnd=!0),s.params.renderExternal?m.push(l[c]):s.domCache[t]?(e=s.domCache[t],e.f7VirtualListIndex=t):(s.renderItem?s.tempDomElement.innerHTML=s.renderItem(l[c],t).trim():s.tempDomElement.innerHTML=l[c].toString().trim(),e=s.tempDomElement.childNodes[0],s.params.cache&&(s.domCache[t]=e),e.f7VirtualListIndex=t),c===r&&(n=s.dynamicHeight||s.autoHeight?o:c*s.params.height/s.params.cols),s.params.renderExternal||(e.style.top=`${n}px`,s.emit("local::itemBeforeInsert vlItemBeforeInsert",s,e,l[c]),s.fragment.appendChild(e))}s.updatableScroll||(s.dynamicHeight||s.autoHeight?s.itemsWrapEl.style.height=`${h}px`:s.itemsWrapEl.style.height=c*s.params.height/s.params.cols+"px"),s.params.renderExternal?l&&0===l.length&&(s.reachEnd=!0):(s.emit("local::beforeClear vlBeforeClear",s,s.fragment),s.itemsWrapEl.innerHTML="",s.emit("local::itemsBeforeInsert vlItemsBeforeInsert",s,s.fragment),l&&0===l.length?(s.reachEnd=!0,s.params.emptyTemplate&&(s.itemsWrapEl.innerHTML=s.params.emptyTemplate)):s.itemsWrapEl.appendChild(s.fragment),s.emit("local::itemsAfterInsert vlItemsAfterInsert",s,s.fragment)),void 0!==t&&e&&s.$scrollableParentEl.scrollTop(t,0),s.params.renderExternal&&s.params.renderExternal(s,{fromIndex:r,toIndex:i,listHeight:s.listHeight,topPosition:n,items:m}),s.autoHeight&&requestAnimationFrame((()=>{s.setListSize(!0)}))}filterItems(e,t){void 0===t&&(t=!0);const s=this;s.filteredItems=[];for(let t=0;t<e.length;t+=1)s.filteredItems.push(s.items[e[t]]);t&&(s.$scrollableParentEl[0].scrollTop=0),s.update()}resetFilter(){const e=this;e.params.showFilteredItemsOnly?e.filteredItems=[]:(e.filteredItems=null,delete e.filteredItems),e.update()}scrollToItem(e){const t=this;if(e>t.items.length)return!1;let s=0;if(t.dynamicHeight||t.autoHeight)for(let a=0;a<e;a+=1)s+=t.heights[a];else s=e*t.params.height;const a=t.$el[0].offsetTop;return t.render(!0,a+s-parseInt(t.$scrollableParentEl.css("padding-top"),10)),!0}handleScroll(){this.render()}isVisible(){const e=this;return!!(e.el.offsetWidth||e.el.offsetHeight||e.el.getClientRects().length)}handleResize(){const e=this;e.isVisible()&&(e.heightsCalculated=[],e.setListSize(),e.render(!0))}appendItems(e){const t=this;for(let s=0;s<e.length;s+=1)t.items.push(e[s]);t.update()}appendItem(e){this.appendItems([e])}replaceAllItems(e){const t=this;t.items=e,delete t.filteredItems,t.domCache={},t.update()}replaceItem(e,t){const s=this;s.items[e]=t,s.params.cache&&delete s.domCache[e],s.update()}prependItems(e){const t=this;for(let s=e.length-1;s>=0;s-=1)t.items.unshift(e[s]);if(t.params.cache){const s={};Object.keys(t.domCache).forEach((a=>{s[parseInt(a,10)+e.length]=t.domCache[a]})),t.domCache=s}t.update()}prependItem(e){this.prependItems([e])}moveItem(e,t){const s=this,a=e;let l=t;if(a===l)return;const r=s.items.splice(a,1)[0];if(l>=s.items.length?(s.items.push(r),l=s.items.length-1):s.items.splice(l,0,r),s.params.cache){const e={};Object.keys(s.domCache).forEach((t=>{const r=parseInt(t,10),i=a<l?a:l,n=a<l?l:a,o=a<l?-1:1;(r<i||r>n)&&(e[r]=s.domCache[r]),r===i&&(e[n]=s.domCache[r]),r>i&&r<=n&&(e[r+o]=s.domCache[r])})),s.domCache=e}s.update()}insertItemBefore(e,t){const s=this;if(0!==e)if(e>=s.items.length)s.appendItem(t);else{if(s.items.splice(e,0,t),s.params.cache){const t={};Object.keys(s.domCache).forEach((a=>{const l=parseInt(a,10);l>=e&&(t[l+1]=s.domCache[l])})),s.domCache=t}s.update()}else s.prependItem(t)}deleteItems(e){const t=this;let s,a=0;for(let l=0;l<e.length;l+=1){let r=e[l];void 0!==s&&r>s&&(a=-l),r+=a,s=e[l];const i=t.items.splice(r,1)[0];if(t.filteredItems&&t.filteredItems.indexOf(i)>=0&&t.filteredItems.splice(t.filteredItems.indexOf(i),1),t.params.cache){const e={};Object.keys(t.domCache).forEach((s=>{const a=parseInt(s,10);a===r?delete t.domCache[r]:parseInt(s,10)>r?e[a-1]=t.domCache[s]:e[a]=t.domCache[s]})),t.domCache=e}}t.update()}deleteAllItems(){const e=this;e.items=[],delete e.filteredItems,e.params.cache&&(e.domCache={}),e.update()}deleteItem(e){this.deleteItems([e])}clearCache(){this.domCache={}}update(e){const t=this;e&&t.params.cache&&(t.domCache={}),t.heightsCalculated=[],t.setListSize(),t.render(!0)}init(){const e=this;e.attachEvents(),e.setListSize(),e.render()}destroy(){let e=this;e.detachEvents(),e.$el[0].f7VirtualList=null,delete e.$el[0].f7VirtualList,o(e),e=null}}var m={name:"virtualList",static:{VirtualList:h},create(){this.virtualList=i({defaultSelector:".virtual-list",constructor:h,app:this,domProp:"f7VirtualList"})}};if(t){if(e.prototype.modules&&e.prototype.modules[m.name])return;e.use(m),e.instance&&(e.instance.useModuleParams(m,e.instance.params),e.instance.useModule(m))}return m}(Framework7, typeof Framework7AutoInstallComponent === 'undefined' ? undefined : Framework7AutoInstallComponent))
