/**
 * Framework7 7.0.2
 * Full featured mobile HTML framework for building iOS & Android apps
 * https://framework7.io/
 *
 * Copyright 2014-2022 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: April 24, 2022
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Framework7=t()}(this,(function(){"use strict";function e(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function t(r,a){void 0===r&&(r={}),void 0===a&&(a={}),Object.keys(a).forEach((n=>{void 0===r[n]?r[n]=a[n]:e(a[n])&&e(r[n])&&Object.keys(a[n]).length>0&&t(r[n],a[n])}))}const r={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){const e="undefined"!=typeof document?document:{};return t(e,r),e}const n={document:r,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){const e="undefined"!=typeof window?window:{};return t(e,n),e}class s extends Array{constructor(e){"number"==typeof e?super(e):(super(...e||[]),function(e){const t=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=>t,set(e){t.__proto__=e}})}(this))}}function i(e){void 0===e&&(e=[]);const t=[];return e.forEach((e=>{Array.isArray(e)?t.push(...i(e)):t.push(e)})),t}function l(e,t){return Array.prototype.filter.call(e,t)}function c(e,t){const r=o(),n=a();let i=[];if(!t&&e instanceof s)return e;if(!e)return new s(i);if("string"==typeof e){const r=e.trim();if(r.indexOf("<")>=0&&r.indexOf(">")>=0){let e="div";0===r.indexOf("<li")&&(e="ul"),0===r.indexOf("<tr")&&(e="tbody"),0!==r.indexOf("<td")&&0!==r.indexOf("<th")||(e="tr"),0===r.indexOf("<tbody")&&(e="table"),0===r.indexOf("<option")&&(e="select");const t=n.createElement(e);t.innerHTML=r;for(let e=0;e<t.childNodes.length;e+=1)i.push(t.childNodes[e])}else i=function(e,t){if("string"!=typeof e)return[e];const r=[],a=t.querySelectorAll(e);for(let e=0;e<a.length;e+=1)r.push(a[e]);return r}(e.trim(),t||n)}else if(e.nodeType||e===r||e===n)i.push(e);else if(Array.isArray(e)){if(e instanceof s)return e;i=e}return new s(function(e){const t=[];for(let r=0;r<e.length;r+=1)-1===t.indexOf(e[r])&&t.push(e[r]);return t}(i))}c.fn=s.prototype;const u="resize scroll".split(" ");function p(e){return function(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];if(void 0===r[0]){for(let t=0;t<this.length;t+=1)u.indexOf(e)<0&&(e in this[t]?this[t][e]():c(this[t]).trigger(e));return this}return this.on(e,...r)}}const d=p("click"),h=p("blur"),f=p("focus"),m=p("focusin"),g=p("focusout"),v=p("keyup"),b=p("keydown"),y=p("keypress"),w=p("submit"),C=p("change"),k=p("mousedown"),E=p("mousemove"),$=p("mouseup"),x=p("mouseenter"),P=p("mouseleave"),O=p("mouseout"),R=p("mouseover"),T=p("touchstart"),S=p("touchend"),A=p("touchmove"),L=p("resize"),M=p("scroll");var B=Object.freeze({__proto__:null,default:c,$:c,add:function(){const e=this;let t,r;for(var a=arguments.length,n=new Array(a),o=0;o<a;o++)n[o]=arguments[o];for(t=0;t<n.length;t+=1){const a=c(n[t]);for(r=0;r<a.length;r+=1)e.push(a[r])}return e},addClass:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const a=i(t.map((e=>e.split(" "))));return this.forEach((e=>{e.classList.add(...a)})),this},animate:function(e,t){const r=o(),a=this,n={props:Object.assign({},e),params:Object.assign({duration:300,easing:"swing"},t),elements:a,animating:!1,que:[],easingProgress:(e,t)=>"swing"===e?.5-Math.cos(t*Math.PI)/2:"function"==typeof e?e(t):t,stop(){n.frameId&&r.cancelAnimationFrame(n.frameId),n.animating=!1,n.elements.each((e=>{delete e.dom7AnimateInstance})),n.que=[]},done(e){if(n.animating=!1,n.elements.each((e=>{delete e.dom7AnimateInstance})),e&&e(a),n.que.length>0){const e=n.que.shift();n.animate(e[0],e[1])}},animate(e,t){if(n.animating)return n.que.push([e,t]),n;const o=[];n.elements.each(((t,a)=>{let s,i,l,c,u;t.dom7AnimateInstance||(n.elements[a].dom7AnimateInstance=n),o[a]={container:t},Object.keys(e).forEach((n=>{s=r.getComputedStyle(t,null).getPropertyValue(n).replace(",","."),i=parseFloat(s),l=s.replace(i,""),c=parseFloat(e[n]),u=e[n]+l,o[a][n]={initialFullValue:s,initialValue:i,unit:l,finalValue:c,finalFullValue:u,currentValue:i}}))}));let s,i,l=null,c=0,u=0,p=!1;return n.animating=!0,n.frameId=r.requestAnimationFrame((function d(){let h,f;s=(new Date).getTime(),p||(p=!0,t.begin&&t.begin(a)),null===l&&(l=s),t.progress&&t.progress(a,Math.max(Math.min((s-l)/t.duration,1),0),l+t.duration-s<0?0:l+t.duration-s,l),o.forEach((r=>{const a=r;i||a.done||Object.keys(e).forEach((r=>{if(i||a.done)return;h=Math.max(Math.min((s-l)/t.duration,1),0),f=n.easingProgress(t.easing,h);const{initialValue:p,finalValue:d,unit:m}=a[r];a[r].currentValue=p+f*(d-p);const g=a[r].currentValue;(d>p&&g>=d||d<p&&g<=d)&&(a.container.style[r]=d+m,u+=1,u===Object.keys(e).length&&(a.done=!0,c+=1),c===o.length&&(i=!0)),i?n.done(t.complete):a.container.style[r]=g+m}))})),i||(n.frameId=r.requestAnimationFrame(d))})),n}};if(0===n.elements.length)return a;let s;for(let e=0;e<n.elements.length;e+=1)n.elements[e].dom7AnimateInstance?s=n.elements[e].dom7AnimateInstance:n.elements[e].dom7AnimateInstance=n;return s||(s=n),"stop"===e?s.stop():s.animate(n.props,n.params),a},animationEnd:function(e){const t=this;return e&&t.on("animationend",(function r(a){a.target===this&&(e.call(this,a),t.off("animationend",r))})),this},append:function(){let e;const t=a();for(let r=0;r<arguments.length;r+=1){e=r<0||arguments.length<=r?void 0:arguments[r];for(let r=0;r<this.length;r+=1)if("string"==typeof e){const a=t.createElement("div");for(a.innerHTML=e;a.firstChild;)this[r].appendChild(a.firstChild)}else if(e instanceof s)for(let t=0;t<e.length;t+=1)this[r].appendChild(e[t]);else this[r].appendChild(e)}return this},appendTo:function(e){return c(e).append(this),this},attr:function(e,t){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let r=0;r<this.length;r+=1)if(2===arguments.length)this[r].setAttribute(e,t);else for(const t in e)this[r][t]=e[t],this[r].setAttribute(t,e[t]);return this},blur:h,change:C,children:function(e){const t=[];for(let r=0;r<this.length;r+=1){const a=this[r].children;for(let r=0;r<a.length;r+=1)e&&!c(a[r]).is(e)||t.push(a[r])}return c(t)},click:d,closest:function(e){let t=this;return void 0===e?c([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},css:function(e,t){const r=o();let a;if(1===arguments.length){if("string"!=typeof e){for(a=0;a<this.length;a+=1)for(const t in e)this[a].style[t]=e[t];return this}if(this[0])return r.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e){for(a=0;a<this.length;a+=1)this[a].style[e]=t;return this}return this},data:function(e,t){let r;if(void 0===t){if(r=this[0],!r)return;if(r.dom7ElementDataStorage&&e in r.dom7ElementDataStorage)return r.dom7ElementDataStorage[e];const t=r.getAttribute(`data-${e}`);return t||void 0}for(let a=0;a<this.length;a+=1)r=this[a],r.dom7ElementDataStorage||(r.dom7ElementDataStorage={}),r.dom7ElementDataStorage[e]=t;return this},dataset:function(){const e=this[0];if(!e)return;const t={};if(e.dataset)for(const r in e.dataset)t[r]=e.dataset[r];else for(let a=0;a<e.attributes.length;a+=1){const n=e.attributes[a];n.name.indexOf("data-")>=0&&(t[(r=n.name.split("data-")[1],r.toLowerCase().replace(/-(.)/g,((e,t)=>t.toUpperCase())))]=n.value)}var r;for(const e in t)"false"===t[e]?t[e]=!1:"true"===t[e]?t[e]=!0:parseFloat(t[e])===1*t[e]&&(t[e]*=1);return t},detach:function(){return this.remove()},each:function(e){return e?(this.forEach(((t,r)=>{e.apply(t,[t,r])})),this):this},empty:function(){for(let e=0;e<this.length;e+=1){const t=this[e];if(1===t.nodeType){for(let e=0;e<t.childNodes.length;e+=1)t.childNodes[e].parentNode&&t.childNodes[e].parentNode.removeChild(t.childNodes[e]);t.textContent=""}}return this},eq:function(e){if(void 0===e)return this;const t=this.length;if(e>t-1)return c([]);if(e<0){const r=t+e;return c(r<0?[]:[this[r]])}return c([this[e]])},filter:function(e){return c(l(this,e))},find:function(e){const t=[];for(let r=0;r<this.length;r+=1){const a=this[r].querySelectorAll(e);for(let e=0;e<a.length;e+=1)t.push(a[e])}return c(t)},focus:f,focusin:m,focusout:g,hasClass:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const a=i(t.map((e=>e.split(" "))));return l(this,(e=>a.filter((t=>e.classList.contains(t))).length>0)).length>0},height:function(){const e=o();return this[0]===e?e.innerHeight:this.length>0?parseFloat(this.css("height")):null},hide:function(){for(let e=0;e<this.length;e+=1)this[e].style.display="none";return this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(let t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},insertAfter:function(e){const t=c(e);for(let e=0;e<this.length;e+=1)if(1===t.length)t[0].parentNode.insertBefore(this[e],t[0].nextSibling);else if(t.length>1)for(let r=0;r<t.length;r+=1)t[r].parentNode.insertBefore(this[e].cloneNode(!0),t[r].nextSibling)},insertBefore:function(e){const t=c(e);for(let e=0;e<this.length;e+=1)if(1===t.length)t[0].parentNode.insertBefore(this[e],t[0]);else if(t.length>1)for(let r=0;r<t.length;r+=1)t[r].parentNode.insertBefore(this[e].cloneNode(!0),t[r])},is:function(e){const t=o(),r=a(),n=this[0];let i,l;if(!n||void 0===e)return!1;if("string"==typeof e){if(n.matches)return n.matches(e);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(e);if(n.msMatchesSelector)return n.msMatchesSelector(e);for(i=c(e),l=0;l<i.length;l+=1)if(i[l]===n)return!0;return!1}if(e===r)return n===r;if(e===t)return n===t;if(e.nodeType||e instanceof s){for(i=e.nodeType?[e]:e,l=0;l<i.length;l+=1)if(i[l]===n)return!0;return!1}return!1},keydown:b,keypress:y,keyup:v,mousedown:k,mouseenter:x,mouseleave:P,mousemove:E,mouseout:O,mouseover:R,mouseup:$,next:function(e){return this.length>0?e?this[0].nextElementSibling&&c(this[0].nextElementSibling).is(e)?c([this[0].nextElementSibling]):c([]):this[0].nextElementSibling?c([this[0].nextElementSibling]):c([]):c([])},nextAll:function(e){const t=[];let r=this[0];if(!r)return c([]);for(;r.nextElementSibling;){const a=r.nextElementSibling;e?c(a).is(e)&&t.push(a):t.push(a),r=a}return c(t)},off:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let[a,n,o,s]=t;"function"==typeof t[1]&&([a,o,s]=t,n=void 0),s||(s=!1);const i=a.split(" ");for(let e=0;e<i.length;e+=1){const t=i[e];for(let e=0;e<this.length;e+=1){const r=this[e];let a;if(!n&&r.dom7Listeners?a=r.dom7Listeners[t]:n&&r.dom7LiveListeners&&(a=r.dom7LiveListeners[t]),a&&a.length)for(let e=a.length-1;e>=0;e-=1){const n=a[e];o&&n.listener===o||o&&n.listener&&n.listener.dom7proxy&&n.listener.dom7proxy===o?(r.removeEventListener(t,n.proxyListener,s),a.splice(e,1)):o||(r.removeEventListener(t,n.proxyListener,s),a.splice(e,1))}}}return this},offset:function(){if(this.length>0){const e=o(),t=a(),r=this[0],n=r.getBoundingClientRect(),s=t.body,i=r.clientTop||s.clientTop||0,l=r.clientLeft||s.clientLeft||0,c=r===e?e.scrollY:r.scrollTop,u=r===e?e.scrollX:r.scrollLeft;return{top:n.top+c-i,left:n.left+u-l}}return null},on:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let[a,n,o,s]=t;function i(e){const t=e.target;if(!t)return;const r=e.target.dom7EventData||[];if(r.indexOf(e)<0&&r.unshift(e),c(t).is(n))o.apply(t,r);else{const e=c(t).parents();for(let t=0;t<e.length;t+=1)c(e[t]).is(n)&&o.apply(e[t],r)}}function l(e){const t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),o.apply(this,t)}"function"==typeof t[1]&&([a,o,s]=t,n=void 0),s||(s=!1);const u=a.split(" ");let p;for(let e=0;e<this.length;e+=1){const t=this[e];if(n)for(p=0;p<u.length;p+=1){const e=u[p];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:o,proxyListener:i}),t.addEventListener(e,i,s)}else for(p=0;p<u.length;p+=1){const e=u[p];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:o,proxyListener:l}),t.addEventListener(e,l,s)}}return this},once:function(){const e=this;for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];let[n,o,s,i]=r;function l(){for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];s.apply(this,r),e.off(n,o,l,i),l.dom7proxy&&delete l.dom7proxy}return"function"==typeof r[1]&&([n,s,i]=r,o=void 0),l.dom7proxy=s,e.on(n,o,l,i)},outerHeight:function(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},outerWidth:function(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},parent:function(e){const t=[];for(let r=0;r<this.length;r+=1)null!==this[r].parentNode&&(e?c(this[r].parentNode).is(e)&&t.push(this[r].parentNode):t.push(this[r].parentNode));return c(t)},parents:function(e){const t=[];for(let r=0;r<this.length;r+=1){let a=this[r].parentNode;for(;a;)e?c(a).is(e)&&t.push(a):t.push(a),a=a.parentNode}return c(t)},prepend:function(e){const t=a();let r,n;for(r=0;r<this.length;r+=1)if("string"==typeof e){const a=t.createElement("div");for(a.innerHTML=e,n=a.childNodes.length-1;n>=0;n-=1)this[r].insertBefore(a.childNodes[n],this[r].childNodes[0])}else if(e instanceof s)for(n=0;n<e.length;n+=1)this[r].insertBefore(e[n],this[r].childNodes[0]);else this[r].insertBefore(e,this[r].childNodes[0]);return this},prependTo:function(e){return c(e).prepend(this),this},prev:function(e){if(this.length>0){const t=this[0];return e?t.previousElementSibling&&c(t.previousElementSibling).is(e)?c([t.previousElementSibling]):c([]):t.previousElementSibling?c([t.previousElementSibling]):c([])}return c([])},prevAll:function(e){const t=[];let r=this[0];if(!r)return c([]);for(;r.previousElementSibling;){const a=r.previousElementSibling;e?c(a).is(e)&&t.push(a):t.push(a),r=a}return c(t)},prop:function(e,t){if(1!==arguments.length||"string"!=typeof e){for(let r=0;r<this.length;r+=1)if(2===arguments.length)this[r][e]=t;else for(const t in e)this[r][t]=e[t];return this}return this[0]?this[0][e]:this},remove:function(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this},removeAttr:function(e){for(let t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},removeClass:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const a=i(t.map((e=>e.split(" "))));return this.forEach((e=>{e.classList.remove(...a)})),this},removeData:function(e){for(let t=0;t<this.length;t+=1){const r=this[t];r.dom7ElementDataStorage&&r.dom7ElementDataStorage[e]&&(r.dom7ElementDataStorage[e]=null,delete r.dom7ElementDataStorage[e])}},resize:L,scroll:M,scrollLeft:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let[a,n,o,s]=t;3===t.length&&"function"==typeof o&&([a,n,s,o]=t);const i=this;return void 0===a?i.length>0?i[0].scrollLeft:null:i.scrollTo(a,void 0,n,o,s)},scrollTo:function(){const e=o();for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];let[n,s,i,l,c]=r;return 4===r.length&&"function"==typeof l&&(c=l,[n,s,i,c,l]=r),void 0===l&&(l="swing"),this.each((function(){const t=this;let r,a,o,u,p,d,h,f,m=s>0||0===s,g=n>0||0===n;if(void 0===l&&(l="swing"),m&&(r=t.scrollTop,i||(t.scrollTop=s)),g&&(a=t.scrollLeft,i||(t.scrollLeft=n)),!i)return;m&&(o=t.scrollHeight-t.offsetHeight,p=Math.max(Math.min(s,o),0)),g&&(u=t.scrollWidth-t.offsetWidth,d=Math.max(Math.min(n,u),0));let v=null;m&&p===r&&(m=!1),g&&d===a&&(g=!1),e.requestAnimationFrame((function n(o){void 0===o&&(o=(new Date).getTime()),null===v&&(v=o);const s=Math.max(Math.min((o-v)/i,1),0),u="linear"===l?s:.5-Math.cos(s*Math.PI)/2;let b;m&&(h=r+u*(p-r)),g&&(f=a+u*(d-a)),m&&p>r&&h>=p&&(t.scrollTop=p,b=!0),m&&p<r&&h<=p&&(t.scrollTop=p,b=!0),g&&d>a&&f>=d&&(t.scrollLeft=d,b=!0),g&&d<a&&f<=d&&(t.scrollLeft=d,b=!0),b?c&&c():(m&&(t.scrollTop=h),g&&(t.scrollLeft=f),e.requestAnimationFrame(n))}))}))},scrollTop:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let[a,n,o,s]=t;3===t.length&&"function"==typeof o&&([a,n,s,o]=t);const i=this;return void 0===a?i.length>0?i[0].scrollTop:null:i.scrollTo(void 0,a,n,o,s)},show:function(){const e=o();for(let t=0;t<this.length;t+=1){const r=this[t];"none"===r.style.display&&(r.style.display=""),"none"===e.getComputedStyle(r,null).getPropertyValue("display")&&(r.style.display="block")}return this},siblings:function(e){return this.nextAll(e).add(this.prevAll(e))},stop:function(){const e=this;for(let t=0;t<e.length;t+=1)e[t].dom7AnimateInstance&&e[t].dom7AnimateInstance.stop()},styles:function(){const e=o();return this[0]?e.getComputedStyle(this[0],null):{}},submit:w,text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(let t=0;t<this.length;t+=1)this[t].textContent=e;return this},toggleClass:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const a=i(t.map((e=>e.split(" "))));this.forEach((e=>{a.forEach((t=>{e.classList.toggle(t)}))}))},touchend:S,touchmove:A,touchstart:T,transform:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?`${e}ms`:e;return this},transitionEnd:function(e){const t=this;return e&&t.on("transitionend",(function r(a){a.target===this&&(e.call(this,a),t.off("transitionend",r))})),this},trigger:function(){const e=o();for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];const n=r[0].split(" "),s=r[1];for(let t=0;t<n.length;t+=1){const a=n[t];for(let t=0;t<this.length;t+=1){const n=this[t];if(e.CustomEvent){const t=new e.CustomEvent(a,{detail:s,bubbles:!0,cancelable:!0});n.dom7EventData=r.filter(((e,t)=>t>0)),n.dispatchEvent(t),n.dom7EventData=[],delete n.dom7EventData}}}return this},val:function(e){if(void 0===e){const e=this[0];if(!e)return;if(e.multiple&&"select"===e.nodeName.toLowerCase()){const t=[];for(let r=0;r<e.selectedOptions.length;r+=1)t.push(e.selectedOptions[r].value);return t}return e.value}for(let t=0;t<this.length;t+=1){const r=this[t];if(Array.isArray(e)&&r.multiple&&"select"===r.nodeName.toLowerCase())for(let t=0;t<r.options.length;t+=1)r.options[t].selected=e.indexOf(r.options[t].value)>=0;else r.value=e}return this},value:function(e){return this.val(e)},width:function(){const e=o();return this[0]===e?e.innerWidth:this.length>0?parseFloat(this.css("width")):null}});Object.keys(B).forEach((e=>{"$"!==e&&(c.fn[e]=B[e])}));var N=c;let H=0;function _(e,t){void 0===e&&(e="xxxxxxxxxx"),void 0===t&&(t="0123456789abcdef");const r=t.length;return e.replace(/x/g,(()=>t[Math.floor(Math.random()*r)]))}const D='\n  <span class="preloader-inner">\n    <svg viewBox="0 0 36 36">\n      <circle cx="18" cy="18" r="16"></circle>\n    </svg>\n  </span>\n'.trim(),q=`\n  <span class="preloader-inner">\n    ${[0,1,2,3,4,5,6,7].map((()=>'<span class="preloader-inner-line"></span>')).join("")}\n  </span>\n`.trim();function j(e){let t;return e.split("").map(((e,r)=>e.match(/[A-Z]/)&&0!==r&&!t?(t=!0,`:${e.toLowerCase()}`):e.toLowerCase())).join("")}function I(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}function U(e){return o().requestAnimationFrame(e)}function z(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function V(e){return U((()=>{U(e)}))}function W(){return Date.now()}function F(e){const t=o(),r={};let a,n,s,i,l=e||t.location.href;if("string"==typeof l&&l.length)for(l=l.indexOf("?")>-1?l.replace(/\S*\?/,""):"",n=l.split("&").filter((e=>""!==e)),i=n.length,a=0;a<i;a+=1)s=n[a].replace(/#\S+/g,"").split("="),r[decodeURIComponent(s[0])]=void 0===s[1]?void 0:decodeURIComponent(s.slice(1).join("="))||"";return r}function X(e,t){if(void 0===t&&(t=[]),"string"==typeof e)return e;const r=[];let a;function n(e){if(t.length>0){let r="";for(let e=0;e<t.length;e+=1)r+=0===e?t[e]:`[${encodeURIComponent(t[e])}]`;return`${r}[${encodeURIComponent(e)}]`}return encodeURIComponent(e)}function o(e){return encodeURIComponent(e)}return Object.keys(e).forEach((s=>{let i;if(Array.isArray(e[s])){i=[];for(let r=0;r<e[s].length;r+=1)Array.isArray(e[s][r])||"object"!=typeof e[s][r]?i.push(`${n(s)}[]=${o(e[s][r])}`):(a=t.slice(),a.push(s),a.push(String(r)),i.push(X(e[s][r],a)));i.length>0&&r.push(i.join("&"))}else null===e[s]||""===e[s]?r.push(`${n(s)}=`):"object"==typeof e[s]?(a=t.slice(),a.push(s),i=X(e[s],a),""!==i&&r.push(i)):void 0!==e[s]&&""!==e[s]?r.push(`${n(s)}=${o(e[s])}`):""===e[s]&&r.push(n(s))})),r.join("&")}function Q(e){return"object"==typeof e&&null!==e&&e.constructor&&e.constructor===Object}function Y(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];const a=t[0];t.splice(0,1);const n=t;for(let e=0;e<n.length;e+=1){const r=t[e];if(null!=r){const e=Object.keys(Object(r));for(let t=0,n=e.length;t<n;t+=1){const n=e[t],o=Object.getOwnPropertyDescriptor(r,n);void 0!==o&&o.enumerable&&(a[n]=r[n])}}}return a}function J(){let e,t,r=!0;for(var a=arguments.length,n=new Array(a),o=0;o<a;o++)n[o]=arguments[o];"boolean"==typeof n[0]?(r=n[0],e=n[1],n.splice(0,2),t=n):(e=n[0],n.splice(0,1),t=n);for(let a=0;a<t.length;a+=1){const t=n[a];if(null!=t){const a=Object.keys(Object(t));for(let n=0,o=a.length;n<o;n+=1){const o=a[n],s=Object.getOwnPropertyDescriptor(t,o);void 0!==s&&s.enumerable&&(r?Q(e[o])&&Q(t[o])?J(e[o],t[o]):!Q(e[o])&&Q(t[o])?(e[o]={},J(e[o],t[o])):e[o]=t[o]:e[o]=t[o])}}}return e}function G(e){const t=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,((e,t,r,a)=>t+t+r+r+a+a)),r=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return r?r.slice(1).map((e=>parseInt(e,16))):null}function K(e,t,r){const a=[e,t,r].map((e=>{const t=e.toString(16);return 1===t.length?`0${t}`:t})).join("");return`#${a}`}function Z(e,t,r){e/=255,t/=255,r/=255;const a=Math.max(e,t,r),n=Math.min(e,t,r),o=a-n;let s;0===o?s=0:a===e?s=(t-r)/o%6:a===t?s=(r-e)/o+2:a===r&&(s=(e-t)/o+4);const i=(n+a)/2;return s<0&&(s=6+s),[60*s,0===o?0:o/(1-Math.abs(2*i-1)),i]}function ee(e,t,r){const a=(1-Math.abs(2*r-1))*t,n=e/60,o=a*(1-Math.abs(n%2-1));let s;Number.isNaN(e)||void 0===e?s=[0,0,0]:n<=1?s=[a,o,0]:n<=2?s=[o,a,0]:n<=3?s=[0,a,o]:n<=4?s=[0,o,a]:n<=5?s=[o,0,a]:n<=6&&(s=[a,0,o]);const i=r-a/2;return s.map((e=>Math.max(0,Math.min(255,Math.round(255*(e+i))))))}function te(e,t){Object.keys(t).forEach((r=>{Q(t[r])&&Object.keys(t[r]).forEach((a=>{"function"==typeof t[r][a]&&(t[r][a]=t[r][a].bind(e))})),e[r]=t[r]}))}function re(){const e=[];for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return r.forEach((t=>{Array.isArray(t)?e.push(...re(...t)):e.push(t)})),e}var ae=Object.freeze({__proto__:null,uniqueNumber:function(){return H+=1,H},id:_,mdPreloaderContent:D,iosPreloaderContent:q,auroraPreloaderContent:'\n  <span class="preloader-inner">\n    <span class="preloader-inner-circle"></span>\n  </span>\n',eventNameToColonCase:j,deleteProps:I,requestAnimationFrame:U,cancelAnimationFrame:function(e){return o().cancelAnimationFrame(e)},nextTick:z,nextFrame:V,now:W,parseUrlQuery:F,getTranslate:function(e,t){void 0===t&&(t="x");const r=o();let a,n,s;const i=r.getComputedStyle(e,null);return r.WebKitCSSMatrix?(n=i.transform||i.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map((e=>e.replace(",","."))).join(", ")),s=new r.WebKitCSSMatrix("none"===n?"":n)):(s=i.MozTransform||i.OTransform||i.MsTransform||i.msTransform||i.transform||i.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),a=s.toString().split(",")),"x"===t&&(n=r.WebKitCSSMatrix?s.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),"y"===t&&(n=r.WebKitCSSMatrix?s.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5])),n||0},serializeObject:X,isObject:Q,merge:Y,extend:J,colorHexToRgb:G,colorRgbToHex:K,colorRgbToHsl:Z,colorHslToRgb:ee,colorHsbToHsl:function(e,t,r){const a={h:e,s:0,l:0},n=t,o=r;return a.l=(2-n)*o/2,a.s=a.l&&a.l<1?n*o/(a.l<.5?2*a.l:2-2*a.l):a.s,[a.h,a.s,a.l]},colorHslToHsb:function(e,t,r){const a={h:e,s:0,b:0},n=r,o=t*(n<.5?n:1-n);return a.b=n+o,a.s=n>0?2*o/a.b:a.s,[a.h,a.s,a.b]},colorThemeCSSProperties:function(){let e,t;for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];if(1===a.length?(e=a[0],t=G(e)):3===a.length&&(t=a,e=K(...t)),!t)return{};const o=Z(...t),s=[o[0],o[1],Math.max(0,o[2]-.08)],i=[o[0],o[1],Math.max(0,o[2]+.08)],l=K(...ee(...s)),c=K(...ee(...i));return{"--f7-theme-color":e,"--f7-theme-color-rgb":t.join(", "),"--f7-theme-color-shade":l,"--f7-theme-color-tint":c}},bindMethods:te,flattenArray:re});let ne,oe;function se(){return ne||(ne=function(){const e=o(),t=a();return{touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch),pointerEvents:!!e.PointerEvent&&"maxTouchPoints"in e.navigator&&e.navigator.maxTouchPoints>=0,passiveListener:function(){let t=!1;try{const r=Object.defineProperty({},"passive",{get(){t=!0}});e.addEventListener("testPassiveListener",null,r)}catch(e){}return t}(),intersectionObserver:"IntersectionObserver"in e}}()),ne}function ie(e,t){return void 0===e&&(e={}),oe&&!t||(oe=function(e){let{userAgent:t}=void 0===e?{}:e;const r=se(),a=o(),n=a.navigator.platform,s=t||a.navigator.userAgent,i={ios:!1,android:!1,androidChrome:!1,desktop:!1,iphone:!1,ipod:!1,ipad:!1,edge:!1,ie:!1,firefox:!1,macos:!1,windows:!1,cordova:!!a.cordova,electron:!1,capacitor:!!a.Capacitor,nwjs:!1},l=a.screen.width,c=a.screen.height,u=s.match(/(Android);?[\s\/]+([\d.]+)?/);let p=s.match(/(iPad).*OS\s([\d_]+)/);const d=s.match(/(iPod)(.*OS\s([\d_]+))?/),h=!p&&s.match(/(iPhone\sOS|iOS|iPhone;\sCPU\sOS)\s([\d_]+)/),f=s.indexOf("MSIE ")>=0||s.indexOf("Trident/")>=0,m=s.indexOf("Edge/")>=0,g=s.indexOf("Gecko/")>=0&&s.indexOf("Firefox/")>=0,v="Win32"===n,b=s.toLowerCase().indexOf("electron")>=0,y="undefined"!=typeof nw&&"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.nw;let w="MacIntel"===n;return!p&&w&&r.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${c}`)>=0&&(p=s.match(/(Version)\/([\d.]+)/),p||(p=[0,1,"13_0_0"]),w=!1),i.ie=f,i.edge=m,i.firefox=g,u&&(i.os="android",i.osVersion=u[2],i.android=!0,i.androidChrome=s.toLowerCase().indexOf("chrome")>=0),(p||h||d)&&(i.os="ios",i.ios=!0),h&&!d&&(i.osVersion=h[2].replace(/_/g,"."),i.iphone=!0),p&&(i.osVersion=p[2].replace(/_/g,"."),i.ipad=!0),d&&(i.osVersion=d[3]?d[3].replace(/_/g,"."):null,i.ipod=!0),i.ios&&i.osVersion&&s.indexOf("Version/")>=0&&"10"===i.osVersion.split(".")[0]&&(i.osVersion=s.toLowerCase().split("version/")[1].split(" ")[0]),i.webView=!(!(h||p||d)||!s.match(/.*AppleWebKit(?!.*Safari)/i)&&!a.navigator.standalone)||a.matchMedia&&a.matchMedia("(display-mode: standalone)").matches,i.webview=i.webView,i.standalone=i.webView,i.desktop=!(i.ios||i.android)||b||y,i.desktop&&(i.electron=b,i.nwjs=y,i.macos=w,i.windows=v,i.macos&&(i.os="macos"),i.windows&&(i.os="windows")),i.pixelRatio=a.devicePixelRatio||1,i.prefersColorScheme=function(){let e;return a.matchMedia&&a.matchMedia("(prefers-color-scheme: light)").matches&&(e="light"),a.matchMedia&&a.matchMedia("(prefers-color-scheme: dark)").matches&&(e="dark"),e},i}(e)),oe}class le{constructor(e){void 0===e&&(e=[]);this.eventsParents=e,this.eventsListeners={}}on(e,t,r){const a=this;if("function"!=typeof t)return a;const n=r?"unshift":"push";return e.split(" ").forEach((e=>{a.eventsListeners[e]||(a.eventsListeners[e]=[]),a.eventsListeners[e][n](t)})),a}once(e,t,r){const a=this;if("function"!=typeof t)return a;function n(){a.off(e,n),n.f7proxy&&delete n.f7proxy;for(var r=arguments.length,o=new Array(r),s=0;s<r;s++)o[s]=arguments[s];t.apply(a,o)}return n.f7proxy=t,a.on(e,n,r)}off(e,t){const r=this;return r.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?r.eventsListeners[e]=[]:r.eventsListeners[e]&&r.eventsListeners[e].forEach(((a,n)=>{(a===t||a.f7proxy&&a.f7proxy===t)&&r.eventsListeners[e].splice(n,1)}))})),r):r}emit(){const e=this;if(!e.eventsListeners)return e;let t,r,a,n;for(var o=arguments.length,s=new Array(o),i=0;i<o;i++)s[i]=arguments[i];"string"==typeof s[0]||Array.isArray(s[0])?(t=s[0],r=s.slice(1,s.length),a=e,n=e.eventsParents):(t=s[0].events,r=s[0].data,a=s[0].context||e,n=s[0].local?[]:s[0].parents||e.eventsParents);const l=Array.isArray(t)?t:t.split(" "),c=l.map((e=>e.replace("local::",""))),u=l.filter((e=>e.indexOf("local::")<0));return c.forEach((t=>{if(e.eventsListeners&&e.eventsListeners[t]){const n=[];e.eventsListeners[t].forEach((e=>{n.push(e)})),n.forEach((e=>{e.apply(a,r)}))}})),n&&n.length>0&&n.forEach((e=>{e.emit(u,...r)})),e}}class ce extends le{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t=[]),super(t);const r=this;r.params=e,r.params&&r.params.on&&Object.keys(r.params.on).forEach((e=>{r.on(e,r.params.on[e])}))}useModuleParams(e,t){if(e.params){const r={};Object.keys(e.params).forEach((e=>{void 0!==t[e]&&(r[e]=J({},t[e]))})),J(t,e.params),Object.keys(r).forEach((e=>{J(t[e],r[e])}))}}useModulesParams(e){const t=this;t.modules&&Object.keys(t.modules).forEach((r=>{const a=t.modules[r];a.params&&J(e,a.params)}))}useModule(e,t){void 0===e&&(e=""),void 0===t&&(t={});const r=this;if(!r.modules)return;const a="string"==typeof e?r.modules[e]:e;a&&(a.instance&&Object.keys(a.instance).forEach((e=>{const t=a.instance[e];r[e]="function"==typeof t?t.bind(r):t})),a.on&&r.on&&Object.keys(a.on).forEach((e=>{r.on(e,a.on[e])})),a.vnode&&(r.vnodeHooks||(r.vnodeHooks={}),Object.keys(a.vnode).forEach((e=>{Object.keys(a.vnode[e]).forEach((t=>{const n=a.vnode[e][t];r.vnodeHooks[t]||(r.vnodeHooks[t]={}),r.vnodeHooks[t][e]||(r.vnodeHooks[t][e]=[]),r.vnodeHooks[t][e].push(n.bind(r))}))}))),a.create&&a.create.bind(r)(t))}useModules(e){void 0===e&&(e={});const t=this;t.modules&&Object.keys(t.modules).forEach((r=>{const a=e[r]||{};t.useModule(r,a)}))}static set components(e){this.use&&this.use(e)}static installModule(e){const t=this;t.prototype.modules||(t.prototype.modules={});const r=e.name||`${Object.keys(t.prototype.modules).length}_${W()}`;if(t.prototype.modules[r]=e,e.proto&&Object.keys(e.proto).forEach((r=>{t.prototype[r]=e.proto[r]})),e.static&&Object.keys(e.static).forEach((r=>{t[r]=e.static[r]})),e.install){for(var a=arguments.length,n=new Array(a>1?a-1:0),o=1;o<a;o++)n[o-1]=arguments[o];e.install.apply(t,n)}return t}static use(e){const t=this;if(Array.isArray(e))return e.forEach((e=>t.installModule(e))),t;for(var r=arguments.length,a=new Array(r>1?r-1:0),n=1;n<r;n++)a[n-1]=arguments[n];return t.installModule(e,...a)}}function ue(e){void 0===e&&(e={});const{defaultSelector:t,constructor:r,domProp:a,app:n,addMethods:o}=e,s={create(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];return n?new r(n,...t):new r(...t)},get(e){if(void 0===e&&(e=t),e instanceof r)return e;const n=N(e);return 0!==n.length?n[0][a]:void 0},destroy(e){const t=s.get(e);if(t&&t.destroy)return t.destroy()}};return o&&Array.isArray(o)&&o.forEach((e=>{s[e]=function(r){void 0===r&&(r=t);const a=s.get(r);for(var n=arguments.length,o=new Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];if(a&&a[e])return a[e](...o)}})),s}const pe=[];class de extends ce{constructor(e){if(void 0===e&&(e={}),super(e),de.instance&&"undefined"!=typeof window)throw new Error("Framework7 is already initialized and can't be initialized more than once");const t=ie({userAgent:e.userAgent||void 0}),r=se(),n=J({},e),s=this;s.device=t,s.support=r;const i=o(),l=a();de.instance=s;const c={version:"1.0.0",id:"io.framework7.myapp",el:"body",theme:"auto",language:i.navigator.language,routes:[],name:"Framework7",lazyModulesPath:null,initOnDeviceReady:!0,init:!0,autoDarkMode:!1,iosTranslucentBars:!0,iosTranslucentModals:!0,component:void 0,componentUrl:void 0,userAgent:null,url:null};return s.useModulesParams(c),s.params=J(c,e),J(s,{id:s.params.id,name:s.params.name,version:s.params.version,routes:s.params.routes,language:s.params.language,theme:"auto"===s.params.theme?t.ios?"ios":t.desktop&&t.electron?"aurora":"md":s.params.theme,passedParams:n,online:i.navigator.onLine}),e.store&&(s.params.store=e.store),s.$el&&s.$el[0]&&(s.$el[0].f7=s),s.useModules(),s.initStore(),s.params.init&&(t.cordova&&s.params.initOnDeviceReady?N(l).on("deviceready",(()=>{s.init()})):s.init()),s}mount(e){const t=this,r=o(),n=a(),s=N(e||t.params.el).eq(0);t.$el=s,t.$el&&t.$el[0]&&(t.el=t.$el[0],t.el.f7=t,t.rtl="rtl"===s.css("direction"));const i="(prefers-color-scheme: dark)",l="(prefers-color-scheme: light)";t.mq={},r.matchMedia&&(t.mq.dark=r.matchMedia(i),t.mq.light=r.matchMedia(l)),t.colorSchemeListener=function(e){let{matches:r,media:a}=e;if(!r)return;const o=n.querySelector("html");a===i?(o.classList.add("dark"),t.darkMode=!0,t.emit("darkModeChange",!0)):a===l&&(o.classList.remove("dark"),t.darkMode=!1,t.emit("darkModeChange",!1))},t.emit("mount")}initStore(){const e=this;void 0!==e.params.store&&e.params.store.__store?e.store=e.params.store:e.store=e.createStore(e.params.store)}enableAutoDarkMode(){const e=o(),t=a();if(!e.matchMedia)return;const r=this,n=t.querySelector("html");r.mq.dark&&r.mq.light&&(r.mq.dark.addListener(r.colorSchemeListener),r.mq.light.addListener(r.colorSchemeListener)),r.mq.dark&&r.mq.dark.matches?(n.classList.add("dark"),r.darkMode=!0,r.emit("darkModeChange",!0)):r.mq.light&&r.mq.light.matches&&(n.classList.remove("dark"),r.darkMode=!1,r.emit("darkModeChange",!1))}disableAutoDarkMode(){if(!o().matchMedia)return;const e=this;e.mq.dark&&e.mq.dark.removeListener(e.colorSchemeListener),e.mq.light&&e.mq.light.removeListener(e.colorSchemeListener)}initAppComponent(e){const t=this;t.router.componentLoader(t.params.component,t.params.componentUrl,{componentOptions:{el:t.$el[0]}},(r=>{t.$el=N(r),t.$el[0].f7=t,t.$elComponent=r.f7Component,t.el=t.$el[0],e&&e()}),(()=>{}))}init(e){const t=this;t.mount(e);const r=()=>{if(t.initialized)return;t.$el.addClass("framework7-initializing"),t.rtl&&N("html").attr("dir","rtl"),t.params.autoDarkMode&&t.enableAutoDarkMode();const e=o();e.addEventListener("offline",(()=>{t.online=!1,t.emit("offline"),t.emit("connection",!1)})),e.addEventListener("online",(()=>{t.online=!0,t.emit("online"),t.emit("connection",!0)})),t.$el.addClass("framework7-root"),N("html").removeClass("ios md aurora").addClass(t.theme);const r=t.device;t.params.iosTranslucentBars&&"ios"===t.theme&&r.ios&&N("html").addClass("ios-translucent-bars"),t.params.iosTranslucentModals&&"ios"===t.theme&&r.ios&&N("html").addClass("ios-translucent-modals"),V((()=>{t.$el.removeClass("framework7-initializing")})),t.initialized=!0,t.emit("init")};return t.params.component||t.params.componentUrl?t.initAppComponent((()=>{r()})):r(),t}loadModule(){return de.loadModule(...arguments)}loadModules(){return de.loadModules(...arguments)}getVnodeHooks(e,t){const r=this;return r.vnodeHooks&&r.vnodeHooks[e]&&r.vnodeHooks[e][t]||[]}get $(){return N}static get Dom7(){return N}static get $(){return N}static get device(){return ie()}static get support(){return se()}static get Class(){return ce}static get Events(){return le}}de.ModalMethods=function(e){void 0===e&&(e={});const{defaultSelector:t,constructor:r,app:a}=e,n=J(ue({defaultSelector:t,constructor:r,app:a,domProp:"f7Modal"}),{open(e,t,n){let o=N(e);if(o.length>1&&n){const e=N(n).parents(".page");e.length&&o.each((t=>{const r=N(t);r.parents(e)[0]===e[0]&&(o=r)}))}if(o.length>1&&(o=o.eq(o.length-1)),!o.length)return;let s=o[0].f7Modal;if(!s){const e=o.dataset();s=new r(a,{el:o,...e})}return s.open(t)},close(e,n,o){void 0===e&&(e=t);let s=N(e);if(!s.length)return;if(s.length>1){let e;if(o){const t=N(o);t.length&&(e=t.parents(s))}s=e&&e.length>0?e:s.eq(s.length-1)}let i=s[0].f7Modal;if(!i){const e=s.dataset();i=new r(a,{el:s,...e})}return i.close(n)}});return n},de.ConstructorMethods=ue,de.loadModule=function(e){const t=this,r=o(),n=a();return new Promise(((a,o)=>{const s=t.instance;let i,l,c;if(e){if("string"==typeof e){const t=e.match(/([a-z0-9-]*)/i);if(e.indexOf(".")<0&&t&&t[0].length===e.length){if(!s||s&&!s.params.lazyModulesPath)return void o(new Error('Framework7: "lazyModulesPath" app parameter must be specified to fetch module by name'));i=`${s.params.lazyModulesPath}/${e}/${e}.lazy.js`}else i=e}else"function"==typeof e?c=e:l=e;if(c){const e=c(t,!1);if(!e)return void o(new Error("Framework7: Can't find Framework7 component in specified component function"));if(t.prototype.modules&&t.prototype.modules[e.name])return void a();u(e),a()}if(l){const e=l;if(!e)return void o(new Error("Framework7: Can't find Framework7 component in specified component"));if(t.prototype.modules&&t.prototype.modules[e.name])return void a();u(e),a()}if(i){if(pe.indexOf(i)>=0)return void a();pe.push(i);const e=new Promise(((e,a)=>{t.request.get(i,(o=>{const s=`f7_component_loader_callback_${_()}`,l=n.createElement("script");l.innerHTML=`window.${s} = function (Framework7, Framework7AutoInstallComponent) {return ${o.trim()}}`,N("head").append(l);const c=r[s];delete r[s],N(l).remove();const p=c(t,!1);p?(t.prototype.modules&&t.prototype.modules[p.name]||u(p),e()):a(new Error(`Framework7: Can't find Framework7 component in ${i} file`))}),((e,t)=>{a(e,t)}))})),l=new Promise((e=>{t.request.get(i.replace(".lazy.js",s.rtl?".rtl.css":".css").replace(".js",s.rtl?".rtl.css":".css"),(t=>{const r=n.createElement("style");r.innerHTML=t,N("head").append(r),e()}),(()=>{e()}))}));Promise.all([e,l]).then((()=>{a()})).catch((e=>{o(e)}))}}else o(new Error("Framework7: Lazy module must be specified"));function u(e){t.use(e),s&&(s.useModuleParams(e,s.params),s.useModule(e))}}))},de.loadModules=function(e){return Promise.all(e.map((e=>de.loadModule(e))))};var he={name:"device",static:{getDevice:ie},on:{init(){const e=a(),t=ie(),r=[],n=e.querySelector("html"),o=e.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');n&&(t.standalone&&t.ios&&o&&"black-translucent"===o.content&&r.push("device-full-viewport"),r.push(`device-pixel-ratio-${Math.floor(t.pixelRatio)}`),t.os&&!t.desktop?r.push(`device-${t.os}`):t.desktop&&(r.push("device-desktop"),t.os&&r.push(`device-${t.os}`)),t.cordova&&r.push("device-cordova"),t.capacitor&&r.push("device-capacitor"),r.forEach((e=>{n.classList.add(e)})))}}},fe={name:"support",static:{getSupport:se}},me={name:"utils",proto:{utils:ae},static:{utils:ae}},ge={name:"resize",create(){const e=this;e.getSize=()=>{if(!e.el)return{width:0,height:0,left:0,top:0};const t=e.$el.offset(),[r,a,n,o]=[e.el.offsetWidth,e.el.offsetHeight,t.left,t.top];return e.width=r,e.height=a,e.left=n,e.top=o,{width:r,height:a,left:n,top:o}}},on:{init(){const e=this,t=o();e.getSize(),t.addEventListener("resize",(()=>{e.emit("resize")}),!1),t.addEventListener("orientationchange",(()=>{e.emit("orientationchange")}))},orientationchange(){const e=a();ie().ipad&&(e.body.scrollLeft=0,setTimeout((()=>{e.body.scrollLeft=0}),0))},resize(){this.getSize()}}};const ve={};let be=0;class ye{constructor(e){Object.assign(this,e)}}class we extends Error{constructor(e){super(),Object.assign(this,e)}}const Ce=e=>new Promise(((t,r)=>{const n=o(),s=a(),i=J({},ve);"beforeCreate beforeOpen beforeSend error complete success statusCode".split(" ").forEach((e=>{delete i[e]}));const l=J({url:n.location.toString(),method:"GET",data:!1,async:!0,cache:!0,user:"",password:"",headers:{},xhrFields:{},statusCode:{},processData:!0,dataType:"text",contentType:"application/x-www-form-urlencoded",timeout:0},i);let c;const u=J({},l,e);if(e.abortController&&(u.abortController=e.abortController),u.abortController&&u.abortController.canceled)return void r(new we({options:u,status:"canceled",message:"canceled"}));function p(e){let t,r;for(var a=arguments.length,n=new Array(a>1?a-1:0),o=1;o<a;o++)n[o-1]=arguments[o];return ve[e]&&(t=ve[e](...n)),u[e]&&(r=u[e](...n)),"boolean"!=typeof t&&(t=!0),"boolean"!=typeof r&&(r=!0),(!u.abortController||!u.abortController.canceled||"beforeCreate"!==e&&"beforeOpen"!==e&&"beforeSend"!==e)&&(t&&r)}if(c=p("beforeCreate",u),!1===c)return void r(new we({options:u,status:"canceled",message:"canceled"}));u.type&&(u.method=u.type);let d=u.url.indexOf("?")>=0?"&":"?";const h=u.method.toUpperCase();if(("GET"===h||"HEAD"===h||"OPTIONS"===h||"DELETE"===h)&&u.data){let e;e="string"==typeof u.data?u.data.indexOf("?")>=0?u.data.split("?")[1]:u.data:X(u.data),e.length&&(u.url+=d+e,"?"===d&&(d="&"))}if("json"===u.dataType&&u.url.indexOf("callback=")>=0){const e=`f7jsonp_${Date.now()+(be+=1)}`;let a;const o=u.url.split("callback=");let i=`${o[0]}callback=${e}`;if(o[1].indexOf("&")>=0){const e=o[1].split("&").filter((e=>e.indexOf("=")>0)).join("&");e.length>0&&(i+=`&${e}`)}let l=s.createElement("script");return l.type="text/javascript",l.onerror=function(){clearTimeout(a),p("error",null,"scripterror","scripterror"),r(new we({options:u,status:"scripterror",message:"scripterror"})),p("complete",null,"scripterror")},l.src=i,n[e]=function(r){clearTimeout(a),p("success",r),l.parentNode.removeChild(l),l=null,delete n[e],t(new ye({options:u,data:r}))},s.querySelector("head").appendChild(l),void(u.timeout>0&&(a=setTimeout((()=>{l.parentNode.removeChild(l),l=null,p("error",null,"timeout","timeout"),r(new we({options:u,status:"timeout",message:"timeout"}))}),u.timeout)))}"GET"!==h&&"HEAD"!==h&&"OPTIONS"!==h&&"DELETE"!==h||!1===u.cache&&(u.url+=`${d}_nocache${Date.now()}`);const f=new XMLHttpRequest;if(u.abortController){let e=!1;u.abortController.onAbort=()=>{e||(e=!0,f.abort(),r(new we({options:u,xhr:f,status:"canceled",message:"canceled"})))}}if(f.requestUrl=u.url,f.requestParameters=u,c=p("beforeOpen",f,u),!1===c)return void r(new we({options:u,xhr:f,status:"canceled",message:"canceled"}));f.open(h,u.url,u.async,u.user,u.password);let m=null;if(("POST"===h||"PUT"===h||"PATCH"===h)&&u.data)if(u.processData){if([ArrayBuffer,Blob,Document,FormData].indexOf(u.data.constructor)>=0)m=u.data;else{const e=`---------------------------${Date.now().toString(16)}`;"multipart/form-data"===u.contentType?f.setRequestHeader("Content-Type",`multipart/form-data; boundary=${e}`):f.setRequestHeader("Content-Type",u.contentType),m="";let t=X(u.data);if("multipart/form-data"===u.contentType){t=t.split("&");const r=[];for(let e=0;e<t.length;e+=1)r.push(`Content-Disposition: form-data; name="${t[e].split("=")[0]}"\r\n\r\n${t[e].split("=")[1]}\r\n`);m=`--${e}\r\n${r.join(`--${e}\r\n`)}--${e}--\r\n`}else m="application/json"===u.contentType?JSON.stringify(u.data):t}}else m=u.data,f.setRequestHeader("Content-Type",u.contentType);"json"!==u.dataType||u.headers&&u.headers.Accept||f.setRequestHeader("Accept","application/json"),u.headers&&Object.keys(u.headers).forEach((e=>{void 0!==u.headers[e]&&f.setRequestHeader(e,u.headers[e])})),void 0===u.crossDomain&&(u.crossDomain=/^([\w-]+:)?\/\/([^\/]+)/.test(u.url)&&RegExp.$2!==n.location.host),u.crossDomain||f.setRequestHeader("X-Requested-With","XMLHttpRequest"),u.xhrFields&&J(f,u.xhrFields),f.onload=function(){if(f.status>=200&&f.status<300||0===f.status){let e;if("json"===u.dataType){let a;try{e=JSON.parse(f.responseText)}catch(e){a=!0}a?(p("error",f,"parseerror","parseerror"),r(new we({options:u,xhr:f,status:"parseerror",message:"parseerror"}))):(p("success",e,f.status,f),t(new ye({options:u,data:e,status:f.status,xhr:f})))}else e="text"===f.responseType||""===f.responseType?f.responseText:f.response,p("success",e,f.status,f),t(new ye({options:u,data:e,status:f.status,xhr:f}))}else p("error",f,f.status,f.statusText),r(new we({options:u,xhr:f,status:f.status,message:f.statusText}));u.statusCode&&(ve.statusCode&&ve.statusCode[f.status]&&ve.statusCode[f.status](f),u.statusCode[f.status]&&u.statusCode[f.status](f)),p("complete",f,f.status)},f.onerror=function(){p("error",f,f.status,f.status),r(new we({options:u,xhr:f,status:f.status,message:f.statusText})),p("complete",f,"error")},u.timeout>0&&(f.timeout=u.timeout,f.ontimeout=()=>{p("error",f,"timeout","timeout"),r(new we({options:u,xhr:f,status:"timeout",message:"timeout"})),p("complete",f,"timeout")}),c=p("beforeSend",f,u),!1!==c?f.send(m):r(new we({options:u,xhr:f,status:"canceled",message:"canceled"}))}));function ke(e){let[t,r,a,n,o]=[];for(var s=arguments.length,i=new Array(s>1?s-1:0),l=1;l<s;l++)i[l-1]=arguments[l];"function"==typeof i[1]?[t,a,n,o]=i:[t,r,a,n,o]=i,[a,n].forEach((e=>{"string"==typeof e&&(o=e,e===a?a=void 0:n=void 0)})),o=o||("json"===e||"postJSON"===e?"json":void 0);const c={url:t,method:"post"===e||"postJSON"===e?"POST":"GET",data:r,success:a,error:n,dataType:o};return"postJSON"===e&&J(c,{contentType:"application/json",processData:!1,crossDomain:!0,data:"string"==typeof r?r:JSON.stringify(r)}),Ce(c)}Object.assign(Ce,{get:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ke("get",...t)},post:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ke("post",...t)},json:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ke("json",...t)},getJSON:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ke("json",...t)},postJSON:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return ke("postJSON",...t)}}),Ce.abortController=()=>{const e={canceled:!1,onAbort:null,abort(){e.canceled=!0,e.onAbort&&e.onAbort()}};return e},Ce.setup=function(e){e.type&&!e.method&&J(e,{method:e.type}),J(ve,e)};var Ee={name:"request",proto:{request:Ce},static:{request:Ce}};var $e={name:"touch",params:{touch:{touchClicksDistanceThreshold:5,disableContextMenu:!1,tapHold:!1,tapHoldDelay:750,tapHoldPreventClicks:!0,activeState:!0,activeStateElements:"a, button, label, span, .actions-button, .stepper-button, .stepper-button-plus, .stepper-button-minus, .card-expandable, .menu-item, .link, .item-link, .accordion-item-toggle",activeStateOnMouseMove:!1,mdTouchRipple:!0,iosTouchRipple:!1,auroraTouchRipple:!1,touchRippleElements:".ripple, .link, .item-link, .list-button, .links-list a, .button, button, .input-clear-button, .dialog-button, .tab-link, .item-radio, .item-checkbox, .actions-button, .searchbar-disable-button, .fab a, .checkbox, .radio, .data-table .sortable-cell:not(.input-cell), .notification-close-button, .stepper-button, .stepper-button-minus, .stepper-button-plus, .menu-item-content, .list.accordion-list .accordion-item-toggle",touchRippleInsetElements:".ripple-inset, .icon-only, .searchbar-disable-button, .input-clear-button, .notification-close-button, .md .navbar .link.back"}},create(){const e=se();J(this,{touchEvents:{start:e.touch?"touchstart":e.pointerEvents?"pointerdown":"mousedown",move:e.touch?"touchmove":e.pointerEvents?"pointermove":"mousemove",end:e.touch?"touchend":e.pointerEvents?"pointerup":"mouseup"}})},on:{init:function(){const e=this,t=ie(),r=se(),n=o(),s=a(),i=e.params.touch,l=i[`${e.theme}TouchRipple`];let c,u,p,d,h,f,m,g,v,b,y,w,C;function k(e){const t=N(e),r=t.parents(i.activeStateElements);if(t.closest(".no-active-state").length)return null;let a;if(t.is(i.activeStateElements)&&(a=t),r.length>0&&(a=a?a.add(r):r),a&&a.length>1){const e=[];let t;for(let r=0;r<a.length;r+=1)t||(e.push(a[r]),(a.eq(r).hasClass("prevent-active-state-propagation")||a.eq(r).hasClass("no-active-state-propagation"))&&(t=!0));a=N(e)}return a||t}function E(e){return e.parents(".page-content").length>0}function $(){g&&g.addClass("active-state")}function x(){g&&(g.removeClass("active-state"),g=null)}function P(t,r,a){t&&(b=e.touchRipple.create(e,t,r,a))}function O(){b&&(b.remove(),b=void 0,y=void 0)}function R(e){if(y=function(e){const t=i.touchRippleElements,r=N(e);if(r.is(t))return!r.hasClass("no-ripple")&&r;if(r.parents(t).length>0){const e=r.parents(t).eq(0);return!e.hasClass("no-ripple")&&e}return!1}(e),!y||0===y.length)return void(y=void 0);E(y)?(clearTimeout(w),w=setTimeout((()=>{O(),P(y,c,u)}),80)):(O(),P(y,c,u))}function T(){clearTimeout(w),O()}function S(){b||!y||d?O():(clearTimeout(w),P(y,c,u),setTimeout(O,0))}function A(){N(".active-state").removeClass("active-state"),l&&S()}t.ios&&t.webView&&n.addEventListener("touchstart",(()=>{}));let L=!1,M=null;const B=".dialog-button, .actions-button";let H=!1,_=null;function D(t,r){e.emit({events:t,data:[r]})}function q(e){D("touchstart touchstart:active",e)}function j(e){D("touchmove touchmove:active",e)}function I(e){D("touchend touchend:active",e)}function U(e){D("touchstart:passive",e)}function z(e){D("touchmove:passive",e)}function V(e){D("touchend:passive",e)}const W=!!r.passiveListener&&{passive:!0},F=!r.passiveListener||{passive:!0,capture:!0},X=!!r.passiveListener&&{passive:!1},Q=!r.passiveListener||{passive:!1,capture:!0};s.addEventListener("click",(function(e){D("click",e)}),!0),r.passiveListener?(s.addEventListener(e.touchEvents.start,q,Q),s.addEventListener(e.touchEvents.move,j,X),s.addEventListener(e.touchEvents.end,I,X),s.addEventListener(e.touchEvents.start,U,F),s.addEventListener(e.touchEvents.move,z,W),s.addEventListener(e.touchEvents.end,V,W)):(s.addEventListener(e.touchEvents.start,(e=>{q(e),U(e)}),!0),s.addEventListener(e.touchEvents.move,(e=>{j(e),z(e)}),!1),s.addEventListener(e.touchEvents.end,(e=>{I(e),V(e)}),!1)),r.touch?(e.on("click",(function(e){const r=e&&e.detail&&"f7Overswipe"===e.detail,a=e&&e.detail&&"f7Segmented"===e.detail,n=e&&e.detail&&"f7TouchMoveActivable"===e.detail;let o=m;return p&&e.target!==p?o=!(r||a||n):n&&(o=!1),i.tapHold&&i.tapHoldPreventClicks&&h&&(o=!0),o&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault()),i.tapHold&&(f=setTimeout((()=>{h=!1}),t.ios||t.androidChrome?100:400)),m=!1,p=null,!o})),e.on("touchstart",(function(t){return d=!1,h=!1,m=!1,C=void 0,t.targetTouches.length>1?(g&&x(),!0):(t.touches.length>1&&g&&x(),i.tapHold&&(f&&clearTimeout(f),f=setTimeout((()=>{t&&t.touches&&t.touches.length>1||(h=!0,t.preventDefault(),m=!0,N(t.target).trigger("taphold",t),e.emit("taphold",t))}),i.tapHoldDelay)),p=t.target,c=t.targetTouches[0].pageX,u=t.targetTouches[0].pageY,L=t.target.closest(".segmented-strong .button-active, .segmented-strong .tab-link-active"),H="ios"===e.theme&&t.target.closest(B),L&&(M=L.closest(".segmented-strong")),i.activeState&&(g=k(p),g&&!E(g)?$():g&&(v=setTimeout($,80))),l&&R(p),!0)})),e.on("touchmove",(function(e){let t,r,a=!0;"touchmove"===e.type&&(t=e.targetTouches[0],r=i.touchClicksDistanceThreshold);const n=e.targetTouches[0].pageX,o=e.targetTouches[0].pageY;if(void 0===C&&(C=!!(C||Math.abs(o-u)>Math.abs(n-c))),(H||!C&&L&&M)&&e.cancelable&&e.preventDefault(),!C&&L&&M){const t=s.elementFromPoint(e.targetTouches[0].clientX,e.targetTouches[0].clientY).closest(".segmented-strong .button:not(.button-active):not(.tab-link-active)");t&&M.contains(t)&&(N(t).trigger("click","f7Segmented"),p=t)}if(r&&t){const e=t.pageX,a=t.pageY;(Math.abs(e-c)>r||Math.abs(a-u)>r)&&(d=!0)}else d=!0;if(d){if(m=!0,H){const t=s.elementFromPoint(e.targetTouches[0].clientX,e.targetTouches[0].clientY);_=t.closest(B),_&&g&&g[0]===_?a=!1:_&&setTimeout((()=>{g=k(_),$()}))}i.tapHold&&clearTimeout(f),i.activeState&&a&&(clearTimeout(v),x()),l&&T()}})),e.on("touchend",(function(e){return C=void 0,L=!1,M=null,H=!1,clearTimeout(v),clearTimeout(f),_&&(N(_).trigger("click","f7TouchMoveActivable"),_=null),s.activeElement===e.target?(i.activeState&&x(),l&&S(),!0):(i.activeState&&($(),setTimeout(x,0)),l&&S(),!(i.tapHoldPreventClicks&&h||m)||(e.cancelable&&e.preventDefault(),m=!0,!1))})),s.addEventListener("touchcancel",(function(){p=null,clearTimeout(v),clearTimeout(f),i.activeState&&x(),l&&S()}),{passive:!0})):i.activeState&&(e.on("touchstart",(function(e){const t=k(e.target);t&&(t.addClass("active-state"),"which"in e&&3===e.which&&setTimeout((()=>{N(".active-state").removeClass("active-state")}),0)),l&&(c=e.pageX,u=e.pageY,R(e.target,e.pageX,e.pageY))})),e.on("touchmove",(function(){i.activeStateOnMouseMove||N(".active-state").removeClass("active-state"),l&&T()})),e.on("touchend",A),s.addEventListener("pointercancel",A,{passive:!0})),s.addEventListener("contextmenu",(e=>{i.disableContextMenu&&(t.ios||t.android||t.cordova||n.Capacitor&&n.Capacitor.isNative)&&e.preventDefault(),l&&(g&&x(),S())}))}}};function xe(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var a=e[r];if("*"!==a&&"+"!==a&&"?"!==a)if("\\"!==a)if("{"!==a)if("}"!==a)if(":"!==a)if("("!==a)t.push({type:"CHAR",index:r,value:e[r++]});else{var n=1,o="";if("?"===e[i=r+1])throw new TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;)if("\\"!==e[i]){if(")"===e[i]){if(0==--n){i++;break}}else if("("===e[i]&&(n++,"?"!==e[i+1]))throw new TypeError("Capturing groups are not allowed at "+i);o+=e[i++]}else o+=e[i++]+e[i++];if(n)throw new TypeError("Unbalanced pattern at "+r);if(!o)throw new TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:o}),r=i}else{for(var s="",i=r+1;i<e.length;){var l=e.charCodeAt(i);if(!(l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||95===l))break;s+=e[i++]}if(!s)throw new TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:s}),r=i}else t.push({type:"CLOSE",index:r,value:e[r++]});else t.push({type:"OPEN",index:r,value:e[r++]});else t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});else t.push({type:"MODIFIER",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),a=t.prefixes,n=void 0===a?"./":a,o="[^"+Oe(t.delimiter||"/#?")+"]+?",s=[],i=0,l=0,c="",u=function(e){if(l<r.length&&r[l].type===e)return r[l++].value},p=function(e){var t=u(e);if(void 0!==t)return t;var a=r[l],n=a.type,o=a.index;throw new TypeError("Unexpected "+n+" at "+o+", expected "+e)},d=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};l<r.length;){var h=u("CHAR"),f=u("NAME"),m=u("PATTERN");if(f||m){var g=h||"";-1===n.indexOf(g)&&(c+=g,g=""),c&&(s.push(c),c=""),s.push({name:f||i++,prefix:g,suffix:"",pattern:m||o,modifier:u("MODIFIER")||""})}else{var v=h||u("ESCAPED_CHAR");if(v)c+=v;else if(c&&(s.push(c),c=""),u("OPEN")){g=d();var b=u("NAME")||"",y=u("PATTERN")||"",w=d();p("CLOSE"),s.push({name:b||(y?i++:""),pattern:b&&!y?o:y,prefix:g,suffix:w,modifier:u("MODIFIER")||""})}else p("END")}}return s}function Pe(e,t){return function(e,t){void 0===t&&(t={});var r=Re(t),a=t.encode,n=void 0===a?function(e){return e}:a,o=t.validate,s=void 0===o||o,i=e.map((function(e){if("object"==typeof e)return new RegExp("^(?:"+e.pattern+")$",r)}));return function(t){for(var r="",a=0;a<e.length;a++){var o=e[a];if("string"!=typeof o){var l=t?t[o.name]:void 0,c="?"===o.modifier||"*"===o.modifier,u="*"===o.modifier||"+"===o.modifier;if(Array.isArray(l)){if(!u)throw new TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===l.length){if(c)continue;throw new TypeError('Expected "'+o.name+'" to not be empty')}for(var p=0;p<l.length;p++){var d=n(l[p],o);if(s&&!i[a].test(d))throw new TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+d+'"');r+=o.prefix+d+o.suffix}}else if("string"!=typeof l&&"number"!=typeof l){if(!c){var h=u?"an array":"a string";throw new TypeError('Expected "'+o.name+'" to be '+h)}}else{d=n(String(l),o);if(s&&!i[a].test(d))throw new TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+d+'"');r+=o.prefix+d+o.suffix}}else r+=o}return r}}(xe(e,t),t)}function Oe(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function Re(e){return e&&e.sensitive?"":"i"}function Te(e,t,r){return function(e,t,r){void 0===r&&(r={});for(var a=r.strict,n=void 0!==a&&a,o=r.start,s=void 0===o||o,i=r.end,l=void 0===i||i,c=r.encode,u=void 0===c?function(e){return e}:c,p="["+Oe(r.endsWith||"")+"]|$",d="["+Oe(r.delimiter||"/#?")+"]",h=s?"^":"",f=0,m=e;f<m.length;f++){var g=m[f];if("string"==typeof g)h+=Oe(u(g));else{var v=Oe(u(g.prefix)),b=Oe(u(g.suffix));if(g.pattern)if(t&&t.push(g),v||b)if("+"===g.modifier||"*"===g.modifier){var y="*"===g.modifier?"?":"";h+="(?:"+v+"((?:"+g.pattern+")(?:"+b+v+"(?:"+g.pattern+"))*)"+b+")"+y}else h+="(?:"+v+"("+g.pattern+")"+b+")"+g.modifier;else h+="("+g.pattern+")"+g.modifier;else h+="(?:"+v+b+")"+g.modifier}}if(l)n||(h+=d+"?"),h+=r.endsWith?"(?="+p+")":"$";else{var w=e[e.length-1],C="string"==typeof w?d.indexOf(w[w.length-1])>-1:void 0===w;n||(h+="(?:"+d+"(?="+p+"))?"),C||(h+="(?="+d+"|"+p+")")}return new RegExp(h,Re(r))}(xe(e,r),t,r)}function Se(e,t,r){return e instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,a=0,n=r.exec(e.source);n;)t.push({name:n[1]||a++,prefix:"",suffix:"",modifier:"",pattern:""}),n=r.exec(e.source);return e}(e,t):Array.isArray(e)?function(e,t,r){var a=e.map((function(e){return Se(e,t,r).source}));return new RegExp("(?:"+a.join("|")+")",Re(r))}(e,t,r):Te(e,t,r)}const Ae={queue:[],clearQueue(){if(0===Ae.queue.length)return;Ae.queue.shift()()},routerQueue:[],clearRouterQueue(){if(0===Ae.routerQueue.length)return;const e=Ae.routerQueue.pop(),{router:t,stateUrl:r,action:a}=e;let n=t.params.animate;!1===t.params.browserHistoryAnimate&&(n=!1),"back"===a&&t.back({animate:n,browserHistory:!1}),"load"===a&&t.navigate(r,{animate:n,browserHistory:!1})},handle(e){if(Ae.blockPopstate)return;let t=e.state;Ae.previousState=Ae.state,Ae.state=t,Ae.allowChange=!0,Ae.clearQueue(),t=Ae.state,t||(t={}),this.views.forEach((e=>{const r=e.router;let a=t[e.id];if(!a&&e.params.browserHistory&&(a={url:e.router.history[0]}),!a)return;const n=a.url||void 0;let o=r.params.animate;!1===r.params.browserHistoryAnimate&&(o=!1),n!==r.url&&(r.history.indexOf(n)>=0?r.allowPageChange?r.back({animate:o,browserHistory:!1}):Ae.routerQueue.push({action:"back",router:r}):r.allowPageChange?r.navigate(n,{animate:o,browserHistory:!1}):Ae.routerQueue.unshift({action:"load",stateUrl:n,router:r}))}))},initViewState(e,t){const r=o(),a=J({},Ae.state||{},{[e]:t});Ae.state=a,r.history.replaceState(a,"")},push(e,t,r){const n=o(),s=a();if("#!/"===r.substr(-3)&&""===(r=r.replace("#!/",""))&&(r=s.location.href).includes("#!/")&&(r=s.location.href.split("#!/")[0]),!Ae.allowChange)return void Ae.queue.push((()=>{Ae.push(e,t,r)}));Ae.previousState=Ae.state;const i=J({},Ae.previousState||{},{[e]:t});Ae.state=i,n.history.pushState(i,"",r)},replace(e,t,r){const a=o();if("#!/"===r.substr(-3)&&(r=r.replace("#!/","")),!Ae.allowChange)return void Ae.queue.push((()=>{Ae.replace(e,t,r)}));Ae.previousState=Ae.state;const n=J({},Ae.previousState||{},{[e]:t});Ae.state=n,a.history.replaceState(n,"",r)},go(e){const t=o();Ae.allowChange=!1,t.history.go(e)},back(){const e=o();Ae.allowChange=!1,e.history.back()},allowChange:!0,previousState:{},state:{},blockPopstate:!0,init(e){const t=o(),r=a();Ae.state=t.history.state,N(t).on("load",(()=>{setTimeout((()=>{Ae.blockPopstate=!1}),0)})),r.readyState&&"complete"===r.readyState&&(Ae.blockPopstate=!1),N(t).on("popstate",Ae.handle.bind(e))}};function Le(e){const t=e,{$el:r,$navbarsEl:a,app:n,params:o}=t,s=se(),i=ie();let l=!1,c=!1;const u={};let p,d,h,f,m,g,v,b,y=[],w=[],C=!0,k=[],E=[];const $=o[`${n.theme}SwipeBackAnimateShadow`],x=o[`${n.theme}SwipeBackAnimateOpacity`],P=o[`${n.theme}SwipeBackActiveArea`],O=o[`${n.theme}SwipeBackThreshold`],R=n.rtl?"right center":"left center",T=n.rtl?"calc(100% - var(--f7-navbar-large-title-padding-left) - var(--f7-safe-area-left)) center":"calc(var(--f7-navbar-large-title-padding-left) + var(--f7-safe-area-left)) center";function S(e){let{progress:t,reset:r,transition:a,reflow:n}=void 0===e?{}:e;const o=["overflow","transform","transform-origin","opacity"];if(!0===a||!1===a)for(let e=0;e<b.length;e+=1){const t=b[e];t&&t.el&&(!0===a&&t.el.classList.add("navbar-page-transitioning"),!1===a&&t.el.classList.remove("navbar-page-transitioning"))}n&&b.length&&b[0]&&b[0].el&&(b[0].el._clientLeft=b[0].el.clientLeft);for(let e=0;e<b.length;e+=1){const a=b[e];if(a&&a.el){!a.className||a.classNameSet||r||(a.el.classList.add(a.className),a.classNameSet=!0),a.className&&r&&a.el.classList.remove(a.className);for(let e=0;e<o.length;e+=1){const n=o[e];a[n]&&(r?a.el.style[n]="":"function"==typeof a[n]?a.el.style[n]=a[n](t):a.el.style[n]=a[n])}}}}function A(e){const r=o[`${n.theme}SwipeBack`];!C||!r||l||n.swipeout&&n.swipeout.el||!t.allowPageChange||N(e.target).closest(".range-slider, .calendar-months").length>0||N(e.target).closest(".page-master, .page-master-detail").length>0&&o.masterDetailBreakpoint>0&&n.width>=o.masterDetailBreakpoint||(c=!1,l=!0,p=void 0,u.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,u.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY,f=W(),m=t.dynamicNavbar)}function L(e){if(!l)return;const s="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,f="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY;if(void 0===p&&(p=!!(p||Math.abs(f-u.y)>Math.abs(s-u.x))||s<u.x&&!n.rtl||s>u.x&&n.rtl),p||e.f7PreventSwipeBack||n.preventSwipeBack)return void(l=!1);if(!c){let t=!1;const s=N(e.target),c=s.closest(".swipeout");c.length>0&&(!n.rtl&&c.find(".swipeout-actions-left").length>0&&(t=!0),n.rtl&&c.find(".swipeout-actions-right").length>0&&(t=!0)),y=s.closest(".page"),(y.hasClass("no-swipeback")||s.closest(".no-swipeback, .card-opened").length>0)&&(t=!0),w=r.find(".page-previous:not(.stacked)"),w.length>1&&(w=w.eq(w.length-1));let p=u.x-r.offset().left>P;if(d=r.width(),p=n.rtl?u.x<r.offset().left-r[0].scrollLeft+(d-P):u.x-r.offset().left>P,p&&(t=!0),0!==w.length&&0!==y.length||(t=!0),t)return void(l=!1);$&&(g=y.find(".page-shadow-effect"),0===g.length&&(g=N('<div class="page-shadow-effect"></div>'),y.append(g))),x&&(v=w.find(".page-opacity-effect"),0===v.length&&(v=N('<div class="page-opacity-effect"></div>'),w.append(v))),m&&(k=a.find(".navbar-current:not(.stacked)"),E=a.find(".navbar-previous:not(.stacked)"),E.length>1&&(E=E.eq(E.length-1)),b=function(){const e=[],t=n.rtl?-1:1,r=k.hasClass("navbar-transparent")&&!k.hasClass("navbar-large")&&!k.hasClass("navbar-transparent-visible"),a=k.hasClass("navbar-large"),s=k.hasClass("navbar-large-collapsed"),l=k.hasClass("navbar-large-transparent")||k.hasClass("navbar-large")&&k.hasClass("navbar-transparent"),c=E.hasClass("navbar-transparent")&&!E.hasClass("navbar-large")&&!E.hasClass("navbar-transparent-visible"),u=E.hasClass("navbar-large"),p=E.hasClass("navbar-large-collapsed"),d=E.hasClass("navbar-large-transparent")||E.hasClass("navbar-large")&&E.hasClass("navbar-transparent"),h=a&&!s,f=u&&!p,m=k.find(".left, .title, .right, .subnavbar, .fading, .title-large, .navbar-bg"),g=E.find(".left, .title, .right, .subnavbar, .fading, .title-large, .navbar-bg");let v,b;return o.iosAnimateNavbarBackIcon&&(v=k.hasClass("sliding")||k.find(".navbar-inner.sliding").length?k.find(".left").find(".back .icon + span").eq(0):k.find(".left.sliding").find(".back .icon + span").eq(0),b=E.hasClass("sliding")||E.find(".navbar-inner.sliding").length?E.find(".left").find(".back .icon + span").eq(0):E.find(".left.sliding").find(".back .icon + span").eq(0),v.length&&g.each((e=>{N(e).hasClass("title")&&(e.f7NavbarLeftOffset+=v.prev(".icon")[0].offsetWidth)}))),m.each((n=>{const c=N(n),u=c.hasClass("subnavbar"),p=c.hasClass("left"),d=c.hasClass("title"),m=c.hasClass("navbar-bg");if((d||m)&&r)return;if(!h&&c.hasClass(".title-large"))return;const g={el:n};if(h){if(d)return;if(c.hasClass("title-large"))return e.indexOf(g)<0&&e.push(g),g.overflow="visible",void c.find(".title-large-text").each((r=>{e.push({el:r,transform:e=>`translateX(${100*e*t}%)`})}))}if(f&&(h||c.hasClass("title-large")&&(e.indexOf(g)<0&&e.push(g),g.opacity=0),p))return e.indexOf(g)<0&&e.push(g),g.opacity=e=>1-e**.33,void c.find(".back span").each((t=>{e.push({el:t,"transform-origin":R,transform:e=>`translateX(calc(${e} * (var(--f7-navbarTitleLargeOffset) - var(--f7-navbarLeftTextOffset)))) translateY(calc(${e} * (var(--f7-navbar-large-title-height) - var(--f7-navbar-large-title-padding-vertical) / 2))) scale(${1+1*e})`})}));if(m)return e.indexOf(g)<0&&e.push(g),h||f||(s?(l&&(g.className="ios-swipeback-navbar-bg-large"),g.transform=e=>`translateX(${100*e*t}%) translateY(calc(-1 * var(--f7-navbar-large-title-height)))`):g.transform=e=>`translateX(${100*e*t}%)`),!h&&f&&(g.className="ios-swipeback-navbar-bg-large",g.transform=e=>`translateX(${100*e*t}%) translateY(calc(-1 * ${1-e} * var(--f7-navbar-large-title-height)))`),h&&f&&(g.transform=e=>`translateX(${100*e*t}%)`),void(h&&!f&&(g.transform=e=>`translateX(${100*e*t}%) translateY(calc(-${e} * var(--f7-navbar-large-title-height)))`));if(c.hasClass("title-large"))return;const b=c.hasClass("sliding")||c.parents(".navbar-inner.sliding").length;if(e.indexOf(g)<0&&e.push(g),(!u||u&&!b)&&(g.opacity=e=>1-e**.33),b){let t=g;if(p&&v.length&&o.iosAnimateNavbarBackIcon){const r={el:v[0]};t=r,e.push(r)}t.transform=e=>{let r=e*t.el.f7NavbarRightOffset;return 1===i.pixelRatio&&(r=Math.round(r)),u&&a?`translate3d(${r}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`:`translate3d(${r}px,0,0)`}}})),g.each((r=>{const a=N(r),n=a.hasClass("subnavbar"),s=a.hasClass("left"),l=a.hasClass("title"),m=a.hasClass("navbar-bg");if((l||m)&&c)return;const g={el:r};if(f){if(l)return;if(e.indexOf(g)<0&&e.push(g),a.hasClass("title-large"))return g.opacity=1,g.overflow="visible",void a.find(".title-large-text").each((t=>{e.push({el:t,"transform-origin":T,opacity:e=>e**3,transform:e=>`translateX(calc(${1-e} * (var(--f7-navbarLeftTextOffset) - var(--f7-navbarTitleLargeOffset)))) translateY(calc(${e-1} * var(--f7-navbar-large-title-height) + ${1-e} * var(--f7-navbar-large-title-padding-vertical))) scale(${.5+.5*e})`})}))}if(m)return e.indexOf(g)<0&&e.push(g),h||f||(p?(d&&(g.className="ios-swipeback-navbar-bg-large"),g.transform=e=>`translateX(${(100*e-100)*t}%) translateY(calc(-1 * var(--f7-navbar-large-title-height)))`):g.transform=e=>`translateX(${(100*e-100)*t}%)`),!h&&f&&(g.transform=e=>`translateX(${(100*e-100)*t}%) translateY(calc(-1 * ${1-e} * var(--f7-navbar-large-title-height)))`),h&&!f&&(g.className="ios-swipeback-navbar-bg-large",g.transform=e=>`translateX(${(100*e-100)*t}%) translateY(calc(-${e} * var(--f7-navbar-large-title-height)))`),void(h&&f&&(g.transform=e=>`translateX(${(100*e-100)*t}%)`));if(a.hasClass("title-large"))return;const v=a.hasClass("sliding")||E.children(".navbar-inner.sliding").length;if(e.indexOf(g)<0&&e.push(g),(!n||n&&!v)&&(g.opacity=e=>e**3),v){let t=g;if(s&&b.length&&o.iosAnimateNavbarBackIcon){const r={el:b[0]};t=r,e.push(r)}t.transform=e=>{let r=t.el.f7NavbarLeftOffset*(1-e);return 1===i.pixelRatio&&(r=Math.round(r)),n&&u?`translate3d(${r}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`:`translate3d(${r}px,0,0)`}}})),e}()),N(".sheet.modal-in").length>0&&n.sheet&&n.sheet.close(N(".sheet.modal-in"))}e.f7PreventSwipePanel=!0,c=!0,n.preventSwipePanelBySwipeBack=!0,e.preventDefault();const C=n.rtl?-1:1;h=(s-u.x-O)*C,h<0&&(h=0);const A=Math.min(Math.max(h/d,0),1),L={percentage:A,progress:A,currentPageEl:y[0],previousPageEl:w[0],currentNavbarEl:k[0],previousNavbarEl:E[0]};r.trigger("swipeback:move",L),t.emit("swipebackMove",L);let M=h*C,B=(h/5-d/5)*C;n.rtl?(M=Math.max(M,-d),B=Math.max(B,0)):(M=Math.min(M,d),B=Math.min(B,0)),1===i.pixelRatio&&(M=Math.round(M),B=Math.round(B)),t.swipeBackActive=!0,N([y[0],w[0]]).addClass("page-swipeback-active"),y.transform(`translate3d(${M}px,0,0)`),$&&(g[0].style.opacity=1-1*A),"ios"===n.theme&&w.transform(`translate3d(${B}px,0,0)`),x&&(v[0].style.opacity=1-1*A),m&&S({progress:A})}function M(){if(n.preventSwipePanelBySwipeBack=!1,!l||!c)return l=!1,void(c=!1);l=!1,c=!1,t.swipeBackActive=!1;const e=N([y[0],w[0]]);if(e.removeClass("page-swipeback-active"),0===h)return e.transform(""),g&&g.length>0&&g.remove(),v&&v.length>0&&v.remove(),void(m&&S({reset:!0}));const a=W()-f;let s=!1;(a<300&&h>10||a>=300&&h>d/2)&&(y.removeClass("page-current").addClass("page-next"+("ios"!==n.theme?" page-next-on-right":"")),w.removeClass("page-previous").addClass("page-current").removeAttr("aria-hidden"),g&&(g[0].style.opacity=""),v&&(v[0].style.opacity=""),m&&(t.setNavbarPosition(k,"next"),t.setNavbarPosition(E,"current",!1)),s=!0),e.addClass("page-transitioning page-transitioning-swipeback"),i.ios&&(y[0]._clientLeft=y[0].clientLeft),e.transform(""),m&&S({progress:s?1:0,transition:!0,reflow:!!i.ios}),C=!1,t.allowPageChange=!1;const u={currentPageEl:y[0],previousPageEl:w[0],currentNavbarEl:k[0],previousNavbarEl:E[0]};s?(t.currentRoute=w[0].f7Page.route,t.currentPage=w[0],t.pageCallback("beforeOut",y,k,"current","next",{route:y[0].f7Page.route,swipeBack:!0}),t.pageCallback("beforeIn",w,E,"previous","current",{route:w[0].f7Page.route,swipeBack:!0},y[0]),r.trigger("swipeback:beforechange",u),t.emit("swipebackBeforeChange",u)):(r.trigger("swipeback:beforereset",u),t.emit("swipebackBeforeReset",u)),y.transitionEnd((()=>{e.removeClass("page-transitioning page-transitioning-swipeback"),m&&S({reset:!0,transition:!1}),C=!0,t.allowPageChange=!0,s?(1===t.history.length&&t.history.unshift(t.url),t.history.pop(),t.saveHistory(),o.browserHistory&&Ae.back(),t.pageCallback("afterOut",y,k,"current","next",{route:y[0].f7Page.route,swipeBack:!0}),t.pageCallback("afterIn",w,E,"previous","current",{route:w[0].f7Page.route,swipeBack:!0}),o.stackPages&&t.initialPages.indexOf(y[0])>=0?(y.addClass("stacked"),m&&k.addClass("stacked")):(t.pageCallback("beforeRemove",y,k,"next",{swipeBack:!0}),t.removePage(y),m&&t.removeNavbar(k)),r.trigger("swipeback:afterchange",u),t.emit("swipebackAfterChange",u),t.emit("routeChanged",t.currentRoute,t.previousRoute,t),o.preloadPreviousPage&&t.back(t.history[t.history.length-2],{preload:!0})):(r.trigger("swipeback:afterreset",u),t.emit("swipebackAfterReset",u)),g&&g.length>0&&g.remove(),v&&v.length>0&&v.remove()}))}!function(){const e=!("touchstart"!==n.touchEvents.start||!s.passiveListener)&&{passive:!0,capture:!1};r.on(n.touchEvents.start,A,e),n.on("touchmove:active",L),n.on("touchend:passive",M)}(),t.on("routerDestroy",(function(){const e=!("touchstart"!==n.touchEvents.start||!s.passiveListener)&&{passive:!0,capture:!1};r.off(n.touchEvents.start,A,e),n.off("touchmove:active",L),n.off("touchend:passive",M)}))}function Me(e,t,r){const a=this,n=t.route.redirect,o="forward"===e?"navigate":"back";if(r.initial&&a.params.browserHistory&&(r.replaceState=!0,r.history=!0),"function"==typeof n){a.allowPageChange=!1;const s=n.call(a,{router:a,to:t,resolve:function(e,t){void 0===t&&(t={}),a.allowPageChange=!0,a[o](e,J({},r,t))},reject:function(){a.allowPageChange=!0},direction:e,app:a.app});return s&&"string"==typeof s?(a.allowPageChange=!0,a[o](s,r)):a}return a[o](n,r)}function Be(e,t,r,a,n,o,s,i){const l=[];Array.isArray(r)?l.push(...r):r&&"function"==typeof r&&l.push(r),t&&(Array.isArray(t)?l.push(...t):l.push(t)),function t(){if(0===l.length)return void o();l.shift().call(e,{router:e,to:a,from:n,resolve(){t()},reject(){s()},direction:i,app:e.app})}()}function Ne(e,t,r,a,n){const o=this;function s(){e&&e.route&&(o.params.routesBeforeEnter||e.route.beforeEnter)?(o.allowPageChange=!1,Be(o,o.params.routesBeforeEnter,e.route.beforeEnter,e,t,(()=>{o.allowPageChange=!0,r()}),(()=>{a()}),n)):r()}t&&t.route&&(o.params.routesBeforeLeave||t.route.beforeLeave)?(o.allowPageChange=!1,Be(o,o.params.routesBeforeLeave,t.route.beforeLeave,e,t,(()=>{o.allowPageChange=!0,s()}),(()=>{a()}),n)):s()}function He(e,t){if(!e.view)throw new Error(`Framework7: it is not allowed to use router methods on global app router. Use router methods only on related View, e.g. app.views.main.router.${t}(...)`)}function _e(e,t,r,a){function n(e){e.then((e=>{r({component:e.default||e._default||e})})).catch((e=>{throw a(),new Error(e)}))}if(t instanceof Promise)return void n(t);const o=t.call(e);o instanceof Promise?n(o):r({component:o})}function De(e,t,r){void 0===r&&(r={});const n=a(),o=N(t),s=e.app,i=e.view,l=J(!1,{animate:e.params.animate,browserHistory:!0,replaceState:!1,history:!0,reloadCurrent:e.params.reloadPages,reloadPrevious:!1,reloadAll:!1,clearPreviousHistory:!1,reloadDetail:e.params.reloadDetail,on:{}},r),c=e.params.masterDetailBreakpoint>0,u=c&&l.route&&l.route.route&&(!0===l.route.route.master||"function"==typeof l.route.route.master&&l.route.route.master(s,e));let p,d,h,f=0,m=e.currentRoute.modal;if(m||"popup popover sheet loginScreen actions customModal panel".split(" ").forEach((t=>{e.currentRoute&&e.currentRoute.route&&e.currentRoute.route[t]&&(m=!0,h=t)})),m){const t=e.currentRoute.modal||e.currentRoute.route.modalInstance||s[h].get(),r=e.history[e.history.length-2];let a=e.findMatchingRoute(r);!a&&r&&(a={url:r,path:r.split("?")[0],query:F(r),route:{path:r.split("?")[0],url:r}}),e.modalRemove(t)}const g=e.dynamicNavbar,v=e.$el,b=o,y=l.reloadPrevious||l.reloadCurrent||l.reloadAll;let w,C,k,E;if(e.allowPageChange=!1,0===b.length)return e.allowPageChange=!0,e;b.length&&e.removeThemeElements(b),g&&(k=b.children(".navbar"),C=e.$navbarsEl,0===k.length&&b[0]&&b[0].f7Page&&(k=b[0].f7Page.$navbarEl)),l.route&&l.route.route&&l.route.route.keepAlive&&!l.route.route.keepAliveData&&(l.route.route.keepAliveData={pageEl:o[0]});const $=v.children(".page:not(.stacked)").filter((e=>e!==b[0]));let x,P,O,R;if(g&&(x=C.children(".navbar:not(.stacked)").filter((e=>e!==k[0]))),l.reloadPrevious&&$.length<2)return e.allowPageChange=!0,e;if(c&&!l.reloadAll){for(let e=0;e<$.length;e+=1)p||!$[e].classList.contains("page-master")||(p=$[e]);if(P=!u&&p,P&&p)for(let e=0;e<$.length;e+=1)$[e].classList.contains("page-master-detail")&&(d=$[e]);O=P&&l.reloadDetail&&s.width>=e.params.masterDetailBreakpoint&&p}P&&(R=!d||O||l.reloadAll||l.reloadCurrent);let T="next";if(l.reloadCurrent||l.reloadAll||O?T="current":l.reloadPrevious&&(T="previous"),b.removeClass("page-previous page-current page-next").addClass(`page-${T}${u?" page-master":""}${P?" page-master-detail":""}${R?" page-master-detail-root":""}`).removeClass("stacked").trigger("page:unstack").trigger("page:position",{position:T}),e.emit("pageUnstack",b[0]),e.emit("pagePosition",b[0],T),(u||P)&&(b.trigger("page:role",{role:u?"master":"detail",root:!!R}),e.emit("pageRole",b[0],{role:u?"master":"detail",detailRoot:!!R})),g&&k.length&&(k.removeClass("navbar-previous navbar-current navbar-next").addClass(`navbar-${T}${u?" navbar-master":""}${P?" navbar-master-detail":""}${R?" navbar-master-detail-root":""}`).removeClass("stacked"),k.trigger("navbar:position",{position:T}),e.emit("navbarPosition",k[0],T),(u||P)&&e.emit("navbarRole",k[0],{role:u?"master":"detail",detailRoot:!!R})),l.reloadCurrent||O)O?(w=$.filter((e=>!e.classList.contains("page-master"))),g&&(E=N(w.map((e=>s.navbar.getElByPage(e))))),w.length>1&&p&&(f=w.length-1,N(p).removeClass("page-master-stacked").trigger("page:masterunstack"),e.emit("pageMasterUnstack",p),g&&(N(s.navbar.getElByPage(p)).removeClass("navbar-master-stacked"),e.emit("navbarMasterUnstack",s.navbar.getElByPage(p))))):(w=$.eq($.length-1),g&&(E=N(s.navbar.getElByPage(w))));else if(l.reloadPrevious)w=$.eq($.length-2),g&&(E=N(s.navbar.getElByPage(w)));else if(l.reloadAll)w=$.filter((e=>e!==b[0])),g&&(E=x.filter((e=>e!==k[0])));else{let t=[],r=[];if($.length>1){let a=0;for(a=0;a<$.length-1;a+=1){if(p&&$[a]===p){$.eq(a).addClass("page-master-stacked"),$.eq(a).trigger("page:masterstack"),e.emit("pageMasterStack",$[a]),g&&(N(s.navbar.getElByPage(p)).addClass("navbar-master-stacked"),e.emit("navbarMasterStack",s.navbar.getElByPage(p)));continue}const n=s.navbar.getElByPage($.eq(a));e.params.stackPages?($.eq(a).addClass("stacked"),$.eq(a).trigger("page:stack"),e.emit("pageStack",$[a]),g&&N(n).addClass("stacked")):(t.push($[a]),e.pageCallback("beforeRemove",$[a],x&&x[a],"previous",void 0,l),e.removePage($[a]),g&&n&&(r.push(n),e.removeNavbar(n)))}}w=v.children(".page:not(.stacked)").filter((e=>e!==b[0]&&t.indexOf(e)<0)),g&&(E=C.children(".navbar:not(.stacked)").filter((e=>e!==k[0]&&r.indexOf(r)<0))),t=[],r=[]}if(P&&!l.reloadAll&&((w.length>1||O)&&(w=w.filter((e=>!e.classList.contains("page-master")))),E&&(E.length>1||O)&&(E=E.filter((e=>!e.classList.contains("navbar-master"))))),e.params.browserHistory&&(l.browserHistory||l.replaceState)&&!l.reloadPrevious){const t=e.params.browserHistoryRoot||"";Ae[l.reloadCurrent||O&&d||l.reloadAll||l.replaceState?"replace":"push"](i.id,{url:l.route.url},t+e.params.browserHistorySeparator+l.route.url)}l.reloadPrevious||(e.currentPageEl=b[0],g&&k.length?e.currentNavbarEl=k[0]:delete e.currentNavbarEl,e.currentRoute=l.route);const S=l.route.url;l.history&&(((l.reloadCurrent||O&&d)&&e.history.length)>0||l.replaceState?(O&&f>0&&(e.history=e.history.slice(0,e.history.length-f)),e.history[e.history.length-(l.reloadPrevious?2:1)]=S):l.reloadPrevious?e.history[e.history.length-2]=S:l.reloadAll?e.history=[S]:e.history.push(S)),e.saveHistory();const A=b.parents(n).length>0,L=b[0].f7Component;if(l.reloadPrevious?(L&&!A?L.mount((e=>{N(e).insertBefore(w)})):b.insertBefore(w),g&&k.length&&(k.find(".title-large").length&&k.addClass("navbar-large"),E.length?k.insertBefore(E):(e.$navbarsEl.parents(n).length||e.$el.prepend(e.$navbarsEl),C.append(k)))):(w.next(".page")[0]!==b[0]&&(L&&!A?L.mount((e=>{v.append(e)})):v.append(b[0])),g&&k.length&&(k.find(".title-large").length&&k.addClass("navbar-large"),e.$navbarsEl.parents(n).length||e.$el.prepend(e.$navbarsEl),C.append(k[0]))),A?l.route&&l.route.route&&l.route.route.keepAlive&&!b[0].f7PageMounted&&(b[0].f7PageMounted=!0,e.pageCallback("mounted",b,k,T,y?T:"current",l,w)):e.pageCallback("mounted",b,k,T,y?T:"current",l,w),(l.reloadCurrent||O)&&w.length>0?e.params.stackPages&&e.initialPages.indexOf(w[0])>=0?(w.addClass("stacked"),w.trigger("page:stack"),e.emit("pageStack",w[0]),g&&E.addClass("stacked")):(e.pageCallback("beforeOut",w,E,"current",void 0,l),e.pageCallback("afterOut",w,E,"current",void 0,l),e.pageCallback("beforeRemove",w,E,"current",void 0,l),e.removePage(w),g&&E&&E.length&&e.removeNavbar(E)):l.reloadAll?w.each(((t,r)=>{const a=N(t),n=N(s.navbar.getElByPage(a));e.params.stackPages&&e.initialPages.indexOf(a[0])>=0?(a.addClass("stacked"),a.trigger("page:stack"),e.emit("pageStack",a[0]),g&&n.addClass("stacked")):(a.hasClass("page-current")&&(e.pageCallback("beforeOut",w,E,"current",void 0,l),e.pageCallback("afterOut",w,E,"current",void 0,l)),e.pageCallback("beforeRemove",a,E&&E.eq(r),"previous",void 0,l),e.removePage(a),g&&n.length&&e.removeNavbar(n))})):l.reloadPrevious&&(e.params.stackPages&&e.initialPages.indexOf(w[0])>=0?(w.addClass("stacked"),w.trigger("page:stack"),e.emit("pageStack",w[0]),g&&E.addClass("stacked")):(e.pageCallback("beforeRemove",w,E,"previous",void 0,l),e.removePage(w),g&&E&&E.length&&e.removeNavbar(E))),l.route.route.tab&&e.tabLoad(l.route.route.tab,J({},l,{history:!1,browserHistory:!1})),c&&i.checkMasterDetailBreakpoint(),e.pageCallback("init",b,k,T,y?T:"current",l,w),l.reloadCurrent||l.reloadAll||O)return e.allowPageChange=!0,e.pageCallback("beforeIn",b,k,T,"current",l),b.removeAttr("aria-hidden"),g&&k&&k.removeAttr("aria-hidden"),e.pageCallback("afterIn",b,k,T,"current",l),l.reloadCurrent&&l.clearPreviousHistory&&e.clearPreviousHistory(),O&&(e.setPagePosition(N(p),"previous"),p.f7Page&&p.f7Page.navbarEl&&e.setNavbarPosition(N(p.f7Page.navbarEl),"previous")),e;if(l.reloadPrevious)return e.allowPageChange=!0,e;function M(){e.setPagePosition(b,"current",!1),e.setPagePosition(w,"previous",!w.hasClass("page-master")),g&&(e.setNavbarPosition(k,"current",!1),e.setNavbarPosition(E,"previous",!E.hasClass("navbar-master"))),e.allowPageChange=!0,e.pageCallback("afterOut",w,E,"current","previous",l),e.pageCallback("afterIn",b,k,"next","current",l);let t=(e.params.preloadPreviousPage||e.params[`${s.theme}SwipeBack`])&&!u;t||(b.hasClass("smart-select-page")||b.hasClass("photo-browser-page")||b.hasClass("autocomplete-page")||b.hasClass("color-picker-page"))&&(t=!0),t||(e.params.stackPages?(w.addClass("stacked"),w.trigger("page:stack"),e.emit("pageStack",w[0]),g&&E.addClass("stacked")):b.attr("data-name")&&"smart-select-page"===b.attr("data-name")||(e.pageCallback("beforeRemove",w,E,"previous",void 0,l),e.removePage(w),g&&E.length&&e.removeNavbar(E))),l.clearPreviousHistory&&e.clearPreviousHistory(),e.emit("routeChanged",e.currentRoute,e.previousRoute,e),e.params.browserHistory&&Ae.clearRouterQueue()}function B(){e.setPagePosition(w,"current",!1),e.setPagePosition(b,"next",!1),g&&(e.setNavbarPosition(E,"current",!1),e.setNavbarPosition(k,"next",!1))}if(e.pageCallback("beforeOut",w,E,"current","previous",l),e.pageCallback("beforeIn",b,k,"next","current",l),!l.animate||u&&s.width>=e.params.masterDetailBreakpoint)M();else{const t=e.params[`${e.app.theme}PageLoadDelay`];let r=e.params.transition;l.transition&&(r=l.transition),!r&&e.currentRoute&&e.currentRoute.route&&(r=e.currentRoute.route.transition),!r&&e.currentRoute&&e.currentRoute.route.options&&(r=e.currentRoute.route.options.transition),r&&(b[0].f7PageTransition=r),t?setTimeout((()=>{B(),e.animate(w,b,E,k,"forward",r,(()=>{M()}))}),t):(B(),e.animate(w,b,E,k,"forward",r,(()=>{M()})))}return e}function qe(e,t,r,a){if(void 0===t&&(t={}),void 0===r&&(r={}),!e.allowPageChange&&!a)return e;const n=t,o=r,{url:s,content:i,el:l,pageName:c,component:u,componentUrl:p}=n;if(!o.reloadCurrent&&o.route&&o.route.route&&o.route.route.parentPath&&e.currentRoute.route&&e.currentRoute.route.parentPath===o.route.route.parentPath){if(o.route.url===e.url)return e.allowPageChange=!0,!1;let t=Object.keys(o.route.params).length===Object.keys(e.currentRoute.params).length;if(t&&Object.keys(o.route.params).forEach((r=>{r in e.currentRoute.params&&e.currentRoute.params[r]===o.route.params[r]||(t=!1)})),t)return!!o.route.route.tab&&e.tabLoad(o.route.route.tab,o);if(!t&&o.route.route.tab&&e.currentRoute.route.tab&&e.currentRoute.parentPath===o.route.parentPath)return e.tabLoad(o.route.route.tab,o)}if(o.route&&o.route.url&&e.url===o.route.url&&!o.reloadCurrent&&!o.reloadPrevious&&!e.params.allowDuplicateUrls)return e.allowPageChange=!0,!1;if(!o.route&&s&&(o.route=e.parseRouteUrl(s),J(o.route,{route:{url:s,path:s}})),(s||p||u)&&(e.allowPageChange=!1),i)De(e,e.getPageEl(i),o);else if(l)De(e,e.getPageEl(l),o);else if(c)De(e,e.$el.children(`.page[data-name="${c}"]`).eq(0),o);else if(u||p)try{e.pageComponentLoader({routerEl:e.el,component:u,componentUrl:p,options:o,resolve:function(t,r){return De(e,t,J(o,r))},reject:function(){return e.allowPageChange=!0,e}})}catch(t){throw e.allowPageChange=!0,t}else s&&(e.xhrAbortController&&(e.xhrAbortController.abort(),e.xhrAbortController=!1),e.xhrRequest(s,o).then((t=>{De(e,e.getPageEl(t),o)})).catch((()=>{e.allowPageChange=!0})));return e}function je(e,t,r){const n=ie(),o=a(),s=N(t),i=e.app,l=e.view,c=J(!1,{animate:e.params.animate,browserHistory:!0,replaceState:!1},r),u=e.params.masterDetailBreakpoint>0,p=u&&c.route&&c.route.route&&(!0===c.route.route.master||"function"==typeof c.route.route.master&&c.route.route.master(i,e));let d,h;const f=e.dynamicNavbar,m=s,g=e.$el.children(".page-current"),v=0===g.length&&c.preload,b=u&&g.hasClass("page-master");let y,w,C,k,E,$;if(m.length&&e.removeThemeElements(m),f&&(w=m.children(".navbar"),y=e.$navbarsEl,0===w.length&&m[0]&&m[0].f7Page&&(w=m[0].f7Page.$navbarEl),C=y.find(".navbar-current")),e.allowPageChange=!1,0===m.length||0===g.length&&!c.preload)return e.allowPageChange=!0,e;if(e.removeThemeElements(m),c.route&&c.route.route&&c.route.route.keepAlive&&!c.route.route.keepAliveData&&(c.route.route.keepAliveData={pageEl:s[0]}),u){const t=e.$el.children(".page:not(.stacked)").filter((e=>e!==m[0]));for(let e=0;e<t.length;e+=1)d||!t[e].classList.contains("page-master")||(d=t[e]);k=!p&&d&&e.history.indexOf(c.route.url)>e.history.indexOf(d.f7Page.route.url),!k&&!p&&d&&d.f7Page&&c.route.route.masterRoute&&(k=c.route.route.masterRoute.path===d.f7Page.route.route.path)}if(k&&d&&d.f7Page&&(E=e.history.indexOf(c.route.url)-e.history.indexOf(d.f7Page.route.url)==1),m.addClass(`page-${v?"current":"previous"}${p?" page-master":""}${k?" page-master-detail":""}${E?" page-master-detail-root":""}`).removeClass("stacked").removeAttr("aria-hidden").trigger("page:unstack").trigger("page:position",{position:v?"current":"previous"}),e.emit("pageUnstack",m[0]),e.emit("pagePosition",m[0],v?"current":"previous"),(p||k)&&(m.trigger("page:role",{role:p?"master":"detail",root:!!E}),e.emit("pageRole",m[0],{role:p?"master":"detail",detailRoot:!!E})),f&&w.length>0&&(w.addClass(`navbar-${v?"current":"previous"}${p?" navbar-master":""}${k?" navbar-master-detail":""}${E?" navbar-master-detail-root":""}`).removeClass("stacked").removeAttr("aria-hidden"),w.trigger("navbar:position",{position:v?"current":"previous"}),e.emit("navbarPosition",w[0],v?"current":"previous"),(p||E)&&e.emit("navbarRole",w[0],{role:p?"master":"detail",detailRoot:!!E})),c.force&&(g.prev(".page-previous:not(.stacked)").length>0||0===g.prev(".page-previous").length))if(e.history.indexOf(c.route.url)>=0?($=e.history.length-e.history.indexOf(c.route.url)-1,e.history=e.history.slice(0,e.history.indexOf(c.route.url)+2),l.history=e.history):e.history[[e.history.length-2]]?e.history[e.history.length-2]=c.route.url:e.history.unshift(e.url),$&&e.params.stackPages)g.prevAll(".page-previous").each((t=>{const r=N(t);let a;f&&(a=N(i.navbar.getElByPage(r))),r[0]!==m[0]&&r.index()>m.index()&&(e.initialPages.indexOf(r[0])>=0?(r.addClass("stacked"),r.trigger("page:stack"),e.emit("pageStack",r[0]),f&&a.addClass("stacked")):(e.pageCallback("beforeRemove",r,a,"previous",void 0,c),r[0]===d&&(h=!0),e.removePage(r),f&&a.length>0&&e.removeNavbar(a)))}));else{const t=g.prev(".page-previous:not(.stacked)");let r;f&&(r=N(i.navbar.getElByPage(t))),e.params.stackPages&&e.initialPages.indexOf(t[0])>=0?(t.addClass("stacked"),t.trigger("page:stack"),e.emit("pageStack",t[0]),r.addClass("stacked")):t.length>0&&(e.pageCallback("beforeRemove",t,r,"previous",void 0,c),t[0]===d&&(h=!0),e.removePage(t),f&&r.length&&e.removeNavbar(r))}const x=m.parents(o).length>0,P=m[0].f7Component;function O(){v&&(!x&&P?P.mount((t=>{e.$el.append(t)})):e.$el.append(m)),0===m.next(g).length&&(!x&&P?P.mount((e=>{N(e).insertBefore(g)})):m.insertBefore(g)),f&&w.length&&(w.find(".title-large").length&&w.addClass("navbar-large"),w.insertBefore(C),C.length>0?w.insertBefore(C):(e.$navbarsEl.parents(o).length||e.$el.prepend(e.$navbarsEl),y.append(w))),x?c.route&&c.route.route&&c.route.route.keepAlive&&!m[0].f7PageMounted&&(m[0].f7PageMounted=!0,e.pageCallback("mounted",m,w,"previous","current",c,g)):e.pageCallback("mounted",m,w,"previous","current",c,g)}if(c.preload){O(),c.route.route.tab&&e.tabLoad(c.route.route.tab,J({},c,{history:!1,browserHistory:!1,preload:!0})),p&&(m.removeClass("page-master-stacked").trigger("page:masterunstack"),e.emit("pageMasterUnstack",m[0]),f&&(N(i.navbar.getElByPage(m)).removeClass("navbar-master-stacked"),e.emit("navbarMasterUnstack",i.navbar.getElByPage(m)))),e.pageCallback("init",m,w,"previous","current",c,g),v&&(e.pageCallback("beforeIn",m,w,"current",void 0,c),e.pageCallback("afterIn",m,w,"current",void 0,c));const t=m.prevAll(".page-previous:not(.stacked):not(.page-master)");return t.length>0&&t.each((t=>{const r=N(t);let a;f&&(a=N(i.navbar.getElByPage(r))),e.params.stackPages&&e.initialPages.indexOf(t)>=0?(r.addClass("stacked"),r.trigger("page:stack"),e.emit("pageStack",r[0]),f&&a.addClass("stacked")):(e.pageCallback("beforeRemove",r,a,"previous",void 0),e.removePage(r),f&&a.length&&e.removeNavbar(a))})),e.allowPageChange=!0,e}if(!(n.ie||n.edge||n.firefox&&!n.ios)&&e.params.browserHistory&&c.browserHistory)if(c.replaceState){const t=e.params.browserHistoryRoot||"";Ae.replace(l.id,{url:c.route.url},t+e.params.browserHistorySeparator+c.route.url)}else $?Ae.go(-$):Ae.back();if(c.replaceState?e.history[e.history.length-1]=c.route.url:(1===e.history.length&&e.history.unshift(e.url),e.history.pop()),e.saveHistory(),e.currentPageEl=m[0],f&&w.length?e.currentNavbarEl=w[0]:delete e.currentNavbarEl,e.currentRoute=c.route,(n.ie||n.edge||n.firefox&&!n.ios)&&e.params.browserHistory&&c.browserHistory)if(c.replaceState){const t=e.params.browserHistoryRoot||"";Ae.replace(l.id,{url:c.route.url},t+e.params.browserHistorySeparator+c.route.url)}else $?Ae.go(-$):Ae.back();function R(){e.setPagePosition(m,"current",!1),e.setPagePosition(g,"next",!0),f&&(e.setNavbarPosition(w,"current",!1),e.setNavbarPosition(C,"next",!0)),e.pageCallback("afterOut",g,C,"current","next",c),e.pageCallback("afterIn",m,w,"previous","current",c),e.params.stackPages&&e.initialPages.indexOf(g[0])>=0?(g.addClass("stacked"),g.trigger("page:stack"),e.emit("pageStack",g[0]),f&&C.addClass("stacked")):(e.pageCallback("beforeRemove",g,C,"next",void 0,c),e.removePage(g),f&&C.length&&e.removeNavbar(C)),e.allowPageChange=!0,e.emit("routeChanged",e.currentRoute,e.previousRoute,e);(e.params.preloadPreviousPage||e.params[`${i.theme}SwipeBack`])&&e.history[e.history.length-2]&&!p&&e.back(e.history[e.history.length-2],{preload:!0}),e.params.browserHistory&&Ae.clearRouterQueue()}if(O(),c.route.route.tab&&e.tabLoad(c.route.route.tab,J({},c,{history:!1,browserHistory:!1})),u&&(b||h)&&l.checkMasterDetailBreakpoint(!1),e.pageCallback("init",m,w,"previous","current",c,g),e.pageCallback("beforeOut",g,C,"current","next",c),e.pageCallback("beforeIn",m,w,"previous","current",c),!c.animate||b&&i.width>=e.params.masterDetailBreakpoint)R();else{let t=e.params.transition;g[0]&&g[0].f7PageTransition&&(t=g[0].f7PageTransition,delete g[0].f7PageTransition),c.transition&&(t=c.transition),!t&&e.previousRoute&&e.previousRoute.route&&(t=e.previousRoute.route.transition),!t&&e.previousRoute&&e.previousRoute.route&&e.previousRoute.route.options&&(t=e.previousRoute.route.options.transition),e.setPagePosition(g,"current"),e.setPagePosition(m,"previous",!1),f&&(e.setNavbarPosition(C,"current"),e.setNavbarPosition(w,"previous",!1)),e.animate(g,m,C,w,"backward",t,(()=>{R()}))}return e}function Ie(e,t,r,a){if(!e.allowPageChange&&!a)return e;const n=t,o=r,{url:s,content:i,el:l,pageName:c,component:u,componentUrl:p}=n;if(o.route.url&&e.url===o.route.url&&!o.reloadCurrent&&!o.reloadPrevious&&!e.params.allowDuplicateUrls)return!1;if(!o.route&&s&&(o.route=e.parseRouteUrl(s)),(s||p||u)&&(e.allowPageChange=!1),i)je(e,e.getPageEl(i),o);else if(l)je(e,e.getPageEl(l),o);else if(c)je(e,e.$el.children(`.page[data-name="${c}"]`).eq(0),o);else if(u||p)try{e.pageComponentLoader({routerEl:e.el,component:u,componentUrl:p,options:o,resolve:function(t,r){return je(e,t,J(o,r))},reject:function(){return e.allowPageChange=!0,e}})}catch(t){throw e.allowPageChange=!0,t}else s&&(e.xhrAbortController&&(e.xhrAbortController.abort(),e.xhrAbortController=!1),e.xhrRequest(s,o).then((t=>{je(e,e.getPageEl(t),o)})).catch((()=>{e.allowPageChange=!0})));return e}class Ue extends ce{constructor(e,t){super({},[void 0===t?e:t]);const r=this;r.isAppRouter=void 0===t,r.isAppRouter?J(!1,r,{app:e,params:e.params.view,routes:e.routes||[],cache:e.cache}):J(!1,r,{app:e,view:t,viewId:t.id,id:t.params.routerId,params:t.params,routes:t.routes,history:t.history,scrollHistory:t.scrollHistory,cache:e.cache,dynamicNavbar:"ios"===e.theme&&t.params.iosDynamicNavbar,initialPages:[],initialNavbars:[]}),r.useModules(),r.allowPageChange=!0;let a={},n={};return Object.defineProperty(r,"currentRoute",{enumerable:!0,configurable:!0,set(e){void 0===e&&(e={}),n=J({},a),a=e,a&&(r.url=a.url,r.emit("routeChange",e,n,r))},get:()=>a}),Object.defineProperty(r,"previousRoute",{enumerable:!0,configurable:!0,get:()=>n,set(e){n=e}}),r}mount(){const e=this,t=e.view;J(!1,e,{tempDom:a().createElement("div"),$el:t.$el,el:t.el,$navbarsEl:t.$navbarsEl,navbarsEl:t.navbarsEl}),e.emit("local::mount routerMount",e)}animatableNavElements(e,t,r,a,n){const o=this,s=o.dynamicNavbar,i=o.params.iosAnimateNavbarBackIcon;let l,c;function u(e,t){const r=e.hasClass("sliding")||t.hasClass("sliding"),a=e.hasClass("subnavbar"),n=!r||!a,o=e.find(".back .icon");let s;return r&&i&&e.hasClass("left")&&o.length>0&&o.next("span").length&&(e=o.next("span"),s=!0),{$el:e,isIconLabel:s,leftOffset:e[0].f7NavbarLeftOffset,rightOffset:e[0].f7NavbarRightOffset,isSliding:r,isSubnavbar:a,needsOpacityTransition:n}}return s&&(l=[],c=[],e.children(".navbar-inner").children(".left, .right, .title, .subnavbar").each((t=>{const o=N(t);o.hasClass("left")&&a&&"forward"===n||o.hasClass("title")&&r||l.push(u(o,e.children(".navbar-inner")))})),t.hasClass("navbar-master")&&o.params.masterDetailBreakpoint>0&&o.app.width>=o.params.masterDetailBreakpoint||t.children(".navbar-inner").children(".left, .right, .title, .subnavbar").each((e=>{const o=N(e);o.hasClass("left")&&r&&!a&&"forward"===n||o.hasClass("left")&&r&&"backward"===n||o.hasClass("title")&&a||c.push(u(o,t.children(".navbar-inner")))})),[c,l].forEach((e=>{e.forEach((t=>{const r=t,{isSliding:a,$el:n}=t,o=e===c?l:c;a&&n.hasClass("title")&&o&&o.forEach((e=>{if(e.isIconLabel){const t=e.$el[0];r.leftOffset+=t&&t.offsetLeft||0}}))}))}))),{newNavEls:l,oldNavEls:c}}animate(e,t,r,a,n,o,s){const i=this;if(i.params.animateCustom)return void i.params.animateCustom.apply(i,[e,t,r,a,n,s]);const l=i.dynamicNavbar,c="ios"===i.app.theme;if(o){const c=`router-transition-custom router-transition-${o}-${n}`,u=()=>{i.$el.removeClass(c),l&&i.$navbarsEl.length&&(a&&i.$navbarsEl.prepend(a),r&&i.$navbarsEl.prepend(r)),s&&s()};return("forward"===n?t:e).animationEnd(u),l&&(a&&t&&(i.setNavbarPosition(a,""),a.removeClass("navbar-next navbar-previous navbar-current"),t.prepend(a)),r&&e&&(i.setNavbarPosition(r,""),r.removeClass("navbar-next navbar-previous navbar-current"),e.prepend(r))),void i.$el.addClass(c)}const u=`router-transition-${n} router-transition`;let p,d,h,f,m,g,v;if(c&&l){i.params.masterDetailBreakpoint>0&&i.app.width>=i.params.masterDetailBreakpoint&&(r.hasClass("navbar-master")&&a.hasClass("navbar-master-detail")||r.hasClass("navbar-master-detail")&&a.hasClass("navbar-master"))||(g=r&&r.hasClass("navbar-large"),v=a&&a.hasClass("navbar-large"),h=g&&!r.hasClass("navbar-large-collapsed"),f=v&&!a.hasClass("navbar-large-collapsed"),m=h&&!f||f&&!h);const e=i.animatableNavElements(a,r,f,h,n);p=e.newNavEls,d=e.oldNavEls}function b(e){c&&l&&(1===e&&(f&&(a.addClass("router-navbar-transition-to-large"),r.addClass("router-navbar-transition-to-large")),h&&(a.addClass("router-navbar-transition-from-large"),r.addClass("router-navbar-transition-from-large"))),p.forEach((t=>{const r=t.$el,a="forward"===n?t.rightOffset:t.leftOffset;t.isSliding&&(t.isSubnavbar&&v?r[0].style.setProperty("transform",`translate3d(${a*(1-e)}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`,"important"):r.transform(`translate3d(${a*(1-e)}px,0,0)`))})),d.forEach((t=>{const r=t.$el,a="forward"===n?t.leftOffset:t.rightOffset;t.isSliding&&(t.isSubnavbar&&g?r.transform(`translate3d(${a*e}px, calc(-1 * var(--f7-navbar-large-collapse-progress) * var(--f7-navbar-large-title-height)), 0)`):r.transform(`translate3d(${a*e}px,0,0)`))})))}("forward"===n?t:e).animationEnd((()=>{i.dynamicNavbar&&(a&&(a.removeClass("router-navbar-transition-to-large router-navbar-transition-from-large"),a.addClass("navbar-no-title-large-transition"),V((()=>{a.removeClass("navbar-no-title-large-transition")}))),r&&r.removeClass("router-navbar-transition-to-large router-navbar-transition-from-large"),a.hasClass("sliding")||a.children(".navbar-inner.sliding").length?a.find(".title, .left, .right, .left .icon, .subnavbar").transform(""):a.find(".sliding").transform(""),r.hasClass("sliding")||r.children(".navbar-inner.sliding").length?r.find(".title, .left, .right, .left .icon, .subnavbar").transform(""):r.find(".sliding").transform("")),i.$el.removeClass(u),s&&s()})),l?(b(0),V((()=>{i.$el.addClass(u),m&&(i.el._clientLeft=i.el.clientLeft),b(1)}))):i.$el.addClass(u)}removeModal(e){this.removeEl(e)}removeTabContent(e){N(e).html("")}removeNavbar(e){this.removeEl(e)}removePage(e){const t=N(e),r=t&&t[0]&&t[0].f7Page;r&&r.route&&r.route.route&&r.route.route.keepAlive?t.remove():this.removeEl(e)}removeEl(e){if(!e)return;const t=this,r=N(e);0!==r.length&&(r.find(".tab").each((e=>{N(e).children().each((e=>{e.f7Component&&(N(e).trigger("tab:beforeremove"),e.f7Component.destroy())}))})),r[0].f7Component&&r[0].f7Component.destroy&&r[0].f7Component.destroy(),t.params.removeElements&&(t.params.removeElementsWithTimeout?setTimeout((()=>{r.remove()}),t.params.removeElementsTimeout):r.remove()))}getPageEl(e){const t=this;if("string"==typeof e)t.tempDom.innerHTML=e;else{if(N(e).hasClass("page"))return e;t.tempDom.innerHTML="",N(t.tempDom).append(e)}return t.findElement(".page",t.tempDom)}findElement(e,t,r){const a=this,n=a.view,o=a.app,s=N(t);let i=e;r&&(i+=":not(.stacked)");let l=s.find(i).filter((e=>0===N(e).parents(".popup, .dialog, .popover, .actions-modal, .sheet-modal, .login-screen, .page").length));return l.length>1&&("string"==typeof n.selector&&(l=s.find(`${n.selector} ${i}`)),l.length>1&&(l=s.find(`.${o.params.viewMainClass} ${i}`))),1===l.length?l:(r||(l=a.findElement(i,s,!0)),l&&1===l.length?l:l&&l.length>1?N(l[0]):void 0)}flattenRoutes(e){void 0===e&&(e=this.routes);const t=this;let r=[];return e.forEach((e=>{let a=!1;if("tabs"in e&&e.tabs){const n=e.tabs.map((t=>{const r=J({},e,{path:`${e.path}/${t.path}`.replace("///","/").replace("//","/"),parentPath:e.path,tab:t});return delete r.tabs,delete r.routes,r}));a=!0,r=r.concat(t.flattenRoutes(n))}if("detailRoutes"in e){const a=e.detailRoutes.map((t=>{const r=J({},t);return r.masterRoute=e,r.masterRoutePath=e.path,r}));r=r.concat(e,t.flattenRoutes(a))}if("routes"in e){const n=e.routes.map((t=>{const r=J({},t);return r.path=`${e.path}/${r.path}`.replace("///","/").replace("//","/"),r}));r=a?r.concat(t.flattenRoutes(n)):r.concat(e,t.flattenRoutes(n))}"routes"in e||"tabs"in e&&e.tabs||"detailRoutes"in e||r.push(e)})),r}parseRouteUrl(e){if(!e)return{};const t=F(e),r=e.split("#")[1],a=e.split("#")[0].split("?")[0];return{query:t,hash:r,params:{},url:e,path:a}}generateUrl(e){if(void 0===e&&(e={}),"string"==typeof e)return e;const{name:t,path:r,params:a,query:n}=e;if(!t&&!r)throw new Error('Framework7: "name" or "path" parameter is required');const o=this,s=t?o.findRouteByKey("name",t):o.findRouteByKey("path",r);if(!s)throw t?new Error(`Framework7: route with name "${t}" not found`):new Error(`Framework7: route with path "${r}" not found`);const i=o.constructRouteUrl(s,{params:a,query:n});if(!i)throw new Error(`Framework7: can't construct URL for route with name "${t}"`);return i}constructRouteUrl(e,t){let{params:r,query:a}=void 0===t?{}:t;const{path:n}=e,o=Pe(n);let s;try{s=o(r||{})}catch(e){throw new Error(`Framework7: error constructing route URL from passed params:\nRoute: ${n}\n${e.toString()}`)}return a&&("string"==typeof a?s+=`?${a}`:Object.keys(a).length&&(s+=`?${X(a)}`)),s}findTabRouteUrl(e){const t=this,r=N(e),a=t.currentRoute.route.parentPath,n=r.attr("id");let o;return t.flattenRoutes(t.routes).forEach((e=>{e.parentPath===a&&e.tab&&e.tab.id===n&&(o=t.currentRoute.params&&Object.keys(t.currentRoute.params).length>0?t.constructRouteUrl(e,{params:t.currentRoute.params,query:t.currentRoute.query}):e.path)})),o}findRouteByKey(e,t){const r=this.routes;let a;return this.flattenRoutes(r).forEach((r=>{a||r[e]===t&&(a=r)})),a}findMatchingRoute(e){if(!e)return;const t=this,r=t.routes,a=t.flattenRoutes(r),{path:n,query:o,hash:s,params:i}=t.parseRouteUrl(e);let l;return a.forEach((t=>{if(l)return;const r=[],a=[t.path];let c;if(t.alias&&("string"==typeof t.alias?a.push(t.alias):Array.isArray(t.alias)&&t.alias.forEach((e=>{a.push(e)}))),a.forEach((e=>{c||(c=Se(e,r).exec(n))})),c){let a;r.forEach(((e,t)=>{if("number"==typeof e.name)return;const r=c[t+1];i[e.name]=null==r?r:decodeURIComponent(r)})),t.parentPath&&(a=n.split("/").slice(0,t.parentPath.split("/").length-1).join("/")),l={query:o,hash:s,params:i,url:e,path:n,parentPath:a,route:t,name:t.name}}})),l}replaceRequestUrlParams(e,t){void 0===e&&(e=""),void 0===t&&(t={});let r=e;return"string"==typeof r&&r.indexOf("{{")>=0&&t&&t.route&&t.route.params&&Object.keys(t.route.params).length&&Object.keys(t.route.params).forEach((e=>{const a=new RegExp(`{{${e}}}`,"g");r=r.replace(a,t.route.params[e]||"")})),r}removeFromXhrCache(e){const t=this.cache.xhr;let r=!1;for(let a=0;a<t.length;a+=1)t[a].url===e&&(r=a);!1!==r&&t.splice(r,1)}xhrRequest(e,t){const r=this,a=r.params,{ignoreCache:n}=t;let o=e,s=o.indexOf("?")>=0;return a.passRouteQueryToRequest&&t&&t.route&&t.route.query&&Object.keys(t.route.query).length&&(o+=`${s?"&":"?"}${X(t.route.query)}`,s=!0),a.passRouteParamsToRequest&&t&&t.route&&t.route.params&&Object.keys(t.route.params).length&&(o+=`${s?"&":"?"}${X(t.route.params)}`,s=!0),o.indexOf("{{")>=0&&(o=r.replaceRequestUrlParams(o,t)),a.xhrCacheIgnoreGetParameters&&o.indexOf("?")>=0&&(o=o.split("?")[0]),new Promise(((e,s)=>{if(a.xhrCache&&!n&&o.indexOf("nocache")<0&&a.xhrCacheIgnore.indexOf(o)<0)for(let t=0;t<r.cache.xhr.length;t+=1){const n=r.cache.xhr[t];if(n.url===o&&W()-n.time<a.xhrCacheDuration)return void e(n.content)}r.xhrAbortController=r.app.request.abortController(),r.app.request({abortController:r.xhrAbortController,url:o,method:"GET",beforeSend(e){r.emit("routerAjaxStart",e,t)},complete(n,i){r.emit("routerAjaxComplete",n),"error"!==i&&"timeout"!==i&&n.status>=200&&n.status<300||0===n.status?(a.xhrCache&&""!==n.responseText&&(r.removeFromXhrCache(o),r.cache.xhr.push({url:o,time:W(),content:n.responseText})),r.emit("routerAjaxSuccess",n,t),e(n.responseText)):(r.emit("routerAjaxError",n,t),s(n))},error(e){r.emit("routerAjaxError",e,t),s(e)}})}))}setNavbarPosition(e,t,r){e.removeClass("navbar-previous navbar-current navbar-next"),t&&e.addClass(`navbar-${t}`),!1===r?e.removeAttr("aria-hidden"):!0===r&&e.attr("aria-hidden","true"),e.trigger("navbar:position",{position:t}),this.emit("navbarPosition",e[0],t)}setPagePosition(e,t,r){e.removeClass("page-previous page-current page-next"),e.addClass(`page-${t}`),!1===r?e.removeAttr("aria-hidden"):!0===r&&e.attr("aria-hidden","true"),e.trigger("page:position",{position:t}),this.emit("pagePosition",e[0],t)}removeThemeElements(e){const t=this.app.theme;let r;"ios"===t?r=".md-only, .aurora-only, .if-md, .if-aurora, .if-not-ios, .not-ios":"md"===t?r=".ios-only, .aurora-only, .if-ios, .if-aurora, .if-not-md, .not-md":"aurora"===t&&(r=".ios-only, .md-only, .if-ios, .if-md, .if-not-aurora, .not-aurora"),N(e).find(r).remove()}getPageData(e,t,r,a,n,o){void 0===n&&(n={});const s=this,i=N(e).eq(0),l=N(t).eq(0),c=i[0].f7Page||{};let u,p;if(("next"===r&&"current"===a||"current"===r&&"previous"===a)&&(u="forward"),("current"===r&&"next"===a||"previous"===r&&"current"===a)&&(u="backward"),c&&!c.fromPage){const e=N(o);e.length&&(p=e[0].f7Page)}p=c.pageFrom||p,p&&p.pageFrom&&(p.pageFrom=null);const d={app:s.app,view:s.view,router:s,$el:i,el:i[0],$pageEl:i,pageEl:i[0],$navbarEl:l,navbarEl:l[0],name:i.attr("data-name"),position:r,from:r,to:a,direction:u,route:c.route?c.route:n,pageFrom:p};return i[0].f7Page=d,d}pageCallback(e,t,r,a,n,o,s){if(void 0===o&&(o={}),!t)return;const i=this,l=N(t);if(!l.length)return;const c=N(r),{route:u}=o,p=i.params.restoreScrollTopOnBack&&!(i.params.masterDetailBreakpoint>0&&l.hasClass("page-master")&&i.app.width>=i.params.masterDetailBreakpoint),d=l[0].f7Page&&l[0].f7Page.route&&l[0].f7Page.route.route&&l[0].f7Page.route.route.keepAlive;"beforeRemove"===e&&d&&(e="beforeUnmount");const h=`page${e[0].toUpperCase()+e.slice(1,e.length)}`,f=`page:${e.toLowerCase()}`;let m={};m="beforeRemove"===e&&l[0].f7Page?J(l[0].f7Page,{from:a,to:n,position:a}):i.getPageData(l[0],c[0],a,n,u,s),m.swipeBack=!!o.swipeBack;const{on:g={},once:v={}}=o.route?o.route.route:{};function b(){l[0].f7RouteEventsAttached||(l[0].f7RouteEventsAttached=!0,g&&Object.keys(g).length>0&&(l[0].f7RouteEventsOn=g,Object.keys(g).forEach((e=>{g[e]=g[e].bind(i),l.on(j(e),g[e])}))),v&&Object.keys(v).length>0&&(l[0].f7RouteEventsOnce=v,Object.keys(v).forEach((e=>{v[e]=v[e].bind(i),l.once(j(e),v[e])}))))}if(o.on&&J(g,o.on),o.once&&J(v,o.once),"mounted"===e&&b(),"init"===e){if(p&&("previous"===a||!a)&&"current"===n&&i.scrollHistory[m.route.url]&&!l.hasClass("no-restore-scroll")){let e=l.find(".page-content");e.length>0&&(e=e.filter((e=>0===N(e).parents(".tab:not(.tab-active)").length&&!N(e).is(".tab:not(.tab-active)")))),e.scrollTop(i.scrollHistory[m.route.url])}if(b(),l[0].f7PageInitialized)return l.trigger("page:reinit",m),void i.emit("pageReinit",m);l[0].f7PageInitialized=!0}if(p&&"beforeOut"===e&&"current"===a&&"previous"===n){let e=l.find(".page-content");e.length>0&&(e=e.filter((e=>0===N(e).parents(".tab:not(.tab-active)").length&&!N(e).is(".tab:not(.tab-active)")))),i.scrollHistory[m.route.url]=e.scrollTop()}p&&"beforeOut"===e&&"current"===a&&"next"===n&&delete i.scrollHistory[m.route.url],l.trigger(f,m),i.emit(h,m),"beforeRemove"!==e&&"beforeUnmount"!==e||(l[0].f7RouteEventsAttached&&(l[0].f7RouteEventsOn&&Object.keys(l[0].f7RouteEventsOn).forEach((e=>{l.off(j(e),l[0].f7RouteEventsOn[e])})),l[0].f7RouteEventsOnce&&Object.keys(l[0].f7RouteEventsOnce).forEach((e=>{l.off(j(e),l[0].f7RouteEventsOnce[e])})),l[0].f7RouteEventsAttached=null,l[0].f7RouteEventsOn=null,l[0].f7RouteEventsOnce=null,delete l[0].f7RouteEventsAttached,delete l[0].f7RouteEventsOn,delete l[0].f7RouteEventsOnce),d||(l[0].f7Page&&l[0].f7Page.navbarEl&&delete l[0].f7Page.navbarEl.f7Page,l[0].f7Page=null))}saveHistory(){const e=this,t=o();e.view.history=e.history,e.params.browserHistory&&e.params.browserHistoryStoreHistory&&t.localStorage&&(t.localStorage[`f7router-${e.view.id}-history`]=JSON.stringify(e.history))}restoreHistory(){const e=this,t=o();e.params.browserHistory&&e.params.browserHistoryStoreHistory&&t.localStorage&&t.localStorage[`f7router-${e.view.id}-history`]&&(e.history=JSON.parse(t.localStorage[`f7router-${e.view.id}-history`]),e.view.history=e.history)}clearHistory(){const e=this;e.history=[],e.view&&(e.view.history=[]),e.saveHistory()}updateCurrentUrl(e){const t=this;He(t,"updateCurrentUrl"),t.history.length?t.history[t.history.length-1]=e:t.history.push(e);const{query:r,hash:a,params:n,url:o,path:s}=t.parseRouteUrl(e);if(t.currentRoute&&J(t.currentRoute,{query:r,hash:a,params:n,url:o,path:s}),t.params.browserHistory){const r=t.params.browserHistoryRoot||"";Ae.replace(t.view.id,{url:e},r+t.params.browserHistorySeparator+e)}t.saveHistory(),t.emit("routeUrlUpdate",t.currentRoute,t)}getInitialUrl(){const e=this;if(e.initialUrl)return{initialUrl:e.initialUrl,historyRestored:e.historyRestored};const{app:t,view:r}=e,n=a(),s=o(),i=t.params.url&&"string"==typeof t.params.url&&"undefined"!=typeof URL?new URL(t.params.url):n.location;let l,c=e.params.url,u=i.href.split(i.origin)[1];const{browserHistory:p,browserHistoryOnLoad:d,browserHistorySeparator:h}=e.params;let{browserHistoryRoot:f}=e.params;return(s.cordova||s.Capacitor&&s.Capacitor.isNative)&&p&&!h&&!f&&i.pathname.indexOf("index.html")&&(console.warn("Framework7: wrong or not complete browserHistory configuration, trying to guess browserHistoryRoot"),f=i.pathname.split("index.html")[0]),p&&d?(f&&u.indexOf(f)>=0&&(u=u.split(f)[1],""===u&&(u="/")),c=h.length>0&&u.indexOf(h)>=0?u.split(h)[1]:u,e.restoreHistory(),e.history.indexOf(c)>=0?e.history=e.history.slice(0,e.history.indexOf(c)+1):e.params.url===c?e.history=[c]:Ae.state&&Ae.state[r.id]&&Ae.state[r.id].url===e.history[e.history.length-1]?c=e.history[e.history.length-1]:e.history=[u.split(h)[0]||"/",c],e.history.length>1?l=!0:e.history=[],e.saveHistory()):(c||(c=u),i.search&&c.indexOf("?")<0&&(c+=i.search),i.hash&&c.indexOf("#")<0&&(c+=i.hash)),e.initialUrl=c,e.historyRestored=l,{initialUrl:c,historyRestored:l}}init(){const e=this,{app:t,view:r}=e,n=a();e.mount();const{initialUrl:o,historyRestored:s}=e.getInitialUrl();(r&&e.params.iosSwipeBack&&"ios"===t.theme||r&&e.params.mdSwipeBack&&"md"===t.theme||r&&e.params.auroraSwipeBack&&"aurora"===t.theme)&&Le(e);const{browserHistory:i,browserHistoryOnLoad:l,browserHistoryAnimateOnLoad:c,browserHistoryInitialMatch:u}=e.params;let p;if(e.history.length>1){const t=u?o:e.history[0];p=e.findMatchingRoute(t),p||(p=J(e.parseRouteUrl(t),{route:{url:t,path:t.split("?")[0]}}))}else p=e.findMatchingRoute(o),p||(p=J(e.parseRouteUrl(o),{route:{url:o,path:o.split("?")[0]}}));if(e.params.stackPages&&e.$el.children(".page").each((t=>{const r=N(t);e.initialPages.push(r[0]),e.dynamicNavbar&&r.children(".navbar").length>0&&e.initialNavbars.push(r.children(".navbar")[0])})),0===e.$el.children(".page:not(.stacked)").length&&o&&e.params.loadInitialPage)e.navigate(o,{initial:!0,reloadCurrent:!0,browserHistory:!1,animate:!1,once:{modalOpen(){if(!s)return;(e.params.preloadPreviousPage||e.params[`${t.theme}SwipeBack`])&&e.history.length>1&&e.back({preload:!0})},pageAfterIn(){if(!s)return;(e.params.preloadPreviousPage||e.params[`${t.theme}SwipeBack`])&&e.history.length>1&&e.back({preload:!0})}}});else if(e.$el.children(".page:not(.stacked)").length){let a;if(e.currentRoute=p,e.$el.children(".page:not(.stacked)").each((o=>{const s=N(o);let i;e.setPagePosition(s,"current"),e.dynamicNavbar&&(i=s.children(".navbar"),i.length>0?(e.$navbarsEl.parents(n).length||e.$el.prepend(e.$navbarsEl),e.setNavbarPosition(i,"current"),e.$navbarsEl.append(i),i.children(".title-large").length&&i.addClass("navbar-large"),s.children(".navbar").remove()):(e.$navbarsEl.addClass("navbar-hidden"),i.children(".title-large").length&&e.$navbarsEl.addClass("navbar-hidden navbar-large-hidden"))),e.currentRoute&&e.currentRoute.route&&(!0===e.currentRoute.route.master||"function"==typeof e.currentRoute.route.master&&e.currentRoute.route.master(t,e))&&e.params.masterDetailBreakpoint>0&&(s.addClass("page-master"),s.trigger("page:role",{role:"master"}),i&&i.length&&i.addClass("navbar-master"),r.checkMasterDetailBreakpoint());const l={route:e.currentRoute};e.currentRoute&&e.currentRoute.route&&e.currentRoute.route.options&&J(l,e.currentRoute.route.options),e.currentPageEl=s[0],e.dynamicNavbar&&i.length&&(e.currentNavbarEl=i[0]),e.removeThemeElements(s),e.dynamicNavbar&&i.length&&e.removeThemeElements(i),l.route.route.tab&&(a=!0,e.tabLoad(l.route.route.tab,J({},l))),e.pageCallback("init",s,i,"current",void 0,l),e.pageCallback("beforeIn",s,i,"current",void 0,l),e.pageCallback("afterIn",s,i,"current",void 0,l)})),s)if(u){(e.params.preloadPreviousPage||e.params[`${t.theme}SwipeBack`])&&e.history.length>1&&e.back({preload:!0})}else e.navigate(o,{initial:!0,browserHistory:!1,history:!1,animate:c,once:{pageAfterIn(){(e.params.preloadPreviousPage||e.params[`${t.theme}SwipeBack`])&&e.history.length>2&&e.back({preload:!0})}}});s||a||(e.history.push(o),e.saveHistory())}!(o&&i&&l)||Ae.state&&Ae.state[r.id]||Ae.initViewState(r.id,{url:o}),e.emit("local::init routerInit",e)}destroy(){let e=this;e.emit("local::destroy routerDestroy",e),Object.keys(e).forEach((t=>{e[t]=null,delete e[t]})),e=null}}Ue.prototype.navigate=function(e,t){void 0===t&&(t={});const r=this;if(r.swipeBackActive)return r;let a,n,o,s,i,l,c;if("string"==typeof e?a=e:(a=e.url,n=e.route,o=e.name,s=e.path,i=e.query,l=e.params),o||s)return a=r.generateUrl({path:s,name:o,params:l,query:i}),a?r.navigate(a,t):r;const u=r.app;if(He(r,"navigate"),"#"===a||""===a)return r;let p=a.replace("./","");if("/"!==p[0]&&0!==p.indexOf("#")){const e=r.currentRoute.parentPath||r.currentRoute.path;p=((e?`${e}/`:"/")+p).replace("///","/").replace("//","/")}if(c=n?J(r.parseRouteUrl(p),{route:J({},n)}):r.findMatchingRoute(p),!c)return r;if(c.route&&c.route.viewName){const a=c.route.viewName,n=u.views[a];if(!n)throw new Error(`Framework7: There is no View with "${a}" name that was specified in this route`);if(n!==r.view)return n.router.navigate(e,t)}if(c.route.redirect)return Me.call(r,"forward",c,t);const d={};if(c.route.options?J(d,c.route.options,t):J(d,t),d.openIn&&(!r.params.ignoreOpenIn||r.params.ignoreOpenIn&&r.history.length>0))return r.openIn(r,p,d);function h(){let e=!1;function t(e,t){r.allowPageChange=!1;let a=!1;"popup popover sheet loginScreen actions customModal panel".split(" ").forEach((n=>{if(e[n]){a=!0;const o=J({},c,{route:e});r.allowPageChange=!0,r.modalLoad(n,o,J(d,t),"forward")}})),a||qe(r,e,J(d,t),!0)}function a(){r.allowPageChange=!0}"popup popover sheet loginScreen actions customModal panel".split(" ").forEach((t=>{c.route[t]&&!e&&(e=!0,r.modalLoad(t,c,d,"forward"))})),c.route.keepAlive&&c.route.keepAliveData&&(qe(r,{el:c.route.keepAliveData.pageEl},d,!1),e=!0),"url content component pageName el componentUrl".split(" ").forEach((t=>{c.route[t]&&!e&&(e=!0,qe(r,{[t]:c.route[t]},d,!1))})),e||(c.route.async&&(r.allowPageChange=!1,c.route.async.call(r,{router:r,to:d.route,from:r.currentRoute,resolve:t,reject:a,direction:"forward",app:u})),c.route.asyncComponent&&_e(r,c.route.asyncComponent,t,a))}function f(){r.allowPageChange=!0}if(d.route=c,r.params.masterDetailBreakpoint>0&&c.route.masterRoute){let a=!0,n=!1;if(r.currentRoute&&r.currentRoute.route&&(!(!0===r.currentRoute.route.master||"function"==typeof r.currentRoute.route.master&&r.currentRoute.route.master(u,r))||r.currentRoute.route!==c.route.masterRoute&&r.currentRoute.route.path!==c.route.masterRoute.path||(a=!1),!r.currentRoute.route.masterRoute||r.currentRoute.route.masterRoute!==c.route.masterRoute&&r.currentRoute.route.masterRoute.path!==c.route.masterRoute.path||(a=!1,n=!0)),a||n&&t.reloadAll)return r.navigate({path:c.route.masterRoute.path,params:c.params||{}},{animate:!1,reloadAll:t.reloadAll,reloadCurrent:t.reloadCurrent,reloadPrevious:t.reloadPrevious,browserHistory:!t.initial,history:!t.initial,once:{pageAfterIn(){r.navigate(e,J({},t,{animate:!1,reloadAll:!1,reloadCurrent:!1,reloadPrevious:!1,history:!t.initial,browserHistory:!t.initial}))}}}),r}return Ne.call(r,c,r.currentRoute,(()=>{c.route.modules?u.loadModules(Array.isArray(c.route.modules)?c.route.modules:[c.route.modules]).then((()=>{h()})).catch((()=>{f()})):h()}),(()=>{f()}),"forward"),r},Ue.prototype.refreshPage=function(){const e=this;return He(e,"refreshPage"),e.navigate(e.currentRoute.url,{ignoreCache:!0,reloadCurrent:!0})},Ue.prototype.tabLoad=function(e,t){void 0===t&&(t={});const r=this,a=J({animate:r.params.animate,browserHistory:!0,history:!0,parentPageEl:null,preload:!1,on:{}},t);let n,o;a.route&&(a.preload||a.route===r.currentRoute||(o=r.previousRoute,r.currentRoute=a.route),a.preload?(n=a.route,o=r.currentRoute):(n=r.currentRoute,o||(o=r.previousRoute)),r.params.browserHistory&&a.browserHistory&&!a.reloadPrevious&&Ae[r.params.browserHistoryTabs](r.view.id,{url:a.route.url},(r.params.browserHistoryRoot||"")+r.params.browserHistorySeparator+a.route.url),a.history&&(r.history[Math.max(r.history.length-1,0)]=a.route.url,r.saveHistory()));const s=N(a.parentPageEl||r.currentPageEl);let i;i=s.length&&s.find(`#${e.id}`).length?s.find(`#${e.id}`).eq(0):r.view.selector?`${r.view.selector} #${e.id}`:`#${e.id}`;const l=r.app.tab.show({tabEl:i,animate:a.animate,tabRoute:a.route}),{$newTabEl:c,$oldTabEl:u,animated:p,onTabsChanged:d}=l;if(c&&c.parents(".page").length>0&&a.route){const e=c.parents(".page")[0].f7Page;e&&a.route&&(e.route=a.route)}if(c[0].f7RouterTabLoaded)return u&&u.length?(p?d((()=>{r.emit("routeChanged",r.currentRoute,r.previousRoute,r)})):r.emit("routeChanged",r.currentRoute,r.previousRoute,r),r):r;function h(t,a){const{url:n,content:o,el:s,component:i,componentUrl:l}=t;function h(t){r.allowPageChange=!0,t&&("string"==typeof t?c.html(t):(c.html(""),t.f7Component?t.f7Component.mount((e=>{c.append(e)})):c.append(t)),c[0].f7RouterTabLoaded=!0,function(t){r.removeThemeElements(c);let a=c;"string"!=typeof t&&(a=N(t)),a.trigger("tab:init tab:mounted",e),r.emit("tabInit tabMounted",c[0],e),u&&u.length&&(p?d((()=>{r.emit("routeChanged",r.currentRoute,r.previousRoute,r),r.params.unloadTabContent&&r.tabRemove(u,c,e)})):(r.emit("routeChanged",r.currentRoute,r.previousRoute,r),r.params.unloadTabContent&&r.tabRemove(u,c,e)))}(t))}if(o)h(o);else if(s)h(s);else if(i||l)try{r.tabComponentLoader({tabEl:c[0],component:i,componentUrl:l,options:a,resolve:h,reject:function(){return r.allowPageChange=!0,r}})}catch(e){throw r.allowPageChange=!0,e}else n&&(r.xhrAbortController&&(r.xhrAbortController.abort(),r.xhrAbortController=!1),r.xhrRequest(n,a).then((e=>{h(e)})).catch((()=>{r.allowPageChange=!0})))}let f;function m(e,t){h(e,J(a,t))}function g(){r.allowPageChange=!0}return"url content component el componentUrl".split(" ").forEach((t=>{e[t]&&(f=!0,h({[t]:e[t]},a))})),e.async?e.async.call(r,{router:r,to:n,from:o,resolve:m,reject:g,app:r.app}):e.asyncComponent?_e(r,e.asyncComponent,m,g):f||(r.allowPageChange=!0),r},Ue.prototype.tabRemove=function(e,t,r){let a;e[0]&&(e[0].f7RouterTabLoaded=!1,delete e[0].f7RouterTabLoaded),e.children().each((e=>{e.f7Component&&(a=!0,N(e).trigger("tab:beforeremove",r),e.f7Component.destroy())})),a||e.trigger("tab:beforeremove",r),this.emit("tabBeforeRemove",e[0],t[0],r),this.removeTabContent(e[0],r)},Ue.prototype.modalLoad=function(e,t,r,a){void 0===r&&(r={});const n=this,o=n.app,s="panel"===e,i=s?"panel":"modal",l=J({animate:n.params.animate,browserHistory:!0,history:!0,on:{},once:{}},r),c=J({},t.route[e]),u=t.route,p=(e,t)=>{const{on:r,once:a}=l;let n;"open"===t&&(n=r.modalOpen||a.modalOpen||r.panelOpen||a.panelOpen),"close"===t&&(n=r.modalClose||a.modalClose||r.panelClose||a.panelClose),"closed"===t&&(n=r.modalClosed||a.modalClosed||r.panelClosed||a.panelClosed),n&&n(e)};function d(){const r=o[e].create(c);u.modalInstance=r;const a=r.el;function d(){r.close()}r.on(`${i}Open`,(()=>{a||(n.removeThemeElements(r.el),r.$el.trigger(`${e.toLowerCase()}:init ${e.toLowerCase()}:mounted`,t,r),n.emit(`${s?"":"modalInit"} ${e}Init ${e}Mounted`,r.el,t,r)),n.once("swipeBackMove",d),p(r,"open")})),r.on(`${i}Close`,(()=>{n.off("swipeBackMove",d),r.closeByRouter||n.back(),p(r,"close")})),r.on(`${i}Closed`,(()=>{r.$el.trigger(`${e.toLowerCase()}:beforeremove`,t,r),r.emit(`${s?"":"modalBeforeRemove "}${e}BeforeRemove`,r.el,t,r);const a=r.el.f7Component;p(r,"closed"),a&&a.destroy(),z((()=>{(a||c.component||c.asyncComponent)&&n.removeModal(r.el),r.destroy(),delete r.route,delete u.modalInstance}))})),l.route&&(n.params.browserHistory&&l.browserHistory&&Ae.push(n.view.id,{url:l.route.url,modal:e},(n.params.browserHistoryRoot||"")+n.params.browserHistorySeparator+l.route.url),l.route!==n.currentRoute&&(r.route=J(l.route,{modal:r}),n.currentRoute=r.route),l.history&&!l.reloadCurrent&&(n.history.push(l.route.url),n.saveHistory())),a&&(n.removeThemeElements(r.el),r.$el.trigger(`${e.toLowerCase()}:init ${e.toLowerCase()}:mounted`,t,r),n.emit(`${i}Init ${e}Init ${e}Mounted`,r.el,t,r)),r.open(!1===l.animate||!0===l.animate?l.animate:void 0)}function h(e,t){const{url:r,content:a,component:s,componentUrl:i}=e;function l(e){e&&("string"==typeof e?c.content=e:e.f7Component?e.f7Component.mount((e=>{c.el=e,o.$el.append(e)})):c.el=e,d())}if(a)l(a);else if(s||i)try{n.modalComponentLoader({rootEl:o.el,component:s,componentUrl:i,options:t,resolve:l,reject:function(){return n.allowPageChange=!0,n}})}catch(e){throw n.allowPageChange=!0,e}else r?(n.xhrAbortController&&(n.xhrAbortController.abort(),n.xhrAbortController=!1),n.xhrRequest(r,t).then((e=>{c.content=e,d()})).catch((()=>{n.allowPageChange=!0}))):d()}let f;function m(e,t){h(e,J(l,t))}function g(){n.allowPageChange=!0}return"url content component el componentUrl template".split(" ").forEach((e=>{c[e]&&!f&&(f=!0,h({[e]:c[e]},l))})),f||"actions"!==e||d(),c.async&&c.async.call(n,{router:n,to:l.route,from:n.currentRoute,resolve:m,reject:g,direction:a,app:o}),c.asyncComponent&&_e(n,c.asyncComponent,m,g),n},Ue.prototype.modalRemove=function(e){J(e,{closeByRouter:!0}),e.close()},Ue.prototype.back=function(){const e=this,t=ie();if(e.swipeBackActive)return e;let r,a,n;"object"==typeof(arguments.length<=0?void 0:arguments[0])?a=(arguments.length<=0?void 0:arguments[0])||{}:(r=arguments.length<=0?void 0:arguments[0],a=(arguments.length<=1?void 0:arguments[1])||{});const{name:o,params:s,query:i}=a;if(o)return r=e.generateUrl({name:o,params:s,query:i}),r?e.back(r,J({},a,{name:null,params:null,query:null})):e;const l=e.app;He(e,"back");let c,u=e.currentRoute.modal;if(u||"popup popover sheet loginScreen actions customModal panel".split(" ").forEach((t=>{e.currentRoute.route[t]&&(u=!0,c=t)})),u&&!a.preload){const n=e.currentRoute.modal||e.currentRoute.route.modalInstance||l[c].get(),o=e.history[e.history.length-2];let s;if(n&&n.$el){const t=n.$el.prevAll(".modal-in");if(t.length&&t[0].f7Modal){const r=t[0];e.$el.parents(r).length||(s=r.f7Modal.route)}}if(s||(s=e.findMatchingRoute(o)),!s&&o&&(s={url:o,path:o.split("?")[0],query:F(o),route:{path:o.split("?")[0],url:o}}),!(r&&0!==r.replace(/[# ]/g,"").trim().length||s&&n))return e;const i=a.force&&s&&r;if(s&&n){const o=t.ie||t.edge||t.firefox&&!t.ios,l=e.params.browserHistory&&!1!==a.browserHistory,c=e.currentRoute&&e.currentRoute.route&&e.currentRoute.route.options&&!1===e.currentRoute.route.options.browserHistory;!l||o||c||Ae.back(),e.currentRoute=s,e.history.pop(),e.saveHistory(),l&&o&&!c&&Ae.back(),e.modalRemove(n),i&&e.navigate(r,{reloadCurrent:!0})}else n&&(e.modalRemove(n),r&&e.navigate(r,{reloadCurrent:!0}));return e}let p,d=e.$el.children(".page-current").prevAll(".page-previous:not(.page-master)").eq(0);if(e.params.masterDetailBreakpoint>0){const t=[];e.$el.children(".page").each((e=>{t.push(e.className)}));const r=e.$el.children(".page-current").prevAll(".page-master").eq(0);if(r.length){const t=e.history[e.history.length-2],n=e.findMatchingRoute(t);n&&r[0].f7Page&&n.route===r[0].f7Page.route.route&&(d=r,a.preload||(p=l.width>=e.params.masterDetailBreakpoint))}}if(!a.force&&d.length&&!p){if(e.params.browserHistory&&d[0].f7Page&&e.history[e.history.length-2]!==d[0].f7Page.route.url)return e.back(e.history[e.history.length-2],J(a,{force:!0})),e;const t=d[0].f7Page.route;return a.preload&&d.hasClass("stacked")?(Ie(e,{el:d},J(a,{route:t})),e):(Ne.call(e,t,e.currentRoute,(()=>{Ie(e,{el:d},J(a,{route:t}))}),(()=>{}),"backward"),e)}if("#"===r&&(r=void 0),r&&"/"!==r[0]&&0!==r.indexOf("#")&&(r=((e.path||"/")+r).replace("//","/")),!r&&e.history.length>1&&(r=e.history[e.history.length-2]),p&&!a.force&&e.history[e.history.length-3])return e.back(e.history[e.history.length-3],J({},a||{},{force:!0,animate:!1}));if(p&&!a.force)return e;if(n=e.findMatchingRoute(r),n||r&&(n={url:r,path:r.split("?")[0],query:F(r),route:{path:r.split("?")[0],url:r}}),!n)return e;if(n.route.redirect)return Me.call(e,"backward",n,a);const h={};let f;if(n.route.options?J(h,n.route.options,a):J(h,a),h.route=n,h.force&&e.params.stackPages&&(e.$el.children(".page-previous.stacked").each((t=>{t.f7Page&&t.f7Page.route&&t.f7Page.route.url===n.url&&(f=!0,Ie(e,{el:t},h))})),f))return e;function m(){let t=!1;function r(t,r){e.allowPageChange=!1,Ie(e,t,J(h,r),!0)}function a(){e.allowPageChange=!0}n.route.keepAlive&&n.route.keepAliveData&&(Ie(e,{el:n.route.keepAliveData.pageEl},h),t=!0),"url content component pageName el componentUrl".split(" ").forEach((r=>{n.route[r]&&!t&&(t=!0,Ie(e,{[r]:n.route[r]},h))})),t||(n.route.async&&(e.allowPageChange=!1,n.route.async.call(e,{router:e,to:n,from:e.currentRoute,resolve:r,reject:a,direction:"backward",app:l})),n.route.asyncComponent&&_e(e,n.route.asyncComponent,r,a))}function g(){e.allowPageChange=!0}return h.preload?m():Ne.call(e,n,e.currentRoute,(()=>{n.route.modules?l.loadModules(Array.isArray(n.route.modules)?n.route.modules:[n.route.modules]).then((()=>{m()})).catch((()=>{g()})):m()}),(()=>{g()}),"backward"),e},Ue.prototype.clearPreviousHistory=function(){const e=this;He(e,"clearPreviousHistory");const t=e.history[e.history.length-1];!function(e){He(e,"clearPreviousPages");const t=e.app,r=e.dynamicNavbar;e.$el.children(".page").filter((t=>!(!e.currentRoute||!e.currentRoute.modal&&!e.currentRoute.panel)||t!==e.currentPageEl)).each((a=>{const n=N(a),o=N(t.navbar.getElByPage(n));e.params.stackPages&&e.initialPages.indexOf(n[0])>=0?(n.addClass("stacked"),r&&o.addClass("stacked")):(e.pageCallback("beforeRemove",n,o,"previous",void 0,{}),e.removePage(n),r&&o.length&&e.removeNavbar(o))}))}(e),e.history=[t],e.view.history=[t],e.saveHistory()};var ze={name:"router",static:{Router:Ue},instance:{cache:{xhr:[],templates:[],components:[]}},create(){const e=this;e.app?e.params.router&&(e.router=new Ue(e.app,e)):e.router=new Ue(e)}};function Ve(e){const t=e.app,r=se();if(e.resizableInitialized)return;J(e,{resizable:!0,resizableWidth:null,resizableInitialized:!0});const a=N("html"),{$el:n}=e;if(!n)return;let o,s,i;const l={};let c,u,p,d;function h(e){if(!e)return null;if(e.indexOf("%")>=0||e.indexOf("vw")>=0)return parseInt(e,10)/100*t.width;const r=parseInt(e,10);return Number.isNaN(r)?null:r}function f(t){if(!(e.resizable&&n.hasClass("view-resizable")&&n.hasClass("view-master-detail")))return;l.x="touchstart"===t.type?t.targetTouches[0].pageX:t.pageX,l.y="touchstart"===t.type?t.targetTouches[0].pageY:t.pageY,i=!1,s=!0;const r=n.children(".page-master");p=h(r.css("min-width")),d=h(r.css("max-width"))}function m(r){if(!s)return;r.f7PreventSwipePanel=!0;const h="touchmove"===r.type?r.targetTouches[0].pageX:r.pageX;i||(u=o[0].offsetLeft+o[0].offsetWidth,n.addClass("view-resizing"),a.css("cursor","col-resize")),i=!0,r.preventDefault(),c=h-l.x;let f=u+c;p&&!Number.isNaN(p)&&(f=Math.max(f,p)),d&&!Number.isNaN(d)&&(f=Math.min(f,d)),f=Math.min(Math.max(f,0),t.width),e.resizableWidth=f,a[0].style.setProperty("--f7-page-master-width",`${f}px`),n.trigger("view:resize",f),e.emit("local::resize viewResize",e,f)}function g(){if(N("html").css("cursor",""),!s||!i)return s=!1,void(i=!1);s=!1,i=!1,a[0].style.setProperty("--f7-page-master-width",`${e.resizableWidth}px`),n.removeClass("view-resizing")}function v(){e.resizableWidth&&(p=h(o.css("min-width")),d=h(o.css("max-width")),p&&!Number.isNaN(p)&&e.resizableWidth<p&&(e.resizableWidth=Math.max(e.resizableWidth,p)),d&&!Number.isNaN(d)&&e.resizableWidth>d&&(e.resizableWidth=Math.min(e.resizableWidth,d)),e.resizableWidth=Math.min(Math.max(e.resizableWidth,0),t.width),a[0].style.setProperty("--f7-page-master-width",`${e.resizableWidth}px`))}o=e.$el.children(".view-resize-handler"),o.length||(e.$el.append('<div class="view-resize-handler"></div>'),o=e.$el.children(".view-resize-handler")),e.$resizeHandlerEl=o,n.addClass("view-resizable");const b=!!r.passiveListener&&{passive:!0};e.$el.on(t.touchEvents.start,".view-resize-handler",f,b),t.on("touchmove:active",m),t.on("touchend:passive",g),t.on("resize",v),e.on("beforeOpen",v),e.once("viewDestroy",(()=>{n.removeClass("view-resizable"),e.$resizeHandlerEl.remove(),e.$el.off(t.touchEvents.start,".view-resize-handler",f,b),t.off("touchmove:active",m),t.off("touchend:passive",g),t.off("resize",v),e.off("beforeOpen",v)}))}class We extends ce{constructor(e,t,r){void 0===r&&(r={}),super(r,[e]);const a=this;if(!a.params.routerId){if(!N(t).length){let e="Framework7: can't create a View instance because ";throw e+="string"==typeof t?`the selector "${t}" didn't match any element`:"el must be an HTMLElement or Dom7 object",new Error(e)}}let n;return a.params=J({el:t},{routes:[],routesAdd:[]},e.params.view,r),a.params.routes.length>0?a.routes=a.params.routes:a.routes=[].concat(e.routes,a.params.routesAdd),J(!1,a,{app:e,name:a.params.name,main:a.params.main,history:[],scrollHistory:{}}),a.useModules(),e.views.push(a),a.main&&(e.views.main=a),a.name&&(e.views[a.name]=a),a.index=e.views.indexOf(a),n=a.name?`view_${a.name}`:a.main?"view_main":`view_${a.index}`,a.id=n,a.params.init?(e.initialized?a.init():e.on("init",(()=>{a.init()})),a):a}destroy(){let e=this;const t=e.app;e.$el.trigger("view:beforedestroy"),e.emit("local::beforeDestroy viewBeforeDestroy",e),t.off("resize",e.checkMasterDetailBreakpoint),e.main?(t.views.main=null,delete t.views.main):e.name&&(t.views[e.name]=null,delete t.views[e.name]),e.$el[0].f7View=null,delete e.$el[0].f7View,t.views.splice(t.views.indexOf(e),1),e.params.router&&e.router&&e.router.destroy(),e.emit("local::destroy viewDestroy",e),Object.keys(e).forEach((t=>{e[t]=null,delete e[t]})),e=null}checkMasterDetailBreakpoint(e){const t=this,r=t.app,a=t.$el.hasClass("view-master-detail"),n=r.width>=t.params.masterDetailBreakpoint&&t.$el.children(".page-master").length;void 0===e&&n||!0===e?(t.$el.addClass("view-master-detail"),a||(t.emit("local::masterDetailBreakpoint viewMasterDetailBreakpoint",t),t.$el.trigger("view:masterDetailBreakpoint"))):(t.$el.removeClass("view-master-detail"),a&&(t.emit("local::masterDetailBreakpoint viewMasterDetailBreakpoint",t),t.$el.trigger("view:masterDetailBreakpoint")))}initMasterDetail(){const e=this,t=e.app;e.checkMasterDetailBreakpoint=e.checkMasterDetailBreakpoint.bind(e),e.checkMasterDetailBreakpoint(),e.params.masterDetailResizable&&Ve(e),t.on("resize",e.checkMasterDetailBreakpoint)}mount(e){const t=this,r=t.app,a=t.params.el||e,n=N(a);let o,s;o="string"==typeof a?a:(n.attr("id")?`#${n.attr("id")}`:"")+(n.attr("class")?`.${n.attr("class").replace(/ /g,".").replace(".active","")}`:""),"ios"===r.theme&&t.params.iosDynamicNavbar&&(s=n.children(".navbars").eq(0),0===s.length&&(s=N('<div class="navbars"></div>'))),J(t,{$el:n,el:n[0],main:t.main||n.hasClass("view-main"),$navbarsEl:s,navbarsEl:s?s[0]:void 0,selector:o}),t.main&&(r.views.main=t),n&&n[0]&&(n[0].f7View=t),t.emit("local::mount viewMount",t)}init(e){const t=this;t.mount(e),t.params.router&&(t.params.masterDetailBreakpoint>0&&t.initMasterDetail(),t.params.initRouterOnTabShow&&t.$el.hasClass("tab")&&!t.$el.hasClass("tab-active")?t.$el.once("tab:show",(()=>{t.router.init()})):t.router.init(),t.$el.trigger("view:init"),t.emit("local::init viewInit",t))}}We.use(ze);var Fe={name:"clicks",params:{clicks:{externalLinks:".external"}},on:{init(){!function(e){e.on("click",(function(t){const r=o(),a=N(t.target),n=a.closest("a"),s=n.length>0,i=s&&n.attr("href");if(s&&(n.is(e.params.clicks.externalLinks)||i&&i.indexOf("javascript:")>=0)){const e=n.attr("target");return void(i&&r.cordova&&r.cordova.InAppBrowser&&("_system"===e||"_blank"===e)?(t.preventDefault(),r.cordova.InAppBrowser.open(i,e)):i&&r.Capacitor&&r.Capacitor.Plugins&&r.Capacitor.Plugins.Browser&&("_system"===e||"_blank"===e)&&(t.preventDefault(),r.Capacitor.Plugins.Browser.open({url:i})))}Object.keys(e.modules).forEach((r=>{const n=e.modules[r].clicks;n&&(t.preventF7Router||Object.keys(n).forEach((r=>{const o=a.closest(r).eq(0);o.length>0&&n[r].call(e,o,o.dataset(),t)})))}));let l={};if(s&&(t.preventDefault(),l=n.dataset()),l.clickedEl=n[0],!t.preventF7Router&&!n.hasClass("prevent-router")&&!n.hasClass("router-prevent")&&(i&&i.length>0&&"#"!==i[0]||n.hasClass("back"))){let t;if(l.view&&"current"===l.view?t=e.views.current:l.view?t=N(l.view)[0].f7View:(t=a.parents(".view")[0]&&a.parents(".view")[0].f7View,!n.hasClass("back")&&t&&t.params.linksView&&("string"==typeof t.params.linksView?t=N(t.params.linksView)[0].f7View:t.params.linksView instanceof We&&(t=t.params.linksView))),t||e.views.main&&(t=e.views.main),!t||!t.router)return;n[0].f7RouteProps&&(l.props=n[0].f7RouteProps),n.hasClass("back")?t.router.back(i,l):t.router.navigate(i,l)}}))}(this)}}},Xe={name:"routerComponentLoader",proto:{openIn(e,t,r){const a={url:t,route:{path:t,options:{...r,openIn:void 0}}},n={...r};if("popup"===r.openIn&&(n.content=`<div class="popup popup-router-open-in" data-url="${t}"><div class="view view-init" data-links-view="${e.view.selector}" data-url="${t}" data-ignore-open-in="true"></div></div>`,a.route.popup=n),"loginScreen"===r.openIn&&(n.content=`<div class="login-screen login-screen-router-open-in" data-url="${t}"><div class="view view-init" data-links-view="${e.view.selector}" data-url="${t}" data-ignore-open-in="true"></div></div>`,a.route.loginScreen=n),"sheet"===r.openIn&&(n.content=`<div class="sheet-modal sheet-modal-router-open-in" data-url="${t}"><div class="sheet-modal-inner"><div class="view view-init" data-links-view="${e.view.selector}" data-url="${t}" data-ignore-open-in="true"></div></div></div>`,a.route.sheet=n),"popover"===r.openIn&&(n.targetEl=r.clickedEl||r.targetEl,n.content=`<div class="popover popover-router-open-in" data-url="${t}"><div class="popover-inner"><div class="view view-init" data-links-view="${e.view.selector}" data-url="${t}" data-ignore-open-in="true"></div></div></div>`,a.route.popover=n),r.openIn.indexOf("panel")>=0){const o=r.openIn.split(":"),s=o[1]||"left",i=o[2]||"cover";n.targetEl=r.clickedEl||r.targetEl,n.content=`<div class="panel panel-router-open-in panel-${s} panel-${i}" data-url="${t}"><div class="view view-init" data-links-view="${e.view.selector}" data-url="${t}" data-ignore-open-in="true"></div></div>`,a.route.panel=n}return e.navigate(a)},componentLoader(e,t,r,a,n){void 0===r&&(r={});const o=this,{app:s}=o,i="string"==typeof e?e:t,l=o.replaceRequestUrlParams(i,r);function c(e){let t=r.context||{};if("function"==typeof t)t=t.call(o);else if("string"==typeof t)try{t=JSON.parse(t)}catch(e){throw n(e),e}const i=Y({},t,{f7route:r.route,f7router:o}),l=Y(r.route&&r.route.params||{},r.props||{},r.routeProps||{});let c,u;r.componentOptions&&r.componentOptions.el&&(c=r.componentOptions.el),r.componentOptions&&r.componentOptions.root&&(u=r.componentOptions.root),s.component.create(e,l,{context:i,el:c,root:u}).then((e=>{a(e.el)})).catch((e=>{throw n(e),new Error(e)}))}let u;l&&o.params.componentCache&&o.cache.components.forEach((e=>{e.url===l&&(u=e.component)})),l&&u?c(u):l&&!u?(o.xhrAbortController&&(o.xhrAbortController.abort(),o.xhrAbortController=!1),o.xhrRequest(i,r).then((e=>{const t=s.component.parse(e);o.params.componentCache&&o.cache.components.push({url:l,component:t}),c(t)})).catch((e=>{throw n(),e}))):c(e)},modalComponentLoader(e){let{component:t,componentUrl:r,options:a,resolve:n,reject:o}=void 0===e?{}:e;this.componentLoader(t,r,a,(e=>{n(e)}),o)},tabComponentLoader(e){let{component:t,componentUrl:r,options:a,resolve:n,reject:o}=void 0===e?{}:e;this.componentLoader(t,r,a,(e=>{n(e)}),o)},pageComponentLoader(e){let{component:t,componentUrl:r,options:a,resolve:n,reject:o}=void 0===e?{}:e;this.componentLoader(t,r,a,(function(e,t){void 0===t&&(t={}),n(e,t)}),o)}}},Qe=function(e,t,r,a){var n;t[0]=0;for(var o=1;o<t.length;o++){var s=t[o++],i=t[o]?(t[0]|=s?1:2,r[t[o++]]):t[++o];3===s?a[0]=i:4===s?a[1]=Object.assign(a[1]||{},i):5===s?(a[1]=a[1]||{})[t[++o]]=i:6===s?a[1][t[++o]]+=i+"":s?(n=e.apply(i,Qe(e,i,r,["",null])),a.push(n),i[0]?t[0]|=2:(t[o-2]=0,t[o]=n)):a.push(i)}return a},Ye=new Map;const Je=[!1,null,"",void 0],Ge=function(e){var t=Ye.get(this);return t||(t=new Map,Ye.set(this,t)),(t=Qe(this,t.get(e)||(t.set(e,t=function(e){for(var t,r,a=1,n="",o="",s=[0],i=function(e){1===a&&(e||(n=n.replace(/^\s*\n\s*|\s*\n\s*$/g,"")))?s.push(0,e,n):3===a&&(e||n)?(s.push(3,e,n),a=2):2===a&&"..."===n&&e?s.push(4,e,0):2===a&&n&&!e?s.push(5,0,!0,n):a>=5&&((n||!e&&5===a)&&(s.push(a,0,n,r),a=6),e&&(s.push(a,e,0,r),a=6)),n=""},l=0;l<e.length;l++){l&&(1===a&&i(),i(l));for(var c=0;c<e[l].length;c++)t=e[l][c],1===a?"<"===t?(i(),s=[s],a=3):n+=t:4===a?"--"===n&&">"===t?(a=1,n=""):n=t+n[0]:o?t===o?o="":n+=t:'"'===t||"'"===t?o=t:">"===t?(i(),a=1):a&&("="===t?(a=5,r=n,n=""):"/"===t&&(a<5||">"===e[l][c+1])?(i(),3===a&&(s=s[0]),a=s,(s=s[0]).push(2,0,a),a=0):" "===t||"\t"===t||"\n"===t||"\r"===t?(i(),a=2):n+=t),3===a&&"!--"===n&&(a=4,s=s[0])}return i(),s}(e)),t),arguments,[])).length>1?t:t[0]}.bind((function(e,t){for(var r=arguments.length,a=new Array(r>2?r-2:0),n=2;n<r;n++)a[n-2]=arguments[n];return{type:e,props:t||{},children:re(a.filter((e=>Je.indexOf(e)<0)))}}));function Ke(e,t,r,a,n){return{sel:e,data:t,children:r,text:a,elm:n,key:void 0===t?void 0:t.key}}var Ze=Array.isArray;function et(e){return"string"==typeof e||"number"==typeof e}function tt(e,t,r){if(e.ns="http://www.w3.org/2000/svg","foreignObject"!==r&&void 0!==t)for(var a=0;a<t.length;++a){var n=t[a].data;void 0!==n&&tt(n,t[a].children,t[a].sel)}}function rt(e,t,r){var a,n,o,s={};if(void 0!==r?(s=t,Ze(r)?a=r:et(r)?n=r:r&&r.sel&&(a=[r])):void 0!==t&&(Ze(t)?a=t:et(t)?n=t:t&&t.sel?a=[t]:s=t),Ze(a))for(o=0;o<a.length;++o)et(a[o])&&(a[o]=Ke(void 0,void 0,void 0,a[o],void 0));return"s"!==e[0]||"v"!==e[1]||"g"!==e[2]||3!==e.length&&"."!==e[3]&&"#"!==e[3]||tt(s,a,e),Ke(e,s,a,n,void 0)}var at={};const nt="area base br col command embed hr img input keygen link menuitem meta param source track wbr".split(" "),ot="hidden checked disabled readonly selected autofocus autoplay required multiple value indeterminate routeProps innerHTML".split(" "),st="hidden checked disabled readonly selected autofocus autoplay required multiple readOnly indeterminate".split(" "),it=e=>"function"==typeof e.type?e.type.name||"CustomComponent":e.type,lt=e=>e.split("-").map(((e,t)=>0===t?e.toLowerCase():e[0].toUpperCase()+e.substr(1))).join(""),ct=function(){const e={};for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return r.forEach((function(t){void 0===t&&(t={}),Object.keys(t).forEach((r=>{e[lt(r)]=t[r]}))})),e},ut=e=>"function"==typeof e||e&&e.indexOf("-")>0&&at[e];function pt(e,t,r,a,n){const o={},s=[],i=[],l=[],c=[];let u=!1,p=it(e);t&&t.attrs&&t.attrs.component&&(p=t.attrs.component,delete t.attrs.component,u=!0);const d=ut(e.type);if(d&&(s.push((a=>{(a.sel===p||u)&&(e=>{let{f7:t,treeNode:r,vnode:a,data:n}=e;const o="function"==typeof r.type?r.type:at[r.type];t.component.create(o,ct(n.attrs||{},n.props||{}),{el:a.elm,children:r.children}).then((e=>{a.data&&a.data.on&&e&&e.$el&&Object.keys(a.data.on).forEach((t=>{e.$el.on(t,a.data.on[t])})),a.elm.__component__=e}))})({f7:r,treeNode:e,vnode:a,data:t})})),i.push((e=>{(e=>{const t=e&&e.elm&&e.elm.__component__;if(t){const{el:r,$el:a}=t;e.data&&e.data.on&&a&&Object.keys(e.data.on).forEach((t=>{a.off(t,e.data.on[t])})),t.destroy&&t.destroy(),r&&r.parentNode&&r.parentNode.removeChild(r),delete e.elm.__component__}})(e)})),l.push(((e,t)=>{(e=>{const t=e&&e.elm&&e.elm.__component__;if(!t)return;const r=ct(e.data.attrs||{},e.data.props||{});t.children=e.data.treeNode.children,Object.assign(t.props,r),t.update()})(t)}))),!d){if(!t||!t.attrs||!t.attrs.class)return o;t.attrs.class.split(" ").forEach((e=>{a||s.push(...r.getVnodeHooks("insert",e)),i.push(...r.getVnodeHooks("destroy",e)),l.push(...r.getVnodeHooks("update",e)),c.push(...r.getVnodeHooks("postpatch",e))}))}return n&&!a&&c.push(((e,t)=>{const r=t||e;r&&r.data&&r.data.component&&r.data.component.hook("onUpdated")})),0===s.length&&0===i.length&&0===l.length&&0===c.length||(s.length&&(o.insert=e=>{s.forEach((t=>t(e)))}),i.length&&(o.destroy=e=>{i.forEach((t=>t(e)))}),l.length&&(o.update=(e,t)=>{l.forEach((r=>r(e,t)))}),c.length&&(o.postpatch=(e,t)=>{c.forEach((r=>r(e,t)))})),o}const dt=(e,t,r,a)=>{if(e&&e.type&&nt.indexOf(e.type)>=0)return[];const n=[],o=e.children;for(let e=0;e<o.length;e+=1){const s=o[e],i=ht(s,t,r,a,!1);Array.isArray(i)?n.push(...i):i&&n.push(i)}return n},ht=(e,t,r,a,n)=>{if(!(e=>Q(e)&&"props"in e&&"type"in e&&"children"in e)(e))return String(e);if("slot"===e.type)return((e,t,r,a)=>{const n=e.props.name||"default",o=(t.children||[]).filter((e=>{let t="default";return e.props&&(t=e.props.slot||"default"),t===n}));return 0===o.length?dt(e,t,r,a):o.map((e=>ht(e,t,r,a)))})(e,t,r,a);const o=((e,t,r,a,n)=>{const o={component:t,treeNode:e},s=it(e);Object.keys(e.props).forEach((t=>{const r=e.props[t];if(void 0!==r)if(ot.indexOf(t)>=0)o.props||(o.props={}),"readonly"===t&&(t="readOnly"),"routeProps"===t&&(t="f7RouteProps"),"option"===s&&"value"===t&&(o.attrs||(o.attrs={}),o.attrs.value=r),st.indexOf(t)>=0?o.props[t]=!1!==r:o.props[t]=r;else if("key"===t)o.key=r;else if(0===t.indexOf("@")||0===t.indexOf("on")&&t.length>2){o.on||(o.on={});let e=0===t.indexOf("@")?t.substr(1):j(t.substr(2)),a=!1,n=!1,s=!1;e.indexOf(".")>=0&&e.split(".").forEach(((t,r)=>{0===r?e=t:("stop"===t&&(a=!0),"prevent"===t&&(n=!0),"once"===t&&(s=!0))})),o.on[e]=function(e,t){let{stop:r,prevent:a,once:n}=void 0===t?{}:t,o=!1;return function(){const t=arguments.length<=0?void 0:arguments[0];n&&o||(r&&t.stopPropagation(),a&&t.preventDefault(),o=!0,e(...arguments))}}(r,{stop:a,prevent:n,once:s})}else"style"===t?"string"!=typeof r?o.style=r:(o.attrs||(o.attrs={}),o.attrs.style=r):(o.attrs||(o.attrs={}),o.attrs[t]=r,"id"!==t||o.key||n||(o.key=r))}));const i=pt(e,o,r,a,n);return i.prepatch=(e,t)=>{e&&t&&e&&e.data&&e.data.props&&Object.keys(e.data.props).forEach((r=>{st.indexOf(r)<0||(t.data||(t.data={}),t.data.props||(t.data.props={}),!0!==e.data.props[r]||r in t.data.props||(t.data.props[r]=!1))}))},o.hook=i,o})(e,t,r,a,n),s=ut(e.type)?[]:dt(e,t,r,a);return rt(it(e),o,s)};function ft(e,t,r){return void 0===e&&(e={}),ht(e,t,t.f7,r,!0)}var mt={createElement:function(e){return document.createElement(e)},createElementNS:function(e,t){return document.createElementNS(e,t)},createTextNode:function(e){return document.createTextNode(e)},createComment:function(e){return document.createComment(e)},insertBefore:function(e,t,r){r&&r.parentNode!==e&&r.__component__&&(r=r.__component__.el),e.insertBefore(t,r)},removeChild:function(e,t){e&&e.removeChild(t)},appendChild:function(e,t){e.appendChild(t)},parentNode:function(e){return e.parentNode},nextSibling:function(e){return e.nextSibling},tagName:function(e){return e.tagName},setTextContent:function(e,t){e.textContent=t},getTextContent:function(e){return e.textContent},isElement:function(e){return 1===e.nodeType},isText:function(e){return 3===e.nodeType},isComment:function(e){return 8===e.nodeType}};function gt(e){return void 0===e}function vt(e){return void 0!==e}var bt=Ke("",{},[],void 0,void 0);function yt(e,t){return e.key===t.key&&e.sel===t.sel}function wt(e,t,r){var a,n,o,s={};for(a=t;a<=r;++a)null!=(o=e[a])&&void 0!==(n=o.key)&&(s[n]=a);return s}var Ct=["create","update","remove","destroy","pre","post"];function kt(e,t){var r,a=t.elm,n=e.data.attrs,o=t.data.attrs;if((n||o)&&n!==o){for(r in n=n||{},o=o||{}){var s=o[r];n[r]!==s&&(!0===s?a.setAttribute(r,""):!1===s?a.removeAttribute(r):120!==r.charCodeAt(0)?a.setAttribute(r,s):58===r.charCodeAt(3)?a.setAttributeNS("http://www.w3.org/XML/1998/namespace",r,s):58===r.charCodeAt(5)?a.setAttributeNS("http://www.w3.org/1999/xlink",r,s):a.setAttribute(r,s))}for(r in n)r in o||a.removeAttribute(r)}}var Et={create:kt,update:kt};function $t(e,t){var r,a,n=t.elm,o=e.data.props,s=t.data.props;if((o||s)&&o!==s){for(r in s=s||{},o=o||{})s[r]||delete n[r];for(r in s)a=s[r],o[r]===a||"value"===r&&n[r]===a||(n[r]=a)}}var xt={create:$t,update:$t},Pt="undefined"!=typeof window&&window.requestAnimationFrame||setTimeout;function Ot(e,t,r){var a;a=function(){e[t]=r},Pt((function(){Pt(a)}))}function Rt(e,t){var r,a,n=t.elm,o=e.data.style,s=t.data.style;if((o||s)&&o!==s){s=s||{};var i="delayed"in(o=o||{});for(a in o)s[a]||("-"===a[0]&&"-"===a[1]?n.style.removeProperty(a):n.style[a]="");for(a in s)if(r=s[a],"delayed"===a&&s.delayed)for(var l in s.delayed)r=s.delayed[l],i&&r===o.delayed[l]||Ot(n.style,l,r);else"remove"!==a&&r!==o[a]&&("-"===a[0]&&"-"===a[1]?n.style.setProperty(a,r):n.style[a]=r)}}var Tt={create:Rt,update:Rt,destroy:function(e){var t,r,a=e.elm,n=e.data.style;if(n&&(t=n.destroy))for(r in t)a.style[r]=t[r]},remove:function(e,t){var r=e.data.style;if(r&&r.remove){var a,n=e.elm,o=0,s=r.remove,i=0,l=[];for(a in s)l.push(a),n.style[a]=s[a];for(var c=getComputedStyle(n)["transition-property"].split(", ");o<c.length;++o)-1!==l.indexOf(c[o])&&i++;n.addEventListener("transitionend",(function(e){e.target===n&&--i,0===i&&t()}))}else t()}};function St(e,t,r){const a=e.type,n=r.data.on;n&&n[a]&&function(e,t,r){"function"==typeof e&&e(t,...r)}(n[a],e,t)}function At(e,t){const r=e.data.on,a=e.listener,n=e.elm,o=t&&t.data.on,s=t&&t.elm;if(r!==o&&(r&&a&&(o?Object.keys(r).forEach((e=>{o[e]||N(n).off(e,a)})):Object.keys(r).forEach((e=>{N(n).off(e,a)}))),o)){const a=e.listener||function e(t){for(var r=arguments.length,a=new Array(r>1?r-1:0),n=1;n<r;n++)a[n-1]=arguments[n];St(t,a,e.vnode)};t.listener=a,a.vnode=t,r?Object.keys(o).forEach((e=>{r[e]||N(s).on(e,a)})):Object.keys(o).forEach((e=>{N(s).on(e,a)}))}}const Lt=function(e,t){var r,a,n={},o=void 0!==t?t:mt;for(r=0;r<Ct.length;++r)for(n[Ct[r]]=[],a=0;a<e.length;++a){var s=e[a][Ct[r]];void 0!==s&&n[Ct[r]].push(s)}function i(e){var t=e.id?"#"+e.id:"",r=e.className?"."+e.className.split(" ").join("."):"";return Ke(o.tagName(e).toLowerCase()+t+r,{},[],void 0,e)}function l(e,t){return function(){if(0==--t){var r=o.parentNode(e);o.removeChild(r,e)}}}function c(e,t){var r,a=e.data;void 0!==a&&vt(r=a.hook)&&vt(r=r.init)&&(r(e),a=e.data);var s=e.children,i=e.sel;if("!"===i)gt(e.text)&&(e.text=""),e.elm=o.createComment(e.text);else if(void 0!==i){var l=i.indexOf("#"),u=i.indexOf(".",l),p=l>0?l:i.length,d=u>0?u:i.length,h=-1!==l||-1!==u?i.slice(0,Math.min(p,d)):i,f=e.elm=vt(a)&&vt(r=a.ns)?o.createElementNS(r,h):o.createElement(h);for(p<d&&f.setAttribute("id",i.slice(p+1,d)),u>0&&f.setAttribute("class",i.slice(d+1).replace(/\./g," ")),r=0;r<n.create.length;++r)n.create[r](bt,e);if(Ze(s))for(r=0;r<s.length;++r){var m=s[r];null!=m&&o.appendChild(f,c(m,t))}else et(e.text)&&o.appendChild(f,o.createTextNode(e.text));vt(r=e.data.hook)&&(r.create&&r.create(bt,e),r.insert&&t.push(e))}else e.elm=o.createTextNode(e.text);return e.elm}function u(e,t,r,a,n,s){for(;a<=n;++a){var i=r[a];null!=i&&o.insertBefore(e,c(i,s),t)}}function p(e){var t,r,a=e.data;if(void 0!==a){for(vt(t=a.hook)&&vt(t=t.destroy)&&t(e),t=0;t<n.destroy.length;++t)n.destroy[t](e);if(void 0!==e.children)for(r=0;r<e.children.length;++r)null!=(t=e.children[r])&&"string"!=typeof t&&p(t)}}function d(e,t,r,a){for(;r<=a;++r){var s=void 0,i=void 0,c=void 0,u=t[r];if(null!=u)if(vt(u.sel)){for(p(u),i=n.remove.length+1,c=l(u.elm,i),s=0;s<n.remove.length;++s)n.remove[s](u,c);vt(s=u.data)&&vt(s=s.hook)&&vt(s=s.remove)?s(u,c):c()}else o.removeChild(e,u.elm)}}function h(e,t,r){var a,s;vt(a=t.data)&&vt(s=a.hook)&&vt(a=s.prepatch)&&a(e,t);var i=t.elm=e.elm,l=e.children,p=t.children;if(e!==t){if(void 0!==t.data){for(a=0;a<n.update.length;++a)n.update[a](e,t);vt(a=t.data.hook)&&vt(a=a.update)&&a(e,t)}gt(t.text)?vt(l)&&vt(p)?l!==p&&function(e,t,r,a){for(var n,s,i,l=0,p=0,f=t.length-1,m=t[0],g=t[f],v=r.length-1,b=r[0],y=r[v];l<=f&&p<=v;)null==m?m=t[++l]:null==g?g=t[--f]:null==b?b=r[++p]:null==y?y=r[--v]:yt(m,b)?(h(m,b,a),m=t[++l],b=r[++p]):yt(g,y)?(h(g,y,a),g=t[--f],y=r[--v]):yt(m,y)?(h(m,y,a),o.insertBefore(e,m.elm,o.nextSibling(g.elm)),m=t[++l],y=r[--v]):yt(g,b)?(h(g,b,a),o.insertBefore(e,g.elm,m.elm),g=t[--f],b=r[++p]):(void 0===n&&(n=wt(t,l,f)),gt(s=n[b.key])?(o.insertBefore(e,c(b,a),m.elm),b=r[++p]):((i=t[s]).sel!==b.sel?o.insertBefore(e,c(b,a),m.elm):(h(i,b,a),t[s]=void 0,o.insertBefore(e,i.elm,m.elm)),b=r[++p]));(l<=f||p<=v)&&(l>f?u(e,null==r[v+1]?null:r[v+1].elm,r,p,v,a):d(e,t,l,f))}(i,l,p,r):vt(p)?(vt(e.text)&&o.setTextContent(i,""),u(i,null,p,0,p.length-1,r)):vt(l)?d(i,l,0,l.length-1):vt(e.text)&&o.setTextContent(i,""):e.text!==t.text&&o.setTextContent(i,t.text),vt(s)&&vt(a=s.postpatch)&&a(e,t)}}return function(e,t){var r,a,s,l=[];for(r=0;r<n.pre.length;++r)n.pre[r]();for(function(e){return void 0!==e.sel}(e)||(e=i(e)),yt(e,t)?h(e,t,l):(a=e.elm,s=o.parentNode(a),c(t,l),null!==s&&(o.insertBefore(s,t.elm,o.nextSibling(a)),d(s,[e],0,0))),r=0;r<l.length;++r)l[r].data.hook.insert(l[r]);for(r=0;r<n.post.length;++r)n.post[r]();return t}}([Et,xt,Tt,{create:At,update:At,destroy:At}]);class Mt{constructor(e,t,r,n){void 0===r&&(r={});let{el:o,context:s,children:i}=void 0===n?{}:n;const l=a();Y(this,{f7:e,props:r||{},context:s||{},id:t.id||_(),children:i||[],theme:{ios:"ios"===e.theme,md:"md"===e.theme,aurora:"aurora"===e.theme},style:t.style,__updateQueue:[],__eventHandlers:[],__onceEventHandlers:[],__onBeforeMount:[],__onMounted:[],__onBeforeUpdate:[],__onUpdated:[],__onBeforeUnmount:[],__onUnmounted:[]});const c=()=>t(this.props,this.getComponentContext(!0));return new Promise(((e,t)=>{(e=>new Promise(((t,r)=>{"function"==typeof e?t(e):e instanceof Promise?e.then((e=>{t(e)})).catch((e=>{r(e)})):r(new Error('Framework7: Component render function is not a "function" type. Didn\'t you forget to "return $render"?'))})))(c()).then((t=>{this.renderFunction=t;const r=this.render();if(o)return this.vnode=ft(r,this,!0),this.style&&(this.styleEl=l.createElement("style"),this.styleEl.innerHTML=this.style),this.el=o,Lt(this.el,this.vnode),this.el=this.vnode.elm,this.$el=N(this.el),this.attachEvents(),this.el.f7Component=this,this.mount(),void e(this);r&&(this.vnode=ft(r,this,!0),this.el=l.createElement(this.vnode.sel||"div"),Lt(this.el,this.vnode),this.$el=N(this.el)),this.style&&(this.styleEl=l.createElement("style"),this.styleEl.innerHTML=this.style),this.attachEvents(),this.el&&(this.el.f7Component=this),e(this)})).catch((e=>{t(e)}))}))}on(e,t){this.__eventHandlers&&this.__eventHandlers.push({eventName:e,handler:t})}once(e,t){this.__eventHandlers&&this.__onceEventHandlers.push({eventName:e,handler:t})}getComponentRef(){const e=this;return t=>{let r=t;const a={};return Object.defineProperty(a,"value",{get:()=>r,set(t){r=t,e.update()}}),a}}getComponentStore(){const{state:e,_gettersPlain:t,dispatch:r}=this.f7.store,a={state:e,dispatch:r};return a.getters=new Proxy(t,{get:(e,t)=>{const r=e[t];return r.onUpdated((e=>{r.value=e,this.update()})),r}}),a}getComponentContext(e){const t={$f7route:this.context.f7route,$f7router:this.context.f7router,$h:Ge,$:N,$id:this.id,$f7:this.f7,$f7ready:this.f7ready.bind(this),$theme:this.theme,$tick:this.tick.bind(this),$update:this.update.bind(this),$emit:this.emit.bind(this),$store:this.getComponentStore(),$ref:this.getComponentRef(),$el:{}};return Object.defineProperty(t.$el,"value",{get:()=>this.$el}),e&&Object.assign(t,{$on:this.on.bind(this),$once:this.once.bind(this),$onBeforeMount:e=>this.__onBeforeMount.push(e),$onMounted:e=>this.__onMounted.push(e),$onBeforeUpdate:e=>this.__onBeforeUpdate.push(e),$onUpdated:e=>this.__onUpdated.push(e),$onBeforeUnmount:e=>this.__onBeforeUnmount.push(e),$onUnmounted:e=>this.__onUnmounted.push(e)}),t}render(){return this.renderFunction(this.getComponentContext())}emit(e,t){this.el&&this.$el.trigger(e,t)}attachEvents(){const{$el:e}=this;this.__eventHandlers&&(this.__eventHandlers.forEach((t=>{let{eventName:r,handler:a}=t;e.on(j(r),a)})),this.__onceEventHandlers.forEach((t=>{let{eventName:r,handler:a}=t;e.once(j(r),a)})))}detachEvents(){const{$el:e}=this;this.__eventHandlers&&(this.__eventHandlers.forEach((t=>{let{eventName:r,handler:a}=t;e.on(j(r),a)})),this.__onceEventHandlers.forEach((t=>{let{eventName:r,handler:a}=t;e.once(j(r),a)})))}startUpdateQueue(){const e=o();if(this.__requestAnimationFrameId)return;const t=()=>{this.hook("onBeforeUpdate");const e=this.render();if(e){const t=ft(e,this,!1);this.vnode=Lt(this.vnode,t)}};this.__requestAnimationFrameId=e.requestAnimationFrame((()=>{this.__updateIsPending&&t();let r=[...this.__updateQueue];this.__updateQueue=[],this.__updateIsPending=!1,e.cancelAnimationFrame(this.__requestAnimationFrameId),delete this.__requestAnimationFrameId,delete this.__updateIsPending,r.forEach((e=>e())),r=[]}))}tick(e){return new Promise((t=>{this.__updateQueue.push((function(){t(),e&&e()})),this.startUpdateQueue()}))}update(e){return this.__destroyed?new Promise((()=>{})):new Promise((t=>{this.__updateIsPending=!0,this.__updateQueue.push((()=>{t(),e&&e()})),this.startUpdateQueue()}))}setState(e){return this.update(e)}f7ready(e){this.f7.initialized?e(this.f7):this.f7.once("init",(()=>{e(this.f7)}))}mount(e){this.hook("onBeforeMount",this.$el),this.styleEl&&N("head").append(this.styleEl),e&&e(this.el),this.hook("onMounted",this.$el)}destroy(){if(this.__destroyed)return;const e=o();this.hook("onBeforeUnmount"),this.styleEl&&N(this.styleEl).remove(),this.detachEvents(),this.hook("onUnmounted"),this.el&&this.el.f7Component&&(this.el.f7Component=null,delete this.el.f7Component),this.vnode&&(this.vnode=Lt(this.vnode,{sel:this.vnode.sel,data:{}})),e.cancelAnimationFrame(this.__requestAnimationFrameId),this.__updateQueue=[],this.__eventHandlers=[],this.__onceEventHandlers=[],this.__onBeforeMount=[],this.__onMounted=[],this.__onBeforeUpdate=[],this.__onUpdated=[],this.__onBeforeUnmount=[],this.__onUnmounted=[],I(this),this.__destroyed=!0}hook(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];this.__destroyed||this[`__${e}`].forEach((e=>{e(...r)}))}}function Bt(e,t){at[e]=t}function Nt(e){delete at[e]}var Ht={name:"component",static:{Component:Mt,registerComponent:Bt,unregisterComponent:Nt},create(){const e=this;e.component={registerComponent:Bt,unregisterComponent:Nt,parse:e=>function(e){const t=o(),r=a(),n=_(),s=`f7_component_create_callback_${n}`;let i;e.match(/<template([ ]?)([a-z0-9-]*)>/)&&(i=e.split(/<template[ ]?[a-z0-9-]*>/).filter(((e,t)=>t>0)).join("<template>").split("</template>").filter(((e,t,r)=>t<r.length-1)).join("</template>").replace(/{{#raw}}([ \n]*)<template/g,"{{#raw}}<template").replace(/\/template>([ \n]*){{\/raw}}/g,"/template>{{/raw}}").replace(/([ \n])<template/g,"$1{{#raw}}<template").replace(/\/template>([ \n])/g,"/template>{{/raw}}$1"));let l,c=null;if(e.indexOf("<style>")>=0&&(c=e.split("<style>")[1].split("</style>")[0]),e.indexOf("<style scoped>")>=0&&(c=e.split("<style scoped>")[1].split("</style>")[0]),e.indexOf("<script>")>=0){const t=e.split("<script>");l=t[t.length-1].split("<\/script>")[0].trim()}else l="return () => {return $render}";l&&l.trim()||(l="return () => {return $render}"),i&&(l=l.replace("$render",`function ($$ctx) {\n          var $ = $$ctx.$$;\n          var $h = $$ctx.$h;\n          var $root = $$ctx.$root;\n          var $f7 = $$ctx.$f7;\n          var $f7route = $$ctx.$f7route;\n          var $f7router = $$ctx.$f7router;\n          var $theme = $$ctx.$theme;\n          var $update = $$ctx.$update;\n          var $store = $$ctx.$store;\n          var $ref = $$ctx.$ref;\n\n          return $h\`${i}\`\n        }\n        `).replace(/export default/g,"return")),l=`window.${s} = function () {${l}}`;const u=r.createElement("script");u.innerHTML=l,N("head").append(u);const p=t[s]();return N(u).remove(),t[s]=null,delete t[s],c&&(p.style=c),p.id=n,p}(e),create(t,r,a){let{root:n,el:o,context:s,children:i}=a;return new Mt(e,t,r,{root:n,el:o,context:s,children:i})}}}},_t={name:"history",static:{history:Ae},on:{init(){Ae.init(this)}}};const Dt={registrations:[],register(e,t){const r=this;return"serviceWorker"in o().navigator&&r.serviceWorker.container?new Promise(((a,n)=>{r.serviceWorker.container.register(e,t?{scope:t}:{}).then((e=>{Dt.registrations.push(e),r.emit("serviceWorkerRegisterSuccess",e),a(e)})).catch((e=>{r.emit("serviceWorkerRegisterError",e),n(e)}))})):new Promise(((e,t)=>{t(new Error("Service worker is not supported"))}))},unregister(e){const t=this;if(!("serviceWorker"in o().navigator)||!t.serviceWorker.container)return new Promise(((e,t)=>{t(new Error("Service worker is not supported"))}));let r;return r=e?Array.isArray(e)?e:[e]:Dt.registrations,Promise.all(r.map((e=>new Promise(((r,a)=>{e.unregister().then((()=>{Dt.registrations.indexOf(e)>=0&&Dt.registrations.splice(Dt.registrations.indexOf(e),1),t.emit("serviceWorkerUnregisterSuccess",e),r()})).catch((r=>{t.emit("serviceWorkerUnregisterError",e,r),a(r)}))})))))}};var qt={name:"sw",params:{serviceWorker:{path:void 0,scope:void 0}},create(){const e=this,t=o();J(e,{serviceWorker:{container:"serviceWorker"in t.navigator?t.navigator.serviceWorker:void 0,registrations:Dt.registrations,register:Dt.register.bind(e),unregister:Dt.unregister.bind(e)}})},on:{init(){const e=o();if(!("serviceWorker"in e.navigator))return;const t=this;if(t.device.cordova||e.Capacitor&&e.Capacitor.isNative)return;if(!t.serviceWorker.container)return;const r=t.params.serviceWorker.path,a=t.params.serviceWorker.scope;if(!r||Array.isArray(r)&&!r.length)return;(Array.isArray(r)?r:[r]).forEach((e=>{t.serviceWorker.register(e,a)}))}}};function jt(e){void 0===e&&(e={});const t={__store:!0},r={...e.state||{}},a={...e.actions||{}},n={...e.getters||{}},o=J({},r);let s=[];const i={},l={};Object.keys(n).forEach((e=>{i[e]=[],l[e]=[]}));const c=e=>n[e]({state:t.state}),u=(e,t)=>{l[e]||(l[e]=[]),l[e].push(t)};t.__removeCallback=e=>{(e=>{Object.keys(l).forEach((t=>{const r=l[t];r.indexOf(e)>=0&&r.splice(r.indexOf(e),1)}))})(e)};const p=function(e,t){if(void 0===t&&(t=!0),"constructor"===e)return;s=[];const r=c(e);((e,t)=>{i[e]||(i[e]=[]),t.forEach((t=>{i[e].indexOf(t)<0&&i[e].push(t)}))})(e,s);const a={value:r,onUpdated:t=>{u(e,t)}};if(!t)return a;const n=e=>{a.value=e};return a.__callback=n,u(e,n),a};return t.state=new Proxy(o,{set:(e,t,r)=>{var a;return e[t]=r,a=t,Object.keys(i).filter((e=>i[e].indexOf(a)>=0)).forEach((e=>{l[e]&&l[e].length&&l[e].forEach((t=>{t(c(e))}))})),!0},get:(e,t)=>(s.push(t),e[t])}),t.getters=new Proxy(n,{set:()=>!1,get:(e,t)=>{if(e[t])return p(t,!0)}}),t._gettersPlain=new Proxy(n,{set:()=>!1,get:(e,t)=>{if(e[t])return p(t,!1)}}),t.dispatch=(e,r)=>new Promise(((n,o)=>{if(!a[e])throw o(),new Error(`Framework7: Store action "${e}" is not found`);n(a[e]({state:t.state,dispatch:t.dispatch},r))})),t}var It={name:"store",static:{createStore:jt},proto:{createStore:jt}};const Ut=()=>{const e=o();return e.Capacitor&&e.Capacitor.isNative&&e.Capacitor.Plugins&&e.Capacitor.Plugins.StatusBar},zt={hide(){const e=o();ie().cordova&&e.StatusBar&&e.StatusBar.hide(),Ut()&&e.Capacitor.Plugins.StatusBar.hide()},show(){const e=o();ie().cordova&&e.StatusBar&&e.StatusBar.show(),Ut()&&e.Capacitor.Plugins.StatusBar.show()},onClick(){const e=this;let t;t=N(".popup.modal-in").length>0?N(".popup.modal-in").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content"):N(".panel.panel-in").length>0?N(".panel.panel-in").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content"):N(".views > .view.tab-active").length>0?N(".views > .view.tab-active").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content"):N(".views").length>0?N(".views").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content"):e.$el.children(".view").find(".page:not(.page-previous):not(.page-next):not(.cached)").find(".page-content"),t&&t.length>0&&(t.hasClass("tab")&&(t=t.parent(".tabs").children(".page-content.tab-active")),t.length>0&&t.scrollTop(0,300))},setTextColor(e){const t=o();ie().cordova&&t.StatusBar&&("white"===e?t.StatusBar.styleLightContent():t.StatusBar.styleDefault()),Ut()&&("white"===e?t.Capacitor.Plugins.StatusBar.setStyle({style:"DARK"}):t.Capacitor.Plugins.StatusBar.setStyle({style:"LIGHT"}))},setBackgroundColor(e){const t=o();ie().cordova&&t.StatusBar&&t.StatusBar.backgroundColorByHexString(e),Ut()&&t.Capacitor.Plugins.StatusBar.setBackgroundColor({color:e})},isVisible(){const e=o(),t=ie();return new Promise((r=>{t.cordova&&e.StatusBar&&r(e.StatusBar.isVisible),Ut()&&e.Capacitor.Plugins.StatusBar.getInfo().then((e=>{r(e.visible)})),r(!1)}))},overlaysWebView(e){void 0===e&&(e=!0);const t=o();ie().cordova&&t.StatusBar&&t.StatusBar.overlaysWebView(e),Ut()&&t.Capacitor.Plugins.StatusBar.setOverlaysWebView({overlay:e})},init(){const e=this,t=o(),r=ie(),a=e.params.statusbar;if(!a.enabled)return;const n=r.cordova&&t.StatusBar,s=Ut();(n||s)&&(a.scrollTopOnClick&&N(t).on("statusTap",zt.onClick.bind(e)),r.ios&&(a.iosOverlaysWebView?zt.overlaysWebView(!0):zt.overlaysWebView(!1),"white"===a.iosTextColor?zt.setTextColor("white"):zt.setTextColor("black")),r.android&&(a.androidOverlaysWebView?zt.overlaysWebView(!0):zt.overlaysWebView(!1),"white"===a.androidTextColor?zt.setTextColor("white"):zt.setTextColor("black"))),a.iosBackgroundColor&&r.ios&&zt.setBackgroundColor(a.iosBackgroundColor),a.androidBackgroundColor&&r.android&&zt.setBackgroundColor(a.androidBackgroundColor)}};var Vt={name:"statusbar",params:{statusbar:{enabled:!0,scrollTopOnClick:!0,iosOverlaysWebView:!0,iosTextColor:"black",iosBackgroundColor:null,androidOverlaysWebView:!1,androidTextColor:"black",androidBackgroundColor:null}},create(){te(this,{statusbar:zt})},on:{init(){zt.init.call(this)}}};var Wt={name:"view",params:{view:{init:!0,initRouterOnTabShow:!1,name:void 0,main:!1,router:!0,linksView:null,stackPages:!1,xhrCache:!0,xhrCacheIgnore:[],xhrCacheIgnoreGetParameters:!1,xhrCacheDuration:6e5,componentCache:!0,preloadPreviousPage:!0,allowDuplicateUrls:!1,reloadPages:!1,reloadDetail:!1,masterDetailBreakpoint:0,masterDetailResizable:!1,removeElements:!0,removeElementsWithTimeout:!1,removeElementsTimeout:0,restoreScrollTopOnBack:!0,unloadTabContent:!0,passRouteQueryToRequest:!0,passRouteParamsToRequest:!1,loadInitialPage:!0,iosSwipeBack:!0,iosSwipeBackAnimateShadow:!0,iosSwipeBackAnimateOpacity:!0,iosSwipeBackActiveArea:30,iosSwipeBackThreshold:0,mdSwipeBack:!1,mdSwipeBackAnimateShadow:!0,mdSwipeBackAnimateOpacity:!1,mdSwipeBackActiveArea:30,mdSwipeBackThreshold:0,auroraSwipeBack:!1,auroraSwipeBackAnimateShadow:!1,auroraSwipeBackAnimateOpacity:!0,auroraSwipeBackActiveArea:30,auroraSwipeBackThreshold:0,browserHistory:!1,browserHistoryRoot:void 0,browserHistoryAnimate:!0,browserHistoryAnimateOnLoad:!1,browserHistorySeparator:"#!",browserHistoryOnLoad:!0,browserHistoryInitialMatch:!1,browserHistoryStoreHistory:!0,browserHistoryTabs:"replace",animate:!0,iosDynamicNavbar:!0,iosAnimateNavbarBackIcon:!0,iosPageLoadDelay:0,mdPageLoadDelay:0,auroraPageLoadDelay:0,routesBeforeEnter:null,routesBeforeLeave:null}},static:{View:We},create(){const e=this;J(e,{views:J([],{create:(t,r)=>new We(e,t,r),get(e){const t=N(e);if(t.length&&t[0].f7View)return t[0].f7View}})}),Object.defineProperty(e.views,"current",{enumerable:!0,configurable:!0,get:()=>function(e){const t=N(".popover.modal-in .view"),r=N(".popup.modal-in .view"),a=N(".panel.panel-in .view");let n=N(".views");0===n.length&&(n=e.$el);let o=n.children(".view");if(0===o.length&&(o=n.children(".tabs").children(".view")),o.length>1&&o.hasClass("tab")&&(o=n.children(".view.tab-active"),0===o.length&&(o=n.children(".tabs").children(".view.tab-active"))),t.length>0&&t[0].f7View)return t[0].f7View;if(r.length>0&&r[0].f7View)return r[0].f7View;if(a.length>0&&a[0].f7View)return a[0].f7View;if(o.length>0){if(1===o.length&&o[0].f7View)return o[0].f7View;if(o.length>1)return e.views.main}}(e)}),e.view=e.views},on:{init(){const e=this;N(".view-init").each((t=>{if(t.f7View)return;const r=N(t).dataset();e.views.create(t,r)}))},"modalOpen panelOpen":function(e){const t=this;e.$el.find(".view-init").each((e=>{if(e.f7View)return;const r=N(e).dataset();t.views.create(e,r)}))},"modalBeforeDestroy panelBeforeDestroy":function(e){e&&e.$el&&e.$el.find(".view-init").each((e=>{const t=e.f7View;t&&t.destroy()}))}},vnode:{"view-init":{insert(e){const t=e.elm;if(t.f7View)return;const r=N(t).dataset();this.views.create(t,r)},destroy(e){const t=e.elm.f7View;t&&t.destroy()}}}};const Ft={size(e){const t=this;let r=N(e);if(r.hasClass("navbars"))return void(r=r.children(".navbar").each((e=>{t.navbar.size(e)})));const a=r.children(".navbar-inner");if(!a.length)return;const n=a.hasClass("navbar-inner-centered-title")||t.params.navbar[`${t.theme}CenterTitle`],o="ios"===t.theme&&!t.params.navbar[`${t.theme}CenterTitle`];if(!n&&!o)return;if(r.hasClass("stacked")||r.parents(".stacked").length>0||r.parents(".tab:not(.tab-active)").length>0||r.parents(".popup:not(.modal-in)").length>0)return;"ios"!==t.theme&&t.params.navbar[`${t.theme}CenterTitle`]&&a.addClass("navbar-inner-centered-title"),"ios"!==t.theme||t.params.navbar.iosCenterTitle||a.addClass("navbar-inner-left-title");const s=r.parents(".view").eq(0),i=t.rtl?a.children(".right"):a.children(".left"),l=t.rtl?a.children(".left"):a.children(".right"),c=a.children(".title"),u=a.children(".subnavbar"),p=0===i.length,d=0===l.length,h=p?0:i.outerWidth(!0),f=d?0:l.outerWidth(!0),m=c.outerWidth(!0),g=a.styles(),v=a[0].offsetWidth-parseInt(g.paddingLeft,10)-parseInt(g.paddingRight,10),b=r.hasClass("navbar-previous"),y=a.hasClass("sliding");let w,C,k,E;s.length>0&&s[0].f7View&&(w=s[0].f7View.router,C=w&&w.dynamicNavbar),d&&(k=v-m),p&&(k=0),p||d||(k=(v-f-m+h)/2);let $=(v-m)/2;v-h-f>m?($<h&&($=h),$+m>v-f&&($=v-f-m),E=$-k):E=0;const x=t.rtl?-1:1;if(C&&"ios"===t.theme){if(c.hasClass("sliding")||c.length>0&&y){let e=-(k+E)*x;const t=(v-k-E-m)*x;if(b&&w&&w.params.iosAnimateNavbarBackIcon){const t=r.parent().find(".navbar-current").children(".left.sliding").find(".back .icon ~ span");t.length>0&&(e+=t[0].offsetLeft)}c[0].f7NavbarLeftOffset=e,c[0].f7NavbarRightOffset=t}if(!p&&(i.hasClass("sliding")||y))if(t.rtl)i[0].f7NavbarLeftOffset=-(v-i[0].offsetWidth)/2*x,i[0].f7NavbarRightOffset=h*x;else if(i[0].f7NavbarLeftOffset=-h,i[0].f7NavbarRightOffset=(v-i[0].offsetWidth)/2,w&&w.params.iosAnimateNavbarBackIcon&&i.find(".back .icon").length>0&&i.find(".back .icon ~ span").length){const e=i[0].f7NavbarLeftOffset,t=i[0].f7NavbarRightOffset;i[0].f7NavbarLeftOffset=0,i[0].f7NavbarRightOffset=0,i.find(".back .icon ~ span")[0].f7NavbarLeftOffset=e,i.find(".back .icon ~ span")[0].f7NavbarRightOffset=t-i.find(".back .icon")[0].offsetWidth}d||!l.hasClass("sliding")&&!y||(t.rtl?(l[0].f7NavbarLeftOffset=-f*x,l[0].f7NavbarRightOffset=(v-l[0].offsetWidth)/2*x):(l[0].f7NavbarLeftOffset=-(v-l[0].offsetWidth)/2,l[0].f7NavbarRightOffset=f)),u.length&&(u.hasClass("sliding")||y)&&(u[0].f7NavbarLeftOffset=t.rtl?u[0].offsetWidth:-u[0].offsetWidth,u[0].f7NavbarRightOffset=-u[0].f7NavbarLeftOffset)}if(n){let e=E;t.rtl&&p&&d&&c.length>0&&(e=-e),c.css({left:`${e}px`})}},hide(e,t,r,a){void 0===t&&(t=!0),void 0===r&&(r=!1),void 0===a&&(a=!1);const n=this;let o=N(e);const s=o.hasClass("navbar")&&o.parent(".navbars").length&&!a;if(s&&(o=o.parents(".navbars")),!o.length)return;if(o.hasClass("navbar-hidden"))return;let i="navbar-hidden"+(t?" navbar-transitioning":"");(s?o.find(".navbar-current .title-large").length:o.find(".title-large").length)&&(i+=" navbar-large-hidden"),r&&(i+=" navbar-hidden-statusbar"),o.transitionEnd((()=>{o.removeClass("navbar-transitioning")})),o.addClass(i),s?o.children(".navbar").each((e=>{N(e).trigger("navbar:hide"),n.emit("navbarHide",e)})):(o.trigger("navbar:hide"),n.emit("navbarHide",o[0]))},show(e,t,r){void 0===e&&(e=".navbar-hidden"),void 0===t&&(t=!0),void 0===r&&(r=!1);const a=this;let n=N(e);const o=n.hasClass("navbar")&&n.parent(".navbars").length&&!r;o&&(n=n.parents(".navbars")),n.length&&n.hasClass("navbar-hidden")&&(t&&(n.addClass("navbar-transitioning"),n.transitionEnd((()=>{n.removeClass("navbar-transitioning")}))),n.removeClass("navbar-hidden navbar-large-hidden navbar-hidden-statusbar"),o?n.children(".navbar").each((e=>{N(e).trigger("navbar:show"),a.emit("navbarShow",e)})):(n.trigger("navbar:show"),a.emit("navbarShow",n[0])))},getElByPage(e){let t,r,a;if(e.$navbarEl||e.$el?(a=e,t=e.$el):(t=N(e),t.length>0&&(a=t[0].f7Page)),a&&a.$navbarEl&&a.$navbarEl.length>0?r=a.$navbarEl:t&&(r=t.children(".navbar")),r&&(!r||0!==r.length))return r[0]},getPageByEl(e){const t=N(e);if(t.parents(".page").length)return t.parents(".page")[0];let r;return t.parents(".view").find(".page").each((e=>{e&&e.f7Page&&e.f7Page.navbarEl&&t[0]===e.f7Page.navbarEl&&(r=e)})),r},collapseLargeTitle(e){const t=this;let r=N(e);if(r.hasClass("navbars")&&(r=r.find(".navbar"),r.length>1&&(r=N(e).find(".navbar-large.navbar-current")),r.length>1||!r.length))return;const a=N(t.navbar.getPageByEl(r));r.addClass("navbar-large-collapsed"),a.eq(0).addClass("page-with-navbar-large-collapsed").trigger("page:navbarlargecollapsed"),t.emit("pageNavbarLargeCollapsed",a[0]),r.trigger("navbar:collapse"),t.emit("navbarCollapse",r[0])},expandLargeTitle(e){const t=this;let r=N(e);if(r.hasClass("navbars")&&(r=r.find(".navbar-large"),r.length>1&&(r=N(e).find(".navbar-large.navbar-current")),r.length>1||!r.length))return;const a=N(t.navbar.getPageByEl(r));r.removeClass("navbar-large-collapsed"),a.eq(0).removeClass("page-with-navbar-large-collapsed").trigger("page:navbarlargeexpanded"),t.emit("pageNavbarLargeExpanded",a[0]),r.trigger("navbar:expand"),t.emit("navbarExpand",r[0])},toggleLargeTitle(e){const t=this;let r=N(e);r.hasClass("navbars")&&(r=r.find(".navbar-large"),r.length>1&&(r=N(e).find(".navbar-large.navbar-current")),r.length>1||!r.length)||(r.hasClass("navbar-large-collapsed")?t.navbar.expandLargeTitle(r):t.navbar.collapseLargeTitle(r))},initNavbarOnScroll(e,t,r,a,n){const o=this,s=se(),i=N(e),l=N(t),c=l.find(".title-large"),u=c.length||l.hasClass(".navbar-large");let p=44;const d=o.params.navbar.snapPageScrollToLargeTitle,h=o.params.navbar.snapPageScrollToTransparentNavbar;let f,m,g,v,b,y,w,C,k,E,$,x,P,O;(a||r&&u)&&(k=l.css("--f7-navbar-large-title-height"),k&&k.indexOf("px")>=0?(k=parseInt(k,10),Number.isNaN(k)&&c.length?k=c[0].offsetHeight:Number.isNaN(k)&&("ios"===o.theme?k=52:"md"===o.theme?k=48:"aurora"===o.theme&&(k=38))):c.length?k=c[0].offsetHeight:"ios"===o.theme?k=52:"md"===o.theme?k=48:"aurora"===o.theme&&(k=38)),r&&u&&(p+=k);function R(){i.find(".page-content").each((e=>{e.f7ScrollableDistance=e.scrollHeight-e.offsetHeight}))}function T(){l.hasClass("with-searchbar-expandable-enabled")||!x||m<0||(m>=k/2&&m<k?N(x).scrollTop(k,100):m<k&&N(x).scrollTop(0,200))}function S(){l.hasClass("with-searchbar-expandable-enabled")||!x||m<0||(m>=E/2&&m<E?N(x).scrollTop(E,100):m<E&&N(x).scrollTop(0,200))}let A=null,L=null;function M(e){x=this,e&&e.target&&e.target!==x||(m=x.scrollTop,$=m,a?function(e){if(l.hasClass("navbar-hidden")||l.parent(".navbars").hasClass("navbar-hidden"))return;const t=l.hasClass("navbar-large-transparent")||l.hasClass("navbar-large")&&l.hasClass("navbar-transparent");A=L;const r=Math.min(k,e.f7ScrollableDistance||k);L=Math.min(Math.max(m/r,0),1);const a=A>0&&A<1;l.hasClass("with-searchbar-expandable-enabled")||(C=l.hasClass("navbar-large-collapsed"),0===L&&C?o.navbar.expandLargeTitle(l[0]):1!==L||C||o.navbar.collapseLargeTitle(l[0]),0===L&&C||0===L&&a||1===L&&!C||1===L&&a?("md"===o.theme&&l.find(".navbar-inner").css("overflow",""),l.find(".title").css("opacity",""),l.find(".title-large-text, .subnavbar").css("transform",""),t?l.find(".navbar-bg").css("opacity",""):l.find(".navbar-bg").css("transform","")):L>0&&L<1&&("md"===o.theme&&l.find(".navbar-inner").css("overflow","visible"),l.find(".title").css("opacity",L),l.find(".title-large-text, .subnavbar").css("transform",`translate3d(0px, ${-1*L*k}px, 0)`),t?l.find(".navbar-bg").css("opacity",L):l.find(".navbar-bg").css("transform",`translate3d(0px, ${-1*L*k}px, 0)`)),d&&(s.touch?O&&(clearTimeout(O),O=null,O=setTimeout((()=>{T(),clearTimeout(O),O=null}),70)):(clearTimeout(P),P=setTimeout((()=>{T()}),300))))}(x):n&&function(){const e=l.hasClass("navbar-hidden")||l.parent(".navbars").hasClass("navbar-hidden");if(l.hasClass("with-searchbar-expandable-enabled")||e)return;E||(E=t.offsetHeight);let r=m/E;const a=l.hasClass("navbar-transparent-visible");if(r=Math.max(Math.min(r,1),0),a&&1===r||!a&&0===r)l.find(".navbar-bg, .title").css("opacity","");else{if(a&&0===r)return l.trigger("navbar:transparenthide"),o.emit("navbarTransparentHide",l[0]),l.removeClass("navbar-transparent-visible"),void l.find(".navbar-bg, .title").css("opacity","");if(!a&&1===r)return l.trigger("navbar:transparentshow"),o.emit("navbarTransparentShow",l[0]),l.addClass("navbar-transparent-visible"),void l.find(".navbar-bg, .title").css("opacity","");l.find(".navbar-bg, .title").css("opacity",r),h&&(s.touch?O&&(clearTimeout(O),O=null,O=setTimeout((()=>{S(),clearTimeout(O),O=null}),70)):(clearTimeout(P),P=setTimeout((()=>{S()}),300)))}}(),i.hasClass("page-previous")||r&&(i.hasClass("page-with-card-opened")||(g=x.scrollHeight,v=x.offsetHeight,b=m+v>=g,w=l.hasClass("navbar-hidden")||l.parent(".navbars").hasClass("navbar-hidden"),b?o.params.navbar.showOnPageScrollEnd&&(y="show"):y=f>m?o.params.navbar.showOnPageScrollTop||m<=p?"show":"hide":m>p?"hide":"show","show"===y&&w?(o.navbar.show(l,!0,!0),w=!1):"hide"!==y||w||(o.navbar.hide(l,!0,!1,!0),w=!0),f=m)))}function B(){$=!1}function H(){clearTimeout(O),O=null,O=setTimeout((()=>{!1!==$&&(n&&!a?S():T(),clearTimeout(O),O=null)}),70)}i.on("scroll",".page-content",M,!0),s.touch&&(a&&d||n&&h)&&(o.on("touchstart:passive",B),o.on("touchend:passive",H)),R(),(a||n)&&i.find(".page-content").each((e=>{e.scrollTop>0&&M.call(e)})),o.on("resize",R),i[0].f7DetachNavbarScrollHandlers=function(){o.off("resize",R),delete i[0].f7DetachNavbarScrollHandlers,i.off("scroll",".page-content",M,!0),s.touch&&(a&&d||n&&h)&&(o.off("touchstart:passive",B),o.off("touchend:passive",H))}}};var Xt={name:"navbar",create(){te(this,{navbar:Ft})},params:{navbar:{scrollTopOnTitleClick:!0,iosCenterTitle:!0,mdCenterTitle:!1,auroraCenterTitle:!0,hideOnPageScroll:!1,showOnPageScrollEnd:!0,showOnPageScrollTop:!0,collapseLargeTitleOnScroll:!0,snapPageScrollToLargeTitle:!0,snapPageScrollToTransparentNavbar:!0}},on:{"panelBreakpoint panelCollapsedBreakpoint panelResize viewResize resize viewMasterDetailBreakpoint":function(){const e=this;N(".navbar").each((t=>{e.navbar.size(t)}))},pageBeforeRemove(e){e.$el[0].f7DetachNavbarScrollHandlers&&e.$el[0].f7DetachNavbarScrollHandlers()},pageBeforeIn(e){const t=this;if("ios"!==t.theme)return;let r;const a=e.$el.parents(".view")[0].f7View,n=t.navbar.getElByPage(e);if(r=n?N(n).parents(".navbars"):e.$el.parents(".view").children(".navbars"),e.$el.hasClass("no-navbar")||a.router.dynamicNavbar&&!n){const a=!!(e.pageFrom&&e.router.history.length>0);t.navbar.hide(r,a)}else t.navbar.show(r)},pageReinit(e){const t=N(this.navbar.getElByPage(e));t&&0!==t.length&&this.navbar.size(t)},pageInit(e){const t=this,r=N(t.navbar.getElByPage(e));if(!r||0===r.length)return;let a,n,o;t.navbar.size(r),r.find(".title-large").length>0&&r.addClass("navbar-large"),r.hasClass("navbar-large")&&(t.params.navbar.collapseLargeTitleOnScroll&&(a=!0),e.$el.addClass("page-with-navbar-large")),!a&&r.hasClass("navbar-transparent")&&(n=!0),(t.params.navbar.hideOnPageScroll||e.$el.find(".hide-navbar-on-scroll").length||e.$el.hasClass("hide-navbar-on-scroll")||e.$el.find(".hide-bars-on-scroll").length||e.$el.hasClass("hide-bars-on-scroll"))&&(o=!(e.$el.find(".keep-navbar-on-scroll").length||e.$el.hasClass("keep-navbar-on-scroll")||e.$el.find(".keep-bars-on-scroll").length||e.$el.hasClass("keep-bars-on-scroll"))),(a||o||n)&&t.navbar.initNavbarOnScroll(e.el,r[0],o,a,n)},"panelOpen panelSwipeOpen modalOpen":function(e){const t=this;e.$el.find(".navbar:not(.navbar-previous):not(.stacked)").each((e=>{t.navbar.size(e)}))},tabShow(e){const t=this;N(e).find(".navbar:not(.navbar-previous):not(.stacked)").each((e=>{t.navbar.size(e)}))}},clicks:{".navbar .title":function(e,t,r){if(!this.params.navbar.scrollTopOnTitleClick)return;if(N(r.target).closest("a, button").length>0)return;let a;const n=e.parents(".navbar"),o=n.parents(".navbars");a=n.parents(".page-content"),0===a.length&&(n.parents(".page").length>0&&(a=n.parents(".page").find(".page-content")),0===a.length&&o.length&&o.nextAll(".page-current:not(.stacked)").length>0&&(a=o.nextAll(".page-current:not(.stacked)").find(".page-content")),0===a.length&&n.nextAll(".page-current:not(.stacked)").length>0&&(a=n.nextAll(".page-current:not(.stacked)").find(".page-content"))),a&&a.length>0&&(a.hasClass("tab")&&(a=a.parent(".tabs").children(".page-content.tab-active")),a.length>0&&a.scrollTop(0,300))}},vnode:{navbar:{postpatch(e){this.navbar.size(e.elm)}}}};const Qt={setHighlight(e){const t=this,r=N(e);if("ios"===t.theme&&!r.hasClass("tabbar-highlight"))return;if(0===r.length||!r.hasClass("tabbar")&&!r.hasClass("tabbar-labels"))return;let a=r.find(".tab-link-highlight");const n=r.find(".tab-link").length;if(0===n)return void a.remove();0===a.length?(r.children(".toolbar-inner").append('<span class="tab-link-highlight"></span>'),a=r.find(".tab-link-highlight")):a.next().length&&r.children(".toolbar-inner").append(a);const o=r.find(".tab-link-active");let s,i;if(r.hasClass("tabbar-scrollable")&&o&&o[0])s=`${o[0].offsetWidth}px`,i=`${o[0].offsetLeft}px`;else{const e=o.index();s=100/n+"%",i=100*(t.rtl?-e:e)+"%"}V((()=>{a.css("width",s).transform(`translate3d(${i},0,0)`)}))},init(e){this.toolbar.setHighlight(e)},hide(e,t){void 0===t&&(t=!0);const r=N(e);if(r.hasClass("toolbar-hidden"))return;const a="toolbar-hidden"+(t?" toolbar-transitioning":"");r.transitionEnd((()=>{r.removeClass("toolbar-transitioning")})),r.addClass(a),r.trigger("toolbar:hide"),this.emit("toolbarHide",r[0])},show(e,t){void 0===t&&(t=!0);const r=N(e);r.hasClass("toolbar-hidden")&&(t&&(r.addClass("toolbar-transitioning"),r.transitionEnd((()=>{r.removeClass("toolbar-transitioning")}))),r.removeClass("toolbar-hidden"),r.trigger("toolbar:show"),this.emit("toolbarShow",r[0]))},initToolbarOnScroll(e){const t=this,r=N(e);let a,n,o,s,i,l,c,u=r.parents(".view").children(".toolbar");function p(e){if(r.hasClass("page-with-card-opened"))return;if(r.hasClass("page-previous"))return;const p=this;e&&e.target&&e.target!==p||(n=p.scrollTop,o=p.scrollHeight,s=p.offsetHeight,i=n+s>=o,c=u.hasClass("toolbar-hidden"),i?t.params.toolbar.showOnPageScrollEnd&&(l="show"):l=a>n?t.params.toolbar.showOnPageScrollTop||n<=44?"show":"hide":n>44?"hide":"show","show"===l&&c?(t.toolbar.show(u),c=!1):"hide"!==l||c||(t.toolbar.hide(u),c=!0),a=n)}0===u.length&&(u=r.find(".toolbar")),0===u.length&&(u=r.parents(".views").children(".tabbar, .tabbar-labels")),0!==u.length&&(r.on("scroll",".page-content",p,!0),r[0].f7ScrollToolbarHandler=p)}};var Yt={name:"toolbar",create(){te(this,{toolbar:Qt})},params:{toolbar:{hideOnPageScroll:!1,showOnPageScrollEnd:!0,showOnPageScrollTop:!0}},on:{pageBeforeRemove(e){e.$el[0].f7ScrollToolbarHandler&&e.$el.off("scroll",".page-content",e.$el[0].f7ScrollToolbarHandler,!0)},pageBeforeIn(e){const t=this;let r=e.$el.parents(".view").children(".toolbar");0===r.length&&(r=e.$el.parents(".views").children(".tabbar, .tabbar-labels")),0===r.length&&(r=e.$el.find(".toolbar")),0!==r.length&&(e.$el.hasClass("no-toolbar")?t.toolbar.hide(r):t.toolbar.show(r))},pageInit(e){const t=this;if(e.$el.find(".tabbar, .tabbar-labels").each((e=>{t.toolbar.init(e)})),t.params.toolbar.hideOnPageScroll||e.$el.find(".hide-toolbar-on-scroll").length||e.$el.hasClass("hide-toolbar-on-scroll")||e.$el.find(".hide-bars-on-scroll").length||e.$el.hasClass("hide-bars-on-scroll")){if(e.$el.find(".keep-toolbar-on-scroll").length||e.$el.hasClass("keep-toolbar-on-scroll")||e.$el.find(".keep-bars-on-scroll").length||e.$el.hasClass("keep-bars-on-scroll"))return;t.toolbar.initToolbarOnScroll(e.el)}},init(){const e=this;e.$el.find(".tabbar, .tabbar-labels").each((t=>{e.toolbar.init(t)}))}},vnode:{tabbar:{insert(e){this.toolbar.init(e.elm)}}}},Jt={name:"subnavbar",on:{pageInit(e){e.$navbarEl&&e.$navbarEl.length&&e.$navbarEl.find(".subnavbar").length&&e.$el.addClass("page-with-subnavbar");e.$el.find(".subnavbar").filter((t=>N(t).parents(".page")[0]===e.$el[0])).length&&e.$el.addClass("page-with-subnavbar")}}};class Gt{constructor(e,t,r,a){const n=this;if(!t)return;const{left:o,top:s,width:i,height:l}=t[0].getBoundingClientRect(),c=r-o,u=a-s;let p=Math.max((l**2+i**2)**.5,48),d=!1;const h=e.params.touch.touchRippleInsetElements||"";if(h&&t.is(h)&&(d=!0),d&&(p=Math.max(Math.min(i,l),48)),d||"hidden"!==t.css("overflow"))n.rippleTransform=`translate3d(${i/2-c}px, ${l/2-u}px, 0) scale(1)`;else{const e=(p/2+((c-i/2)**2+(u-l/2)**2)**.5)/(p/2);n.rippleTransform=`translate3d(0px, 0px, 0) scale(${e})`}return d&&t.addClass("ripple-inset"),n.$rippleWaveEl=N(`<div class="ripple-wave" style="width: ${p}px; height: ${p}px; margin-top:-${p/2}px; margin-left:-${p/2}px; left:${c}px; top:${u}px; --f7-ripple-transform: ${n.rippleTransform}"></div>`),t.prepend(n.$rippleWaveEl),n.$rippleWaveEl.animationEnd((()=>{n.$rippleWaveEl&&(n.$rippleWaveEl.hasClass("ripple-wave-out")||(n.$rippleWaveEl.addClass("ripple-wave-in"),n.shouldBeRemoved&&n.out()))})),n}destroy(){let e=this;e.$rippleWaveEl&&e.$rippleWaveEl.remove(),Object.keys(e).forEach((t=>{e[t]=null,delete e[t]})),e=null}out(){const e=this,{$rippleWaveEl:t}=this;clearTimeout(e.removeTimeout),t.addClass("ripple-wave-out"),e.removeTimeout=setTimeout((()=>{e.destroy()}),300),t.animationEnd((()=>{clearTimeout(e.removeTimeout),e.destroy()}))}remove(){const e=this;e.shouldBeRemoved||(e.removeTimeout=setTimeout((()=>{e.destroy()}),400),e.shouldBeRemoved=!0,e.$rippleWaveEl.hasClass("ripple-wave-in")&&e.out())}}var Kt={name:"touch-ripple",static:{TouchRipple:Gt},create(){this.touchRipple={create(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return new Gt(...t)}}}};const Zt=[],er=[];class tr extends ce{constructor(e,t){super(t,[e]);const r=this,a={};r.useModulesParams(a),r.params=J(a,t),r.opened=!1;let n=r.params.containerEl?N(r.params.containerEl).eq(0):e.$el;return n.length||(n=e.$el),r.$containerEl=n,r.containerEl=n[0],r.useModules(),this}onOpen(){const e=this;e.opened=!0,Zt.push(e),N("html").addClass(`with-modal-${e.type.toLowerCase()}`),e.$el.trigger(`modal:open ${e.type.toLowerCase()}:open`),e.emit(`local::open modalOpen ${e.type}Open`,e)}onOpened(){const e=this;e.$el.trigger(`modal:opened ${e.type.toLowerCase()}:opened`),e.emit(`local::opened modalOpened ${e.type}Opened`,e)}onClose(){const e=this;e.opened=!1,e.type&&e.$el&&(Zt.splice(Zt.indexOf(e),1),N("html").removeClass(`with-modal-${e.type.toLowerCase()}`),e.$el.trigger(`modal:close ${e.type.toLowerCase()}:close`),e.emit(`local::close modalClose ${e.type}Close`,e))}onClosed(){const e=this;e.type&&e.$el&&(e.$el.removeClass("modal-out"),e.$el.hide(),e.params.backdrop&&(e.params.backdropUnique||e.forceBackdropUnique)&&e.$backdropEl&&e.$backdropEl.remove(),e.$el.trigger(`modal:closed ${e.type.toLowerCase()}:closed`),e.emit(`local::closed modalClosed ${e.type}Closed`,e))}open(e){const t=this,r=a(),n=t.app,o=t.$el,s=t.$backdropEl,i=t.type;let l=!0;if(void 0!==e?l=e:void 0!==t.params.animate&&(l=t.params.animate),!o||o.hasClass("modal-in"))return!1===e&&o[0]&&"dialog"!==i&&(o[0].style.display="block"),t;if("dialog"===i&&n.params.modal.queueDialogs){let e;if(N(".dialog.modal-in").length>0?e=!0:Zt.length>0&&Zt.forEach((t=>{"dialog"===t.type&&(e=!0)})),e)return er.push(t),t}const c=o.parent(),u=o.parents(r).length>0;function p(){o.hasClass("modal-out")?t.onClosed():o.hasClass("modal-in")&&t.onOpened()}return c.is(t.$containerEl)||(t.$containerEl.append(o),t.once(`${i}Closed`,(()=>{u?c.append(o):o.remove()}))),o.show(),t.params.backdrop&&(t.params.backdropUnique||t.forceBackdropUnique)&&t.$backdropEl&&t.$backdropEl.insertBefore(o),t._clientLeft=o[0].clientLeft,l?(s&&(s.removeClass("not-animated"),s.addClass("backdrop-in")),o.animationEnd((()=>{p()})),o.transitionEnd((()=>{p()})),o.removeClass("modal-out not-animated").addClass("modal-in"),t.onOpen()):(s&&s.addClass("backdrop-in not-animated"),o.removeClass("modal-out").addClass("modal-in not-animated"),t.onOpen(),t.onOpened()),t}close(e){const t=this,r=t.$el,a=t.$backdropEl;let n=!0;if(void 0!==e?n=e:void 0!==t.params.animate&&(n=t.params.animate),!r||!r.hasClass("modal-in"))return er.indexOf(t)>=0&&er.splice(er.indexOf(t),1),t;if(a){let e=!0;"popup"===t.type&&t.$el.prevAll(".popup.modal-in").add(t.$el.nextAll(".popup.modal-in")).each((r=>{const a=r.f7Modal;a&&a.params.closeByBackdropClick&&a.params.backdrop&&a.backdropEl===t.backdropEl&&(e=!1)})),e&&(a[n?"removeClass":"addClass"]("not-animated"),a.removeClass("backdrop-in"))}function o(){r.hasClass("modal-out")?t.onClosed():r.hasClass("modal-in")&&t.onOpened()}return r[n?"removeClass":"addClass"]("not-animated"),n?(r.animationEnd((()=>{o()})),r.transitionEnd((()=>{o()})),r.removeClass("modal-in").addClass("modal-out"),t.onClose()):(r.addClass("not-animated").removeClass("modal-in").addClass("modal-out"),t.onClose(),t.onClosed()),"dialog"===t.type&&function(){if(0===er.length)return;er.shift().open()}(),t}destroy(){const e=this;e.destroyed||(e.emit(`local::beforeDestroy modalBeforeDestroy ${e.type}BeforeDestroy`,e),e.$el&&(e.$el.trigger(`modal:beforedestroy ${e.type.toLowerCase()}:beforedestroy`),e.$el.length&&e.$el[0].f7Modal&&delete e.$el[0].f7Modal),I(e),e.destroyed=!0)}}class rr extends tr{constructor(e,t){const r=J({backdrop:!0,closeByBackdropClick:!0,on:{}},t);super(e,r);const a=this;let n,o;if(a.params=r,n=a.params.el?N(a.params.el):N(a.params.content),n&&n.length>0&&n[0].f7Modal)return n[0].f7Modal;if(0===n.length)return a.destroy();function s(e){a&&!a.destroyed&&o&&e.target===o[0]&&a.close()}return a.params.backdrop&&(o=e.$el.children(".custom-modal-backdrop"),0===o.length&&(o=N('<div class="custom-modal-backdrop"></div>'),e.$el.append(o))),a.on("customModalOpened",(()=>{a.params.closeByBackdropClick&&a.params.backdrop&&e.on("click",s)})),a.on("customModalClose",(()=>{a.params.closeByBackdropClick&&a.params.backdrop&&e.off("click",s)})),J(a,{app:e,$el:n,el:n[0],$backdropEl:o,backdropEl:o&&o[0],type:"customModal"}),n[0].f7Modal=a,a}}var ar={name:"modal",static:{Modal:tr,CustomModal:rr},create(){const e=this;e.customModal={create:t=>new rr(e,t)}},params:{modal:{queueDialogs:!0}}};return"undefined"!=typeof window&&(window.Dom7||(window.Dom7=N)),Ue.use([Xe]),de.use([he,fe,me,ge,Ee,$e,Fe,ze,_t,Ht,qt,It,Vt,Wt,Xt,Yt,Jt,Kt,ar]),de}));
//# sourceMappingURL=framework7.min.js.map