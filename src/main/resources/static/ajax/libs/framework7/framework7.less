/**
 * Framework7 7.0.2
 * Full featured mobile HTML framework for building iOS & Android apps
 * https://framework7.io/
 *
 * Copyright 2014-2022 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: April 24, 2022
 */

@import (reference) './less/mixins.less';
@import (reference) './less/vars.less';

@includeIosTheme: true;
@includeMdTheme: true;
@includeAuroraTheme: true;

@includeDarkTheme: true;
@includeLightTheme: true;

@themeColor: #007aff;

@colors: {
  red: #ff3b30;
  green: #4cd964;
  blue: #2196f3;
  pink: #ff2d55;
  yellow: #ffcc00;
  orange: #ff9500;
  purple: #9c27b0;
  deeppurple: #673ab7;
  lightblue: #5ac8fa;
  teal: #009688;
  lime: #cddc39;
  deeporange: #ff6b22;
  gray: #8e8e93;
  white: #ffffff;
  black: #000000;
};

@rtl: false;

// Core
@import './components/app/app.less';
@import './components/statusbar/statusbar.less';
@import './components/view/view.less';
@import './components/page/page.less';
@import './components/link/link.less';
@import './components/navbar/navbar.less';
@import './components/toolbar/toolbar.less';
@import './components/subnavbar/subnavbar.less';
@import './components/block/block.less';
@import './components/list/list.less';
@import './components/badge/badge.less';
@import './components/button/button.less';
@import './components/touch-ripple/touch-ripple.less';
@import './components/icon/icon.less';
@import './components/modal/modal.less';

//IMPORT_COMPONENTS
