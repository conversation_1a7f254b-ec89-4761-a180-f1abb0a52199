# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ù¾ÚÚ¾ÙØ§ ØµÙØ­Û
previous_label=Ù¾ÚÚ¾ÙØ§
next.title=Ø§Ú¯ÙØ§ ØµÙØ­Û
next_label=Ø¢Ú¯Û

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ØµÙØ­Û
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} Ú©Ø§
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ú©Ø§ {{pagesCount}})

zoom_out.title=Ø¨Ø§ÛØ± Ø²ÙÙ Ú©Ø±ÛÚº
zoom_out_label=Ø¨Ø§ÛØ± Ø²ÙÙ Ú©Ø±ÛÚº
zoom_in.title=Ø§ÙØ¯Ø± Ø²ÙÙ Ú©Ø±ÛÚº
zoom_in_label=Ø§ÙØ¯Ø± Ø²ÙÙ Ú©Ø±ÛÚº
zoom.title=Ø²ÙÙ
presentation_mode.title=Ù¾ÛØ´Ú©Ø´ ÙÙÚ ÙÛÚº ÚÙÛ Ø¬Ø§Ø¦ÛÚº
presentation_mode_label=Ù¾ÛØ´Ú©Ø´ ÙÙÚ
open_file.title=ÙØ³Ù Ú©Ú¾ÙÙÛÚº
open_file_label=Ú©Ú¾ÙÙÛÚº
print.title=ÚÚ¾Ø§Ù¾ÛÚº
print_label=ÚÚ¾Ø§Ù¾ÛÚº
download.title=ÚØ§Ø¤Ù ÙÙÚ
download_label=ÚØ§Ø¤Ù ÙÙÚ
bookmark.title=Ø­Ø§ÙÛÛ ÙØ¸Ø§Ø±Û (ÙÛ Ø¯Ø±ÛÚÛ ÙÛÚº ÙÙÙ Ú©Ø±ÛÚº ÛØ§ Ú©Ú¾ÙÙÛÚº)
bookmark_label=Ø­Ø§ÙÛÛ ÙØ¸Ø§Ø±Û

# Secondary toolbar and context menu
tools.title=Ø¢ÙØ§Øª
tools_label=Ø¢ÙØ§Øª
first_page.title=Ù¾ÛÙÛ ØµÙØ­Û Ù¾Ø± Ø¬Ø§Ø¦ÛÚº
first_page_label=Ù¾ÛÙÛ ØµÙØ­Û Ù¾Ø± Ø¬Ø§Ø¦ÛÚº
last_page.title=Ø¢Ø®Ø±Û ØµÙØ­Û Ù¾Ø± Ø¬Ø§Ø¦ÛÚº
last_page_label=Ø¢Ø®Ø±Û ØµÙØ­Û Ù¾Ø± Ø¬Ø§Ø¦ÛÚº
page_rotate_cw.title=Ú¯Ú¾ÚÛ ÙØ§Ø± Ú¯Ú¾ÙØ§Ø¦ÛÚº
page_rotate_cw_label=Ú¯Ú¾ÚÛ ÙØ§Ø± Ú¯Ú¾ÙØ§Ø¦ÛÚº
page_rotate_ccw.title=Ø¶Ø¯ Ú¯Ú¾ÚÛ ÙØ§Ø± Ú¯Ú¾ÙØ§Ø¦ÛÚº
page_rotate_ccw_label=Ø¶Ø¯ Ú¯Ú¾ÚÛ ÙØ§Ø± Ú¯Ú¾ÙØ§Ø¦ÛÚº

cursor_text_select_tool.title=ÙØªÙ Ú©Û Ø§ÙØªØ®Ø§Ø¨ Ú©Û Ù¹ÙÙ Ú©Ù ÙØ¹Ø§Ù Ø¨ÙØ§Û
cursor_text_select_tool_label=ÙØªÙ Ú©Û Ø§ÙØªØ®Ø§Ø¨ Ú©Ø§ Ø¢ÙÛ
cursor_hand_tool.title=ÛÛÙÚ Ù¹ÙÙ Ú©Ù ÙØ¹Ø§Ù Ø¨ÙØ§ÛÛÚº
cursor_hand_tool_label=ÛØ§ØªÚ¾ Ú©Ø§ Ø¢ÙÛ

scroll_vertical.title=Ø¹ÙÙØ¯Û Ø§Ø³Ú©Ø±ÙÙÙÚ¯ Ú©Ø§ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±ÛÚº
scroll_vertical_label=Ø¹ÙÙØ¯Û Ø§Ø³Ú©Ø±ÙÙÙÚ¯
scroll_horizontal.title=Ø§ÙÙÛ Ø³Ú©Ø±ÙÙÙÚ¯ Ú©Ø§ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±ÛÚº
scroll_horizontal_label=Ø§ÙÙÛ Ø³Ú©Ø±ÙÙÙÚ¯

spread_none.title=ØµÙØ­Û Ù¾Ú¾ÛÙØ§ÙÛ ÙÛÚº Ø´Ø§ÙÙ ÙÛ ÛÙÚº
spread_none_label=Ú©ÙØ¦Û Ù¾Ú¾ÛÙØ§Ø¤ ÙÛÛÚº
spread_odd_label=ØªØ§Ú© Ù¾Ú¾ÛÙØ§Ø¤
spread_even_label=Ø¬ÙØª Ù¾Ú¾ÛÙØ§Ø¤

# Document properties dialog box
document_properties.title=Ø¯Ø³ØªØ§ÙÛØ² Ø®ÙØ§Øµâ¦
document_properties_label=Ø¯Ø³ØªØ§ÙÛØ² Ø®ÙØ§Øµâ¦\u0020
document_properties_file_name=ÙØ§Ù ÙØ³Ù:
document_properties_file_size=ÙØ³Ù Ø³Ø§Ø¦Ø²:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Ø¹ÙÙØ§Ù:
document_properties_author=ØªØ®ÙÛÙ Ú©Ø§Ø±:
document_properties_subject=ÙÙØ¶ÙØ¹:
document_properties_keywords=Ú©ÙÛØ¯Û Ø§ÙÙØ§Ø¸:
document_properties_creation_date=ØªØ®ÙÛÙ Ú©Û ØªØ§Ø±ÛØ®:
document_properties_modification_date=ØªØ±ÙÛÙ Ú©Û ØªØ§Ø±ÛØ®:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}Ø {{time}}
document_properties_creator=ØªØ®ÙÛÙ Ú©Ø§Ø±:
document_properties_producer=PDF Ù¾ÛØ¯Ø§ Ú©Ø§Ø±:
document_properties_version=PDF ÙØ±ÚÙ:
document_properties_page_count=ØµÙØ­Û Ø´ÙØ§Ø±:
document_properties_page_size=ØµÙÛ Ú©Û ÙÙØ¨Ø§Ø¦:
document_properties_page_size_unit_inches=ÙÛÚº
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=Ø¹ÙÙØ¯Û Ø§ÙØ¯Ø§Ø²
document_properties_page_size_orientation_landscape=Ø§ÙÙÙ Ø§ÙØ¯Ø§Ø²
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Ø®Ø·
document_properties_page_size_name_legal=ÙØ§ÙÙÙÛ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} {{name}} {{orientation}}
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ØªÛØ² ÙÛØ¨ Ø¯ÛÚ©Ú¾ÛÚº:
document_properties_linearized_yes=ÛØ§Úº
document_properties_linearized_no=ÙÛÛÚº
document_properties_close=Ø¨ÙØ¯ Ú©Ø±ÛÚº

print_progress_message=ÚÚ¾Ø§Ù¾ÙÛ Ú©Ø±ÙÛ Ú©Û ÙÛÛ Ø¯Ø³ØªØ§ÙÛØ² ØªÛØ§Ø± Ú©ÛÛ Ø¬Ø§ Ø±Ú¾Û Ú¾ÛÚº
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent=*{{progress}}%*
print_progress_close=ÙÙØ³ÙØ® Ú©Ø±ÛÚº

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ø³ÙØ§Ø¦ÛÚ Ù¹ÙÚ¯Ù Ú©Ø±ÛÚº
toggle_sidebar_label=Ø³ÙØ§Ø¦ÛÚ Ù¹ÙÚ¯Ù Ú©Ø±ÛÚº
document_outline.title=Ø¯Ø³ØªØ§ÙÛØ² Ú©Û Ø³Ø±Ø®ÛØ§Úº Ø¯Ú©Ú¾Ø§ÛÚº (ØªÙØ§Ù Ø§Ø´ÛØ§Ø¡ ÙØ³ÛØ¹ / ØºØ§Ø¦Ø¨ Ú©Ø±ÙÛ Ú©Û ÙÛÛ ÚØ¨Ù Ú©ÙÚ© Ú©Ø±ÛÚº)
document_outline_label=Ø¯Ø³ØªØ§ÙÛØ² Ø¢Ø¤Ù¹ ÙØ§Ø¦Ù
attachments.title=ÙÙØ³ÙÚ©Ø§Øª Ø¯Ú©Ú¾Ø§Ø¦ÛÚº
attachments_label=ÙÙØ³ÙÚ©Ø§Øª
thumbs.title=ØªÚ¾ÙØ¨ÙÛÙ Ø¯Ú©Ú¾Ø§Ø¦ÛÚº
thumbs_label=ÙØ¬ÙÙ
findbar.title=Ø¯Ø³ØªØ§ÙÛØ² ÙÛÚº ÚÚ¾ÙÙÚÛÚº
findbar_label=ÚÚ¾ÙÙÚÛÚº

# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=ØµÙØ­Û {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ØµÙØ­Û {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ØµÙØ­Û Ú©Ø§ ÙØ¬ÙÙ {{page}}

# Find panel button title and messages
find_input.title=ÚÚ¾ÙÙÚÛÚº
find_input.placeholder=Ø¯Ø³ØªØ§ÙÛØ²â¦ ÙÛÚº ÚÚ¾ÙÙÚÛÚº
find_previous.title=ÙÙØ±Û Ú©Ø§ Ù¾ÚÚ¾ÙØ§ ÙÙÙØ¹ ÚÚ¾ÙÙÚÛÚº
find_previous_label=Ù¾ÚÚ¾ÙØ§
find_next.title=ÙÙØ±Û Ú©Ø§ Ø§Ú¯ÙÛ ÙÙÙØ¹ ÚÚ¾ÙÙÚÛÚº
find_next_label=Ø¢Ú¯Û
find_highlight=ØªÙØ§Ù ÙÙØ§ÛØ§Úº Ú©Ø±ÛÚº
find_match_case_label=Ø­Ø±ÙÙ ÙØ´Ø§Ø¨Û Ú©Ø±ÛÚº
find_entire_word_label=ØªÙØ§Ù Ø§ÙÙØ§Ø¸
find_reached_top=ØµÙØ­Û Ú©Û Ø´Ø±ÙØ¹ Ù¾Ø± Ù¾ÛÙÚ Ú¯ÛØ§Ø ÙÛÚÛ Ø³Û Ø¬Ø§Ø±Û Ú©ÛØ§
find_reached_bottom=ØµÙØ­Û Ú©Û Ø§Ø®ØªØªØ§Ù Ù¾Ø± Ù¾ÛÙÚ Ú¯ÛØ§Ø Ø§ÙÙ¾Ø± Ø³Û Ø¬Ø§Ø±Û Ú©ÛØ§
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} ÙÛÚ Ú©Ø§ {{current}}
find_match_count[few]={{total}} ÙÛÚÙÚº ÙÛÚº Ø³Û {{current}}
find_match_count[many]={{total}} ÙÛÚÙÚº ÙÛÚº Ø³Û {{current}}
find_match_count[other]={{total}} ÙÛÚÙÚº ÙÛÚº Ø³Û {{current}}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(total) ]}
find_match_count_limit[zero]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_match_count_limit[one]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_match_count_limit[two]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_match_count_limit[few]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_match_count_limit[many]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_match_count_limit[other]={{limit}} Ø³Û Ø²ÛØ§Ø¯Û ÙÛÚ
find_not_found=ÙÙØ±Ø§ ÙÛÛÚº ÙÙØ§

# Error panel labels
error_more_info=ÙØ²ÛØ¯ ÙØ¹ÙÙÙØ§Øª
error_less_info=Ú©Ù ÙØ¹ÙÙÙØ§Øª
error_close=Ø¨ÙØ¯ Ú©Ø±ÛÚº
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ù¾ÛØºØ§Ù: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ø³Ù¹ÛÚ©: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ÙØ³Ù: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÙØ§Ø¦Ù: {{line}}
rendering_error=ØµÙØ­Û Ø¨ÙØ§ØªÛ ÛÙØ¦Û ÙÙØµ Ø¢ Ú¯ÛØ§Û

# Predefined zoom values
page_scale_width=ØµÙØ­Û ÚÙÚØ§Ø¦Û
page_scale_fit=ØµÙØ­Û ÙÙ¹ÙÚ¯
page_scale_auto=Ø®ÙØ¯Ú©Ø§Ø± Ø²ÙÙ
page_scale_actual=Ø§ØµÙ Ø³Ø§Ø¦Ø²
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error=PDF ÙÙÚ Ú©Ø±ØªÛ ÙÙØª ÙÙØµ Ø¢ Ú¯ÛØ§Û
invalid_file_error=ÙØ§Ø¬Ø§Ø¦Ø² ÛØ§ Ø®Ø±Ø§Ø¨ PDF ÙØ³Ù
missing_file_error=PDF ÙØ³Ù ØºØ§Ø¦Ø¨ ÛÛÛ
unexpected_response_error=ØºÛØ±ÙØªÙÙØ¹ Ù¾ÛØ´ Ú©Ø§Ø± Ø¬ÙØ§Ø¨

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}.{{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ÙÙÙ¹]
password_label=PDF ÙØ³Ù Ú©Ú¾ÙÙÙÛ Ú©Û ÙÛÛ Ù¾Ø§Ø³ ÙØ±Ú Ø¯Ø§Ø®Ù Ú©Ø±ÛÚº.
password_invalid=ÙØ§Ø¬Ø§Ø¦Ø² Ù¾Ø§Ø³ ÙØ±Ú. Ø¨Ø±Ø§ÛØ Ú©Ø±Ù Ø¯ÙØ¨Ø§Ø±Û Ú©ÙØ´Ø´ Ú©Ø±ÛÚº.
password_ok=Ù¹Ú¾ÛÚ© ÛÛ
password_cancel=ÙÙØ³ÙØ® Ú©Ø±ÛÚº

printing_not_supported=ØªÙØ¨ÛÛ:ÚÚ¾Ø§Ù¾ÙØ§ Ø§Ø³ Ø¨Ø±Ø§Ø¤Ø²Ø± Ù¾Ø± Ù¾ÙØ±Û Ø·Ø±Ø­ ÙØ¹Ø§ÙÙØª Ø´Ø¯Û ÙÛÛÚº ÛÛÛ
printing_not_ready=ØªÙØ¨ÛÛ: PDF ÚÚ¾Ù¾Ø§Ø¦Û Ú©Û ÙÛÛ Ù¾ÙØ±Û Ø·Ø±Ø­ ÙÙÚ ÙÛÛÚº ÛÙØ¦ÛÛ
web_fonts_disabled=ÙÛØ¨ ÙØ§ÙÙ¹ ÙØ§ Ø§ÛÙ ÛÛÚº: Ø´Ø§ÙÙ PDF ÙØ§ÙÙ¹ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±ÙÛ ÙÛÚº ÙØ§Ú©Ø§ÙÛ
# LOCALIZATION NOTE (unsupported_feature_signatures): Should contain the same
# exact string as in the `chrome.properties` file.
