{"version": 3, "sources": ["webpack://Tesseract/webpack/universalModuleDefinition", "webpack://Tesseract/webpack/bootstrap", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-meta.js", "webpack://Tesseract/./src/utils/getId.js", "webpack://Tesseract/./src/utils/log.js", "webpack://Tesseract/./node_modules/process/browser.js", "webpack://Tesseract/./node_modules/resolve-url/resolve-url.js", "webpack://Tesseract/./src/createJob.js", "webpack://Tesseract/./src/createWorker.js", "webpack://Tesseract/./src/constants/OEM.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-scale.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-exif.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-iptc.js", "webpack://Tesseract/./src/index.js", "webpack://Tesseract/./node_modules/regenerator-runtime/runtime.js", "webpack://Tesseract/(webpack)/buildin/module.js", "webpack://Tesseract/./src/createScheduler.js", "webpack://Tesseract/./src/utils/resolvePaths.js", "webpack://Tesseract/./src/utils/getEnvironment.js", "webpack://Tesseract/./node_modules/is-electron/index.js", "webpack://Tesseract/./src/utils/circularize.js", "webpack://Tesseract/./src/constants/config.js", "webpack://Tesseract/./src/worker/browser/index.js", "webpack://Tesseract/./src/worker/browser/defaultOptions.js", "webpack://Tesseract/./src/constants/defaultOptions.js", "webpack://Tesseract/./src/worker/browser/spawnWorker.js", "webpack://Tesseract/./src/worker/browser/terminateWorker.js", "webpack://Tesseract/./src/worker/browser/onMessage.js", "webpack://Tesseract/./src/worker/browser/send.js", "webpack://Tesseract/./src/worker/browser/loadImage.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/index.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-fetch.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-exif-map.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-iptc-map.js", "webpack://Tesseract/./node_modules/blueimp-load-image/js/load-image-orientation.js", "webpack://Tesseract/./src/Tesseract.js", "webpack://Tesseract/./src/constants/languages.js", "webpack://Tesseract/./src/constants/PSM.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "window", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "$", "loadImage", "file", "callback", "options", "url", "img", "document", "createElement", "fetchBlobCallback", "blob", "err", "console", "log", "isInstanceOf", "createObjectURL", "crossOrigin", "src", "onerror", "event", "onload", "hasMetaOption", "fetchBlob", "readFile", "e", "target", "result", "urlAPI", "URL", "revokeObjectURL", "webkitURL", "revokeHelper", "slice", "noRevoke", "meta", "type", "obj", "toString", "transform", "data", "originalWidth", "naturalWidth", "width", "originalHeight", "naturalHeight", "height", "method", "FileReader", "fileReader", "this", "hasblobSlice", "Blob", "webkitSlice", "mozSlice", "blobSlice", "apply", "arguments", "metaDataParsers", "jpeg", "parseMetaData", "that", "maxMetaDataSize", "DataView", "size", "error", "markerBytes", "<PERSON><PERSON><PERSON><PERSON>", "parsers", "buffer", "dataView", "offset", "maxOffset", "byteLength", "head<PERSON><PERSON><PERSON>", "getUint16", "disableMetaDataParsers", "length", "disableImageHead", "imageHead", "Uint8Array", "subarray", "replaceHead", "head", "originalTransform", "prefix", "cnt", "Math", "random", "logging", "setLogging", "_logging", "args", "cachedSetTimeout", "cachedClearTimeout", "process", "defaultSetTimout", "Error", "defaultClearTimeout", "runTimeout", "fun", "setTimeout", "clearTimeout", "currentQueue", "queue", "draining", "queueIndex", "cleanUpNextTick", "concat", "drainQueue", "timeout", "len", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "array", "noop", "nextTick", "Array", "push", "title", "browser", "env", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "dir", "umask", "numUrls", "base", "href", "getElementsByTagName", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "resolved", "a", "index", "<PERSON><PERSON><PERSON><PERSON>", "getId", "require", "jobCounter", "_id", "id", "action", "payload", "resolvePaths", "circularize", "createJob", "defaultOEM", "defaultOptions", "spawnWorker", "terminateWorker", "onMessage", "send", "workerCounter", "_options", "logger", "<PERSON><PERSON><PERSON><PERSON>", "resolves", "rejects", "worker", "setResolve", "res", "setReject", "rej", "startJob", "jobId", "Promise", "resolve", "reject", "workerId", "load", "writeText", "path", "text", "readText", "encoding", "removeFile", "FS", "loadLanguage", "langs", "initialize", "oem", "setParameters", "params", "recognize", "image", "opts", "getPDF", "textonly", "detect", "terminate", "status", "from", "keys", "userJobId", "TESSERACT_ONLY", "LSTM_ONLY", "TESSERACT_LSTM_COMBINED", "DEFAULT", "scale", "transformCoordinates", "getTransformedOptions", "newOptions", "aspectRatio", "crop", "max<PERSON><PERSON><PERSON>", "maxHeight", "renderImageToCanvas", "canvas", "sourceX", "sourceY", "sourceWidth", "sourceHeight", "destX", "destY", "destWidth", "destHeight", "ctx", "getContext", "imageSmoothingEnabled", "imageSmoothingQuality", "drawImage", "hasCanvasOption", "min<PERSON><PERSON><PERSON>", "minHeight", "pixelRatio", "downsamplingRatio", "tmp", "useCanvas", "scaleUp", "max", "scaleDown", "min", "left", "top", "undefined", "right", "bottom", "contain", "cover", "style", "ExifMap", "tagCode", "privateIFDs", "map", "tags", "Orientation", "<PERSON><PERSON><PERSON><PERSON>", "Exif", "GPSInfo", "Interoperability", "ExifTagTypes", "1", "getValue", "dataOffset", "getUint8", "2", "String", "fromCharCode", "ascii", "3", "littleEndian", "4", "getUint32", "5", "9", "getInt32", "10", "getExifValue", "tiffOffset", "tagSize", "values", "str", "tagType", "parseExifTags", "dirOffset", "tagOffsets", "includeTags", "excludeTags", "tagsNumber", "dirEndOffset", "tagOffset", "tagNumber", "tagValue", "parseExifData", "disableExif", "includeExifTags", "excludeExifTags", "exif", "disableExifOffsets", "exifOffsets", "exifTiffOffset", "exifLittleEndian", "disableExifThumbnail", "getExifThumbnail", "for<PERSON>ach", "parseExifPrivateIFD", "exifWriters", "setUint16", "writeExifData", "IptcMap", "getTagValue", "types", "outstr", "end", "getStringValue", "combineTagValues", "newValue", "parseIptcTags", "segmentOffset", "segmentLength", "segmentEnd", "getInt16", "iptc", "iptcOffsets", "isSegmentStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ObjectName", "0", "200", "201", "202", "parseIptcData", "disableIptc", "<PERSON><PERSON><PERSON><PERSON>", "disableIptcOffsets", "includeIptcTags", "excludeIptcTags", "createScheduler", "createWorker", "Tesseract", "languages", "OEM", "PSM", "runtime", "Op", "hasOwn", "$Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "context", "Context", "_invoke", "state", "arg", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "done", "makeInvokeMethod", "fn", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "Gp", "defineIteratorMethods", "AsyncIterator", "previousPromise", "callInvokeWithMethodAndArg", "invoke", "__await", "then", "unwrapped", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "constructor", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "iter", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "thrown", "<PERSON><PERSON><PERSON>", "regeneratorRuntime", "accidentalStrictMode", "Function", "webpackPolyfill", "deprecate", "paths", "children", "schedulerCounter", "workers", "runningWorkers", "jobQueue", "getNumWorkers", "dequeue", "wIds", "job", "w", "shift", "addWorker", "addJob", "wid", "getQueueLen", "resolveURL", "isElectron", "WorkerGlobalScope", "electron", "navigator", "userAgent", "indexOf", "page", "blocks", "paragraphs", "lines", "words", "symbols", "block", "paragraph", "line", "word", "sym", "dependencies", "worker<PERSON><PERSON>", "TESS_ENV", "corePath", "substring", "WebAssembly", "lang<PERSON><PERSON>", "workerBlobURL", "Worker", "handler", "onmessage", "packet", "postMessage", "blueimpLoadImage", "readFromBlobOrFile", "code", "readAsA<PERSON>y<PERSON><PERSON>er", "fixOrientationFromUrlOrBlobOrFile", "toBlob", "orientation", "endsWith", "fetch", "resp", "arrayBuffer", "test", "HTMLElement", "tagName", "poster", "File", "Request", "response", "catch", "XMLHttpRequest", "ProgressEvent", "req", "open", "headers", "setRequestHeader", "withCredentials", "credentials", "responseType", "<PERSON>ab<PERSON>", "ontimeout", "body", "ExifMapProto", "stringValues", "ExposureProgram", "6", "7", "8", "MeteringMode", "255", "LightSource", "11", "12", "13", "14", "15", "17", "18", "19", "20", "21", "22", "23", "24", "Flash", "SensingMethod", "SceneCaptureType", "SceneType", "CustomRendered", "WhiteBalance", "GainControl", "Contrast", "Saturation", "Sharpness", "SubjectDistanceRange", "FileSource", "ComponentsConfiguration", "getText", "getAll", "prop", "getName", "privateIFD", "subTags", "Number", "IptcMapProto", "25", "26", "27", "30", "35", "37", "38", "40", "42", "45", "47", "50", "55", "60", "62", "63", "65", "70", "75", "80", "85", "90", "92", "95", "100", "101", "103", "105", "110", "115", "116", "118", "120", "121", "122", "125", "130", "131", "135", "150", "151", "152", "153", "154", "184", "185", "186", "187", "188", "221", "225", "228", "230", "231", "232", "b", "L", "P", "S", "stringValue", "originalHasCanvasOption", "originalHasMetaOption", "originalTransformCoordinates", "originalGetTransformedOptions", "styleWidth", "styleHeight", "translate", "rotate", "PI", "finally", "AFR", "AMH", "ARA", "ASM", "AZE", "AZE_CYRL", "BEL", "BEN", "BOD", "BOS", "BUL", "CAT", "CEB", "CES", "CHI_SIM", "CHI_TRA", "CHR", "CYM", "DAN", "DEU", "DZO", "ELL", "ENG", "ENM", "EPO", "EST", "EUS", "FAS", "FIN", "FRA", "FRK", "FRM", "GLE", "GLG", "GRC", "GUJ", "HAT", "HEB", "HIN", "HRV", "HUN", "IKU", "IND", "ISL", "ITA", "ITA_OLD", "JAV", "JPN", "KAN", "KAT", "KAT_OLD", "KAZ", "KHM", "KIR", "KOR", "KUR", "LAO", "LAT", "LAV", "LIT", "MAL", "MAR", "MKD", "MLT", "MSA", "MYA", "NEP", "NLD", "NOR", "ORI", "PAN", "POL", "POR", "PUS", "RON", "RUS", "SAN", "SIN", "SLK", "SLV", "SPA", "SPA_OLD", "SQI", "SRP", "SRP_LATN", "SWA", "SWE", "SYR", "TAM", "TEL", "TGK", "TGL", "THA", "TIR", "TUR", "UIG", "UKR", "URD", "UZB", "UZB_CYRL", "VIE", "YID", "OSD_ONLY", "AUTO_OSD", "AUTO_ONLY", "AUTO", "SINGLE_COLUMN", "SINGLE_BLOCK_VERT_TEXT", "SINGLE_BLOCK", "SINGLE_LINE", "SINGLE_WORD", "CIRCLE_WORD", "SINGLE_CHAR", "SPARSE_TEXT", "SPARSE_TEXT_OSD"], "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAmB,UAAID,IAEvBD,EAAgB,UAAIC,IARtB,CASGK,QAAQ,WACX,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,I,yBCrEpD,SAAWC,GACV,aAYA,SAASC,EAAUC,EAAMC,EAAUC,GACjC,IACIC,EADAC,EAAMC,SAASC,cAAc,OAQjC,SAASC,EAAkBC,EAAMC,GAC3BA,GAAKC,QAAQC,IAAIF,GACjBD,GAAQT,EAAUa,aAAa,OAAQJ,IAEzCR,EAAOQ,EACPL,EAAMJ,EAAUc,gBAAgBb,KAEhCG,EAAMH,EACFE,GAAWA,EAAQY,cACrBV,EAAIU,YAAcZ,EAAQY,cAG9BV,EAAIW,IAAMZ,EAQZ,OANAC,EAAIY,QAAU,SAAUC,GACtB,OAAOlB,EAAUiB,QAAQZ,EAAKa,EAAOjB,EAAMG,EAAKF,EAAUC,IAE5DE,EAAIc,OAAS,SAAUD,GACrB,OAAOlB,EAAUmB,OAAOd,EAAKa,EAAOjB,EAAMG,EAAKF,EAAUC,IAEvC,iBAATF,GACLD,EAAUoB,cAAcjB,GAC1BH,EAAUqB,UAAUpB,EAAMO,EAAmBL,GAE7CK,IAEKH,GAEPL,EAAUa,aAAa,OAAQZ,IAG/BD,EAAUa,aAAa,OAAQZ,IAE/BG,EAAMJ,EAAUc,gBAAgBb,KAE9BI,EAAIW,IAAMZ,EACHC,GAEFL,EAAUsB,SAASrB,GAAM,SAAUsB,GACxC,IAAIC,EAASD,EAAEC,OACXA,GAAUA,EAAOC,OACnBpB,EAAIW,IAAMQ,EAAOC,OACRvB,GACTA,EAASqB,WAhBR,EAuBT,IAAIG,EACD3B,EAAEe,iBAAmBf,GACrBA,EAAE4B,KAAOA,IAAIC,iBAAmBD,KAChC5B,EAAE8B,WAAaA,UAQlB,SAASC,EAAa1B,EAAKD,IACrBC,GAA2B,UAApBA,EAAI2B,MAAM,EAAG,IAAoB5B,GAAWA,EAAQ6B,UAC7DhC,EAAU4B,gBAAgBxB,GAM9BJ,EAAUoB,cAAgB,SAAUjB,GAClC,OAAOA,GAAWA,EAAQ8B,MAM5BjC,EAAUqB,UAAY,SAAUjB,EAAKF,GACnCA,KAGFF,EAAUa,aAAe,SAAUqB,EAAMC,GAEvC,OAAO1D,OAAOkB,UAAUyC,SAASlE,KAAKiE,KAAS,WAAaD,EAAO,KAGrElC,EAAUqC,UAAY,SAAUhC,EAAKF,EAASD,EAAUD,EAAMqC,GAC5DpC,EAASG,EAAKiC,IAGhBtC,EAAUiB,QAAU,SAAUZ,EAAKa,EAAOjB,EAAMG,EAAKF,EAAUC,GAC7D2B,EAAa1B,EAAKD,GACdD,GACFA,EAAShC,KAAKmC,EAAKa,IAIvBlB,EAAUmB,OAAS,SAAUd,EAAKa,EAAOjB,EAAMG,EAAKF,EAAUC,GAC5D2B,EAAa1B,EAAKD,GACdD,GACFF,EAAUqC,UAAUhC,EAAKF,EAASD,EAAUD,EAAM,CAChDsC,cAAelC,EAAImC,cAAgBnC,EAAIoC,MACvCC,eAAgBrC,EAAIsC,eAAiBtC,EAAIuC,UAK/C5C,EAAUc,gBAAkB,SAAUb,GACpC,QAAOyB,GAASA,EAAOZ,gBAAgBb,IAGzCD,EAAU4B,gBAAkB,SAAUxB,GACpC,QAAOsB,GAASA,EAAOE,gBAAgBxB,IAMzCJ,EAAUsB,SAAW,SAAUrB,EAAMC,EAAU2C,GAC7C,GAAI9C,EAAE+C,WAAY,CAChB,IAAIC,EAAa,IAAID,WAIrB,GAHAC,EAAW5B,OAAS4B,EAAW9B,QAAUf,EAGrC6C,EADJF,EAASA,GAAU,iBAGjB,OADAE,EAAWF,GAAQ5C,GACZ8C,EAGX,OAAO,QAID,KAANtF,aACE,OAAOuC,GADH,8BA1JT,CAkKqB,oBAAXrC,QAA0BA,QAAWqF,O,2BC9J/C,SAAW1F,GACV,aAGEG,EAAO,CAAC,WAAF,4BAOP,SAAUuC,GAGX,IAAIiD,EACc,oBAATC,OACNA,KAAKvD,UAAUoC,OACdmB,KAAKvD,UAAUwD,aACfD,KAAKvD,UAAUyD,UAEnBpD,EAAUqD,UACRJ,GACA,WACE,IAAIlB,EAAQiB,KAAKjB,OAASiB,KAAKG,aAAeH,KAAKI,SACnD,OAAOrB,EAAMuB,MAAMN,KAAMO,YAG7BvD,EAAUwD,gBAAkB,CAC1BC,KAAM,CACJ,MAAQ,GACR,MAAQ,KAWZzD,EAAU0D,cAAgB,SAAUzD,EAAMC,EAAUC,EAASmC,GAI3DA,EAAOA,GAAQ,GACf,IAAIqB,EAAOX,KAEPY,GALJzD,EAAUA,GAAW,IAKSyD,iBAAmB,UAE3B,oBAAbC,UACP5D,GACAA,EAAK6D,MAAQ,IACC,eAAd7D,EAAKiC,MACLlC,EAAUqD,YAITrD,EAAUsB,SACTtB,EAAUqD,UAAUnF,KAAK+B,EAAM,EAAG2D,IAClC,SAAUrC,GACR,GAAIA,EAAEC,OAAOuC,MAKX,OAFApD,QAAQC,IAAIW,EAAEC,OAAOuC,YACrB7D,EAASoC,GAOX,IAKI0B,EACAC,EACAC,EACAnG,EARAoG,EAAS5C,EAAEC,OAAOC,OAClB2C,EAAW,IAAIP,SAASM,GACxBE,EAAS,EACTC,EAAYF,EAASG,WAAa,EAClCC,EAAaH,EAMjB,GAA8B,QAA1BD,EAASK,UAAU,GAAe,CACpC,KAAOJ,EAASC,KACdN,EAAcI,EAASK,UAAUJ,KAKf,OAAUL,GAAe,OACzB,QAAhBA,IAPuB,CAcvB,GAAIK,GADJJ,EAAeG,EAASK,UAAUJ,EAAS,GAAK,GACpBD,EAASG,WAAY,CAE/C5D,QAAQC,IAAI,4CACZ,MAGF,IADAsD,EAAUlE,EAAUwD,gBAAgBC,KAAKO,MACzB7D,EAAQuE,uBACtB,IAAK3G,EAAI,EAAGA,EAAImG,EAAQS,OAAQ5G,GAAK,EACnCmG,EAAQnG,GAAGG,KACTyF,EACAS,EACAC,EACAJ,EACA3B,EACAnC,GAKNqE,EADAH,GAAUJ,GAUT9D,EAAQyE,kBAAoBJ,EAAa,IACxCL,EAAOpC,MACTO,EAAKuC,UAAYV,EAAOpC,MAAM,EAAGyC,GAIjClC,EAAKuC,UAAY,IAAIC,WAAWX,GAAQY,SAAS,EAAGP,SAKxD7D,QAAQC,IAAI,2CAEdV,EAASoC,KAEX,sBAGFpC,EAASoC,IAMbtC,EAAUgF,YAAc,SAAUvE,EAAMwE,EAAM/E,GAC5CF,EAAU0D,cACRjD,GACA,SAAU6B,GACRpC,EACE,IAAIgD,KACF,CAAC+B,EAAMjF,EAAUqD,UAAUnF,KAAKuC,EAAM6B,EAAKuC,UAAUN,aACrD,CAAErC,KAAM,kBAId,CAAE0B,gBAAiB,IAAKc,wBAAwB,KAIpD,IAAIQ,EAAoBlF,EAAUqC,UAClCrC,EAAUqC,UAAY,SAAUhC,EAAKF,EAASD,EAAUD,EAAMqC,GACxDtC,EAAUoB,cAAcjB,GAC1BH,EAAU0D,cACRzD,GACA,SAAUqC,GACR4C,EAAkBhH,KAAK8B,EAAWK,EAAKF,EAASD,EAAUD,EAAMqC,KAElEnC,EACAmC,GAGF4C,EAAkB5B,MAAMtD,EAAWuD,cA5K/B,+BAJT,I,cCjBD/F,EAAOD,QAAU,SAAC4H,EAAQC,GAAT,gBACZD,EADY,YACFC,EADE,YACKC,KAAKC,SAASlD,SAAS,IAAIL,MAAM,EAAG,M,yBCDtDwD,GAAU,EAEdhI,EAAQgI,QAAUA,EAElBhI,EAAQiI,WAAa,SAACC,GACpBF,EAAUE,GAGZlI,EAAQqD,IAAM,sCAAI8E,EAAJ,yBAAIA,EAAJ,uBAAcH,EAAU5E,QAAQC,IAAI0C,MAAM,EAAMoC,GAAQ,O,cCPtE,IAOIC,EACAC,EARAC,EAAUrI,EAAOD,QAAU,GAU/B,SAASuI,IACL,MAAM,IAAIC,MAAM,mCAEpB,SAASC,IACL,MAAM,IAAID,MAAM,qCAsBpB,SAASE,EAAWC,GAChB,GAAIP,IAAqBQ,WAErB,OAAOA,WAAWD,EAAK,GAG3B,IAAKP,IAAqBG,IAAqBH,IAAqBQ,WAEhE,OADAR,EAAmBQ,WACZA,WAAWD,EAAK,GAE3B,IAEI,OAAOP,EAAiBO,EAAK,GAC/B,MAAM3E,GACJ,IAEI,OAAOoE,EAAiBzH,KAAK,KAAMgI,EAAK,GAC1C,MAAM3E,GAEJ,OAAOoE,EAAiBzH,KAAK8E,KAAMkD,EAAK,MAvCnD,WACG,IAEQP,EADsB,mBAAfQ,WACYA,WAEAL,EAEzB,MAAOvE,GACLoE,EAAmBG,EAEvB,IAEQF,EADwB,mBAAjBQ,aACcA,aAEAJ,EAE3B,MAAOzE,GACLqE,EAAqBI,GAjB5B,GAwED,IAEIK,EAFAC,EAAQ,GACRC,GAAW,EAEXC,GAAc,EAElB,SAASC,IACAF,GAAaF,IAGlBE,GAAW,EACPF,EAAa1B,OACb2B,EAAQD,EAAaK,OAAOJ,GAE5BE,GAAc,EAEdF,EAAM3B,QACNgC,KAIR,SAASA,IACL,IAAIJ,EAAJ,CAGA,IAAIK,EAAUX,EAAWQ,GACzBF,GAAW,EAGX,IADA,IAAIM,EAAMP,EAAM3B,OACVkC,GAAK,CAGP,IAFAR,EAAeC,EACfA,EAAQ,KACCE,EAAaK,GACdR,GACAA,EAAaG,GAAYM,MAGjCN,GAAc,EACdK,EAAMP,EAAM3B,OAEhB0B,EAAe,KACfE,GAAW,EAnEf,SAAyBQ,GACrB,GAAInB,IAAuBQ,aAEvB,OAAOA,aAAaW,GAGxB,IAAKnB,IAAuBI,IAAwBJ,IAAuBQ,aAEvE,OADAR,EAAqBQ,aACdA,aAAaW,GAExB,IAEWnB,EAAmBmB,GAC5B,MAAOxF,GACL,IAEI,OAAOqE,EAAmB1H,KAAK,KAAM6I,GACvC,MAAOxF,GAGL,OAAOqE,EAAmB1H,KAAK8E,KAAM+D,KAgD7CC,CAAgBJ,IAiBpB,SAASK,EAAKf,EAAKgB,GACflE,KAAKkD,IAAMA,EACXlD,KAAKkE,MAAQA,EAYjB,SAASC,KA5BTtB,EAAQuB,SAAW,SAAUlB,GACzB,IAAIR,EAAO,IAAI2B,MAAM9D,UAAUoB,OAAS,GACxC,GAAIpB,UAAUoB,OAAS,EACnB,IAAK,IAAI5G,EAAI,EAAGA,EAAIwF,UAAUoB,OAAQ5G,IAClC2H,EAAK3H,EAAI,GAAKwF,UAAUxF,GAGhCuI,EAAMgB,KAAK,IAAIL,EAAKf,EAAKR,IACJ,IAAjBY,EAAM3B,QAAiB4B,GACvBN,EAAWU,IASnBM,EAAKtH,UAAUmH,IAAM,WACjB9D,KAAKkD,IAAI5C,MAAM,KAAMN,KAAKkE,QAE9BrB,EAAQ0B,MAAQ,UAChB1B,EAAQ2B,SAAU,EAClB3B,EAAQ4B,IAAM,GACd5B,EAAQ6B,KAAO,GACf7B,EAAQ8B,QAAU,GAClB9B,EAAQ+B,SAAW,GAInB/B,EAAQgC,GAAKV,EACbtB,EAAQiC,YAAcX,EACtBtB,EAAQkC,KAAOZ,EACftB,EAAQmC,IAAMb,EACdtB,EAAQoC,eAAiBd,EACzBtB,EAAQqC,mBAAqBf,EAC7BtB,EAAQsC,KAAOhB,EACftB,EAAQuC,gBAAkBjB,EAC1BtB,EAAQwC,oBAAsBlB,EAE9BtB,EAAQyC,UAAY,SAAUhK,GAAQ,MAAO,IAE7CuH,EAAQ0C,QAAU,SAAUjK,GACxB,MAAM,IAAIyH,MAAM,qCAGpBF,EAAQ2C,IAAM,WAAc,MAAO,KACnC3C,EAAQ4C,MAAQ,SAAUC,GACtB,MAAM,IAAI3C,MAAM,mCAEpBF,EAAQ8C,MAAQ,WAAa,OAAO,I,6BClL1B,0BAANlL,EAMI,WAiCN,OA/BA,WACE,IAAImL,EAAUrF,UAAUoB,OAExB,GAAgB,IAAZiE,EACF,MAAM,IAAI7C,MAAM,wDAGlB,IAAI8C,EAAOvI,SAASC,cAAc,QAGlC,GAFAsI,EAAKC,KAAOvF,UAAU,GAEN,IAAZqF,EACF,OAAOC,EAAKC,KAGd,IAAI7D,EAAO3E,SAASyI,qBAAqB,QAAQ,GACjD9D,EAAK+D,aAAaH,EAAM5D,EAAKgE,YAK7B,IAHA,IACIC,EADAC,EAAI7I,SAASC,cAAc,KAGtB6I,EAAQ,EAAGA,EAAQR,EAASQ,IACnCD,EAAEL,KAAOvF,UAAU6F,GACnBF,EAAWC,EAAEL,KACbD,EAAKC,KAAOI,EAKd,OAFAjE,EAAKoE,YAAYR,GAEVK,KApCD,mC,gBCLV,IAAMI,EAAQC,EAAQ,GAElBC,EAAa,EAEjBhM,EAAOD,QAAU,YAIX,IAHAkM,EAGA,EAHJC,GACAC,EAEI,EAFJA,OAEI,IADJC,eACI,MADM,GACN,EACAF,EAAKD,EAMT,YALkB,IAAPC,IACTA,EAAKJ,EAAM,MAAOE,GAClBA,GAAc,GAGT,CACLE,KACAC,SACAC,a,u3CClBJ,IAAMC,EAAeN,EAAQ,IACvBO,EAAcP,EAAQ,IACtBQ,EAAYR,EAAQ,GAClB3I,EAAQ2I,EAAQ,GAAhB3I,IACF0I,EAAQC,EAAQ,GACdS,EAAeT,EAAQ,IAAvBS,W,EAQJT,EAAQ,IANVU,E,EAAAA,eACAC,E,EAAAA,YACAC,E,EAAAA,gBACAC,E,EAAAA,UACApK,E,EAAAA,UACAqK,E,EAAAA,KAGEC,EAAgB,EAEpB9M,EAAOD,QAAU,WAAmB,IAAlBgN,EAAkB,uDAAP,GACrBb,EAAKJ,EAAM,SAAUgB,GADO,EAM9BT,EAAa,EAAD,GACXI,EADW,GAEXM,IALHC,EAHgC,EAGhCA,OACAC,EAJgC,EAIhCA,aACGtK,EAL6B,+BAU5BuK,EAAW,GACXC,EAAU,GACZC,EAASV,EAAY/J,GAEzBmK,GAAiB,EAEjB,IAAMO,EAAa,SAAClB,EAAQmB,GAC1BJ,EAASf,GAAUmB,GAGfC,EAAY,SAACpB,EAAQqB,GACzBL,EAAQhB,GAAUqB,GAGdC,EAAW,SAAC,GAAD,IAAOC,EAAP,EAAGxB,GAAWC,EAAd,EAAcA,OAAQC,EAAtB,EAAsBA,QAAtB,OACf,IAAIuB,SAAQ,SAACC,EAASC,GACpBzK,EAAI,IAAD,OAAK8I,EAAL,oBAAmBwB,EAAnB,oBAAoCvB,IACvCkB,EAAWlB,EAAQyB,GACnBL,EAAUpB,EAAQ0B,GAClBhB,EAAKO,EAAQ,CACXU,SAAU5B,EACVwB,QACAvB,SACAC,gBAKA2B,EAAO,SAACL,GAAD,OACXD,EAASlB,EAAU,CACjBL,GAAIwB,EAAOvB,OAAQ,OAAQC,QAAS,CAAEzJ,eAIpCqL,EAAY,SAACC,EAAMC,EAAMR,GAAb,OAChBD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,KACRC,QAAS,CAAE/G,OAAQ,YAAa6C,KAAM,CAAC+F,EAAMC,QAI3CC,EAAW,SAACF,EAAMP,GAAP,OACfD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,KACRC,QAAS,CAAE/G,OAAQ,WAAY6C,KAAM,CAAC+F,EAAM,CAAEG,SAAU,cAItDC,EAAa,SAACJ,EAAMP,GAAP,OACjBD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,KACRC,QAAS,CAAE/G,OAAQ,SAAU6C,KAAM,CAAC+F,QAIlCK,EAAK,SAACjJ,EAAQ6C,EAAMwF,GAAf,OACTD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,KACRC,QAAS,CAAE/G,SAAQ6C,YAIjBqG,EAAe,eAACC,EAAD,uDAAS,MAAOd,EAAhB,8CACnBD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,eACRC,QAAS,CAAEoC,QAAO7L,eAIhB8L,EAAa,eAACD,EAAD,uDAAS,MAAOE,EAAhB,uDAAsBlC,EAAYkB,EAAlC,8CACjBD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,aACRC,QAAS,CAAEoC,QAAOE,WAIhBC,EAAgB,eAACC,EAAD,uDAAU,GAAIlB,EAAd,8CACpBD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,gBACRC,QAAS,CAAEwC,cAITC,EAAS,4CAAG,WAAOC,GAAP,2GAAcC,EAAd,+BAAqB,GAAIrB,EAAzB,4BAChBD,EADgB,KACPlB,EADO,KAEVmB,EAFU,SAIUlL,EAAUsM,GAJpB,+BAIqCC,EAJrC,MAIHD,MAJG,KAI4BnM,QAJ5B,YAEduJ,GAFc,KAGdC,OAAQ,YACRC,QAJc,wGAAH,sDAQT4C,EAAS,eAACjF,EAAD,uDAAS,uBAAwBkF,EAAjC,wDAAmDvB,EAAnD,8CACbD,EAASlB,EAAU,CACjBL,GAAIwB,EACJvB,OAAQ,SACRC,QAAS,CAAErC,QAAOkF,gBAIhBC,EAAM,4CAAG,WAAOJ,EAAOpB,GAAd,4FACbD,EADa,KACJlB,EADI,KAEPmB,EAFO,SAIalL,EAAUsM,GAJvB,gCAIAA,MAJA,YAEX5C,GAFW,KAGXC,OAAQ,SACRC,QAJW,wGAAH,wDAQN+C,EAAS,4CAAG,oGACD,OAAX/B,IAOFT,EAAgBS,GAChBA,EAAS,MATK,kBAWTO,QAAQC,WAXC,2CAAH,qDAsCf,OAxBAhB,EAAUQ,GAAQ,YAEZ,IADJU,EACI,EADJA,SAAUJ,EACN,EADMA,MAAO0B,EACb,EADaA,OAAQjD,EACrB,EADqBA,OAAQrH,EAC7B,EAD6BA,KAEjC,GAAe,YAAXsK,EAAsB,CACxBhM,EAAI,IAAD,OAAK0K,EAAL,uBAA4BJ,IAC/B,IAAI7M,EAAIiE,EACO,cAAXqH,EACFtL,EAAIyL,EAAYxH,GACI,WAAXqH,IACTtL,EAAIgJ,MAAMwF,KAAN,KAAgBvK,EAAhB,CAAsBqC,OAAQlG,OAAOqO,KAAKxK,GAAMqC,WAEtD+F,EAASf,GAAQ,CAAEuB,QAAO5I,KAAMjE,SAC3B,GAAe,WAAXuO,EAAqB,CAE9B,GADAjC,EAAQhB,GAAQrH,IACZmI,EAGF,MAAM1E,MAAMzD,GAFZmI,EAAanI,OAIK,aAAXsK,GACTpC,EAAO,EAAD,GAAMlI,EAAN,CAAYyK,UAAW7B,QAI1B,CACLxB,KACAkB,SACAC,aACAE,YACAQ,OACAC,YACAG,WACAE,aACAC,KACAC,eACAE,aACAE,gBACAE,YACAG,SACAE,SACAC,e,cC7LJnP,EAAOD,QAAU,CACfyP,eAAgB,EAChBC,UAAW,EACXC,wBAAyB,EACzBC,QAAS,I,2BCGV,SAAW7P,GACV,aAGEG,EAAO,CAAC,WAAF,4BAOP,SAAUuC,GAGX,IAAIkF,EAAoBlF,EAAUqC,UAElCrC,EAAUqC,UAAY,SAAUhC,EAAKF,EAASD,EAAUD,EAAMqC,GAC5D4C,EAAkBhH,KAChB8B,EACAA,EAAUoN,MAAM/M,EAAKF,EAASmC,GAC9BnC,EACAD,EACAD,EACAqC,IAOJtC,EAAUqN,qBAAuB,aAKjCrN,EAAUsN,sBAAwB,SAAUjN,EAAKF,GAC/C,IACIoN,EACAxP,EACA0E,EACAG,EAJA4K,EAAcrN,EAAQqN,YAK1B,IAAKA,EACH,OAAOrN,EAGT,IAAKpC,KADLwP,EAAa,GACHpN,EACJ1B,OAAOkB,UAAUC,eAAe1B,KAAKiC,EAASpC,KAChDwP,EAAWxP,GAAKoC,EAAQpC,IAa5B,OAVAwP,EAAWE,MAAO,GAClBhL,EAAQpC,EAAImC,cAAgBnC,EAAIoC,QAChCG,EAASvC,EAAIsC,eAAiBtC,EAAIuC,QACb4K,GACnBD,EAAWG,SAAW9K,EAAS4K,EAC/BD,EAAWI,UAAY/K,IAEvB2K,EAAWG,SAAWjL,EACtB8K,EAAWI,UAAYlL,EAAQ+K,GAE1BD,GAITvN,EAAU4N,oBAAsB,SAC9BC,EACAxN,EACAyN,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAlO,GAEA,IAAImO,EAAMT,EAAOU,WAAW,MAiB5B,OAhBsC,IAAlCpO,EAAQqO,sBACVF,EAAIE,uBAAwB,EACnBrO,EAAQsO,wBACjBH,EAAIG,sBAAwBtO,EAAQsO,uBAEtCH,EAAII,UACFrO,EACAyN,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEKR,GAIT7N,EAAU2O,gBAAkB,SAAUxO,GACpC,OAAOA,EAAQ0N,QAAU1N,EAAQsN,QAAUtN,EAAQqN,aAQrDxN,EAAUoN,MAAQ,SAAU/M,EAAKF,EAASmC,GAExCnC,EAAUA,GAAW,GACrB,IAQIuN,EACAC,EACAiB,EACAC,EACAb,EACAC,EACAH,EACAC,EACAe,EACAC,EACAC,EAlBAnB,EAASvN,SAASC,cAAc,UAChC0O,EACF5O,EAAIkO,YACHvO,EAAU2O,gBAAgBxO,IAAY0N,EAAOU,WAC5C9L,EAAQpC,EAAImC,cAAgBnC,EAAIoC,MAChCG,EAASvC,EAAIsC,eAAiBtC,EAAIuC,OAClCwL,EAAY3L,EACZ4L,EAAazL,EAejB,SAASsM,IACP,IAAI9B,EAAQ/H,KAAK8J,KACdP,GAAYR,GAAaA,GACzBS,GAAaR,GAAcA,GAE1BjB,EAAQ,IACVgB,GAAahB,EACbiB,GAAcjB,GAMlB,SAASgC,IACP,IAAIhC,EAAQ/H,KAAKgK,KACd3B,GAAYU,GAAaA,GACzBT,GAAaU,GAAcA,GAE1BjB,EAAQ,IACVgB,GAAahB,EACbiB,GAAcjB,GA2DlB,GAxDI6B,IAGFnB,GADA3N,EAAUH,EAAUsN,sBAAsBjN,EAAKF,EAASmC,IACtCgN,MAAQ,EAC1BvB,EAAU5N,EAAQoP,KAAO,EACrBpP,EAAQ6N,aACVA,EAAc7N,EAAQ6N,iBACAwB,IAAlBrP,EAAQsP,YAAwCD,IAAjBrP,EAAQmP,OACzCxB,EAAUrL,EAAQuL,EAAc7N,EAAQsP,QAG1CzB,EAAcvL,EAAQqL,GAAW3N,EAAQsP,OAAS,GAEhDtP,EAAQ8N,cACVA,EAAe9N,EAAQ8N,kBACAuB,IAAnBrP,EAAQuP,aAAwCF,IAAhBrP,EAAQoP,MAC1CxB,EAAUnL,EAASqL,EAAe9N,EAAQuP,SAG5CzB,EAAerL,EAASmL,GAAW5N,EAAQuP,QAAU,GAEvDtB,EAAYJ,EACZK,EAAaJ,GAEfP,EAAWvN,EAAQuN,SACnBC,EAAYxN,EAAQwN,UACpBiB,EAAWzO,EAAQyO,SACnBC,EAAY1O,EAAQ0O,UAChBI,GAAavB,GAAYC,GAAaxN,EAAQsN,MAChDW,EAAYV,EACZW,EAAaV,GACbqB,EAAMhB,EAAcC,EAAeP,EAAWC,GACpC,GACRM,EAAgBN,EAAYK,EAAeN,OACvB8B,IAAhBrP,EAAQoP,UAAwCC,IAAnBrP,EAAQuP,SACvC3B,GAAWnL,EAASqL,GAAgB,IAE7Be,EAAM,IACfhB,EAAeN,EAAWO,EAAgBN,OACrB6B,IAAjBrP,EAAQmP,WAAwCE,IAAlBrP,EAAQsP,QACxC3B,GAAWrL,EAAQuL,GAAe,OAIlC7N,EAAQwP,SAAWxP,EAAQyP,SAC7BhB,EAAWlB,EAAWA,GAAYkB,EAClCC,EAAYlB,EAAYA,GAAakB,GAEnC1O,EAAQyP,OACVR,IACAF,MAEAA,IACAE,MAGAH,EAAW,CAUb,IATAH,EAAa3O,EAAQ2O,YACJ,IACfjB,EAAOgC,MAAMpN,MAAQ2L,EAAY,KACjCP,EAAOgC,MAAMjN,OAASyL,EAAa,KACnCD,GAAaU,EACbT,GAAcS,EACdjB,EAAOU,WAAW,MAAMnB,MAAM0B,EAAYA,KAE5CC,EAAoB5O,EAAQ4O,mBAEN,GACpBA,EAAoB,GACpBX,EAAYJ,GACZK,EAAaJ,EAEb,KAAOD,EAAce,EAAoBX,GACvCP,EAAOpL,MAAQuL,EAAce,EAC7BlB,EAAOjL,OAASqL,EAAec,EAC/B/O,EAAU4N,oBACRC,EACAxN,EACAyN,EACAC,EACAC,EACAC,EACA,EACA,EACAJ,EAAOpL,MACPoL,EAAOjL,OACPzC,GAEF2N,EAAU,EACVC,EAAU,EACVC,EAAcH,EAAOpL,MACrBwL,EAAeJ,EAAOjL,QAEtBvC,EAAMC,SAASC,cAAc,WACzBkC,MAAQuL,EACZ3N,EAAIuC,OAASqL,EACbjO,EAAU4N,oBACRvN,EACAwN,EACA,EACA,EACAG,EACAC,EACA,EACA,EACAD,EACAC,EACA9N,GAON,OAHA0N,EAAOpL,MAAQ2L,EACfP,EAAOjL,OAASyL,EAChBrO,EAAUqN,qBAAqBQ,EAAQ1N,GAChCH,EAAU4N,oBACfC,EACAxN,EACAyN,EACAC,EACAC,EACAC,EACA,EACA,EACAG,EACAC,EACAlO,GAKJ,OAFAE,EAAIoC,MAAQ2L,EACZ/N,EAAIuC,OAASyL,EACNhO,KAzRD,+BAJT,I,2BCEA,SAAW/C,GACV,aAGEG,EAAO,CAAC,KAAgB,WAAlB,4BAOP,SAAUuC,GAUX,SAAS8P,EAAQC,GACXA,IACFtR,OAAOC,eAAesE,KAAM,MAAO,CACjChE,MAAOgE,KAAKgN,YAAYD,GAASE,MAEnCxR,OAAOC,eAAesE,KAAM,OAAQ,CAClChE,MAAQgE,KAAKkN,MAAQlN,KAAKkN,KAAKH,IAAa,MAKlDD,EAAQnQ,UAAUsQ,IAAM,CACtBE,YAAa,IACbC,UAAW,IACXC,KAAM,MACNC,QAAS,MACTC,iBAAkB,OAGpBT,EAAQnQ,UAAUqQ,YAAc,CAC9B,MAAQ,CAAE1R,KAAM,OAAQ2R,IAAK,IAC7B,MAAQ,CAAE3R,KAAM,UAAW2R,IAAK,IAChC,MAAQ,CAAE3R,KAAM,mBAAoB2R,IAAK,KAS3CH,EAAQnQ,UAAUf,IAAM,SAAU8K,GAChC,OAAO1G,KAAK0G,IAAO1G,KAAKA,KAAKiN,IAAIvG,KAqBnC,IAAI8G,EAAe,CAEjBC,EAAG,CACDC,SAAU,SAAUtM,EAAUuM,GAC5B,OAAOvM,EAASwM,SAASD,IAE3B7M,KAAM,GAGR+M,EAAG,CACDH,SAAU,SAAUtM,EAAUuM,GAC5B,OAAOG,OAAOC,aAAa3M,EAASwM,SAASD,KAE/C7M,KAAM,EACNkN,OAAO,GAGTC,EAAG,CACDP,SAAU,SAAUtM,EAAUuM,EAAYO,GACxC,OAAO9M,EAASK,UAAUkM,EAAYO,IAExCpN,KAAM,GAGRqN,EAAG,CACDT,SAAU,SAAUtM,EAAUuM,EAAYO,GACxC,OAAO9M,EAASgN,UAAUT,EAAYO,IAExCpN,KAAM,GAGRuN,EAAG,CACDX,SAAU,SAAUtM,EAAUuM,EAAYO,GACxC,OACE9M,EAASgN,UAAUT,EAAYO,GAC/B9M,EAASgN,UAAUT,EAAa,EAAGO,IAGvCpN,KAAM,GAGRwN,EAAG,CACDZ,SAAU,SAAUtM,EAAUuM,EAAYO,GACxC,OAAO9M,EAASmN,SAASZ,EAAYO,IAEvCpN,KAAM,GAGR0N,GAAI,CACFd,SAAU,SAAUtM,EAAUuM,EAAYO,GACxC,OACE9M,EAASmN,SAASZ,EAAYO,GAC9B9M,EAASmN,SAASZ,EAAa,EAAGO,IAGtCpN,KAAM,IAiBV,SAAS2N,EACPrN,EACAsN,EACArN,EACAnC,EACAyC,EACAuM,GAEA,IACIS,EACAhB,EACAiB,EACA7T,EACA8T,EACAzT,EANA0T,EAAUtB,EAAatO,GAO3B,GAAK4P,EAAL,CAWA,MAJAnB,GAHAgB,EAAUG,EAAQhO,KAAOa,GAIb,EACN+M,EAAatN,EAASgN,UAAU/M,EAAS,EAAG6M,GAC5C7M,EAAS,GACEsN,EAAUvN,EAASG,YAApC,CAIA,GAAe,IAAXI,EACF,OAAOmN,EAAQpB,SAAStM,EAAUuM,EAAYO,GAGhD,IADAU,EAAS,GACJ7T,EAAI,EAAGA,EAAI4G,EAAQ5G,GAAK,EAC3B6T,EAAO7T,GAAK+T,EAAQpB,SAClBtM,EACAuM,EAAa5S,EAAI+T,EAAQhO,KACzBoN,GAGJ,GAAIY,EAAQd,MAAO,CAGjB,IAFAa,EAAM,GAED9T,EAAI,EAAGA,EAAI6T,EAAOjN,QAGX,QAFVvG,EAAIwT,EAAO7T,IADkBA,GAAK,EAMlC8T,GAAOzT,EAET,OAAOyT,EAET,OAAOD,EA3BLjR,QAAQC,IAAI,gDAXZD,QAAQC,IAAI,wCAsDhB,SAASmR,EACP3N,EACAsN,EACAM,EACAd,EACAhB,EACA+B,EACAC,EACAC,GAEA,IAAIC,EAAYC,EAActU,EAAGuU,EAAWC,EAAWC,EACvD,GAAIR,EAAY,EAAI5N,EAASG,WAC3B5D,QAAQC,IAAI,oDADd,CAMA,MADAyR,EAAeL,EAAY,EAAI,IAD/BI,EAAahO,EAASK,UAAUuN,EAAWd,KAExB,EAAI9M,EAASG,YAAhC,CAIA,IAAKxG,EAAI,EAAGA,EAAIqU,EAAYrU,GAAK,EAC/BuU,EAAYN,EAAY,EAAI,GAAKjU,EACjCwU,EAAYnO,EAASK,UAAU6N,EAAWpB,GACtCgB,IAAgBA,EAAYK,IAC5BJ,IAA0C,IAA3BA,EAAYI,KAC/BC,EAAWf,EACTrN,EACAsN,EACAY,EACAlO,EAASK,UAAU6N,EAAY,EAAGpB,GAClC9M,EAASgN,UAAUkB,EAAY,EAAGpB,GAClCA,GAEFhB,EAAKqC,GAAaC,EACdP,IACFA,EAAWM,GAAaD,IAI5B,OAAOlO,EAASgN,UAAUiB,EAAcnB,GAtBtCvQ,QAAQC,IAAI,+CArGhB4P,EAAa,GAAKA,EAAa,GAqK/BxQ,EAAUyS,cAAgB,SAAUrO,EAAUC,EAAQM,EAAQrC,EAAMnC,GAClE,IAAIA,EAAQuS,YAAZ,CAGA,IAQIxB,EACAc,EATAE,EAAc/R,EAAQwS,gBACtBR,EAAchS,EAAQyS,iBAAmB,CAC3C,MAAQ,CAEN,OAAQ,IAGRlB,EAAarN,EAAS,GAK1B,GAAuC,aAAnCD,EAASgN,UAAU/M,EAAS,GAIhC,GAAIqN,EAAa,EAAItN,EAASG,WAC5B5D,QAAQC,IAAI,iDAId,GAAuC,IAAnCwD,EAASK,UAAUJ,EAAS,GAAhC,CAKA,OAAQD,EAASK,UAAUiN,IACzB,KAAK,MACHR,GAAe,EACf,MACF,KAAK,MACHA,GAAe,EACf,MACF,QAEE,YADAvQ,QAAQC,IAAI,qDAIyC,KAArDwD,EAASK,UAAUiN,EAAa,EAAGR,IAKvCc,EAAY5N,EAASgN,UAAUM,EAAa,EAAGR,GAE/C5O,EAAKuQ,KAAO,IAAI/C,EACX3P,EAAQ2S,qBACXxQ,EAAKyQ,YAAc,IAAIjD,EACvBxN,EAAK0Q,eAAiBtB,EACtBpP,EAAK2Q,iBAAmB/B,IAI1Bc,EAAYD,EACV3N,EACAsN,EACAA,EAAaM,EACbd,EACA5O,EAAKuQ,KACLvQ,EAAKyQ,YACLb,EACAC,MAEgBhS,EAAQ+S,uBACxBlB,EAAYD,EACV3N,EACAsN,EACAA,EAAaM,EACbd,EACA5O,EAAKuQ,KACLvQ,EAAKyQ,YACLb,EACAC,GAGE7P,EAAKuQ,KAAK,MAAWvQ,EAAKuQ,KAAK,OACjCvQ,EAAKuQ,KAAK,KA1ThB,SAA0BzO,EAAUC,EAAQM,GAC1C,GAAKA,KAAUN,EAASM,EAASP,EAASG,YAI1C,OAAO,IAAIrB,KAAK,CAACkB,EAASD,OAAOpC,MAAMsC,EAAQA,EAASM,IAAU,CAChEzC,KAAM,eAJNvB,QAAQC,IAAI,8CAwTUuS,CAClB/O,EACAsN,EAAapP,EAAKuQ,KAAK,KACvBvQ,EAAKuQ,KAAK,QAIFpU,OAAOqO,KAAKxK,EAAKuQ,KAAK7C,aACxBoD,SAAQ,SAAUrD,IApHhC,SACEzN,EACAyN,EACA3L,EACAsN,EACAR,EACAgB,EACAC,GAEA,IAAIH,EAAY1P,EAAKuQ,KAAK9C,GACtBiC,IACF1P,EAAKuQ,KAAK9C,GAAW,IAAID,EAAQC,GAC7BzN,EAAKyQ,cACPzQ,EAAKyQ,YAAYhD,GAAW,IAAID,EAAQC,IAE1CgC,EACE3N,EACAsN,EACAA,EAAaM,EACbd,EACA5O,EAAKuQ,KAAK9C,GACVzN,EAAKyQ,aAAezQ,EAAKyQ,YAAYhD,GACrCmC,GAAeA,EAAYnC,GAC3BoC,GAAeA,EAAYpC,KA8F7BsD,CACE/Q,EACAyN,EACA3L,EACAsN,EACAR,EACAgB,EACAC,OArDFxR,QAAQC,IAAI,gDAjBZD,QAAQC,IAAI,uDA4EhBZ,EAAUwD,gBAAgBC,KAAK,OAAQ6D,KAAKtH,EAAUyS,eAEtDzS,EAAUsT,YAAc,CAEtB,IAAQ,SAAUnP,EAAQ7B,EAAMtD,GAG9B,OAFW,IAAI6E,SAASM,EAAQ7B,EAAKyQ,YAAY,KAAU,EAAG,GACzDQ,UAAU,EAAGvU,EAAOsD,EAAK2Q,kBACvB9O,IAIXnE,EAAUwT,cAAgB,SAAUrP,EAAQ7B,EAAMoH,EAAI1K,GACpDgB,EAAUsT,YAAYhR,EAAKuQ,KAAK5C,IAAIvG,IAAKvF,EAAQ7B,EAAMtD,IAGzDgB,EAAU8P,QAAUA,IA3ZZ,+BAJT,I,2BCDA,SAAWxS,GACV,aAGEG,EAAO,CAAC,KAAgB,WAAlB,4BAOP,SAAUuC,GASX,SAASyT,KAkDT,SAASC,EAAY3D,EAASE,EAAK7L,EAAUC,EAAQM,GACnD,MAA2B,WAAvBsL,EAAI0D,MAAM5D,GACL,IAAI7M,KAAK,CAACkB,EAASD,OAAOpC,MAAMsC,EAAQA,EAASM,KAE/B,WAAvBsL,EAAI0D,MAAM5D,GACL3L,EAASK,UAAUJ,GAxB9B,SAAwBD,EAAUC,EAAQM,GAGxC,IAFA,IAAIiP,EAAS,GACTC,EAAMxP,EAASM,EACVnF,EAAI6E,EAAQ7E,EAAIqU,EAAKrU,GAAK,EACjCoU,GAAU9C,OAAOC,aAAa3M,EAASwM,SAASpR,IAElD,OAAOoU,EAoBAE,CAAe1P,EAAUC,EAAQM,GAU1C,SAASoP,EAAiB/U,EAAOgV,GAC/B,YAAcxE,IAAVxQ,EAA4BgV,EAC5BhV,aAAiBqI,OACnBrI,EAAMsI,KAAK0M,GACJhV,GAEF,CAACA,EAAOgV,GAajB,SAASC,EACP7P,EACA8P,EACAC,EACA7R,EACA4P,EACAC,GAKA,IAHA,IAAInT,EAAO2S,EAAS5B,EAChBqE,EAAaF,EAAgBC,EAC7B9P,EAAS6P,EACN7P,EAAS+P,GAEkB,KAA9BhQ,EAASwM,SAASvM,IACgB,IAAlCD,EAASwM,SAASvM,EAAS,KAE3B0L,EAAU3L,EAASwM,SAASvM,EAAS,GAEjC6N,IAAeA,EAAYnC,IAC3BoC,GAAgBA,EAAYpC,KAE9B4B,EAAUvN,EAASiQ,SAAShQ,EAAS,GACrCrF,EAAQ0U,EAAY3D,EAASzN,EAAKgS,KAAMlQ,EAAUC,EAAS,EAAGsN,GAC9DrP,EAAKgS,KAAKvE,GAAWgE,EAAiBzR,EAAKgS,KAAKvE,GAAU/Q,GACtDsD,EAAKiS,cACPjS,EAAKiS,YAAYxE,GAAW1L,KAIlCA,GAAU,EAWd,SAASmQ,EAAepQ,EAAUC,GAChC,OACiC,YAA/BD,EAASgN,UAAU/M,IACgB,OAAnCD,EAASK,UAAUJ,EAAS,GAWhC,SAASoQ,EAAgBrQ,EAAUC,GACjC,IAAIM,EAASP,EAASwM,SAASvM,EAAS,GAOxC,OANIM,EAAS,GAAM,IAAGA,GAAU,GAEjB,IAAXA,IAEFA,EAAS,GAEJA,EAlJT8O,EAAQ9T,UAAUsQ,IAAM,CACtByE,WAAY,GAGdjB,EAAQ9T,UAAUgU,MAAQ,CACxBgB,EAAG,SACHC,IAAK,SACLC,IAAK,SACLC,IAAK,UASPrB,EAAQ9T,UAAUf,IAAM,SAAU8K,GAChC,OAAO1G,KAAK0G,IAAO1G,KAAKA,KAAKiN,IAAIvG,KAmInC1J,EAAU+U,cAAgB,SAAU3Q,EAAUC,EAAQM,EAAQrC,EAAMnC,GAClE,IAAIA,EAAQ6U,YAIZ,IADA,IAAI/Q,EAAeI,EAASM,EACrBN,EAAS,EAAIJ,GAAc,CAChC,GAAIuQ,EAAepQ,EAAUC,GAAS,CACpC,IAAI4Q,EAAeR,EAAgBrQ,EAAUC,GACzC6P,EAAgB7P,EAAS,EAAI4Q,EACjC,GAAIf,EAAgBjQ,EAAc,CAEhCtD,QAAQC,IAAI,8CACZ,MAEF,IAAIuT,EAAgB/P,EAASK,UAAUJ,EAAS,EAAI4Q,GACpD,GAAI5Q,EAAS8P,EAAgBlQ,EAAc,CAEzCtD,QAAQC,IAAI,4CACZ,MAeF,OAZA0B,EAAKgS,KAAO,IAAIb,EACXtT,EAAQ+U,qBACX5S,EAAKiS,YAAc,IAAId,QAEzBQ,EACE7P,EACA8P,EACAC,EACA7R,EACAnC,EAAQgV,gBACRhV,EAAQiV,iBAAmB,CAAEN,KAAK,IAKtCzQ,GAAU,IAKdrE,EAAUwD,gBAAgBC,KAAK,OAAQ6D,KAAKtH,EAAU+U,eAEtD/U,EAAUyT,QAAUA,IAlNZ,+BAJT,I,sWCLDlK,EAAQ,IACR,IAAM8L,EAAkB9L,EAAQ,IAC1B+L,EAAe/L,EAAQ,GACvBgM,EAAYhM,EAAQ,IACpBiM,EAAYjM,EAAQ,IACpBkM,EAAMlM,EAAQ,GACdmM,EAAMnM,EAAQ,IACZ/D,EAAe+D,EAAQ,GAAvB/D,WAERhI,EAAOD,Q,+VAAP,EACEiY,YACAC,MACAC,MACAL,kBACAC,eACA9P,cACG+P,I,kQClBL,IAAII,EAAW,SAAUpY,GACvB,aAEA,IAAIqY,EAAKnX,OAAOkB,UACZkW,EAASD,EAAGhW,eAEZkW,EAA4B,mBAAXhX,OAAwBA,OAAS,GAClDiX,EAAiBD,EAAQE,UAAY,aACrCC,EAAsBH,EAAQI,eAAiB,kBAC/CC,EAAoBL,EAAQ/W,aAAe,gBAE/C,SAASqX,EAAKC,EAASC,EAASC,EAAMC,GAEpC,IAAIC,EAAiBH,GAAWA,EAAQ3W,qBAAqB+W,EAAYJ,EAAUI,EAC/EC,EAAYlY,OAAOY,OAAOoX,EAAe9W,WACzCiX,EAAU,IAAIC,EAAQL,GAAe,IAMzC,OAFAG,EAAUG,QAkMZ,SAA0BT,EAASE,EAAMK,GACvC,IAAIG,EA3KuB,iBA6K3B,OAAO,SAAgBlU,EAAQmU,GAC7B,GA5KoB,cA4KhBD,EACF,MAAM,IAAIhR,MAAM,gCAGlB,GA/KoB,cA+KhBgR,EAA6B,CAC/B,GAAe,UAAXlU,EACF,MAAMmU,EAKR,OAAOC,IAMT,IAHAL,EAAQ/T,OAASA,EACjB+T,EAAQI,IAAMA,IAED,CACX,IAAIE,EAAWN,EAAQM,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUN,GACnD,GAAIO,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,GAIX,GAAuB,SAAnBP,EAAQ/T,OAGV+T,EAAQU,KAAOV,EAAQW,MAAQX,EAAQI,SAElC,GAAuB,UAAnBJ,EAAQ/T,OAAoB,CACrC,GA/MqB,mBA+MjBkU,EAEF,MADAA,EA7Mc,YA8MRH,EAAQI,IAGhBJ,EAAQY,kBAAkBZ,EAAQI,SAEN,WAAnBJ,EAAQ/T,QACjB+T,EAAQa,OAAO,SAAUb,EAAQI,KAGnCD,EAxNkB,YA0NlB,IAAIW,EAASC,EAAStB,EAASE,EAAMK,GACrC,GAAoB,WAAhBc,EAAOxV,KAAmB,CAO5B,GAJA6U,EAAQH,EAAQgB,KA7NA,YAFK,iBAmOjBF,EAAOV,MAAQK,EACjB,SAGF,MAAO,CACLrY,MAAO0Y,EAAOV,IACdY,KAAMhB,EAAQgB,MAGS,UAAhBF,EAAOxV,OAChB6U,EA3OgB,YA8OhBH,EAAQ/T,OAAS,QACjB+T,EAAQI,IAAMU,EAAOV,OA1QPa,CAAiBxB,EAASE,EAAMK,GAE7CD,EAcT,SAASgB,EAASG,EAAI3V,EAAK6U,GACzB,IACE,MAAO,CAAE9U,KAAM,SAAU8U,IAAKc,EAAG5Z,KAAKiE,EAAK6U,IAC3C,MAAOtW,GACP,MAAO,CAAEwB,KAAM,QAAS8U,IAAKtW,IAhBjCnD,EAAQ6Y,KAAOA,EAoBf,IAOIiB,EAAmB,GAMvB,SAASX,KACT,SAASqB,KACT,SAASC,KAIT,IAAIC,EAAoB,GACxBA,EAAkBlC,GAAkB,WAClC,OAAO/S,MAGT,IAAIkV,EAAWzZ,OAAO0Z,eAClBC,EAA0BF,GAAYA,EAASA,EAAStG,EAAO,MAC/DwG,GACAA,IAA4BxC,GAC5BC,EAAO3X,KAAKka,EAAyBrC,KAGvCkC,EAAoBG,GAGtB,IAAIC,EAAKL,EAA2BrY,UAClC+W,EAAU/W,UAAYlB,OAAOY,OAAO4Y,GAQtC,SAASK,EAAsB3Y,GAC7B,CAAC,OAAQ,QAAS,UAAUyT,SAAQ,SAASvQ,GAC3ClD,EAAUkD,GAAU,SAASmU,GAC3B,OAAOhU,KAAK8T,QAAQjU,EAAQmU,OAoClC,SAASuB,EAAc5B,GAgCrB,IAAI6B,EAgCJxV,KAAK8T,QA9BL,SAAiBjU,EAAQmU,GACvB,SAASyB,IACP,OAAO,IAAItN,SAAQ,SAASC,EAASC,IAnCzC,SAASqN,EAAO7V,EAAQmU,EAAK5L,EAASC,GACpC,IAAIqM,EAASC,EAAShB,EAAU9T,GAAS8T,EAAWK,GACpD,GAAoB,UAAhBU,EAAOxV,KAEJ,CACL,IAAIT,EAASiW,EAAOV,IAChBhY,EAAQyC,EAAOzC,MACnB,OAAIA,GACiB,WAAjB,EAAOA,IACP6W,EAAO3X,KAAKc,EAAO,WACdmM,QAAQC,QAAQpM,EAAM2Z,SAASC,MAAK,SAAS5Z,GAClD0Z,EAAO,OAAQ1Z,EAAOoM,EAASC,MAC9B,SAAS3K,GACVgY,EAAO,QAAShY,EAAK0K,EAASC,MAI3BF,QAAQC,QAAQpM,GAAO4Z,MAAK,SAASC,GAI1CpX,EAAOzC,MAAQ6Z,EACfzN,EAAQ3J,MACP,SAASsC,GAGV,OAAO2U,EAAO,QAAS3U,EAAOqH,EAASC,MAvBzCA,EAAOqM,EAAOV,KAiCZ0B,CAAO7V,EAAQmU,EAAK5L,EAASC,MAIjC,OAAOmN,EAaLA,EAAkBA,EAAgBI,KAChCH,EAGAA,GACEA,KA+GV,SAASrB,EAAoBF,EAAUN,GACrC,IAAI/T,EAASqU,EAASlB,SAASY,EAAQ/T,QACvC,QApSE2M,IAoSE3M,EAAsB,CAKxB,GAFA+T,EAAQM,SAAW,KAEI,UAAnBN,EAAQ/T,OAAoB,CAE9B,GAAIqU,EAASlB,SAAT,SAGFY,EAAQ/T,OAAS,SACjB+T,EAAQI,SA/SZxH,EAgTI4H,EAAoBF,EAAUN,GAEP,UAAnBA,EAAQ/T,QAGV,OAAOwU,EAIXT,EAAQ/T,OAAS,QACjB+T,EAAQI,IAAM,IAAI8B,UAChB,kDAGJ,OAAOzB,EAGT,IAAIK,EAASC,EAAS9U,EAAQqU,EAASlB,SAAUY,EAAQI,KAEzD,GAAoB,UAAhBU,EAAOxV,KAIT,OAHA0U,EAAQ/T,OAAS,QACjB+T,EAAQI,IAAMU,EAAOV,IACrBJ,EAAQM,SAAW,KACZG,EAGT,IAAI0B,EAAOrB,EAAOV,IAElB,OAAM+B,EAOFA,EAAKnB,MAGPhB,EAAQM,EAAS8B,YAAcD,EAAK/Z,MAGpC4X,EAAQqC,KAAO/B,EAASgC,QAQD,WAAnBtC,EAAQ/T,SACV+T,EAAQ/T,OAAS,OACjB+T,EAAQI,SAnWVxH,GA6WFoH,EAAQM,SAAW,KACZG,GANE0B,GA3BPnC,EAAQ/T,OAAS,QACjB+T,EAAQI,IAAM,IAAI8B,UAAU,oCAC5BlC,EAAQM,SAAW,KACZG,GAoDX,SAAS8B,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxBpW,KAAK0W,WAAWpS,KAAK+R,GAGvB,SAASM,EAAcN,GACrB,IAAI3B,EAAS2B,EAAMO,YAAc,GACjClC,EAAOxV,KAAO,gBACPwV,EAAOV,IACdqC,EAAMO,WAAalC,EAGrB,SAASb,EAAQL,GAIfxT,KAAK0W,WAAa,CAAC,CAAEJ,OAAQ,SAC7B9C,EAAYpD,QAAQ+F,EAAcnW,MAClCA,KAAK6W,OAAM,GA8Bb,SAASjI,EAAOkI,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAAS/D,GAC9B,GAAIgE,EACF,OAAOA,EAAe7b,KAAK4b,GAG7B,GAA6B,mBAAlBA,EAASb,KAClB,OAAOa,EAGT,IAAKE,MAAMF,EAASnV,QAAS,CAC3B,IAAI5G,GAAK,EAAGkb,EAAO,SAASA,IAC1B,OAASlb,EAAI+b,EAASnV,QACpB,GAAIkR,EAAO3X,KAAK4b,EAAU/b,GAGxB,OAFAkb,EAAKja,MAAQ8a,EAAS/b,GACtBkb,EAAKrB,MAAO,EACLqB,EAOX,OAHAA,EAAKja,WAndTwQ,EAodIyJ,EAAKrB,MAAO,EAELqB,GAGT,OAAOA,EAAKA,KAAOA,GAKvB,MAAO,CAAEA,KAAMhC,GAIjB,SAASA,IACP,MAAO,CAAEjY,WAnePwQ,EAmeyBoI,MAAM,GA+MnC,OAxmBAG,EAAkBpY,UAAY0Y,EAAG4B,YAAcjC,EAC/CA,EAA2BiC,YAAclC,EACzCC,EAA2B7B,GACzB4B,EAAkBmC,YAAc,oBAYlC3c,EAAQ4c,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOH,YAClD,QAAOI,IACHA,IAAStC,GAG2B,uBAAnCsC,EAAKH,aAAeG,EAAK/b,QAIhCf,EAAQ+c,KAAO,SAASF,GAUtB,OATI3b,OAAO8b,eACT9b,OAAO8b,eAAeH,EAAQpC,IAE9BoC,EAAOI,UAAYxC,EACb7B,KAAqBiE,IACzBA,EAAOjE,GAAqB,sBAGhCiE,EAAOza,UAAYlB,OAAOY,OAAOgZ,GAC1B+B,GAOT7c,EAAQkd,MAAQ,SAASzD,GACvB,MAAO,CAAE2B,QAAS3B,IAsEpBsB,EAAsBC,EAAc5Y,WACpC4Y,EAAc5Y,UAAUsW,GAAuB,WAC7C,OAAOjT,MAETzF,EAAQgb,cAAgBA,EAKxBhb,EAAQmd,MAAQ,SAASrE,EAASC,EAASC,EAAMC,GAC/C,IAAImE,EAAO,IAAIpC,EACbnC,EAAKC,EAASC,EAASC,EAAMC,IAG/B,OAAOjZ,EAAQ4c,oBAAoB7D,GAC/BqE,EACAA,EAAK1B,OAAOL,MAAK,SAASnX,GACxB,OAAOA,EAAOmW,KAAOnW,EAAOzC,MAAQ2b,EAAK1B,WAuKjDX,EAAsBD,GAEtBA,EAAGlC,GAAqB,YAOxBkC,EAAGtC,GAAkB,WACnB,OAAO/S,MAGTqV,EAAGjW,SAAW,WACZ,MAAO,sBAkCT7E,EAAQuP,KAAO,SAASrN,GACtB,IAAIqN,EAAO,GACX,IAAK,IAAIxN,KAAOG,EACdqN,EAAKxF,KAAKhI,GAMZ,OAJAwN,EAAK8N,UAIE,SAAS3B,IACd,KAAOnM,EAAKnI,QAAQ,CAClB,IAAIrF,EAAMwN,EAAK+N,MACf,GAAIvb,KAAOG,EAGT,OAFAwZ,EAAKja,MAAQM,EACb2Z,EAAKrB,MAAO,EACLqB,EAQX,OADAA,EAAKrB,MAAO,EACLqB,IAsCX1b,EAAQqU,OAASA,EAMjBiF,EAAQlX,UAAY,CAClBsa,YAAapD,EAEbgD,MAAO,SAASiB,GAcd,GAbA9X,KAAK+X,KAAO,EACZ/X,KAAKiW,KAAO,EAGZjW,KAAKsU,KAAOtU,KAAKuU,WA9ejB/H,EA+eAxM,KAAK4U,MAAO,EACZ5U,KAAKkU,SAAW,KAEhBlU,KAAKH,OAAS,OACdG,KAAKgU,SAnfLxH,EAqfAxM,KAAK0W,WAAWtG,QAAQuG,IAEnBmB,EACH,IAAK,IAAIxc,KAAQ0E,KAEQ,MAAnB1E,EAAK0c,OAAO,IACZnF,EAAO3X,KAAK8E,KAAM1E,KACjB0b,OAAO1b,EAAKyD,MAAM,MACrBiB,KAAK1E,QA7fXkR,IAmgBFyL,KAAM,WACJjY,KAAK4U,MAAO,EAEZ,IACIsD,EADYlY,KAAK0W,WAAW,GACLE,WAC3B,GAAwB,UAApBsB,EAAWhZ,KACb,MAAMgZ,EAAWlE,IAGnB,OAAOhU,KAAKmY,MAGd3D,kBAAmB,SAAS4D,GAC1B,GAAIpY,KAAK4U,KACP,MAAMwD,EAGR,IAAIxE,EAAU5T,KACd,SAASqY,EAAOC,EAAKC,GAYnB,OAXA7D,EAAOxV,KAAO,QACdwV,EAAOV,IAAMoE,EACbxE,EAAQqC,KAAOqC,EAEXC,IAGF3E,EAAQ/T,OAAS,OACjB+T,EAAQI,SA9hBZxH,KAiiBY+L,EAGZ,IAAK,IAAIxd,EAAIiF,KAAK0W,WAAW/U,OAAS,EAAG5G,GAAK,IAAKA,EAAG,CACpD,IAAIsb,EAAQrW,KAAK0W,WAAW3b,GACxB2Z,EAAS2B,EAAMO,WAEnB,GAAqB,SAAjBP,EAAMC,OAIR,OAAO+B,EAAO,OAGhB,GAAIhC,EAAMC,QAAUtW,KAAK+X,KAAM,CAC7B,IAAIS,EAAW3F,EAAO3X,KAAKmb,EAAO,YAC9BoC,EAAa5F,EAAO3X,KAAKmb,EAAO,cAEpC,GAAImC,GAAYC,EAAY,CAC1B,GAAIzY,KAAK+X,KAAO1B,EAAME,SACpB,OAAO8B,EAAOhC,EAAME,UAAU,GACzB,GAAIvW,KAAK+X,KAAO1B,EAAMG,WAC3B,OAAO6B,EAAOhC,EAAMG,iBAGjB,GAAIgC,GACT,GAAIxY,KAAK+X,KAAO1B,EAAME,SACpB,OAAO8B,EAAOhC,EAAME,UAAU,OAG3B,KAAIkC,EAMT,MAAM,IAAI1V,MAAM,0CALhB,GAAI/C,KAAK+X,KAAO1B,EAAMG,WACpB,OAAO6B,EAAOhC,EAAMG,gBAU9B/B,OAAQ,SAASvV,EAAM8U,GACrB,IAAK,IAAIjZ,EAAIiF,KAAK0W,WAAW/U,OAAS,EAAG5G,GAAK,IAAKA,EAAG,CACpD,IAAIsb,EAAQrW,KAAK0W,WAAW3b,GAC5B,GAAIsb,EAAMC,QAAUtW,KAAK+X,MACrBlF,EAAO3X,KAAKmb,EAAO,eACnBrW,KAAK+X,KAAO1B,EAAMG,WAAY,CAChC,IAAIkC,EAAerC,EACnB,OAIAqC,IACU,UAATxZ,GACS,aAATA,IACDwZ,EAAapC,QAAUtC,GACvBA,GAAO0E,EAAalC,aAGtBkC,EAAe,MAGjB,IAAIhE,EAASgE,EAAeA,EAAa9B,WAAa,GAItD,OAHAlC,EAAOxV,KAAOA,EACdwV,EAAOV,IAAMA,EAET0E,GACF1Y,KAAKH,OAAS,OACdG,KAAKiW,KAAOyC,EAAalC,WAClBnC,GAGFrU,KAAK2Y,SAASjE,IAGvBiE,SAAU,SAASjE,EAAQ+B,GACzB,GAAoB,UAAhB/B,EAAOxV,KACT,MAAMwV,EAAOV,IAcf,MAXoB,UAAhBU,EAAOxV,MACS,aAAhBwV,EAAOxV,KACTc,KAAKiW,KAAOvB,EAAOV,IACM,WAAhBU,EAAOxV,MAChBc,KAAKmY,KAAOnY,KAAKgU,IAAMU,EAAOV,IAC9BhU,KAAKH,OAAS,SACdG,KAAKiW,KAAO,OACa,WAAhBvB,EAAOxV,MAAqBuX,IACrCzW,KAAKiW,KAAOQ,GAGPpC,GAGTuE,OAAQ,SAASpC,GACf,IAAK,IAAIzb,EAAIiF,KAAK0W,WAAW/U,OAAS,EAAG5G,GAAK,IAAKA,EAAG,CACpD,IAAIsb,EAAQrW,KAAK0W,WAAW3b,GAC5B,GAAIsb,EAAMG,aAAeA,EAGvB,OAFAxW,KAAK2Y,SAAStC,EAAMO,WAAYP,EAAMI,UACtCE,EAAcN,GACPhC,IAKb,MAAS,SAASiC,GAChB,IAAK,IAAIvb,EAAIiF,KAAK0W,WAAW/U,OAAS,EAAG5G,GAAK,IAAKA,EAAG,CACpD,IAAIsb,EAAQrW,KAAK0W,WAAW3b,GAC5B,GAAIsb,EAAMC,SAAWA,EAAQ,CAC3B,IAAI5B,EAAS2B,EAAMO,WACnB,GAAoB,UAAhBlC,EAAOxV,KAAkB,CAC3B,IAAI2Z,EAASnE,EAAOV,IACpB2C,EAAcN,GAEhB,OAAOwC,GAMX,MAAM,IAAI9V,MAAM,0BAGlB+V,cAAe,SAAShC,EAAUd,EAAYE,GAa5C,OAZAlW,KAAKkU,SAAW,CACdlB,SAAUpE,EAAOkI,GACjBd,WAAYA,EACZE,QAASA,GAGS,SAAhBlW,KAAKH,SAGPG,KAAKgU,SAvqBPxH,GA0qBO6H,IAQJ9Z,EAvrBM,CA8rBK,WAAlB,EAAOC,GAAsBA,EAAOD,QAAU,IAGhD,IACEwe,mBAAqBpG,EACrB,MAAOqG,GAUPC,SAAS,IAAK,yBAAdA,CAAwCtG,M,mCCptB1CnY,EAAOD,QAAU,SAASC,GAoBzB,OAnBKA,EAAO0e,kBACX1e,EAAO2e,UAAY,aACnB3e,EAAO4e,MAAQ,GAEV5e,EAAO6e,WAAU7e,EAAO6e,SAAW,IACxC5d,OAAOC,eAAelB,EAAQ,SAAU,CACvCmB,YAAY,EACZC,IAAK,WACJ,OAAOpB,EAAOQ,KAGhBS,OAAOC,eAAelB,EAAQ,KAAM,CACnCmB,YAAY,EACZC,IAAK,WACJ,OAAOpB,EAAOO,KAGhBP,EAAO0e,gBAAkB,GAEnB1e,I,4qBCpBR,IAAMuM,EAAYR,EAAQ,GAClB3I,EAAQ2I,EAAQ,GAAhB3I,IACF0I,EAAQC,EAAQ,GAElB+S,EAAmB,EAEvB9e,EAAOD,QAAU,WACf,IAAMmM,EAAKJ,EAAM,YAAagT,GACxBC,EAAU,GACVC,EAAiB,GACnBC,EAAW,GAEfH,GAAoB,EAEpB,IACMI,EAAgB,kBAAMje,OAAOqO,KAAKyP,GAAS5X,QAE3CgY,EAAU,WACd,GAAwB,IAApBF,EAAS9X,OAEX,IADA,IAAMiY,EAAOne,OAAOqO,KAAKyP,GAChBxe,EAAI,EAAGA,EAAI6e,EAAKjY,OAAQ5G,GAAK,EACpC,QAAuC,IAA5Bye,EAAeI,EAAK7e,IAAqB,CAClD0e,EAAS,GAAGF,EAAQK,EAAK7e,KACzB,QAMFuI,EAAQ,SAACqD,EAAQC,GAAT,OACZ,IAAIuB,SAAQ,SAACC,EAASC,GACpB,IAAMwR,EAAM9S,EAAU,CAAEJ,SAAQC,YAChC6S,EAASnV,KAAT,4CAAc,WAAOwV,GAAP,uFACZL,EAASM,QACTP,EAAeM,EAAEpT,IAAMmT,EAFX,cAIVzR,EAJU,SAII0R,EAAEnT,GAAQrG,MAAM,EAAhB,YAA0BsG,GAA1B,CAAmCiT,EAAInT,MAJ3C,oFAMV2B,EAAO,EAAD,IANI,gCAQHmR,EAAeM,EAAEpT,IACxBiT,IATU,6EAAd,uDAYA/b,EAAI,IAAD,OAAK8I,EAAL,kBAAiBmT,EAAInT,GAArB,iBACH9I,EAAI,IAAD,OAAK8I,EAAL,8BAA6B+S,EAAS9X,SACzCgY,QA0BJ,MAAO,CACLK,UAvBgB,SAACF,GAKjB,OAJAP,EAAQO,EAAEpT,IAAMoT,EAChBlc,EAAI,IAAD,OAAK8I,EAAL,kBAAiBoT,EAAEpT,KACtB9I,EAAI,IAAD,OAAK8I,EAAL,gCAA+BgT,MAClCC,IACOG,EAAEpT,IAmBTuT,OAhBU,4CAAG,WAAOtT,GAAP,yGACW,IAApB+S,IADS,sBAEL3W,MAAM,IAAD,OAAK2D,EAAL,+DAFA,sBAAkBE,EAAlB,iCAAkBA,EAAlB,mCAINtD,EAAMqD,EAAQC,IAJR,2CAAH,sDAiBV+C,UAVa,4CAAG,6FAChBlO,OAAOqO,KAAKyP,GAASnJ,QAArB,4CAA6B,WAAO8J,GAAP,gGACrBX,EAAQW,GAAKvQ,YADQ,2CAA7B,uDAGA8P,EAAW,GAJK,2CAAH,qDAWbU,YA9DkB,kBAAMV,EAAS9X,QA+DjC+X,mB,sWC7EJ,IACMU,EADoD,YAAxC7T,EAAQ,GAARA,CAA4B,QACfA,EAAQ,GAAiB,SAAAzJ,GAAC,OAAIA,GAE7DtC,EAAOD,QAAU,SAAC4C,GAChB,IAAMoM,E,+VAAO,CAAH,GAAQpM,GAMlB,MALA,CAAC,WAAY,aAAc,YAAYiT,SAAQ,SAAC9T,QAClB,IAAjBa,EAAQb,KACjBiN,EAAKjN,GAAO8d,EAAW7Q,EAAKjN,QAGzBiN,I,kQCVT,IAAM8Q,EAAa9T,EAAQ,IAE3B/L,EAAOD,QAAU,SAAC+B,GAChB,IAAMmI,EAAM,GAYZ,MAViC,oBAAtB6V,kBACT7V,EAAIvF,KAAO,YACFmb,IACT5V,EAAIvF,KAAO,WACgB,YAAlB,oBAAOvE,OAAP,cAAOA,SAChB8J,EAAIvF,KAAO,UACiB,iBAAnB,IAAO2D,EAAP,cAAOA,MAChB4B,EAAIvF,KAAO,aAGM,IAAR5C,EACFmI,EAGFA,EAAInI,M,mRCCb9B,EAAOD,QAnBP,WAEI,MAAsB,oBAAXI,QAAoD,WAA1B,EAAOA,OAAOkI,UAAgD,aAAxBlI,OAAOkI,QAAQ3D,cAKnE,IAAZ2D,GAAuD,WAA5B,EAAOA,EAAQ+B,YAA2B/B,EAAQ+B,SAAS2V,WAKxE,YAArB,oBAAOC,UAAP,cAAOA,aAAyD,iBAAxBA,UAAUC,WAA0BD,UAAUC,UAAUC,QAAQ,aAAe,M,stBCI/HlgB,EAAOD,QAAU,SAACogB,GAChB,IAAMC,EAAS,GACTC,EAAa,GACbC,EAAQ,GACRC,EAAQ,GACRC,EAAU,GA4BhB,OA1BAL,EAAKC,OAAOxK,SAAQ,SAAC6K,GACnBA,EAAMJ,WAAWzK,SAAQ,SAAC8K,GACxBA,EAAUJ,MAAM1K,SAAQ,SAAC+K,GACvBA,EAAKJ,MAAM3K,SAAQ,SAACgL,GAClBA,EAAKJ,QAAQ5K,SAAQ,SAACiL,GACpBL,EAAQ1W,KAAR,KACK+W,EADL,CACUV,OAAMM,QAAOC,YAAWC,OAAMC,aAG1CL,EAAMzW,KAAN,KACK8W,EADL,CACWT,OAAMM,QAAOC,YAAWC,aAGrCL,EAAMxW,KAAN,KACK6W,EADL,CACWR,OAAMM,QAAOC,kBAG1BL,EAAWvW,KAAX,KACK4W,EADL,CACgBP,OAAMM,cAGxBL,EAAOtW,KAAP,KACK2W,EADL,CACYN,aAId,KACKA,EADL,CACWC,SAAQC,aAAYC,QAAOC,QAAOC,c,gBCnD/C,IAAMvI,EAAMlM,EAAQ,GAEpB/L,EAAOD,QAAU,CACfyM,WAAYyL,EAAItI,U,gBCMlB,IAAMlD,EAAiBV,EAAQ,IACzBW,EAAcX,EAAQ,IACtBY,EAAkBZ,EAAQ,IAC1Ba,EAAYb,EAAQ,IACpBc,EAAOd,EAAQ,IACfvJ,EAAYuJ,EAAQ,IAE1B/L,EAAOD,QAAU,CACf0M,iBACAC,cACAC,kBACAC,YACAC,OACArK,c,wlBCtBF,IAAMod,EAAa7T,EAAQ,G,EACOA,EAAQ,IAAlC5B,E,EAAAA,QAAS2W,E,EAAAA,aACXrU,EAAiBV,EAAQ,IAK/B/L,EAAOD,Q,+VAAP,IACK0M,EADL,CAEEsU,gBAAgC,IAAZ1Y,GAAoD,gBAAzBA,EAAQ4B,IAAI+W,SACvDpB,EAAW,+BAAD,OAAgC/X,KAAKC,SAASlD,SAAS,IAAIL,MAAM,KADnE,0CAE2B4F,EAF3B,uBAOZ8W,SAAU,wCAAF,OAA0CH,EAAa,qBAAqBI,UAAU,GAAtF,2BAAkI,YAAvB,oBAAOC,YAAP,cAAOA,cAA2B,OAAS,MAAtJ,W,gnFChBVnhB,EAAOD,QAAU,CAIfqhB,SAAU,2CAMVC,eAAe,EACfrU,OAAQ,e,cCJVhN,EAAOD,QAAU,YAAmC,IAC9CqN,EADc2T,EAAgC,EAAhCA,WAAYM,EAAoB,EAApBA,cAE9B,GAAI3b,MAAQvB,KAAOkd,EAAe,CAChC,IAAMpe,EAAO,IAAIyC,KAAK,CAAC,kBAAD,OAAmBqb,EAAnB,QAAqC,CACzDrc,KAAM,2BAER0I,EAAS,IAAIkU,OAAOnd,IAAIb,gBAAgBL,SAExCmK,EAAS,IAAIkU,OAAOP,GAGtB,OAAO3T,I,cCXTpN,EAAOD,QAAU,SAACqN,GAChBA,EAAO+B,c,cCRTnP,EAAOD,QAAU,SAACqN,EAAQmU,GACxBnU,EAAOoU,UAAY,YAAc,IAAX1c,EAAW,EAAXA,KACpByc,EAAQzc,M,uICKZ9E,EAAOD,QAAP,e,EAAA,G,EAAA,yBAAiB,WAAOqN,EAAQqU,GAAf,gFACfrU,EAAOsU,YAAYD,GADJ,0C,+KAAjB,yD,4UCPA,IAAM7B,EAAa7T,EAAQ,GACrB4V,EAAmB5V,EAAQ,IAS3B6V,EAAqB,SAAC3e,GAAD,OACzB,IAAI0K,SAAQ,SAACC,EAASC,GACpB,IAAMtI,EAAa,IAAID,WACvBC,EAAW5B,OAAS,WAClBiK,EAAQrI,EAAWtB,SAErBsB,EAAW9B,QAAU,YAAqC,IAAfoe,EAAe,EAAlC7d,OAAUuC,MAASsb,KACzChU,EAAOtF,MAAM,gCAAD,OAAiCsZ,MAE/Ctc,EAAWuc,kBAAkB7e,OAI3B8e,EAAoC,SAAC9e,GAAD,OACxC,IAAI0K,SAAQ,SAACC,GACX+T,EACE1e,GACA,SAACJ,GAAD,OAASA,EAAImf,OAAOpU,KACpB,CACEqU,aAAa,EACb5R,QAAQ,QAaV7N,EAAS,4CAAG,WAAOsM,GAAP,+FACZhK,EAAOgK,OACU,IAAVA,EAFK,yCAGP,aAHO,UAMK,iBAAVA,EANK,qBAOVA,EAAMoT,SAAS,QAPL,iCAQOC,MAAMvC,EAAW9Q,IARxB,cAQNsT,EARM,iBASCA,EAAKC,cATN,QASZvd,EATY,sCAWRjC,EAAMiM,EAEL,yCAAyCwT,KAAKxT,KACjDjM,EAAM+c,EAAW9Q,IAdP,KAgBC8S,EAhBD,UAiBJG,EAAkClf,GAjB9B,4DAgBZiC,EAhBY,4CAoBLgK,aAAiByT,aApBZ,oBAqBQ,QAAlBzT,EAAM0T,QArBI,kCAsBChgB,EAAUsM,EAAMtL,KAtBjB,QAsBZsB,EAtBY,kBAwBQ,UAAlBgK,EAAM0T,QAxBI,kCAyBChgB,EAAUsM,EAAM2T,QAzBjB,QAyBZ3d,EAzBY,kBA2BQ,WAAlBgK,EAAM0T,QA3BI,kCA4BN,IAAI7U,SAAQ,SAACC,GACjBkB,EAAMkT,OAAN,4CAAa,WAAO/e,GAAP,gGACE2e,EAAmB3e,GADrB,OACX6B,EADW,OAEX8I,IAFW,2CAAb,0DA7BU,qCAmCLkB,aAAiB4T,MAAQ5T,aAAiBpJ,MAnCrC,oBAoCV7C,EAAMiM,EACLA,EAAMhO,KAAKohB,SAAS,QArCX,kCAsCAH,EAAkClf,GAtClC,QAsCZA,EAtCY,gCAwCD+e,EAAmB/e,GAxClB,QAwCdiC,EAxCc,wCA2CT,IAAIwC,WAAWxC,IA3CN,4CAAH,sDA8Cf9E,EAAOD,QAAUyC,G,gBCvFjBxC,EAAOD,QAAUgM,EAAQ,GAEzBA,EAAQ,GACRA,EAAQ,GACRA,EAAQ,IACRA,EAAQ,IACRA,EAAQ,IACRA,EAAQ,IACRA,EAAQ,IACRA,EAAQ,K,2BCEP,SAAWjM,GACV,aAGEG,EAAO,CAAC,WAAF,4BAOP,SAAUuC,GAGU,oBAAV2f,OAA4C,oBAAZQ,QACzCngB,EAAUqB,UAAY,SAAUjB,EAAKF,EAAUC,GAC7Cwf,MAAM,IAAIQ,QAAQ/f,EAAKD,IACpByY,MAAK,SAAUwH,GACd,OAAOA,EAAS3f,UAEjBmY,KAAK1Y,GACLmgB,OAAM,SAAU3f,GACfR,EAAS,KAAMQ,OAKK,oBAAnB4f,gBACkB,oBAAlBC,gBAEPvgB,EAAUqB,UAAY,SAAUjB,EAAKF,EAAUC,GAE7CA,EAAUA,GAAW,GACrB,IAAIqgB,EAAM,IAAIF,eACdE,EAAIC,KAAKtgB,EAAQ0C,QAAU,MAAOzC,GAC9BD,EAAQugB,SACVjiB,OAAOqO,KAAK3M,EAAQugB,SAAStN,SAAQ,SAAU9T,GAC7CkhB,EAAIG,iBAAiBrhB,EAAKa,EAAQugB,QAAQphB,OAG9CkhB,EAAII,gBAA0C,YAAxBzgB,EAAQ0gB,YAC9BL,EAAIM,aAAe,OACnBN,EAAIrf,OAAS,WACXjB,EAASsgB,EAAIJ,WAEfI,EAAIvf,QAAUuf,EAAIO,QAAUP,EAAIQ,UAAY,SAAUtgB,GACpDR,EAAS,KAAMQ,IAEjB8f,EAAInW,KAAKlK,EAAQ8gB,UA5Cb,+BAJT,I,gQCGA,SAAW3jB,GACV,aAGEG,EAAO,CAAC,KAAgB,YAAlB,4BAOP,SAAUuC,GAGX,IAAIkhB,EAAelhB,EAAU8P,QAAQnQ,UAErCuhB,EAAahR,KAAO,CAIlB,IAAQ,aACR,IAAQ,cACR,IAAQ,gBACR,IAAQ,cACR,IAAQ,4BACR,IAAQ,cACR,IAAQ,kBACR,IAAQ,sBACR,IAAQ,mBACR,IAAQ,mBACR,IAAQ,cACR,IAAQ,cACR,IAAQ,iBACR,IAAQ,eACR,IAAQ,eACR,IAAQ,kBACR,IAAQ,wBACR,IAAQ,8BACR,IAAQ,mBACR,IAAQ,aACR,IAAQ,wBACR,IAAQ,oBACR,IAAQ,sBACR,IAAQ,WACR,IAAQ,mBACR,IAAQ,OACR,IAAQ,QACR,IAAQ,WACR,IAAQ,SACR,MAAQ,YACR,MAAQ,CAEN,MAAQ,cACR,MAAQ,kBACR,MAAQ,aACR,MAAQ,kBACR,MAAQ,kBACR,MAAQ,QACR,MAAQ,0BACR,MAAQ,yBACR,MAAQ,YACR,MAAQ,cACR,MAAQ,mBACR,MAAQ,mBACR,MAAQ,oBACR,MAAQ,aACR,MAAQ,qBACR,MAAQ,sBACR,MAAQ,eACR,MAAQ,UACR,MAAQ,kBACR,MAAQ,sBACR,MAAQ,0BACR,MAAQ,OACR,MAAQ,kBACR,MAAQ,4BACR,MAAQ,2BACR,MAAQ,WACR,MAAQ,sBACR,MAAQ,sBACR,MAAQ,oBACR,MAAQ,gBACR,MAAQ,kBACR,MAAQ,eACR,MAAQ,mBACR,MAAQ,kBACR,MAAQ,eACR,MAAQ,cACR,MAAQ,QACR,MAAQ,cACR,MAAQ,cACR,MAAQ,cACR,MAAQ,2BACR,MAAQ,wBACR,MAAQ,wBACR,MAAQ,2BACR,MAAQ,kBACR,MAAQ,gBACR,MAAQ,gBACR,MAAQ,aACR,MAAQ,YACR,MAAQ,aACR,MAAQ,iBACR,MAAQ,eACR,MAAQ,eACR,MAAQ,mBACR,MAAQ,wBACR,MAAQ,mBACR,MAAQ,cACR,MAAQ,WACR,MAAQ,aACR,MAAQ,YACR,MAAQ,2BACR,MAAQ,uBACR,MAAQ,gBACR,MAAQ,kBACR,MAAQ,mBACR,MAAQ,oBACR,MAAQ,WACR,MAAQ,YACR,MAAQ,oBAEV,MAAQ,CAENyE,EAAQ,eACR,EAAQ,iBACR,EAAQ,cACR,EAAQ,kBACR,EAAQ,eACR,EAAQ,iBACR,EAAQ,cACR,EAAQ,eACR,EAAQ,gBACR,EAAQ,YACR,GAAQ,iBACR,GAAQ,SACR,GAAQ,cACR,GAAQ,WACR,GAAQ,cACR,GAAQ,WACR,GAAQ,qBACR,GAAQ,kBACR,GAAQ,cACR,GAAQ,qBACR,GAAQ,kBACR,GAAQ,sBACR,GAAQ,mBACR,GAAQ,oBACR,GAAQ,iBACR,GAAQ,qBACR,GAAQ,kBACR,GAAQ,sBACR,GAAQ,qBACR,GAAQ,eACR,GAAQ,kBACR,GAAQ,wBAEV,MAAQ,CAEN,EAAQ,0BAIZuM,EAAaC,aAAe,CAC1BC,gBAAiB,CACfzM,EAAG,YACHlE,EAAG,SACHI,EAAG,iBACHI,EAAG,oBACHE,EAAG,mBACHE,EAAG,mBACHgQ,EAAG,iBACHC,EAAG,gBACHC,EAAG,kBAELC,aAAc,CACZ7M,EAAG,UACHlE,EAAG,UACHI,EAAG,wBACHI,EAAG,OACHE,EAAG,YACHE,EAAG,UACHgQ,EAAG,UACHI,IAAK,SAEPC,YAAa,CACX/M,EAAG,UACHlE,EAAG,WACHI,EAAG,cACHI,EAAG,gCACHE,EAAG,QACHG,EAAG,eACHE,GAAI,iBACJmQ,GAAI,QACJC,GAAI,wCACJC,GAAI,yCACJC,GAAI,0CACJC,GAAI,sCACJC,GAAI,mBACJC,GAAI,mBACJC,GAAI,mBACJC,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,sBACJd,IAAK,SAEPe,MAAO,CACL7N,EAAQ,qBACR,EAAQ,cACR,EAAQ,mCACR,EAAQ,+BACR,EAAQ,qCACR,GAAQ,gEACR,GAAQ,4DACR,GAAQ,4CACR,GAAQ,gCACR,GAAQ,yBACR,GAAQ,oDACR,GAAQ,gDACR,GAAQ,oBACR,GAAQ,sCACR,GAAQ,iEACR,GAAQ,6DACR,GAAQ,6DACR,GAAQ,wFACR,GAAQ,oFACR,GAAQ,iDACR,GAAQ,4EACR,GAAQ,yEAEV8N,cAAe,CACbhS,EAAG,YACHI,EAAG,6BACHI,EAAG,6BACHE,EAAG,+BACHE,EAAG,+BACHiQ,EAAG,mBACHC,EAAG,kCAELmB,iBAAkB,CAChB/N,EAAG,WACHlE,EAAG,YACHI,EAAG,WACHI,EAAG,eAEL0R,UAAW,CACTlS,EAAG,yBAELmS,eAAgB,CACdjO,EAAG,iBACHlE,EAAG,kBAELoS,aAAc,CACZlO,EAAG,qBACHlE,EAAG,wBAELqS,YAAa,CACXnO,EAAG,OACHlE,EAAG,cACHI,EAAG,eACHI,EAAG,gBACHE,EAAG,kBAEL4R,SAAU,CACRpO,EAAG,SACHlE,EAAG,OACHI,EAAG,QAELmS,WAAY,CACVrO,EAAG,SACHlE,EAAG,iBACHI,EAAG,mBAELoS,UAAW,CACTtO,EAAG,SACHlE,EAAG,OACHI,EAAG,QAELqS,qBAAsB,CACpBvO,EAAG,UACHlE,EAAG,QACHI,EAAG,aACHI,EAAG,gBAELkS,WAAY,CACVlS,EAAG,OAELmS,wBAAyB,CACvBzO,EAAG,GACHlE,EAAG,IACHI,EAAG,KACHI,EAAG,KACHE,EAAG,IACHE,EAAG,IACHgQ,EAAG,KAELlR,YAAa,CACXM,EAAG,WACHI,EAAG,YACHI,EAAG,eACHE,EAAG,cACHE,EAAG,WACHgQ,EAAG,YACHC,EAAG,eACHC,EAAG,gBAIPL,EAAamC,QAAU,SAAU/kB,GAC/B,IAAIU,EAAQgE,KAAKpE,IAAIN,GACrB,OAAQA,GACN,IAAK,cACL,IAAK,QACL,IAAK,eACL,IAAK,kBACL,IAAK,gBACL,IAAK,mBACL,IAAK,YACL,IAAK,iBACL,IAAK,eACL,IAAK,cACL,IAAK,WACL,IAAK,aACL,IAAK,YACL,IAAK,uBACL,IAAK,aACL,IAAK,cACH,OAAO0E,KAAKme,aAAa7iB,GAAMU,GACjC,IAAK,cACL,IAAK,kBACH,IAAKA,EAAO,OACZ,OAAO8R,OAAOC,aAAa/R,EAAM,GAAIA,EAAM,GAAIA,EAAM,GAAIA,EAAM,IACjE,IAAK,0BACH,IAAKA,EAAO,OACZ,OACEgE,KAAKme,aAAa7iB,GAAMU,EAAM,IAC9BgE,KAAKme,aAAa7iB,GAAMU,EAAM,IAC9BgE,KAAKme,aAAa7iB,GAAMU,EAAM,IAC9BgE,KAAKme,aAAa7iB,GAAMU,EAAM,IAElC,IAAK,eACH,IAAKA,EAAO,OACZ,OAAOA,EAAM,GAAK,IAAMA,EAAM,GAAK,IAAMA,EAAM,GAAK,IAAMA,EAAM,GAEpE,OAAO8R,OAAO9R,IAGhBkiB,EAAaoC,OAAS,WACpB,IACIC,EACAphB,EACA7D,EAHA2R,EAAM,GAIV,IAAKsT,KAAQvgB,KACPvE,OAAOkB,UAAUC,eAAe1B,KAAK8E,KAAMugB,MAC7CphB,EAAMa,KAAKugB,KACAphB,EAAImhB,OACbrT,EAAIjN,KAAKgN,YAAYuT,GAAMjlB,MAAQ6D,EAAImhB,UAEvChlB,EAAO0E,KAAKkN,KAAKqT,MACPtT,EAAI3R,GAAQ0E,KAAKqgB,QAAQ/kB,KAIzC,OAAO2R,GAGTiR,EAAasC,QAAU,SAAUzT,GAC/B,IAAIzR,EAAO0E,KAAKkN,KAAKH,GACrB,MAAoB,WAAhB,EAAOzR,GAA0B0E,KAAKgN,YAAYD,GAASzR,KACxDA,GAIR,WACC,IACIilB,EACAE,EACAC,EAHAxT,EAAOgR,EAAahR,KAKxB,IAAKqT,KAAQrT,EACX,GAAIzR,OAAOkB,UAAUC,eAAe1B,KAAKgS,EAAMqT,GAE7C,GADAE,EAAavC,EAAalR,YAAYuT,GAGpC,IAAKA,KADLG,EAAUxT,EAAKqT,GAET9kB,OAAOkB,UAAUC,eAAe1B,KAAKwlB,EAASH,KAChDE,EAAWxT,IAAIyT,EAAQH,IAASI,OAAOJ,SAI3CrC,EAAajR,IAAIC,EAAKqT,IAASI,OAAOJ,GAjB7C,KAnXO,+BAJT,I,2BCCA,SAAWjmB,GACV,aAGEG,EAAO,CAAC,KAAgB,YAAlB,4BAOP,SAAUuC,GAGX,IAAI4jB,EAAe5jB,EAAUyT,QAAQ9T,UAErCikB,EAAa1T,KAAO,CAClByE,EAAG,2BACH1D,EAAG,sBACHE,EAAG,2BACHE,EAAG,aACHiQ,EAAG,aACHC,EAAG,kBACH/P,GAAI,UACJoQ,GAAI,mBACJG,GAAI,WACJI,GAAI,yBACJE,GAAI,oBACJwB,GAAI,WACJC,GAAI,sBACJC,GAAI,sBACJC,GAAI,cACJC,GAAI,cACJC,GAAI,iBACJC,GAAI,iBACJC,GAAI,sBACJC,GAAI,gBACJC,GAAI,mBACJC,GAAI,gBACJC,GAAI,kBACJC,GAAI,cACJC,GAAI,cACJC,GAAI,sBACJC,GAAI,sBACJC,GAAI,qBACJC,GAAI,iBACJC,GAAI,cACJC,GAAI,SACJC,GAAI,cACJC,GAAI,OACJC,GAAI,cACJC,GAAI,QACJC,IAAK,cACLC,IAAK,UACLC,IAAK,gCACLC,IAAK,WACLC,IAAK,SACLC,IAAK,SACLC,IAAK,kBACLC,IAAK,UACLC,IAAK,UACLC,IAAK,eACLC,IAAK,SACLC,IAAK,oBACLC,IAAK,YACLC,IAAK,mBACLC,IAAK,qBACLC,IAAK,YACLC,IAAK,oBACLC,IAAK,0BACLC,IAAK,gBACLC,IAAK,cACLC,IAAK,QACLC,IAAK,mBACLC,IAAK,kBACLC,IAAK,mBACLC,IAAK,UACLjS,IAAK,0BACLC,IAAK,2BACLC,IAAK,oBACLgS,IAAK,QACLC,IAAK,gBACLC,IAAK,kBACLC,IAAK,gBACLC,IAAK,kBACLC,IAAK,iBACL1F,IAAK,eAGPmC,EAAazC,aAAe,CAC1B3P,GAAI,CACFmD,EAAG,eACHlE,EAAG,kBACHI,EAAG,IACHI,EAAG,IACHE,EAAG,IACHE,EAAG,qBACHgQ,EAAG,IACHC,EAAG,IACHC,EAAG,mBACHjQ,EAAG,6BAELyT,GAAI,CACF5b,EAAG,UACHie,EAAG,2BACHvnB,EAAG,WAELqmB,IAAK,CACHmB,EAAG,YACHC,EAAG,WACHC,EAAG,WAIP3D,EAAaP,QAAU,SAAU3Z,GAC/B,IAAI1K,EAAQgE,KAAKpE,IAAI8K,GACjBqG,EAAU/M,KAAKiN,IAAIvG,GACnB8d,EAAcxkB,KAAKme,aAAapR,GACpC,OAAIyX,EAAoBA,EAAYxoB,GAC7B8R,OAAO9R,IAGhB4kB,EAAaN,OAAS,WACpB,IACIC,EACAjlB,EAFA2R,EAAM,GAGV,IAAKsT,KAAQvgB,KACPvE,OAAOkB,UAAUC,eAAe1B,KAAK8E,KAAMugB,KAC7CjlB,EAAO0E,KAAKkN,KAAKqT,MACPtT,EAAI3R,GAAQ0E,KAAKqgB,QAAQ/kB,IAGvC,OAAO2R,GAGT2T,EAAaJ,QAAU,SAAUzT,GAC/B,OAAO/M,KAAKkN,KAAKH,IAIlB,WACC,IAEIwT,EAFArT,EAAO0T,EAAa1T,KACpBD,EAAM2T,EAAa3T,KAAO,GAG9B,IAAKsT,KAAQrT,EACPzR,OAAOkB,UAAUC,eAAe1B,KAAKgS,EAAMqT,KAC7CtT,EAAIC,EAAKqT,IAASI,OAAOJ,IAP9B,KAxIO,+BAJT,I,2BCJA,SAAWjmB,GACV,aAGEG,EAAO,CAAC,KAAgB,KAAsB,WAAxC,4BAWP,SAAUuC,GAGX,IAAIynB,EAA0BznB,EAAU2O,gBACpC+Y,EAAwB1nB,EAAUoB,cAClCumB,EAA+B3nB,EAAUqN,qBACzCua,EAAgC5nB,EAAUsN,sBAYxCjN,EAAMC,SAASC,cAAc,OACjCF,EAAIc,OAAS,WAEXnB,EAAUyf,YAA4B,IAAdpf,EAAIoC,OAA8B,IAAfpC,EAAIuC,QAEjDvC,EAAIW,IAXF,0ZAeJhB,EAAU2O,gBAAkB,SAAUxO,GACpC,OAC6B,KAAxBA,EAAQsf,cAAyBzf,EAAUyf,aAC7Ctf,EAAQsf,YAAc,GAAKtf,EAAQsf,YAAc,GAClDgI,EAAwBvpB,KAAK8B,EAAWG,IAK5CH,EAAUoB,cAAgB,SAAUjB,GAClC,OACGA,IAAmC,IAAxBA,EAAQsf,cAAyBzf,EAAUyf,aACvDiI,EAAsBxpB,KAAK8B,EAAWG,IAM1CH,EAAUqN,qBAAuB,SAAUQ,EAAQ1N,GACjDwnB,EAA6BzpB,KAAK8B,EAAW6N,EAAQ1N,GACrD,IAAImO,EAAMT,EAAOU,WAAW,MACxB9L,EAAQoL,EAAOpL,MACfG,EAASiL,EAAOjL,OAChBilB,EAAaha,EAAOgC,MAAMpN,MAC1BqlB,EAAcja,EAAOgC,MAAMjN,OAC3B6c,EAActf,EAAQsf,YAC1B,GAAMA,EAAc,GAAKA,EAAc,EASvC,OANIA,EAAc,IAChB5R,EAAOpL,MAAQG,EACfiL,EAAOjL,OAASH,EAChBoL,EAAOgC,MAAMpN,MAAQqlB,EACrBja,EAAOgC,MAAMjN,OAASilB,GAEhBpI,GACN,KAAK,EAEHnR,EAAIyZ,UAAUtlB,EAAO,GACrB6L,EAAIlB,OAAO,EAAG,GACd,MACF,KAAK,EAEHkB,EAAIyZ,UAAUtlB,EAAOG,GACrB0L,EAAI0Z,OAAO3iB,KAAK4iB,IAChB,MACF,KAAK,EAEH3Z,EAAIyZ,UAAU,EAAGnlB,GACjB0L,EAAIlB,MAAM,GAAI,GACd,MACF,KAAK,EAEHkB,EAAI0Z,OAAO,GAAM3iB,KAAK4iB,IACtB3Z,EAAIlB,MAAM,GAAI,GACd,MACF,KAAK,EAEHkB,EAAI0Z,OAAO,GAAM3iB,KAAK4iB,IACtB3Z,EAAIyZ,UAAU,GAAInlB,GAClB,MACF,KAAK,EAEH0L,EAAI0Z,OAAO,GAAM3iB,KAAK4iB,IACtB3Z,EAAIyZ,UAAUtlB,GAAQG,GACtB0L,EAAIlB,OAAO,EAAG,GACd,MACF,KAAK,EAEHkB,EAAI0Z,QAAQ,GAAM3iB,KAAK4iB,IACvB3Z,EAAIyZ,WAAWtlB,EAAO,KAO5BzC,EAAUsN,sBAAwB,SAAUjN,EAAKkM,EAAMjK,GACrD,IAEIiL,EACAxP,EAHAoC,EAAUynB,EAA8B1pB,KAAK8B,EAAWK,EAAKkM,GAC7DkT,EAActf,EAAQsf,YAG1B,IAAoB,IAAhBA,EAAsB,CACxB,GAAIzf,EAAUyf,YAEZ,OAAOtf,EAETsf,EAAcnd,GAAQA,EAAKuQ,MAAQvQ,EAAKuQ,KAAKjU,IAAI,eAEnD,KAAM6gB,EAAc,GAAKA,EAAc,GACrC,OAAOtf,EAGT,IAAKpC,KADLwP,EAAa,GACHpN,EACJ1B,OAAOkB,UAAUC,eAAe1B,KAAKiC,EAASpC,KAChDwP,EAAWxP,GAAKoC,EAAQpC,IAI5B,OADAwP,EAAWkS,YAAcA,EACjBA,GACN,KAAK,EAEHlS,EAAW+B,KAAOnP,EAAQsP,MAC1BlC,EAAWkC,MAAQtP,EAAQmP,KAC3B,MACF,KAAK,EAEH/B,EAAW+B,KAAOnP,EAAQsP,MAC1BlC,EAAWgC,IAAMpP,EAAQuP,OACzBnC,EAAWkC,MAAQtP,EAAQmP,KAC3B/B,EAAWmC,OAASvP,EAAQoP,IAC5B,MACF,KAAK,EAEHhC,EAAWgC,IAAMpP,EAAQuP,OACzBnC,EAAWmC,OAASvP,EAAQoP,IAC5B,MACF,KAAK,EAEHhC,EAAW+B,KAAOnP,EAAQoP,IAC1BhC,EAAWgC,IAAMpP,EAAQmP,KACzB/B,EAAWkC,MAAQtP,EAAQuP,OAC3BnC,EAAWmC,OAASvP,EAAQsP,MAC5B,MACF,KAAK,EAEHlC,EAAW+B,KAAOnP,EAAQoP,IAC1BhC,EAAWgC,IAAMpP,EAAQsP,MACzBlC,EAAWkC,MAAQtP,EAAQuP,OAC3BnC,EAAWmC,OAASvP,EAAQmP,KAC5B,MACF,KAAK,EAEH/B,EAAW+B,KAAOnP,EAAQuP,OAC1BnC,EAAWgC,IAAMpP,EAAQsP,MACzBlC,EAAWkC,MAAQtP,EAAQoP,IAC3BhC,EAAWmC,OAASvP,EAAQmP,KAC5B,MACF,KAAK,EAEH/B,EAAW+B,KAAOnP,EAAQuP,OAC1BnC,EAAWgC,IAAMpP,EAAQmP,KACzB/B,EAAWkC,MAAQtP,EAAQoP,IAC3BhC,EAAWmC,OAASvP,EAAQsP,MAWhC,OARIlC,EAAWkS,YAAc,IAC3BlS,EAAWG,SAAWvN,EAAQwN,UAC9BJ,EAAWI,UAAYxN,EAAQuN,SAC/BH,EAAWqB,SAAWzO,EAAQ0O,UAC9BtB,EAAWsB,UAAY1O,EAAQyO,SAC/BrB,EAAWS,YAAc7N,EAAQ8N,aACjCV,EAAWU,aAAe9N,EAAQ6N,aAE7BT,GA7KR,IAUKlN,IA7BE,+BAJT,I,4UCbD,IAAMiV,EAAe/L,EAAQ,GAEvB8C,EAAS,4CAAG,WAAOC,EAAON,EAAO7L,GAArB,6FACVyK,EAAS0K,EAAanV,GADZ,SAEVyK,EAAOW,OAFG,uBAGVX,EAAOmB,aAAaC,GAHV,uBAIVpB,EAAOqB,WAAWD,GAJR,gCAKTpB,EAAOyB,UAAUC,GACrB4b,QADI,2BACI,6GACDtd,EAAO+B,YADN,6CANK,2CAAH,0DAWTD,EAAM,4CAAG,WAAOJ,EAAOnM,GAAd,6FACPyK,EAAS0K,EAAanV,GADf,SAEPyK,EAAOW,OAFA,uBAGPX,EAAOmB,aAAa,OAHb,uBAIPnB,EAAOqB,WAAW,OAJX,gCAKNrB,EAAO8B,OAAOJ,GAClB4b,QADI,2BACI,6GACDtd,EAAO+B,YADN,6CANE,2CAAH,wDAWZnP,EAAOD,QAAU,CACf8O,YACAK,W,cCwFFlP,EAAOD,QAAU,CACf4qB,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,QAAS,UACTC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,SAAU,WACVC,IAAK,MACLC,IAAK,Q,cCrNPhxB,EAAOD,QAAU,CACfkxB,SAAU,IACVC,SAAU,IACVC,UAAW,IACXC,KAAM,IACNC,cAAe,IACfC,uBAAwB,IACxBC,aAAc,IACdC,YAAa,IACbC,YAAa,IACbC,YAAa,IACbC,YAAa,KACbC,YAAa,KACbC,gBAAiB", "file": "tesseract.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Tesseract\"] = factory();\n\telse\n\t\troot[\"Tesseract\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 12);\n", "/*\n * JavaScript Load Image\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, webkitURL, module */\n\n;(function ($) {\n  'use strict'\n\n  /**\n   * Loads an image for a given File object.\n   * Invokes the callback with an img or optional canvas element\n   * (if supported by the browser) as parameter:.\n   *\n   * @param {File|Blob|string} file File or Blob object or image URL\n   * @param {Function} [callback] Image load event callback\n   * @param {object} [options] Options object\n   * @returns {HTMLImageElement|HTMLCanvasElement|FileReader} image object\n   */\n  function loadImage(file, callback, options) {\n    var img = document.createElement('img')\n    var url\n    /**\n     * Callback for the fetchBlob call.\n     *\n     * @param {Blob} blob Blob object\n     * @param {Error} err Error object\n     */\n    function fetchBlobCallback(blob, err) {\n      if (err) console.log(err) // eslint-disable-line no-console\n      if (blob && loadImage.isInstanceOf('Blob', blob)) {\n        // eslint-disable-next-line no-param-reassign\n        file = blob\n        url = loadImage.createObjectURL(file)\n      } else {\n        url = file\n        if (options && options.crossOrigin) {\n          img.crossOrigin = options.crossOrigin\n        }\n      }\n      img.src = url\n    }\n    img.onerror = function (event) {\n      return loadImage.onerror(img, event, file, url, callback, options)\n    }\n    img.onload = function (event) {\n      return loadImage.onload(img, event, file, url, callback, options)\n    }\n    if (typeof file === 'string') {\n      if (loadImage.hasMetaOption(options)) {\n        loadImage.fetchBlob(file, fetchBlobCallback, options)\n      } else {\n        fetchBlobCallback()\n      }\n      return img\n    } else if (\n      loadImage.isInstanceOf('Blob', file) ||\n      // Files are also Blob instances, but some browsers\n      // (Firefox 3.6) support the File API but not Blobs:\n      loadImage.isInstanceOf('File', file)\n    ) {\n      url = loadImage.createObjectURL(file)\n      if (url) {\n        img.src = url\n        return img\n      }\n      return loadImage.readFile(file, function (e) {\n        var target = e.target\n        if (target && target.result) {\n          img.src = target.result\n        } else if (callback) {\n          callback(e)\n        }\n      })\n    }\n  }\n  // The check for URL.revokeObjectURL fixes an issue with Opera 12,\n  // which provides URL.createObjectURL but doesn't properly implement it:\n  var urlAPI =\n    ($.createObjectURL && $) ||\n    ($.URL && URL.revokeObjectURL && URL) ||\n    ($.webkitURL && webkitURL)\n\n  /**\n   * Helper function to revoke an object URL\n   *\n   * @param {string} url Blob Object URL\n   * @param {object} [options] Options object\n   */\n  function revokeHelper(url, options) {\n    if (url && url.slice(0, 5) === 'blob:' && !(options && options.noRevoke)) {\n      loadImage.revokeObjectURL(url)\n    }\n  }\n\n  // Determines if meta data should be loaded automatically.\n  // Requires the load image meta extension to load meta data.\n  loadImage.hasMetaOption = function (options) {\n    return options && options.meta\n  }\n\n  // If the callback given to this function returns a blob, it is used as image\n  // source instead of the original url and overrides the file argument used in\n  // the onload and onerror event callbacks:\n  loadImage.fetchBlob = function (url, callback) {\n    callback()\n  }\n\n  loadImage.isInstanceOf = function (type, obj) {\n    // Cross-frame instanceof check\n    return Object.prototype.toString.call(obj) === '[object ' + type + ']'\n  }\n\n  loadImage.transform = function (img, options, callback, file, data) {\n    callback(img, data)\n  }\n\n  loadImage.onerror = function (img, event, file, url, callback, options) {\n    revokeHelper(url, options)\n    if (callback) {\n      callback.call(img, event)\n    }\n  }\n\n  loadImage.onload = function (img, event, file, url, callback, options) {\n    revokeHelper(url, options)\n    if (callback) {\n      loadImage.transform(img, options, callback, file, {\n        originalWidth: img.naturalWidth || img.width,\n        originalHeight: img.naturalHeight || img.height\n      })\n    }\n  }\n\n  loadImage.createObjectURL = function (file) {\n    return urlAPI ? urlAPI.createObjectURL(file) : false\n  }\n\n  loadImage.revokeObjectURL = function (url) {\n    return urlAPI ? urlAPI.revokeObjectURL(url) : false\n  }\n\n  // Loads a given File object via FileReader interface,\n  // invokes the callback with the event object (load or error).\n  // The result can be read via event.target.result:\n  loadImage.readFile = function (file, callback, method) {\n    if ($.FileReader) {\n      var fileReader = new FileReader()\n      fileReader.onload = fileReader.onerror = callback\n      // eslint-disable-next-line no-param-reassign\n      method = method || 'readAsDataURL'\n      if (fileReader[method]) {\n        fileReader[method](file)\n        return fileReader\n      }\n    }\n    return false\n  }\n\n  if (typeof define === 'function' && define.amd) {\n    define(function () {\n      return loadImage\n    })\n  } else if (typeof module === 'object' && module.exports) {\n    module.exports = loadImage\n  } else {\n    $.loadImage = loadImage\n  }\n})((typeof window !== 'undefined' && window) || this)\n", "/*\n * JavaScript Load Image Meta\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Image meta data handling implementation\n * based on the help and contribution of\n * <PERSON><PERSON><PERSON>.\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require, DataView, Uint8Array */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  var hasblobSlice =\n    typeof Blob !== 'undefined' &&\n    (Blob.prototype.slice ||\n      Blob.prototype.webkitSlice ||\n      Blob.prototype.mozSlice)\n\n  loadImage.blobSlice =\n    hasblobSlice &&\n    function () {\n      var slice = this.slice || this.webkitSlice || this.mozSlice\n      return slice.apply(this, arguments)\n    }\n\n  loadImage.metaDataParsers = {\n    jpeg: {\n      0xffe1: [], // APP1 marker\n      0xffed: [] // APP13 marker\n    }\n  }\n\n  // Parses image meta data and calls the callback with an object argument\n  // with the following properties:\n  // * imageHead: The complete image head as ArrayBuffer (Uint8Array for IE10)\n  // The options argument accepts an object and supports the following\n  // properties:\n  // * maxMetaDataSize: Defines the maximum number of bytes to parse.\n  // * disableImageHead: Disables creating the imageHead property.\n  loadImage.parseMetaData = function (file, callback, options, data) {\n    // eslint-disable-next-line no-param-reassign\n    options = options || {}\n    // eslint-disable-next-line no-param-reassign\n    data = data || {}\n    var that = this\n    // 256 KiB should contain all EXIF/ICC/IPTC segments:\n    var maxMetaDataSize = options.maxMetaDataSize || 262144\n    var noMetaData = !(\n      typeof DataView !== 'undefined' &&\n      file &&\n      file.size >= 12 &&\n      file.type === 'image/jpeg' &&\n      loadImage.blobSlice\n    )\n    if (\n      noMetaData ||\n      !loadImage.readFile(\n        loadImage.blobSlice.call(file, 0, maxMetaDataSize),\n        function (e) {\n          if (e.target.error) {\n            // FileReader error\n            // eslint-disable-next-line no-console\n            console.log(e.target.error)\n            callback(data)\n            return\n          }\n          // Note on endianness:\n          // Since the marker and length bytes in JPEG files are always\n          // stored in big endian order, we can leave the endian parameter\n          // of the DataView methods undefined, defaulting to big endian.\n          var buffer = e.target.result\n          var dataView = new DataView(buffer)\n          var offset = 2\n          var maxOffset = dataView.byteLength - 4\n          var headLength = offset\n          var markerBytes\n          var markerLength\n          var parsers\n          var i\n          // Check for the JPEG marker (0xffd8):\n          if (dataView.getUint16(0) === 0xffd8) {\n            while (offset < maxOffset) {\n              markerBytes = dataView.getUint16(offset)\n              // Search for APPn (0xffeN) and COM (0xfffe) markers,\n              // which contain application-specific meta-data like\n              // Exif, ICC and IPTC data and text comments:\n              if (\n                (markerBytes >= 0xffe0 && markerBytes <= 0xffef) ||\n                markerBytes === 0xfffe\n              ) {\n                // The marker bytes (2) are always followed by\n                // the length bytes (2), indicating the length of the\n                // marker segment, which includes the length bytes,\n                // but not the marker bytes, so we add 2:\n                markerLength = dataView.getUint16(offset + 2) + 2\n                if (offset + markerLength > dataView.byteLength) {\n                  // eslint-disable-next-line no-console\n                  console.log('Invalid meta data: Invalid segment size.')\n                  break\n                }\n                parsers = loadImage.metaDataParsers.jpeg[markerBytes]\n                if (parsers && !options.disableMetaDataParsers) {\n                  for (i = 0; i < parsers.length; i += 1) {\n                    parsers[i].call(\n                      that,\n                      dataView,\n                      offset,\n                      markerLength,\n                      data,\n                      options\n                    )\n                  }\n                }\n                offset += markerLength\n                headLength = offset\n              } else {\n                // Not an APPn or COM marker, probably safe to\n                // assume that this is the end of the meta data\n                break\n              }\n            }\n            // Meta length must be longer than JPEG marker (2)\n            // plus APPn marker (2), followed by length bytes (2):\n            if (!options.disableImageHead && headLength > 6) {\n              if (buffer.slice) {\n                data.imageHead = buffer.slice(0, headLength)\n              } else {\n                // Workaround for IE10, which does not yet\n                // support ArrayBuffer.slice:\n                data.imageHead = new Uint8Array(buffer).subarray(0, headLength)\n              }\n            }\n          } else {\n            // eslint-disable-next-line no-console\n            console.log('Invalid JPEG file: Missing JPEG marker.')\n          }\n          callback(data)\n        },\n        'readAsArrayBuffer'\n      )\n    ) {\n      callback(data)\n    }\n  }\n\n  // Replaces the image head of a JPEG blob with the given one.\n  // Calls the callback with the new Blob:\n  loadImage.replaceHead = function (blob, head, callback) {\n    loadImage.parseMetaData(\n      blob,\n      function (data) {\n        callback(\n          new Blob(\n            [head, loadImage.blobSlice.call(blob, data.imageHead.byteLength)],\n            { type: 'image/jpeg' }\n          )\n        )\n      },\n      { maxMetaDataSize: 256, disableMetaDataParsers: true }\n    )\n  }\n\n  var originalTransform = loadImage.transform\n  loadImage.transform = function (img, options, callback, file, data) {\n    if (loadImage.hasMetaOption(options)) {\n      loadImage.parseMetaData(\n        file,\n        function (data) {\n          originalTransform.call(loadImage, img, options, callback, file, data)\n        },\n        options,\n        data\n      )\n    } else {\n      originalTransform.apply(loadImage, arguments)\n    }\n  }\n})\n", "module.exports = (prefix, cnt) => (\n  `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`\n);\n", "let logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "// Copyright 2014 <PERSON>\r\n// X11 (“MIT”) Licensed. (See LICENSE.)\r\n\r\nvoid (function(root, factory) {\r\n  if (typeof define === \"function\" && define.amd) {\r\n    define(factory)\r\n  } else if (typeof exports === \"object\") {\r\n    module.exports = factory()\r\n  } else {\r\n    root.resolveUrl = factory()\r\n  }\r\n}(this, function() {\r\n\r\n  function resolveUrl(/* ...urls */) {\r\n    var numUrls = arguments.length\r\n\r\n    if (numUrls === 0) {\r\n      throw new Error(\"resolveUrl requires at least one argument; got none.\")\r\n    }\r\n\r\n    var base = document.createElement(\"base\")\r\n    base.href = arguments[0]\r\n\r\n    if (numUrls === 1) {\r\n      return base.href\r\n    }\r\n\r\n    var head = document.getElementsByTagName(\"head\")[0]\r\n    head.insertBefore(base, head.firstChild)\r\n\r\n    var a = document.createElement(\"a\")\r\n    var resolved\r\n\r\n    for (var index = 1; index < numUrls; index++) {\r\n      a.href = arguments[index]\r\n      resolved = a.href\r\n      base.href = resolved\r\n    }\r\n\r\n    head.removeChild(base)\r\n\r\n    return resolved\r\n  }\r\n\r\n  return resolveUrl\r\n\r\n}));\r\n", "const getId = require('./utils/getId');\n\nlet jobCounter = 0;\n\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {},\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n\n  return {\n    id,\n    action,\n    payload,\n  };\n};\n", "const resolvePaths = require('./utils/resolvePaths');\nconst circularize = require('./utils/circularize');\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\nconst { defaultOEM } = require('./constants/config');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send,\n} = require('./worker/node');\n\nlet workerCounter = 0;\n\nmodule.exports = (_options = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options,\n  });\n  const resolves = {};\n  const rejects = {};\n  let worker = spawnWorker(options);\n\n  workerCounter += 1;\n\n  const setResolve = (action, res) => {\n    resolves[action] = res;\n  };\n\n  const setReject = (action, rej) => {\n    rejects[action] = rej;\n  };\n\n  const startJob = ({ id: jobId, action, payload }) => (\n    new Promise((resolve, reject) => {\n      log(`[${id}]: Start ${jobId}, action=${action}`);\n      setResolve(action, resolve);\n      setReject(action, reject);\n      send(worker, {\n        workerId: id,\n        jobId,\n        action,\n        payload,\n      });\n    })\n  );\n\n  const load = (jobId) => (\n    startJob(createJob({\n      id: jobId, action: 'load', payload: { options },\n    }))\n  );\n\n  const writeText = (path, text, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'writeFile', args: [path, text] },\n    }))\n  );\n\n  const readText = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'readFile', args: [path, { encoding: 'utf8' }] },\n    }))\n  );\n\n  const removeFile = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'unlink', args: [path] },\n    }))\n  );\n\n  const FS = (method, args, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method, args },\n    }))\n  );\n\n  const loadLanguage = (langs = 'eng', jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'loadLanguage',\n      payload: { langs, options },\n    }))\n  );\n\n  const initialize = (langs = 'eng', oem = defaultOEM, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'initialize',\n      payload: { langs, oem },\n    }))\n  );\n\n  const setParameters = (params = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'setParameters',\n      payload: { params },\n    }))\n  );\n\n  const recognize = async (image, opts = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'recognize',\n      payload: { image: await loadImage(image), options: opts },\n    }))\n  );\n\n  const getPDF = (title = 'Tesseract OCR Result', textonly = false, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'getPDF',\n      payload: { title, textonly },\n    }))\n  );\n\n  const detect = async (image, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: { image: await loadImage(image) },\n    }))\n  );\n\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n\n  onMessage(worker, ({\n    workerId, jobId, status, action, data,\n  }) => {\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      let d = data;\n      if (action === 'recognize') {\n        d = circularize(data);\n      } else if (action === 'getPDF') {\n        d = Array.from({ ...data, length: Object.keys(data).length });\n      }\n      resolves[action]({ jobId, data: d });\n    } else if (status === 'reject') {\n      rejects[action](data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({ ...data, userJobId: jobId });\n    }\n  });\n\n  return {\n    id,\n    worker,\n    setResolve,\n    setReject,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    loadLanguage,\n    initialize,\n    setParameters,\n    recognize,\n    getPDF,\n    detect,\n    terminate,\n  };\n};\n", "/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3,\n};\n", "/*\n * JavaScript Load Image Scaling\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  var originalTransform = loadImage.transform\n\n  loadImage.transform = function (img, options, callback, file, data) {\n    originalTransform.call(\n      loadImage,\n      loadImage.scale(img, options, data),\n      options,\n      callback,\n      file,\n      data\n    )\n  }\n\n  // Transform image coordinates, allows to override e.g.\n  // the canvas orientation based on the orientation option,\n  // gets canvas, options passed as arguments:\n  loadImage.transformCoordinates = function () {}\n\n  // Returns transformed options, allows to override e.g.\n  // maxWidth, maxHeight and crop options based on the aspectRatio.\n  // gets img, options passed as arguments:\n  loadImage.getTransformedOptions = function (img, options) {\n    var aspectRatio = options.aspectRatio\n    var newOptions\n    var i\n    var width\n    var height\n    if (!aspectRatio) {\n      return options\n    }\n    newOptions = {}\n    for (i in options) {\n      if (Object.prototype.hasOwnProperty.call(options, i)) {\n        newOptions[i] = options[i]\n      }\n    }\n    newOptions.crop = true\n    width = img.naturalWidth || img.width\n    height = img.naturalHeight || img.height\n    if (width / height > aspectRatio) {\n      newOptions.maxWidth = height * aspectRatio\n      newOptions.maxHeight = height\n    } else {\n      newOptions.maxWidth = width\n      newOptions.maxHeight = width / aspectRatio\n    }\n    return newOptions\n  }\n\n  // Canvas render method, allows to implement a different rendering algorithm:\n  loadImage.renderImageToCanvas = function (\n    canvas,\n    img,\n    sourceX,\n    sourceY,\n    sourceWidth,\n    sourceHeight,\n    destX,\n    destY,\n    destWidth,\n    destHeight,\n    options\n  ) {\n    var ctx = canvas.getContext('2d')\n    if (options.imageSmoothingEnabled === false) {\n      ctx.imageSmoothingEnabled = false\n    } else if (options.imageSmoothingQuality) {\n      ctx.imageSmoothingQuality = options.imageSmoothingQuality\n    }\n    ctx.drawImage(\n      img,\n      sourceX,\n      sourceY,\n      sourceWidth,\n      sourceHeight,\n      destX,\n      destY,\n      destWidth,\n      destHeight\n    )\n    return canvas\n  }\n\n  // Determines if the target image should be a canvas element:\n  loadImage.hasCanvasOption = function (options) {\n    return options.canvas || options.crop || !!options.aspectRatio\n  }\n\n  // Scales and/or crops the given image (img or canvas HTML element)\n  // using the given options.\n  // Returns a canvas object if the browser supports canvas\n  // and the hasCanvasOption method returns true or a canvas\n  // object is passed as image, else the scaled image:\n  loadImage.scale = function (img, options, data) {\n    // eslint-disable-next-line no-param-reassign\n    options = options || {}\n    var canvas = document.createElement('canvas')\n    var useCanvas =\n      img.getContext ||\n      (loadImage.hasCanvasOption(options) && canvas.getContext)\n    var width = img.naturalWidth || img.width\n    var height = img.naturalHeight || img.height\n    var destWidth = width\n    var destHeight = height\n    var maxWidth\n    var maxHeight\n    var minWidth\n    var minHeight\n    var sourceWidth\n    var sourceHeight\n    var sourceX\n    var sourceY\n    var pixelRatio\n    var downsamplingRatio\n    var tmp\n    /**\n     * Scales up image dimensions\n     */\n    function scaleUp() {\n      var scale = Math.max(\n        (minWidth || destWidth) / destWidth,\n        (minHeight || destHeight) / destHeight\n      )\n      if (scale > 1) {\n        destWidth *= scale\n        destHeight *= scale\n      }\n    }\n    /**\n     * Scales down image dimensions\n     */\n    function scaleDown() {\n      var scale = Math.min(\n        (maxWidth || destWidth) / destWidth,\n        (maxHeight || destHeight) / destHeight\n      )\n      if (scale < 1) {\n        destWidth *= scale\n        destHeight *= scale\n      }\n    }\n    if (useCanvas) {\n      // eslint-disable-next-line no-param-reassign\n      options = loadImage.getTransformedOptions(img, options, data)\n      sourceX = options.left || 0\n      sourceY = options.top || 0\n      if (options.sourceWidth) {\n        sourceWidth = options.sourceWidth\n        if (options.right !== undefined && options.left === undefined) {\n          sourceX = width - sourceWidth - options.right\n        }\n      } else {\n        sourceWidth = width - sourceX - (options.right || 0)\n      }\n      if (options.sourceHeight) {\n        sourceHeight = options.sourceHeight\n        if (options.bottom !== undefined && options.top === undefined) {\n          sourceY = height - sourceHeight - options.bottom\n        }\n      } else {\n        sourceHeight = height - sourceY - (options.bottom || 0)\n      }\n      destWidth = sourceWidth\n      destHeight = sourceHeight\n    }\n    maxWidth = options.maxWidth\n    maxHeight = options.maxHeight\n    minWidth = options.minWidth\n    minHeight = options.minHeight\n    if (useCanvas && maxWidth && maxHeight && options.crop) {\n      destWidth = maxWidth\n      destHeight = maxHeight\n      tmp = sourceWidth / sourceHeight - maxWidth / maxHeight\n      if (tmp < 0) {\n        sourceHeight = (maxHeight * sourceWidth) / maxWidth\n        if (options.top === undefined && options.bottom === undefined) {\n          sourceY = (height - sourceHeight) / 2\n        }\n      } else if (tmp > 0) {\n        sourceWidth = (maxWidth * sourceHeight) / maxHeight\n        if (options.left === undefined && options.right === undefined) {\n          sourceX = (width - sourceWidth) / 2\n        }\n      }\n    } else {\n      if (options.contain || options.cover) {\n        minWidth = maxWidth = maxWidth || minWidth\n        minHeight = maxHeight = maxHeight || minHeight\n      }\n      if (options.cover) {\n        scaleDown()\n        scaleUp()\n      } else {\n        scaleUp()\n        scaleDown()\n      }\n    }\n    if (useCanvas) {\n      pixelRatio = options.pixelRatio\n      if (pixelRatio > 1) {\n        canvas.style.width = destWidth + 'px'\n        canvas.style.height = destHeight + 'px'\n        destWidth *= pixelRatio\n        destHeight *= pixelRatio\n        canvas.getContext('2d').scale(pixelRatio, pixelRatio)\n      }\n      downsamplingRatio = options.downsamplingRatio\n      if (\n        downsamplingRatio > 0 &&\n        downsamplingRatio < 1 &&\n        destWidth < sourceWidth &&\n        destHeight < sourceHeight\n      ) {\n        while (sourceWidth * downsamplingRatio > destWidth) {\n          canvas.width = sourceWidth * downsamplingRatio\n          canvas.height = sourceHeight * downsamplingRatio\n          loadImage.renderImageToCanvas(\n            canvas,\n            img,\n            sourceX,\n            sourceY,\n            sourceWidth,\n            sourceHeight,\n            0,\n            0,\n            canvas.width,\n            canvas.height,\n            options\n          )\n          sourceX = 0\n          sourceY = 0\n          sourceWidth = canvas.width\n          sourceHeight = canvas.height\n          // eslint-disable-next-line no-param-reassign\n          img = document.createElement('canvas')\n          img.width = sourceWidth\n          img.height = sourceHeight\n          loadImage.renderImageToCanvas(\n            img,\n            canvas,\n            0,\n            0,\n            sourceWidth,\n            sourceHeight,\n            0,\n            0,\n            sourceWidth,\n            sourceHeight,\n            options\n          )\n        }\n      }\n      canvas.width = destWidth\n      canvas.height = destHeight\n      loadImage.transformCoordinates(canvas, options)\n      return loadImage.renderImageToCanvas(\n        canvas,\n        img,\n        sourceX,\n        sourceY,\n        sourceWidth,\n        sourceHeight,\n        0,\n        0,\n        destWidth,\n        destHeight,\n        options\n      )\n    }\n    img.width = destWidth\n    img.height = destHeight\n    return img\n  }\n})\n", "/*\n * JavaScript Load Image Exif Parser\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require, DataView */\n\n/* eslint-disable no-console */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image', './load-image-meta'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'), require('./load-image-meta'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  /**\n   * Exif tag map\n   *\n   * @name ExifMap\n   * @class\n   * @param {number} tagCode Private IFD tag code\n   */\n  function ExifMap(tagCode) {\n    if (tagCode) {\n      Object.defineProperty(this, 'map', {\n        value: this.privateIFDs[tagCode].map\n      })\n      Object.defineProperty(this, 'tags', {\n        value: (this.tags && this.tags[tagCode]) || {}\n      })\n    }\n  }\n\n  ExifMap.prototype.map = {\n    Orientation: 0x0112,\n    Thumbnail: 0x0201,\n    Exif: 0x8769,\n    GPSInfo: 0x8825,\n    Interoperability: 0xa005\n  }\n\n  ExifMap.prototype.privateIFDs = {\n    0x8769: { name: 'Exif', map: {} },\n    0x8825: { name: 'GPSInfo', map: {} },\n    0xa005: { name: 'Interoperability', map: {} }\n  }\n\n  /**\n   * Retrieves exif tag value\n   *\n   * @param {number|string} id Exif tag code or name\n   * @returns {object} Exif tag value\n   */\n  ExifMap.prototype.get = function (id) {\n    return this[id] || this[this.map[id]]\n  }\n\n  /**\n   * Returns the Exif Thumbnail data as Blob.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} offset Thumbnail data offset\n   * @param {number} length Thumbnail data length\n   * @returns {undefined|Blob} Returns the Thumbnail Blob or undefined\n   */\n  function getExifThumbnail(dataView, offset, length) {\n    if (!length || offset + length > dataView.byteLength) {\n      console.log('Invalid Exif data: Invalid thumbnail data.')\n      return\n    }\n    return new Blob([dataView.buffer.slice(offset, offset + length)], {\n      type: 'image/jpeg'\n    })\n  }\n\n  var ExifTagTypes = {\n    // byte, 8-bit unsigned int:\n    1: {\n      getValue: function (dataView, dataOffset) {\n        return dataView.getUint8(dataOffset)\n      },\n      size: 1\n    },\n    // ascii, 8-bit byte:\n    2: {\n      getValue: function (dataView, dataOffset) {\n        return String.fromCharCode(dataView.getUint8(dataOffset))\n      },\n      size: 1,\n      ascii: true\n    },\n    // short, 16 bit int:\n    3: {\n      getValue: function (dataView, dataOffset, littleEndian) {\n        return dataView.getUint16(dataOffset, littleEndian)\n      },\n      size: 2\n    },\n    // long, 32 bit int:\n    4: {\n      getValue: function (dataView, dataOffset, littleEndian) {\n        return dataView.getUint32(dataOffset, littleEndian)\n      },\n      size: 4\n    },\n    // rational = two long values, first is numerator, second is denominator:\n    5: {\n      getValue: function (dataView, dataOffset, littleEndian) {\n        return (\n          dataView.getUint32(dataOffset, littleEndian) /\n          dataView.getUint32(dataOffset + 4, littleEndian)\n        )\n      },\n      size: 8\n    },\n    // slong, 32 bit signed int:\n    9: {\n      getValue: function (dataView, dataOffset, littleEndian) {\n        return dataView.getInt32(dataOffset, littleEndian)\n      },\n      size: 4\n    },\n    // srational, two slongs, first is numerator, second is denominator:\n    10: {\n      getValue: function (dataView, dataOffset, littleEndian) {\n        return (\n          dataView.getInt32(dataOffset, littleEndian) /\n          dataView.getInt32(dataOffset + 4, littleEndian)\n        )\n      },\n      size: 8\n    }\n  }\n  // undefined, 8-bit byte, value depending on field:\n  ExifTagTypes[7] = ExifTagTypes[1]\n\n  /**\n   * Returns Exif tag value.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} tiffOffset TIFF offset\n   * @param {number} offset Tag offset\n   * @param {number} type Tag type\n   * @param {number} length Tag length\n   * @param {boolean} littleEndian Little endian encoding\n   * @returns {object} Tag value\n   */\n  function getExifValue(\n    dataView,\n    tiffOffset,\n    offset,\n    type,\n    length,\n    littleEndian\n  ) {\n    var tagType = ExifTagTypes[type]\n    var tagSize\n    var dataOffset\n    var values\n    var i\n    var str\n    var c\n    if (!tagType) {\n      console.log('Invalid Exif data: Invalid tag type.')\n      return\n    }\n    tagSize = tagType.size * length\n    // Determine if the value is contained in the dataOffset bytes,\n    // or if the value at the dataOffset is a pointer to the actual data:\n    dataOffset =\n      tagSize > 4\n        ? tiffOffset + dataView.getUint32(offset + 8, littleEndian)\n        : offset + 8\n    if (dataOffset + tagSize > dataView.byteLength) {\n      console.log('Invalid Exif data: Invalid data offset.')\n      return\n    }\n    if (length === 1) {\n      return tagType.getValue(dataView, dataOffset, littleEndian)\n    }\n    values = []\n    for (i = 0; i < length; i += 1) {\n      values[i] = tagType.getValue(\n        dataView,\n        dataOffset + i * tagType.size,\n        littleEndian\n      )\n    }\n    if (tagType.ascii) {\n      str = ''\n      // Concatenate the chars:\n      for (i = 0; i < values.length; i += 1) {\n        c = values[i]\n        // Ignore the terminating NULL byte(s):\n        if (c === '\\u0000') {\n          break\n        }\n        str += c\n      }\n      return str\n    }\n    return values\n  }\n\n  /**\n   * Parses Exif tags.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} tiffOffset TIFF offset\n   * @param {number} dirOffset Directory offset\n   * @param {boolean} littleEndian Little endian encoding\n   * @param {ExifMap} tags Map to store parsed exif tags\n   * @param {ExifMap} tagOffsets Map to store parsed exif tag offsets\n   * @param {object} includeTags Map of tags to include\n   * @param {object} excludeTags Map of tags to exclude\n   * @returns {number} Next directory offset\n   */\n  function parseExifTags(\n    dataView,\n    tiffOffset,\n    dirOffset,\n    littleEndian,\n    tags,\n    tagOffsets,\n    includeTags,\n    excludeTags\n  ) {\n    var tagsNumber, dirEndOffset, i, tagOffset, tagNumber, tagValue\n    if (dirOffset + 6 > dataView.byteLength) {\n      console.log('Invalid Exif data: Invalid directory offset.')\n      return\n    }\n    tagsNumber = dataView.getUint16(dirOffset, littleEndian)\n    dirEndOffset = dirOffset + 2 + 12 * tagsNumber\n    if (dirEndOffset + 4 > dataView.byteLength) {\n      console.log('Invalid Exif data: Invalid directory size.')\n      return\n    }\n    for (i = 0; i < tagsNumber; i += 1) {\n      tagOffset = dirOffset + 2 + 12 * i\n      tagNumber = dataView.getUint16(tagOffset, littleEndian)\n      if (includeTags && !includeTags[tagNumber]) continue\n      if (excludeTags && excludeTags[tagNumber] === true) continue\n      tagValue = getExifValue(\n        dataView,\n        tiffOffset,\n        tagOffset,\n        dataView.getUint16(tagOffset + 2, littleEndian), // tag type\n        dataView.getUint32(tagOffset + 4, littleEndian), // tag length\n        littleEndian\n      )\n      tags[tagNumber] = tagValue\n      if (tagOffsets) {\n        tagOffsets[tagNumber] = tagOffset\n      }\n    }\n    // Return the offset to the next directory:\n    return dataView.getUint32(dirEndOffset, littleEndian)\n  }\n\n  /**\n   * Parses Private IFD tags.\n   *\n   * @param {object} data Data object to store exif tags and offsets\n   * @param {number} tagCode Private IFD tag code\n   * @param {DataView} dataView Data view interface\n   * @param {number} tiffOffset TIFF offset\n   * @param {boolean} littleEndian Little endian encoding\n   * @param {object} includeTags Map of tags to include\n   * @param {object} excludeTags Map of tags to exclude\n   */\n  function parseExifPrivateIFD(\n    data,\n    tagCode,\n    dataView,\n    tiffOffset,\n    littleEndian,\n    includeTags,\n    excludeTags\n  ) {\n    var dirOffset = data.exif[tagCode]\n    if (dirOffset) {\n      data.exif[tagCode] = new ExifMap(tagCode)\n      if (data.exifOffsets) {\n        data.exifOffsets[tagCode] = new ExifMap(tagCode)\n      }\n      parseExifTags(\n        dataView,\n        tiffOffset,\n        tiffOffset + dirOffset,\n        littleEndian,\n        data.exif[tagCode],\n        data.exifOffsets && data.exifOffsets[tagCode],\n        includeTags && includeTags[tagCode],\n        excludeTags && excludeTags[tagCode]\n      )\n    }\n  }\n\n  loadImage.parseExifData = function (dataView, offset, length, data, options) {\n    if (options.disableExif) {\n      return\n    }\n    var includeTags = options.includeExifTags\n    var excludeTags = options.excludeExifTags || {\n      0x8769: {\n        // ExifIFDPointer\n        0x927c: true // MakerNote\n      }\n    }\n    var tiffOffset = offset + 10\n    var littleEndian\n    var dirOffset\n    var privateIFDs\n    // Check for the ASCII code for \"Exif\" (0x45786966):\n    if (dataView.getUint32(offset + 4) !== 0x45786966) {\n      // No Exif data, might be XMP data instead\n      return\n    }\n    if (tiffOffset + 8 > dataView.byteLength) {\n      console.log('Invalid Exif data: Invalid segment size.')\n      return\n    }\n    // Check for the two null bytes:\n    if (dataView.getUint16(offset + 8) !== 0x0000) {\n      console.log('Invalid Exif data: Missing byte alignment offset.')\n      return\n    }\n    // Check the byte alignment:\n    switch (dataView.getUint16(tiffOffset)) {\n      case 0x4949:\n        littleEndian = true\n        break\n      case 0x4d4d:\n        littleEndian = false\n        break\n      default:\n        console.log('Invalid Exif data: Invalid byte alignment marker.')\n        return\n    }\n    // Check for the TIFF tag marker (0x002A):\n    if (dataView.getUint16(tiffOffset + 2, littleEndian) !== 0x002a) {\n      console.log('Invalid Exif data: Missing TIFF marker.')\n      return\n    }\n    // Retrieve the directory offset bytes, usually 0x00000008 or 8 decimal:\n    dirOffset = dataView.getUint32(tiffOffset + 4, littleEndian)\n    // Create the exif object to store the tags:\n    data.exif = new ExifMap()\n    if (!options.disableExifOffsets) {\n      data.exifOffsets = new ExifMap()\n      data.exifTiffOffset = tiffOffset\n      data.exifLittleEndian = littleEndian\n    }\n    // Parse the tags of the main image directory and retrieve the\n    // offset to the next directory, usually the thumbnail directory:\n    dirOffset = parseExifTags(\n      dataView,\n      tiffOffset,\n      tiffOffset + dirOffset,\n      littleEndian,\n      data.exif,\n      data.exifOffsets,\n      includeTags,\n      excludeTags\n    )\n    if (dirOffset && !options.disableExifThumbnail) {\n      dirOffset = parseExifTags(\n        dataView,\n        tiffOffset,\n        tiffOffset + dirOffset,\n        littleEndian,\n        data.exif,\n        data.exifOffsets,\n        includeTags,\n        excludeTags\n      )\n      // Check for JPEG Thumbnail offset:\n      if (data.exif[0x0201] && data.exif[0x0202]) {\n        data.exif[0x0201] = getExifThumbnail(\n          dataView,\n          tiffOffset + data.exif[0x0201],\n          data.exif[0x0202] // Thumbnail data length\n        )\n      }\n    }\n    privateIFDs = Object.keys(data.exif.privateIFDs)\n    privateIFDs.forEach(function (tagCode) {\n      parseExifPrivateIFD(\n        data,\n        tagCode,\n        dataView,\n        tiffOffset,\n        littleEndian,\n        includeTags,\n        excludeTags\n      )\n    })\n  }\n\n  // Registers the Exif parser for the APP1 JPEG meta data segment:\n  loadImage.metaDataParsers.jpeg[0xffe1].push(loadImage.parseExifData)\n\n  loadImage.exifWriters = {\n    // Orientation writer:\n    0x0112: function (buffer, data, value) {\n      var view = new DataView(buffer, data.exifOffsets[0x0112] + 8, 2)\n      view.setUint16(0, value, data.exifLittleEndian)\n      return buffer\n    }\n  }\n\n  loadImage.writeExifData = function (buffer, data, id, value) {\n    loadImage.exifWriters[data.exif.map[id]](buffer, data, value)\n  }\n\n  loadImage.ExifMap = ExifMap\n\n  // Adds the following properties to the parseMetaData callback data:\n  // - exif: The parsed Exif tags\n  // - exifOffsets: The parsed Exif tag offsets\n  // - exifTiffOffset: TIFF header offset (used for offset pointers)\n  // - exifLittleEndian: little endian order if true, big endian if false\n\n  // Adds the following options to the parseMetaData method:\n  // - disableExif: Disables Exif parsing when true.\n  // - disableExifThumbnail: Disables parsing of Thumbnail data when true.\n  // - disableExifOffsets: Disables storing Exif tag offsets when true.\n  // - includeExifTags: A map of Exif tags to include for parsing.\n  // - excludeExifTags: A map of Exif tags to exclude from parsing.\n})\n", "/*\n * JavaScript Load Image IPTC Parser\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * Copyright 2018, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require, DataView */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image', './load-image-meta'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'), require('./load-image-meta'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  /**\n   * IPTC tag map\n   *\n   * @name IptcMap\n   * @class\n   */\n  function IptcMap() {}\n\n  IptcMap.prototype.map = {\n    ObjectName: 5\n  }\n\n  IptcMap.prototype.types = {\n    0: 'Uint16', // ApplicationRecordVersion\n    200: 'Uint16', // ObjectPreviewFileFormat\n    201: 'Uint16', // ObjectPreviewFileVersion\n    202: 'binary' // ObjectPreviewData\n  }\n\n  /**\n   * Retrieves IPTC tag value\n   *\n   * @param {number|string} id IPTC tag code or name\n   * @returns {object} IPTC tag value\n   */\n  IptcMap.prototype.get = function (id) {\n    return this[id] || this[this.map[id]]\n  }\n\n  /**\n   * Retrieves string for the given DataView and range\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} offset Offset start\n   * @param {number} length Offset length\n   * @returns {string} String value\n   */\n  function getStringValue(dataView, offset, length) {\n    var outstr = ''\n    var end = offset + length\n    for (var n = offset; n < end; n += 1) {\n      outstr += String.fromCharCode(dataView.getUint8(n))\n    }\n    return outstr\n  }\n\n  /**\n   * Retrieves tag value for the given DataView and range\n   *\n   * @param {number} tagCode Private IFD tag code\n   * @param {IptcMap} map IPTC tag map\n   * @param {DataView} dataView Data view interface\n   * @param {number} offset Range start\n   * @param {number} length Range length\n   * @returns {object} Tag value\n   */\n  function getTagValue(tagCode, map, dataView, offset, length) {\n    if (map.types[tagCode] === 'binary') {\n      return new Blob([dataView.buffer.slice(offset, offset + length)])\n    }\n    if (map.types[tagCode] === 'Uint16') {\n      return dataView.getUint16(offset)\n    }\n    return getStringValue(dataView, offset, length)\n  }\n\n  /**\n   * Combines IPTC value with existing ones.\n   *\n   * @param {object} value Existing IPTC field value\n   * @param {object} newValue New IPTC field value\n   * @returns {object} Resulting IPTC field value\n   */\n  function combineTagValues(value, newValue) {\n    if (value === undefined) return newValue\n    if (value instanceof Array) {\n      value.push(newValue)\n      return value\n    }\n    return [value, newValue]\n  }\n\n  /**\n   * Parses IPTC tags.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} segmentOffset Segment offset\n   * @param {number} segmentLength Segment length\n   * @param {object} data Data export object\n   * @param {object} includeTags Map of tags to include\n   * @param {object} excludeTags Map of tags to exclude\n   */\n  function parseIptcTags(\n    dataView,\n    segmentOffset,\n    segmentLength,\n    data,\n    includeTags,\n    excludeTags\n  ) {\n    var value, tagSize, tagCode\n    var segmentEnd = segmentOffset + segmentLength\n    var offset = segmentOffset\n    while (offset < segmentEnd) {\n      if (\n        dataView.getUint8(offset) === 0x1c && // tag marker\n        dataView.getUint8(offset + 1) === 0x02 // record number, only handles v2\n      ) {\n        tagCode = dataView.getUint8(offset + 2)\n        if (\n          (!includeTags || includeTags[tagCode]) &&\n          (!excludeTags || !excludeTags[tagCode])\n        ) {\n          tagSize = dataView.getInt16(offset + 3)\n          value = getTagValue(tagCode, data.iptc, dataView, offset + 5, tagSize)\n          data.iptc[tagCode] = combineTagValues(data.iptc[tagCode], value)\n          if (data.iptcOffsets) {\n            data.iptcOffsets[tagCode] = offset\n          }\n        }\n      }\n      offset += 1\n    }\n  }\n\n  /**\n   * Tests if field segment starts at offset.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} offset Segment offset\n   * @returns {boolean} True if '8BIM<EOT><EOT>' exists at offset\n   */\n  function isSegmentStart(dataView, offset) {\n    return (\n      dataView.getUint32(offset) === 0x3842494d && // Photoshop segment start\n      dataView.getUint16(offset + 4) === 0x0404 // IPTC segment start\n    )\n  }\n\n  /**\n   * Returns header length.\n   *\n   * @param {DataView} dataView Data view interface\n   * @param {number} offset Segment offset\n   * @returns {number} Header length\n   */\n  function getHeaderLength(dataView, offset) {\n    var length = dataView.getUint8(offset + 7)\n    if (length % 2 !== 0) length += 1\n    // Check for pre photoshop 6 format\n    if (length === 0) {\n      // Always 4\n      length = 4\n    }\n    return length\n  }\n\n  loadImage.parseIptcData = function (dataView, offset, length, data, options) {\n    if (options.disableIptc) {\n      return\n    }\n    var markerLength = offset + length\n    while (offset + 8 < markerLength) {\n      if (isSegmentStart(dataView, offset)) {\n        var headerLength = getHeaderLength(dataView, offset)\n        var segmentOffset = offset + 8 + headerLength\n        if (segmentOffset > markerLength) {\n          // eslint-disable-next-line no-console\n          console.log('Invalid IPTC data: Invalid segment offset.')\n          break\n        }\n        var segmentLength = dataView.getUint16(offset + 6 + headerLength)\n        if (offset + segmentLength > markerLength) {\n          // eslint-disable-next-line no-console\n          console.log('Invalid IPTC data: Invalid segment size.')\n          break\n        }\n        // Create the iptc object to store the tags:\n        data.iptc = new IptcMap()\n        if (!options.disableIptcOffsets) {\n          data.iptcOffsets = new IptcMap()\n        }\n        parseIptcTags(\n          dataView,\n          segmentOffset,\n          segmentLength,\n          data,\n          options.includeIptcTags,\n          options.excludeIptcTags || { 202: true } // ObjectPreviewData\n        )\n        return\n      }\n      // eslint-disable-next-line no-param-reassign\n      offset += 1\n    }\n  }\n\n  // Registers this IPTC parser for the APP13 JPEG meta data segment:\n  loadImage.metaDataParsers.jpeg[0xffed].push(loadImage.parseIptcData)\n\n  loadImage.IptcMap = IptcMap\n\n  // Adds the following properties to the parseMetaData callback data:\n  // - iptc: The iptc tags, parsed by the parseIptcData method\n\n  // Adds the following options to the parseMetaData method:\n  // - disableIptc: Disables IPTC parsing when true.\n  // - disableIptcOffsets: Disables storing IPTC tag offsets when true.\n  // - includeIptcTags: A map of IPTC tags to include for parsing.\n  // - excludeIptcTags: A map of IPTC tags to exclude from parsing.\n})\n", "/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst { setLogging } = require('./utils/log');\n\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract,\n};\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return Promise.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return Promise.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new Promise(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList) {\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList)\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n", "module.exports = function(module) {\n\tif (!module.webpackPolyfill) {\n\t\tmodule.deprecate = function() {};\n\t\tmodule.paths = [];\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n", "const createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\n\nlet schedulerCounter = 0;\n\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n\n  schedulerCounter += 1;\n\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n\n  const queue = (action, payload) => (\n    new Promise((resolve, reject) => {\n      const job = createJob({ action, payload });\n      jobQueue.push(async (w) => {\n        jobQueue.shift();\n        runningWorkers[w.id] = job;\n        try {\n          resolve(await w[action].apply(this, [...payload, job.id]));\n        } catch (err) {\n          reject(err);\n        } finally {\n          delete runningWorkers[w.id];\n          dequeue();\n        }\n      });\n      log(`[${id}]: Add ${job.id} to JobQueue`);\n      log(`[${id}]: JobQueue length=${jobQueue.length}`);\n      dequeue();\n    })\n  );\n\n  const addWorker = (w) => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n\n  const terminate = async () => {\n    Object.keys(workers).forEach(async (wid) => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers,\n  };\n};\n", "const isBrowser = require('./getEnvironment')('type') === 'browser';\nconst resolveURL = isBrowser ? require('resolve-url') : s => s; // eslint-disable-line\n\nmodule.exports = (options) => {\n  const opts = { ...options };\n  ['corePath', 'workerPath', 'langPath'].forEach((key) => {\n    if (typeof options[key] !== 'undefined') {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};\n", "const isElectron = require('is-electron');\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (isElectron()) {\n    env.type = 'electron';\n  } else if (typeof window === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n", "// https://github.com/electron/electron/issues/2288\nfunction isElectron() {\n    // Renderer process\n    if (typeof window !== 'undefined' && typeof window.process === 'object' && window.process.type === 'renderer') {\n        return true;\n    }\n\n    // Main process\n    if (typeof process !== 'undefined' && typeof process.versions === 'object' && !!process.versions.electron) {\n        return true;\n    }\n\n    // Detect the user agent when the `nodeIntegration` option is set to true\n    if (typeof navigator === 'object' && typeof navigator.userAgent === 'string' && navigator.userAgent.indexOf('Electron') >= 0) {\n        return true;\n    }\n\n    return false;\n}\n\nmodule.exports = isElectron;\n", "/**\n * In the recognition result of tesseract, there\n * is a deep JSON object for details, it has around\n *\n * The result of dump.js is a big JSON tree\n * which can be easily serialized (for instance\n * to be sent from a webworker to the main app\n * or through Node's IPC), but we want\n * a (circular) DOM-like interface for walking\n * through the data.\n *\n * @fileoverview DOM-like interface for walking through data\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <j<PERSON><PERSON><PERSON><EMAIL>>\n */\n\nmodule.exports = (page) => {\n  const blocks = [];\n  const paragraphs = [];\n  const lines = [];\n  const words = [];\n  const symbols = [];\n\n  page.blocks.forEach((block) => {\n    block.paragraphs.forEach((paragraph) => {\n      paragraph.lines.forEach((line) => {\n        line.words.forEach((word) => {\n          word.symbols.forEach((sym) => {\n            symbols.push({\n              ...sym, page, block, paragraph, line, word,\n            });\n          });\n          words.push({\n            ...word, page, block, paragraph, line,\n          });\n        });\n        lines.push({\n          ...line, page, block, paragraph,\n        });\n      });\n      paragraphs.push({\n        ...paragraph, page, block,\n      });\n    });\n    blocks.push({\n      ...block, page,\n    });\n  });\n\n  return {\n    ...page, blocks, paragraphs, lines, words, symbols,\n  };\n};\n", "const OEM = require('./OEM');\n\nmodule.exports = {\n  defaultOEM: OEM.DEFAULT,\n};\n", "/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\n\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage,\n};\n", "const resolveURL = require('resolve-url');\nconst { version, dependencies } = require('../../../package.json');\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: (typeof process !== 'undefined' && process.env.TESS_ENV === 'development')\n    ? resolveURL(`/dist/worker.dev.js?nocache=${Math.random().toString(36).slice(3)}`)\n    : `https://unpkg.com/tesseract.js@v${version}/dist/worker.min.js`,\n  /*\n   * If browser doesn't support WebAssembly,\n   * load ASM version instead\n   */\n  corePath: `https://unpkg.com/tesseract.js-core@v${dependencies['tesseract.js-core'].substring(1)}/tesseract-core.${typeof WebAssembly === 'object' ? 'wasm' : 'asm'}.js`,\n};\n", "module.exports = {\n  /*\n   * default path for downloading *.traineddata\n   */\n  langPath: 'https://tessdata.projectnaptha.com/4.0.0',\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {},\n};\n", "/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({ workerPath, workerBlobURL }) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript',\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n\n  return worker;\n};\n", "/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = (worker) => {\n  worker.terminate();\n};\n", "module.exports = (worker, handler) => {\n  worker.onmessage = ({ data }) => { // eslint-disable-line\n    handler(data);\n  };\n};\n", "/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};\n", "const resolveURL = require('resolve-url');\nconst blueimpLoadImage = require('blueimp-load-image');\n\n/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nconst readFromBlobOrFile = (blob) => (\n  new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n      resolve(fileReader.result);\n    };\n    fileReader.onerror = ({ target: { error: { code } } }) => {\n      reject(Error(`File could not be read! Code=${code}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n  })\n);\n\nconst fixOrientationFromUrlOrBlobOrFile = (blob) => (\n  new Promise((resolve) => {\n    blueimpLoadImage(\n      blob,\n      (img) => img.toBlob(resolve),\n      {\n        orientation: true,\n        canvas: true,\n      },\n    );\n  })\n);\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async (image) => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n\n  if (typeof image === 'string') {\n    if (image.endsWith('.pbm')) {\n      const resp = await fetch(resolveURL(image));\n      data = await resp.arrayBuffer();\n    } else {\n      let img = image;\n      // If not Base64 Image\n      if (!/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n        img = resolveURL(image);\n      }\n      data = await readFromBlobOrFile(\n        await fixOrientationFromUrlOrBlobOrFile(img),\n      );\n    }\n  } else if (image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise((resolve) => {\n        image.toBlob(async (blob) => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (image instanceof File || image instanceof Blob) {\n    let img = image;\n    if (!image.name.endsWith('.pbm')) {\n      img = await fixOrientationFromUrlOrBlobOrFile(img);\n    }\n    data = await readFromBlobOrFile(img);\n  }\n\n  return new Uint8Array(data);\n};\n\nmodule.exports = loadImage;\n", "/* global module, require */\n\nmodule.exports = require('./load-image')\n\nrequire('./load-image-scale')\nrequire('./load-image-meta')\nrequire('./load-image-fetch')\nrequire('./load-image-exif')\nrequire('./load-image-exif-map')\nrequire('./load-image-iptc')\nrequire('./load-image-iptc-map')\nrequire('./load-image-orientation')\n", "/*\n * JavaScript Load Image Fetch\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2017, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  if (typeof fetch !== 'undefined' && typeof Request !== 'undefined') {\n    loadImage.fetchBlob = function (url, callback, options) {\n      fetch(new Request(url, options))\n        .then(function (response) {\n          return response.blob()\n        })\n        .then(callback)\n        .catch(function (err) {\n          callback(null, err)\n        })\n    }\n  } else if (\n    // Check for XHR2 support:\n    typeof XMLHttpRequest !== 'undefined' &&\n    typeof ProgressEvent !== 'undefined'\n  ) {\n    loadImage.fetchBlob = function (url, callback, options) {\n      // eslint-disable-next-line no-param-reassign\n      options = options || {}\n      var req = new XMLHttpRequest()\n      req.open(options.method || 'GET', url)\n      if (options.headers) {\n        Object.keys(options.headers).forEach(function (key) {\n          req.setRequestHeader(key, options.headers[key])\n        })\n      }\n      req.withCredentials = options.credentials === 'include'\n      req.responseType = 'blob'\n      req.onload = function () {\n        callback(req.response)\n      }\n      req.onerror = req.onabort = req.ontimeout = function (err) {\n        callback(null, err)\n      }\n      req.send(options.body)\n    }\n  }\n})\n", "/*\n * JavaScript Load Image Exif Map\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Exif tags mapping based on\n * https://github.com/jseidelin/exif-js\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image', './load-image-exif'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'), require('./load-image-exif'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  var ExifMapProto = loadImage.ExifMap.prototype\n\n  ExifMapProto.tags = {\n    // =================\n    // TIFF tags (IFD0):\n    // =================\n    0x0100: 'ImageWidth',\n    0x0101: 'ImageHeight',\n    0x0102: 'BitsPerSample',\n    0x0103: 'Compression',\n    0x0106: 'PhotometricInterpretation',\n    0x0112: 'Orientation',\n    0x0115: 'SamplesPerPixel',\n    0x011c: 'PlanarConfiguration',\n    0x0212: 'YCbCrSubSampling',\n    0x0213: 'YCbCrPositioning',\n    0x011a: 'XResolution',\n    0x011b: 'YResolution',\n    0x0128: 'ResolutionUnit',\n    0x0111: 'StripOffsets',\n    0x0116: 'RowsPerStrip',\n    0x0117: 'StripByteCounts',\n    0x0201: 'JPEGInterchangeFormat',\n    0x0202: 'JPEGInterchangeFormatLength',\n    0x012d: 'TransferFunction',\n    0x013e: 'WhitePoint',\n    0x013f: 'PrimaryChromaticities',\n    0x0211: 'YCbCrCoefficients',\n    0x0214: 'ReferenceBlackWhite',\n    0x0132: 'DateTime',\n    0x010e: 'ImageDescription',\n    0x010f: 'Make',\n    0x0110: 'Model',\n    0x0131: 'Software',\n    0x013b: 'Artist',\n    0x8298: 'Copyright',\n    0x8769: {\n      // ExifIFDPointer\n      0x9000: 'ExifVersion', // EXIF version\n      0xa000: 'FlashpixVersion', // Flashpix format version\n      0xa001: 'ColorSpace', // Color space information tag\n      0xa002: 'PixelXDimension', // Valid width of meaningful image\n      0xa003: 'PixelYDimension', // Valid height of meaningful image\n      0xa500: 'Gamma',\n      0x9101: 'ComponentsConfiguration', // Information about channels\n      0x9102: 'CompressedBitsPerPixel', // Compressed bits per pixel\n      0x927c: 'MakerNote', // Any desired information written by the manufacturer\n      0x9286: 'UserComment', // Comments by user\n      0xa004: 'RelatedSoundFile', // Name of related sound file\n      0x9003: 'DateTimeOriginal', // Date and time when the original image was generated\n      0x9004: 'DateTimeDigitized', // Date and time when the image was stored digitally\n      0x9290: 'SubSecTime', // Fractions of seconds for DateTime\n      0x9291: 'SubSecTimeOriginal', // Fractions of seconds for DateTimeOriginal\n      0x9292: 'SubSecTimeDigitized', // Fractions of seconds for DateTimeDigitized\n      0x829a: 'ExposureTime', // Exposure time (in seconds)\n      0x829d: 'FNumber',\n      0x8822: 'ExposureProgram', // Exposure program\n      0x8824: 'SpectralSensitivity', // Spectral sensitivity\n      0x8827: 'PhotographicSensitivity', // EXIF 2.3, ISOSpeedRatings in EXIF 2.2\n      0x8828: 'OECF', // Optoelectric conversion factor\n      0x8830: 'SensitivityType',\n      0x8831: 'StandardOutputSensitivity',\n      0x8832: 'RecommendedExposureIndex',\n      0x8833: 'ISOSpeed',\n      0x8834: 'ISOSpeedLatitudeyyy',\n      0x8835: 'ISOSpeedLatitudezzz',\n      0x9201: 'ShutterSpeedValue', // Shutter speed\n      0x9202: 'ApertureValue', // Lens aperture\n      0x9203: 'BrightnessValue', // Value of brightness\n      0x9204: 'ExposureBias', // Exposure bias\n      0x9205: 'MaxApertureValue', // Smallest F number of lens\n      0x9206: 'SubjectDistance', // Distance to subject in meters\n      0x9207: 'MeteringMode', // Metering mode\n      0x9208: 'LightSource', // Kind of light source\n      0x9209: 'Flash', // Flash status\n      0x9214: 'SubjectArea', // Location and area of main subject\n      0x920a: 'FocalLength', // Focal length of the lens in mm\n      0xa20b: 'FlashEnergy', // Strobe energy in BCPS\n      0xa20c: 'SpatialFrequencyResponse',\n      0xa20e: 'FocalPlaneXResolution', // Number of pixels in width direction per FPRUnit\n      0xa20f: 'FocalPlaneYResolution', // Number of pixels in height direction per FPRUnit\n      0xa210: 'FocalPlaneResolutionUnit', // Unit for measuring the focal plane resolution\n      0xa214: 'SubjectLocation', // Location of subject in image\n      0xa215: 'ExposureIndex', // Exposure index selected on camera\n      0xa217: 'SensingMethod', // Image sensor type\n      0xa300: 'FileSource', // Image source (3 == DSC)\n      0xa301: 'SceneType', // Scene type (1 == directly photographed)\n      0xa302: 'CFAPattern', // Color filter array geometric pattern\n      0xa401: 'CustomRendered', // Special processing\n      0xa402: 'ExposureMode', // Exposure mode\n      0xa403: 'WhiteBalance', // 1 = auto white balance, 2 = manual\n      0xa404: 'DigitalZoomRatio', // Digital zoom ratio\n      0xa405: 'FocalLengthIn35mmFilm',\n      0xa406: 'SceneCaptureType', // Type of scene\n      0xa407: 'GainControl', // Degree of overall image gain adjustment\n      0xa408: 'Contrast', // Direction of contrast processing applied by camera\n      0xa409: 'Saturation', // Direction of saturation processing applied by camera\n      0xa40a: 'Sharpness', // Direction of sharpness processing applied by camera\n      0xa40b: 'DeviceSettingDescription',\n      0xa40c: 'SubjectDistanceRange', // Distance to subject\n      0xa420: 'ImageUniqueID', // Identifier assigned uniquely to each image\n      0xa430: 'CameraOwnerName',\n      0xa431: 'BodySerialNumber',\n      0xa432: 'LensSpecification',\n      0xa433: 'LensMake',\n      0xa434: 'LensModel',\n      0xa435: 'LensSerialNumber'\n    },\n    0x8825: {\n      // GPSInfoIFDPointer\n      0x0000: 'GPSVersionID',\n      0x0001: 'GPSLatitudeRef',\n      0x0002: 'GPSLatitude',\n      0x0003: 'GPSLongitudeRef',\n      0x0004: 'GPSLongitude',\n      0x0005: 'GPSAltitudeRef',\n      0x0006: 'GPSAltitude',\n      0x0007: 'GPSTimeStamp',\n      0x0008: 'GPSSatellites',\n      0x0009: 'GPSStatus',\n      0x000a: 'GPSMeasureMode',\n      0x000b: 'GPSDOP',\n      0x000c: 'GPSSpeedRef',\n      0x000d: 'GPSSpeed',\n      0x000e: 'GPSTrackRef',\n      0x000f: 'GPSTrack',\n      0x0010: 'GPSImgDirectionRef',\n      0x0011: 'GPSImgDirection',\n      0x0012: 'GPSMapDatum',\n      0x0013: 'GPSDestLatitudeRef',\n      0x0014: 'GPSDestLatitude',\n      0x0015: 'GPSDestLongitudeRef',\n      0x0016: 'GPSDestLongitude',\n      0x0017: 'GPSDestBearingRef',\n      0x0018: 'GPSDestBearing',\n      0x0019: 'GPSDestDistanceRef',\n      0x001a: 'GPSDestDistance',\n      0x001b: 'GPSProcessingMethod',\n      0x001c: 'GPSAreaInformation',\n      0x001d: 'GPSDateStamp',\n      0x001e: 'GPSDifferential',\n      0x001f: 'GPSHPositioningError'\n    },\n    0xa005: {\n      // InteroperabilityIFDPointer\n      0x0001: 'InteroperabilityIndex'\n    }\n  }\n\n  ExifMapProto.stringValues = {\n    ExposureProgram: {\n      0: 'Undefined',\n      1: 'Manual',\n      2: 'Normal program',\n      3: 'Aperture priority',\n      4: 'Shutter priority',\n      5: 'Creative program',\n      6: 'Action program',\n      7: 'Portrait mode',\n      8: 'Landscape mode'\n    },\n    MeteringMode: {\n      0: 'Unknown',\n      1: 'Average',\n      2: 'CenterWeightedAverage',\n      3: 'Spot',\n      4: 'MultiSpot',\n      5: 'Pattern',\n      6: 'Partial',\n      255: 'Other'\n    },\n    LightSource: {\n      0: 'Unknown',\n      1: 'Daylight',\n      2: 'Fluorescent',\n      3: 'Tungsten (incandescent light)',\n      4: 'Flash',\n      9: 'Fine weather',\n      10: 'Cloudy weather',\n      11: 'Shade',\n      12: 'Daylight fluorescent (D 5700 - 7100K)',\n      13: 'Day white fluorescent (N 4600 - 5400K)',\n      14: 'Cool white fluorescent (W 3900 - 4500K)',\n      15: 'White fluorescent (WW 3200 - 3700K)',\n      17: 'Standard light A',\n      18: 'Standard light B',\n      19: 'Standard light C',\n      20: 'D55',\n      21: 'D65',\n      22: 'D75',\n      23: 'D50',\n      24: 'ISO studio tungsten',\n      255: 'Other'\n    },\n    Flash: {\n      0x0000: 'Flash did not fire',\n      0x0001: 'Flash fired',\n      0x0005: 'Strobe return light not detected',\n      0x0007: 'Strobe return light detected',\n      0x0009: 'Flash fired, compulsory flash mode',\n      0x000d: 'Flash fired, compulsory flash mode, return light not detected',\n      0x000f: 'Flash fired, compulsory flash mode, return light detected',\n      0x0010: 'Flash did not fire, compulsory flash mode',\n      0x0018: 'Flash did not fire, auto mode',\n      0x0019: 'Flash fired, auto mode',\n      0x001d: 'Flash fired, auto mode, return light not detected',\n      0x001f: 'Flash fired, auto mode, return light detected',\n      0x0020: 'No flash function',\n      0x0041: 'Flash fired, red-eye reduction mode',\n      0x0045: 'Flash fired, red-eye reduction mode, return light not detected',\n      0x0047: 'Flash fired, red-eye reduction mode, return light detected',\n      0x0049: 'Flash fired, compulsory flash mode, red-eye reduction mode',\n      0x004d: 'Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected',\n      0x004f: 'Flash fired, compulsory flash mode, red-eye reduction mode, return light detected',\n      0x0059: 'Flash fired, auto mode, red-eye reduction mode',\n      0x005d: 'Flash fired, auto mode, return light not detected, red-eye reduction mode',\n      0x005f: 'Flash fired, auto mode, return light detected, red-eye reduction mode'\n    },\n    SensingMethod: {\n      1: 'Undefined',\n      2: 'One-chip color area sensor',\n      3: 'Two-chip color area sensor',\n      4: 'Three-chip color area sensor',\n      5: 'Color sequential area sensor',\n      7: 'Trilinear sensor',\n      8: 'Color sequential linear sensor'\n    },\n    SceneCaptureType: {\n      0: 'Standard',\n      1: 'Landscape',\n      2: 'Portrait',\n      3: 'Night scene'\n    },\n    SceneType: {\n      1: 'Directly photographed'\n    },\n    CustomRendered: {\n      0: 'Normal process',\n      1: 'Custom process'\n    },\n    WhiteBalance: {\n      0: 'Auto white balance',\n      1: 'Manual white balance'\n    },\n    GainControl: {\n      0: 'None',\n      1: 'Low gain up',\n      2: 'High gain up',\n      3: 'Low gain down',\n      4: 'High gain down'\n    },\n    Contrast: {\n      0: 'Normal',\n      1: 'Soft',\n      2: 'Hard'\n    },\n    Saturation: {\n      0: 'Normal',\n      1: 'Low saturation',\n      2: 'High saturation'\n    },\n    Sharpness: {\n      0: 'Normal',\n      1: 'Soft',\n      2: 'Hard'\n    },\n    SubjectDistanceRange: {\n      0: 'Unknown',\n      1: 'Macro',\n      2: 'Close view',\n      3: 'Distant view'\n    },\n    FileSource: {\n      3: 'DSC'\n    },\n    ComponentsConfiguration: {\n      0: '',\n      1: 'Y',\n      2: 'Cb',\n      3: 'Cr',\n      4: 'R',\n      5: 'G',\n      6: 'B'\n    },\n    Orientation: {\n      1: 'top-left',\n      2: 'top-right',\n      3: 'bottom-right',\n      4: 'bottom-left',\n      5: 'left-top',\n      6: 'right-top',\n      7: 'right-bottom',\n      8: 'left-bottom'\n    }\n  }\n\n  ExifMapProto.getText = function (name) {\n    var value = this.get(name)\n    switch (name) {\n      case 'LightSource':\n      case 'Flash':\n      case 'MeteringMode':\n      case 'ExposureProgram':\n      case 'SensingMethod':\n      case 'SceneCaptureType':\n      case 'SceneType':\n      case 'CustomRendered':\n      case 'WhiteBalance':\n      case 'GainControl':\n      case 'Contrast':\n      case 'Saturation':\n      case 'Sharpness':\n      case 'SubjectDistanceRange':\n      case 'FileSource':\n      case 'Orientation':\n        return this.stringValues[name][value]\n      case 'ExifVersion':\n      case 'FlashpixVersion':\n        if (!value) return\n        return String.fromCharCode(value[0], value[1], value[2], value[3])\n      case 'ComponentsConfiguration':\n        if (!value) return\n        return (\n          this.stringValues[name][value[0]] +\n          this.stringValues[name][value[1]] +\n          this.stringValues[name][value[2]] +\n          this.stringValues[name][value[3]]\n        )\n      case 'GPSVersionID':\n        if (!value) return\n        return value[0] + '.' + value[1] + '.' + value[2] + '.' + value[3]\n    }\n    return String(value)\n  }\n\n  ExifMapProto.getAll = function () {\n    var map = {}\n    var prop\n    var obj\n    var name\n    for (prop in this) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        obj = this[prop]\n        if (obj && obj.getAll) {\n          map[this.privateIFDs[prop].name] = obj.getAll()\n        } else {\n          name = this.tags[prop]\n          if (name) map[name] = this.getText(name)\n        }\n      }\n    }\n    return map\n  }\n\n  ExifMapProto.getName = function (tagCode) {\n    var name = this.tags[tagCode]\n    if (typeof name === 'object') return this.privateIFDs[tagCode].name\n    return name\n  }\n\n  // Extend the map of tag names to tag codes:\n  ;(function () {\n    var tags = ExifMapProto.tags\n    var prop\n    var privateIFD\n    var subTags\n    // Map the tag names to tags:\n    for (prop in tags) {\n      if (Object.prototype.hasOwnProperty.call(tags, prop)) {\n        privateIFD = ExifMapProto.privateIFDs[prop]\n        if (privateIFD) {\n          subTags = tags[prop]\n          for (prop in subTags) {\n            if (Object.prototype.hasOwnProperty.call(subTags, prop)) {\n              privateIFD.map[subTags[prop]] = Number(prop)\n            }\n          }\n        } else {\n          ExifMapProto.map[tags[prop]] = Number(prop)\n        }\n      }\n    }\n  })()\n})\n", "/*\n * JavaScript Load Image IPTC Map\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * Copyright 2018, <PERSON>\n *\n * IPTC tags mapping based on\n * https://iptc.org/standards/photo-metadata\n * https://exiftool.org/TagNames/IPTC.html\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image', './load-image-iptc'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(require('./load-image'), require('./load-image-iptc'))\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  var IptcMapProto = loadImage.IptcMap.prototype\n\n  IptcMapProto.tags = {\n    0: 'ApplicationRecordVersion',\n    3: 'ObjectTypeReference',\n    4: 'ObjectAttributeReference',\n    5: 'ObjectName',\n    7: 'EditStatus',\n    8: 'EditorialUpdate',\n    10: 'Urgency',\n    12: 'SubjectReference',\n    15: 'Category',\n    20: 'SupplementalCategories',\n    22: 'FixtureIdentifier',\n    25: 'Keywords',\n    26: 'ContentLocationCode',\n    27: 'ContentLocationName',\n    30: 'ReleaseDate',\n    35: 'ReleaseTime',\n    37: 'ExpirationDate',\n    38: 'ExpirationTime',\n    40: 'SpecialInstructions',\n    42: 'ActionAdvised',\n    45: 'ReferenceService',\n    47: 'ReferenceDate',\n    50: 'ReferenceNumber',\n    55: 'DateCreated',\n    60: 'TimeCreated',\n    62: 'DigitalCreationDate',\n    63: 'DigitalCreationTime',\n    65: 'OriginatingProgram',\n    70: 'ProgramVersion',\n    75: 'ObjectCycle',\n    80: 'Byline',\n    85: 'BylineTitle',\n    90: 'City',\n    92: 'Sublocation',\n    95: 'State',\n    100: 'CountryCode',\n    101: 'Country',\n    103: 'OriginalTransmissionReference',\n    105: 'Headline',\n    110: 'Credit',\n    115: 'Source',\n    116: 'CopyrightNotice',\n    118: 'Contact',\n    120: 'Caption',\n    121: 'LocalCaption',\n    122: 'Writer',\n    125: 'RasterizedCaption',\n    130: 'ImageType',\n    131: 'ImageOrientation',\n    135: 'LanguageIdentifier',\n    150: 'AudioType',\n    151: 'AudioSamplingRate',\n    152: 'AudioSamplingResolution',\n    153: 'AudioDuration',\n    154: 'AudioOutcue',\n    184: 'JobID',\n    185: 'MasterDocumentID',\n    186: 'ShortDocumentID',\n    187: 'UniqueDocumentID',\n    188: 'OwnerID',\n    200: 'ObjectPreviewFileFormat',\n    201: 'ObjectPreviewFileVersion',\n    202: 'ObjectPreviewData',\n    221: 'Prefs',\n    225: 'ClassifyState',\n    228: 'SimilarityIndex',\n    230: 'DocumentNotes',\n    231: 'DocumentHistory',\n    232: 'ExifCameraInfo',\n    255: 'CatalogSets'\n  }\n\n  IptcMapProto.stringValues = {\n    10: {\n      0: '0 (reserved)',\n      1: '1 (most urgent)',\n      2: '2',\n      3: '3',\n      4: '4',\n      5: '5 (normal urgency)',\n      6: '6',\n      7: '7',\n      8: '8 (least urgent)',\n      9: '9 (user-defined priority)'\n    },\n    75: {\n      a: 'Morning',\n      b: 'Both Morning and Evening',\n      p: 'Evening'\n    },\n    131: {\n      L: 'Landscape',\n      P: 'Portrait',\n      S: 'Square'\n    }\n  }\n\n  IptcMapProto.getText = function (id) {\n    var value = this.get(id)\n    var tagCode = this.map[id]\n    var stringValue = this.stringValues[tagCode]\n    if (stringValue) return stringValue[value]\n    return String(value)\n  }\n\n  IptcMapProto.getAll = function () {\n    var map = {}\n    var prop\n    var name\n    for (prop in this) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        name = this.tags[prop]\n        if (name) map[name] = this.getText(name)\n      }\n    }\n    return map\n  }\n\n  IptcMapProto.getName = function (tagCode) {\n    return this.tags[tagCode]\n  }\n\n  // Extend the map of tag names to tag codes:\n  ;(function () {\n    var tags = IptcMapProto.tags\n    var map = IptcMapProto.map || {}\n    var prop\n    // Map the tag names to tags:\n    for (prop in tags) {\n      if (Object.prototype.hasOwnProperty.call(tags, prop)) {\n        map[tags[prop]] = Number(prop)\n      }\n    }\n  })()\n})\n", "/*\n * JavaScript Load Image Orientation\n * https://github.com/blueimp/JavaScript-Load-Image\n *\n * Copyright 2013, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n */\n\n/* global define, module, require */\n\n;(function (factory) {\n  'use strict'\n  if (typeof define === 'function' && define.amd) {\n    // Register as an anonymous AMD module:\n    define(['./load-image', './load-image-scale', './load-image-meta'], factory)\n  } else if (typeof module === 'object' && module.exports) {\n    factory(\n      require('./load-image'),\n      require('./load-image-scale'),\n      require('./load-image-meta')\n    )\n  } else {\n    // Browser globals:\n    factory(window.loadImage)\n  }\n})(function (loadImage) {\n  'use strict'\n\n  var originalHasCanvasOption = loadImage.hasCanvasOption\n  var originalHasMetaOption = loadImage.hasMetaOption\n  var originalTransformCoordinates = loadImage.transformCoordinates\n  var originalGetTransformedOptions = loadImage.getTransformedOptions\n\n  ;(function () {\n    // black 2x1 JPEG, with the following meta information set:\n    // - EXIF Orientation: 6 (Rotated 90° CCW)\n    var testImageURL =\n      'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' +\n      'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' +\n      'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' +\n      'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x' +\n      'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA' +\n      'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q=='\n    var img = document.createElement('img')\n    img.onload = function () {\n      // Check if browser supports automatic image orientation:\n      loadImage.orientation = img.width === 1 && img.height === 2\n    }\n    img.src = testImageURL\n  })()\n\n  // Determines if the target image should be a canvas element:\n  loadImage.hasCanvasOption = function (options) {\n    return (\n      (!!options.orientation === true && !loadImage.orientation) ||\n      (options.orientation > 1 && options.orientation < 9) ||\n      originalHasCanvasOption.call(loadImage, options)\n    )\n  }\n\n  // Determines if meta data should be loaded automatically:\n  loadImage.hasMetaOption = function (options) {\n    return (\n      (options && options.orientation === true && !loadImage.orientation) ||\n      originalHasMetaOption.call(loadImage, options)\n    )\n  }\n\n  // Transform image orientation based on\n  // the given EXIF orientation option:\n  loadImage.transformCoordinates = function (canvas, options) {\n    originalTransformCoordinates.call(loadImage, canvas, options)\n    var ctx = canvas.getContext('2d')\n    var width = canvas.width\n    var height = canvas.height\n    var styleWidth = canvas.style.width\n    var styleHeight = canvas.style.height\n    var orientation = options.orientation\n    if (!(orientation > 1 && orientation < 9)) {\n      return\n    }\n    if (orientation > 4) {\n      canvas.width = height\n      canvas.height = width\n      canvas.style.width = styleHeight\n      canvas.style.height = styleWidth\n    }\n    switch (orientation) {\n      case 2:\n        // horizontal flip\n        ctx.translate(width, 0)\n        ctx.scale(-1, 1)\n        break\n      case 3:\n        // 180° rotate left\n        ctx.translate(width, height)\n        ctx.rotate(Math.PI)\n        break\n      case 4:\n        // vertical flip\n        ctx.translate(0, height)\n        ctx.scale(1, -1)\n        break\n      case 5:\n        // vertical flip + 90 rotate right\n        ctx.rotate(0.5 * Math.PI)\n        ctx.scale(1, -1)\n        break\n      case 6:\n        // 90° rotate right\n        ctx.rotate(0.5 * Math.PI)\n        ctx.translate(0, -height)\n        break\n      case 7:\n        // horizontal flip + 90 rotate right\n        ctx.rotate(0.5 * Math.PI)\n        ctx.translate(width, -height)\n        ctx.scale(-1, 1)\n        break\n      case 8:\n        // 90° rotate left\n        ctx.rotate(-0.5 * Math.PI)\n        ctx.translate(-width, 0)\n        break\n    }\n  }\n\n  // Transforms coordinate and dimension options\n  // based on the given orientation option:\n  loadImage.getTransformedOptions = function (img, opts, data) {\n    var options = originalGetTransformedOptions.call(loadImage, img, opts)\n    var orientation = options.orientation\n    var newOptions\n    var i\n    if (orientation === true) {\n      if (loadImage.orientation) {\n        // Browser supports automatic image orientation\n        return options\n      }\n      orientation = data && data.exif && data.exif.get('Orientation')\n    }\n    if (!(orientation > 1 && orientation < 9)) {\n      return options\n    }\n    newOptions = {}\n    for (i in options) {\n      if (Object.prototype.hasOwnProperty.call(options, i)) {\n        newOptions[i] = options[i]\n      }\n    }\n    newOptions.orientation = orientation\n    switch (orientation) {\n      case 2:\n        // horizontal flip\n        newOptions.left = options.right\n        newOptions.right = options.left\n        break\n      case 3:\n        // 180° rotate left\n        newOptions.left = options.right\n        newOptions.top = options.bottom\n        newOptions.right = options.left\n        newOptions.bottom = options.top\n        break\n      case 4:\n        // vertical flip\n        newOptions.top = options.bottom\n        newOptions.bottom = options.top\n        break\n      case 5:\n        // vertical flip + 90 rotate right\n        newOptions.left = options.top\n        newOptions.top = options.left\n        newOptions.right = options.bottom\n        newOptions.bottom = options.right\n        break\n      case 6:\n        // 90° rotate right\n        newOptions.left = options.top\n        newOptions.top = options.right\n        newOptions.right = options.bottom\n        newOptions.bottom = options.left\n        break\n      case 7:\n        // horizontal flip + 90 rotate right\n        newOptions.left = options.bottom\n        newOptions.top = options.right\n        newOptions.right = options.top\n        newOptions.bottom = options.left\n        break\n      case 8:\n        // 90° rotate left\n        newOptions.left = options.bottom\n        newOptions.top = options.left\n        newOptions.right = options.top\n        newOptions.bottom = options.right\n        break\n    }\n    if (newOptions.orientation > 4) {\n      newOptions.maxWidth = options.maxHeight\n      newOptions.maxHeight = options.maxWidth\n      newOptions.minWidth = options.minHeight\n      newOptions.minHeight = options.minWidth\n      newOptions.sourceWidth = options.sourceHeight\n      newOptions.sourceHeight = options.sourceWidth\n    }\n    return newOptions\n  }\n})\n", "const createWorker = require('./createWorker');\n\nconst recognize = async (image, langs, options) => {\n  const worker = createWorker(options);\n  await worker.load();\n  await worker.loadLanguage(langs);\n  await worker.initialize(langs);\n  return worker.recognize(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nconst detect = async (image, options) => {\n  const worker = createWorker(options);\n  await worker.load();\n  await worker.loadLanguage('osd');\n  await worker.initialize('osd');\n  return worker.detect(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nmodule.exports = {\n  recognize,\n  detect,\n};\n", "/*\n * languages with existing tesseract traineddata\n * https://tesseract-ocr.github.io/tessdoc/Data-Files#data-files-for-version-400-november-29-2016\n */\n\n/**\n * @typedef {object} Languages\n * @property {string} AFR Afrikaans\n * @property {string} AMH Amharic\n * @property {string} ARA Arabic\n * @property {string} ASM Assamese\n * @property {string} AZE Azerbaijani\n * @property {string} AZE_CYRL Azerbaijani - Cyrillic\n * @property {string} BEL Belarusian\n * @property {string} BEN Bengali\n * @property {string} BOD Tibetan\n * @property {string} BOS Bosnian\n * @property {string} BUL Bulgarian\n * @property {string} CAT Catalan; Valencian\n * @property {string} CEB Cebuano\n * @property {string} CES Czech\n * @property {string} CHI_SIM Chinese - Simplified\n * @property {string} CHI_TRA Chinese - Traditional\n * @property {string} CHR Cherokee\n * @property {string} CYM Welsh\n * @property {string} DAN Danish\n * @property {string} DEU German\n * @property {string} DZO Dzongkha\n * @property {string} ELL Greek, Modern (1453-)\n * @property {string} ENG English\n * @property {string} ENM English, Middle (1100-1500)\n * @property {string} EPO Esperanto\n * @property {string} EST Estonian\n * @property {string} EUS Basque\n * @property {string} FAS Persian\n * @property {string} FIN Finnish\n * @property {string} FRA French\n * @property {string} FRK German Fraktur\n * @property {string} FRM French, Middle (ca. 1400-1600)\n * @property {string} GLE Irish\n * @property {string} GLG Galician\n * @property {string} GRC Greek, Ancient (-1453)\n * @property {string} GUJ Gujarati\n * @property {string} HAT Haitian; Haitian Creole\n * @property {string} HEB Hebrew\n * @property {string} HIN Hindi\n * @property {string} HRV Croatian\n * @property {string} HUN Hungarian\n * @property {string} IKU Inuktitut\n * @property {string} IND Indonesian\n * @property {string} ISL Icelandic\n * @property {string} ITA Italian\n * @property {string} ITA_OLD Italian - Old\n * @property {string} JAV Javanese\n * @property {string} JPN Japanese\n * @property {string} KAN Kannada\n * @property {string} KAT Georgian\n * @property {string} KAT_OLD Georgian - Old\n * @property {string} KAZ Kazakh\n * @property {string} KHM Central Khmer\n * @property {string} KIR Kirghiz; Kyrgyz\n * @property {string} KOR Korean\n * @property {string} KUR Kurdish\n * @property {string} LAO Lao\n * @property {string} LAT Latin\n * @property {string} LAV Latvian\n * @property {string} LIT Lithuanian\n * @property {string} MAL Malayalam\n * @property {string} MAR Marathi\n * @property {string} MKD Macedonian\n * @property {string} MLT Maltese\n * @property {string} MSA Malay\n * @property {string} MYA Burmese\n * @property {string} NEP Nepali\n * @property {string} NLD Dutch; Flemish\n * @property {string} NOR Norwegian\n * @property {string} ORI Oriya\n * @property {string} PAN Panjabi; Punjabi\n * @property {string} POL Polish\n * @property {string} POR Portuguese\n * @property {string} PUS Pushto; Pashto\n * @property {string} RON Romanian; Moldavian; Moldovan\n * @property {string} RUS Russian\n * @property {string} SAN Sanskrit\n * @property {string} SIN Sinhala; Sinhalese\n * @property {string} SLK Slovak\n * @property {string} SLV Slovenian\n * @property {string} SPA Spanish; Castilian\n * @property {string} SPA_OLD Spanish; Castilian - Old\n * @property {string} SQI Albanian\n * @property {string} SRP Serbian\n * @property {string} SRP_LATN Serbian - Latin\n * @property {string} SWA Swahili\n * @property {string} SWE Swedish\n * @property {string} SYR Syriac\n * @property {string} TAM Tamil\n * @property {string} TEL Telugu\n * @property {string} TGK Tajik\n * @property {string} TGL Tagalog\n * @property {string} THA Thai\n * @property {string} TIR Tigrinya\n * @property {string} TUR Turkish\n * @property {string} UIG Uighur; Uyghur\n * @property {string} UKR Ukrainian\n * @property {string} URD Urdu\n * @property {string} UZB Uzbek\n * @property {string} UZB_CYRL Uzbek - Cyrillic\n * @property {string} VIE Vietnamese\n * @property {string} YID Yiddish\n */\n\n/**\n  * @type {Languages}\n  */\nmodule.exports = {\n  AFR: 'afr',\n  AMH: 'amh',\n  ARA: 'ara',\n  ASM: 'asm',\n  AZE: 'aze',\n  AZE_CYRL: 'aze_cyrl',\n  BEL: 'bel',\n  BEN: 'ben',\n  BOD: 'bod',\n  BOS: 'bos',\n  BUL: 'bul',\n  CAT: 'cat',\n  CEB: 'ceb',\n  CES: 'ces',\n  CHI_SIM: 'chi_sim',\n  CHI_TRA: 'chi_tra',\n  CHR: 'chr',\n  CYM: 'cym',\n  DAN: 'dan',\n  DEU: 'deu',\n  DZO: 'dzo',\n  ELL: 'ell',\n  ENG: 'eng',\n  ENM: 'enm',\n  EPO: 'epo',\n  EST: 'est',\n  EUS: 'eus',\n  FAS: 'fas',\n  FIN: 'fin',\n  FRA: 'fra',\n  FRK: 'frk',\n  FRM: 'frm',\n  GLE: 'gle',\n  GLG: 'glg',\n  GRC: 'grc',\n  GUJ: 'guj',\n  HAT: 'hat',\n  HEB: 'heb',\n  HIN: 'hin',\n  HRV: 'hrv',\n  HUN: 'hun',\n  IKU: 'iku',\n  IND: 'ind',\n  ISL: 'isl',\n  ITA: 'ita',\n  ITA_OLD: 'ita_old',\n  JAV: 'jav',\n  JPN: 'jpn',\n  KAN: 'kan',\n  KAT: 'kat',\n  KAT_OLD: 'kat_old',\n  KAZ: 'kaz',\n  KHM: 'khm',\n  KIR: 'kir',\n  KOR: 'kor',\n  KUR: 'kur',\n  LAO: 'lao',\n  LAT: 'lat',\n  LAV: 'lav',\n  LIT: 'lit',\n  MAL: 'mal',\n  MAR: 'mar',\n  MKD: 'mkd',\n  MLT: 'mlt',\n  MSA: 'msa',\n  MYA: 'mya',\n  NEP: 'nep',\n  NLD: 'nld',\n  NOR: 'nor',\n  ORI: 'ori',\n  PAN: 'pan',\n  POL: 'pol',\n  POR: 'por',\n  PUS: 'pus',\n  RON: 'ron',\n  RUS: 'rus',\n  SAN: 'san',\n  SIN: 'sin',\n  SLK: 'slk',\n  SLV: 'slv',\n  SPA: 'spa',\n  SPA_OLD: 'spa_old',\n  SQI: 'sqi',\n  SRP: 'srp',\n  SRP_LATN: 'srp_latn',\n  SWA: 'swa',\n  SWE: 'swe',\n  SYR: 'syr',\n  TAM: 'tam',\n  TEL: 'tel',\n  TGK: 'tgk',\n  TGL: 'tgl',\n  THA: 'tha',\n  TIR: 'tir',\n  TUR: 'tur',\n  UIG: 'uig',\n  UKR: 'ukr',\n  URD: 'urd',\n  UZB: 'uzb',\n  UZB_CYRL: 'uzb_cyrl',\n  VIE: 'vie',\n  YID: 'yid',\n};\n", "/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n};\n"], "sourceRoot": ""}