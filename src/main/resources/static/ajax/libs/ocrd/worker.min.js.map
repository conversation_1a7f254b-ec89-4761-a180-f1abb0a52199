{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./node_modules/buffer/index.js", "webpack:///(webpack)/buildin/global.js", "webpack:///./node_modules/process/browser.js", "webpack:///./node_modules/file-type/index.js", "webpack:///./src/worker-script/browser/index.js", "webpack:///./src/worker-script/index.js", "webpack:///./node_modules/regenerator-runtime/runtime.js", "webpack:///(webpack)/buildin/module.js", "webpack:///./node_modules/base64-js/index.js", "webpack:///./node_modules/ieee754/index.js", "webpack:///./node_modules/isarray/index.js", "webpack:///./node_modules/file-type/util.js", "webpack:///./node_modules/file-type/supported.js", "webpack:///./node_modules/is-url/index.js", "webpack:///./src/worker-script/utils/dump.js", "webpack:///./src/utils/getEnvironment.js", "webpack:///./node_modules/is-electron/index.js", "webpack:///./src/worker-script/utils/setImage.js", "webpack:///./node_modules/bmp-js/index.js", "webpack:///./node_modules/bmp-js/lib/encoder.js", "webpack:///./node_modules/bmp-js/lib/decoder.js", "webpack:///./src/worker-script/constants/defaultParams.js", "webpack:///./src/constants/PSM.js", "webpack:///./src/utils/log.js", "webpack:///./src/worker-script/browser/getCore.js", "webpack:///./src/worker-script/browser/gunzip.js", "webpack:///./node_modules/zlibjs/bin/node-zlib.js", "webpack:///./src/worker-script/browser/cache.js", "webpack:///./node_modules/idb-keyval/dist/idb-keyval.mjs"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "base64", "require", "ieee754", "isArray", "kMaxLength", "<PERSON><PERSON><PERSON>", "TYPED_ARRAY_SUPPORT", "createBuffer", "that", "length", "RangeError", "Uint8Array", "__proto__", "arg", "encodingOrOffset", "this", "Error", "allocUnsafe", "from", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "array", "byteOffset", "byteLength", "undefined", "fromArrayLike", "fromArrayBuffer", "string", "encoding", "isEncoding", "actual", "write", "slice", "fromString", "obj", "<PERSON><PERSON><PERSON><PERSON>", "len", "checked", "copy", "buffer", "val", "type", "data", "fromObject", "assertSize", "size", "toString", "<PERSON><PERSON><PERSON><PERSON>", "loweredCase", "utf8ToBytes", "base64ToBytes", "toLowerCase", "slowToString", "start", "end", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "b", "bidirectionalIndexOf", "dir", "isNaN", "arrayIndexOf", "indexOf", "lastIndexOf", "arr", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "String", "read", "buf", "readUInt16BE", "foundIndex", "found", "j", "hexWrite", "offset", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "str", "byteArray", "push", "charCodeAt", "asciiToBytes", "latin1Write", "base64Write", "ucs2Write", "units", "hi", "lo", "utf16leToBytes", "fromByteArray", "Math", "min", "res", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "firstByte", "codePoint", "bytesPerSequence", "codePoints", "fromCharCode", "apply", "decodeCodePointsArray", "<PERSON><PERSON><PERSON><PERSON>", "alloc", "INSPECT_MAX_BYTES", "global", "foo", "subarray", "e", "typedArraySupport", "poolSize", "_augment", "species", "configurable", "fill", "allocUnsafeSlow", "_isBuffer", "compare", "a", "x", "y", "concat", "list", "pos", "swap16", "swap32", "swap64", "arguments", "equals", "inspect", "max", "match", "join", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "includes", "isFinite", "toJSON", "Array", "_arr", "ret", "out", "toHex", "bytes", "checkOffset", "ext", "checkInt", "objectWriteUInt16", "littleEndian", "objectWriteUInt32", "checkIEEE754", "writeFloat", "noAssert", "writeDouble", "newBuf", "sliceLen", "readUIntLE", "mul", "readUIntBE", "readUInt8", "readUInt16LE", "readUInt32LE", "readUInt32BE", "readIntLE", "pow", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "readFloatBE", "readDoubleLE", "readDoubleBE", "writeUIntLE", "writeUIntBE", "writeUInt8", "floor", "writeUInt16LE", "writeUInt16BE", "writeUInt32LE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "writeFloatLE", "writeFloatBE", "writeDoubleLE", "writeDoubleBE", "targetStart", "set", "code", "INVALID_BASE64_RE", "Infinity", "leadSurrogate", "toByteArray", "trim", "replace", "stringtrim", "base64clean", "src", "dst", "g", "Function", "window", "cachedSetTimeout", "cachedClearTimeout", "process", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "setTimeout", "clearTimeout", "currentQueue", "queue", "draining", "queueIndex", "cleanUpNextTick", "drainQueue", "timeout", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "noop", "nextTick", "args", "title", "browser", "env", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "umask", "multiByteIndexOf", "stringToBytes", "readUInt64LE", "tarHeaderChecksumMatches", "uint8ArrayUtf8ByteString", "supported", "xpiZipFilename", "oxmlContentTypes", "oxmlRels", "fileType", "input", "check", "header", "options", "mask", "checkString", "mime", "firstImageDataChunkIndex", "findIndex", "el", "sliced", "zipHeader", "zipHeaderIndex", "oxmlFound", "brandMajor", "startsWith", "idPos", "docTypePos", "findDocType", "every", "objectSize", "stream", "readableStream", "Promise", "resolve", "reject", "eval", "pass", "PassThrough", "chunk", "minimumBytes", "error", "unshift", "pipeline", "pipe", "Set", "extensions", "mimeTypes", "worker", "getCore", "gunzip", "cache", "addEventListener", "dispatchHandlers", "postMessage", "setAdapter", "fetch", "TessModule", "latestJob", "isURL", "dump", "isWebWorker", "setImage", "defaultParams", "log", "setLogging", "api", "adapter", "params", "load", "workerId", "jobId", "payload", "corePath", "logging", "loaded", "Core", "progress", "status", "TesseractProgress", "percent", "then", "tessModule", "FS", "method", "loadLanguage", "langs", "lang<PERSON><PERSON>", "dataPath", "cachePath", "cacheMethod", "gzip", "loadAndGunzipFile", "_lang", "lang", "readCache", "_data", "path", "resp", "arrayBuffer", "mkdir", "err", "writeFile", "writeCache", "all", "split", "map", "DOMException", "setParameters", "_params", "keys", "filter", "k", "for<PERSON>ach", "SetVariable", "initialize", "_langs", "oem", "End", "TessBaseAPI", "Init", "recognize", "image", "rec", "rectangle", "ptr", "SetRectangle", "left", "top", "width", "height", "Recognize", "_free", "getPDF", "textonly", "pdf<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BeginDocument", "AddImage", "EndDocument", "readFile", "detect", "results", "OSResults", "DetectOS", "best", "best_result", "oid", "orientation_id", "sid", "script_id", "tesseract_script_id", "script", "unicharset", "get_script_from_script_id", "script_confidence", "sconfidence", "orientation_degrees", "orientation_confidence", "oconfidence", "terminate", "_", "terminated", "packet", "send", "action", "_adapter", "runtime", "Op", "hasOwn", "$Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "context", "Context", "_invoke", "state", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "done", "makeInvokeMethod", "fn", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "AsyncIterator", "previousPromise", "callInvokeWithMethodAndArg", "invoke", "result", "__await", "unwrapped", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "constructor", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "mark", "setPrototypeOf", "awrap", "async", "iter", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "thrown", "<PERSON><PERSON><PERSON>", "regeneratorRuntime", "accidentalStrictMode", "webpackPolyfill", "deprecate", "paths", "children", "b64", "lens", "getLens", "validLen", "placeHoldersLen", "tmp", "Arr", "_byteLength", "curByte", "revLookup", "uint8", "extraBytes", "parts", "len2", "encodeChunk", "lookup", "num", "output", "isLE", "mLen", "nBytes", "eLen", "eMax", "eBias", "nBits", "NaN", "rt", "abs", "LN2", "character", "sum", "signedBitSum", "byte", "readSum", "bytesToSearch", "startAt", "nextBytesMatch", "startIndex", "index", "protocolAndDomainRE", "everythingAfterProtocol", "localhostDomainRE", "test", "nonLocalhostDomainRE", "deindent", "html", "lines", "substring", "block", "para", "textline", "word", "symbol", "tessjs_create_hocr", "tessjs_create_tsv", "tessjs_create_box", "tessjs_create_unlv", "tessjs_create_osd", "ri", "GetIterator", "RIL_BLOCK", "RIL_PARA", "RIL_TEXTLINE", "RIL_WORD", "RIL_SYMBOL", "blocks", "enumToString", "prefix", "<PERSON><PERSON>", "IsAtBeginningOf", "poly", "BlockPolygon", "polygon", "getPointer", "get_n", "px", "get_x", "py", "get_y", "getValue", "paragraphs", "text", "GetUTF8Text", "confidence", "Confidence", "baseline", "getBaseline", "bbox", "getBoundingBox", "blocktype", "BlockType", "is_ltr", "ParagraphIsLtr", "words", "fontInfo", "getWordFontAttributes", "wordDir", "WordDirection", "symbols", "choices", "is_numeric", "WordIsNumeric", "in_dictionary", "WordIsFromDictionary", "direction", "language", "WordRecognitionLanguage", "is_bold", "is_italic", "is_underlined", "is_monospace", "is_serif", "is_smallcaps", "font_size", "pointsize", "font_id", "font_name", "wc", "WordChoiceIterator", "Next", "destroy", "is_superscript", "SymbolIsSuperscript", "is_subscript", "SymbolIsSubscript", "is_dropcap", "SymbolIsDropcap", "ci", "ChoiceIterator", "hocr", "GetHOCRText", "tsv", "GetTSVText", "box", "GetBoxText", "unlv", "GetUNLVText", "osd", "GetOsdText", "MeanTextConf", "psm", "GetPageSegMode", "Version", "isElectron", "WorkerGlobalScope", "electron", "navigator", "userAgent", "bmp", "bytesPerPixel", "pix", "w", "h", "bmpBuf", "decode", "_malloc", "BYTES_PER_ELEMENT", "HEAPU8", "_pixReadMem", "setValue", "v", "idx", "SetImage", "encode", "BmpEncoder", "imgData", "rgbSize", "headerInfoSize", "flag", "reserved", "fileSize", "planes", "bitPP", "compress", "hr", "vr", "colors", "importantColors", "temp<PERSON><PERSON><PERSON>", "rowBytes", "fillOffset", "quality", "BmpDecoder", "is_with_alpha", "bottom_up", "parse<PERSON><PERSON><PERSON>", "parseRGBA", "headerSize", "rawSize", "palette", "blue", "green", "red", "quad", "bitn", "bit1", "xlen", "ceil", "line", "location", "rgb", "bit4", "setPixelData", "rgbIndex", "low_nibble", "before", "after", "bit8", "bit15", "dif_w", "_1_5", "B", "alpha", "bit16", "maskRed", "<PERSON><PERSON><PERSON>", "maskBlue", "mask0", "bit24", "bit32", "getData", "bmpData", "PSM", "tessedit_pageseg_mode", "SINGLE_BLOCK", "tessedit_char_whitelist", "OSD_ONLY", "AUTO_OSD", "AUTO_ONLY", "AUTO", "SINGLE_COLUMN", "SINGLE_BLOCK_VERT_TEXT", "SINGLE_LINE", "SINGLE_WORD", "CIRCLE_WORD", "SINGLE_CHAR", "SPARSE_TEXT", "SPARSE_TEXT_OSD", "_logging", "console", "TesseractCore", "importScripts", "TesseractCoreWASM", "WebAssembly", "TesseractCoreASM", "gunzipSync", "q", "Uint16Array", "Uint32Array", "DataView", "G", "f", "I", "L", "aa", "ba", "R", "ca", "ha", "S", "ia", "ja", "ka", "T", "POSITIVE_INFINITY", "na", "oa", "F", "lazy", "compressionType", "outputBuffer", "outputIndex", "getParent", "U", "pa", "NONE", "X", "qa", "va", "N", "C", "u", "ra", "M", "z", "Y", "da", "Fa", "ea", "Ga", "la", "Ha", "Z", "ma", "E", "Ia", "D", "qb", "ta", "ua", "sa", "O", "A", "fa", "H", "<PERSON>a", "<PERSON>", "K", "J", "P", "Q", "Na", "ga", "wa", "Oa", "Pa", "Qa", "Ra", "La", "Ma", "xa", "ya", "shift", "za", "Aa", "Ba", "flags", "filename", "comment", "deflateOptions", "fname", "Ca", "fcomment", "Da", "fhcrc", "Ea", "Date", "now", "Sa", "V", "Ta", "bufferSize", "bufferType", "resize", "Ua", "W", "Va", "Wa", "Xa", "Ya", "$", "ib", "<PERSON>a", "$a", "ab", "bb", "cb", "db", "eb", "fb", "gb", "hb", "kb", "lb", "jb", "mb", "nb", "ob", "verify", "pb", "rb", "sb", "ub", "Bb", "wb", "noB<PERSON>er", "yb", "Ab", "LOG2E", "deflate", "deflateSync", "inflate", "inflateSync", "gzipSync", "del", "deleteCache", "checkCache", "store", "Store", "dbN<PERSON>", "storeName", "_dbp", "openreq", "indexedDB", "open", "onerror", "onsuccess", "onupgradeneeded", "createObjectStore", "callback", "transaction", "oncomplete", "<PERSON>ab<PERSON>", "objectStore", "getDefaultStore", "req", "_withIDBStore", "put", "delete", "clear", "openKeyCursor", "openCursor", "continue"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,gCClFrD;;;;;;;AAUA,IAAIC,EAASC,EAAQ,GACjBC,EAAUD,EAAQ,GAClBE,EAAUF,EAAQ,IAmDtB,SAASG,IACP,OAAOC,EAAOC,oBACV,WACA,WAGN,SAASC,EAAcC,EAAMC,GAC3B,GAAIL,IAAeK,EACjB,MAAM,IAAIC,WAAW,8BAcvB,OAZIL,EAAOC,qBAETE,EAAO,IAAIG,WAAWF,IACjBG,UAAYP,EAAOT,WAGX,OAATY,IACFA,EAAO,IAAIH,EAAOI,IAEpBD,EAAKC,OAASA,GAGTD,EAaT,SAASH,EAAQQ,EAAKC,EAAkBL,GACtC,KAAKJ,EAAOC,qBAAyBS,gBAAgBV,GACnD,OAAO,IAAIA,EAAOQ,EAAKC,EAAkBL,GAI3C,GAAmB,iBAARI,EAAkB,CAC3B,GAAgC,iBAArBC,EACT,MAAM,IAAIE,MACR,qEAGJ,OAAOC,EAAYF,KAAMF,GAE3B,OAAOK,EAAKH,KAAMF,EAAKC,EAAkBL,GAW3C,SAASS,EAAMV,EAAMvB,EAAO6B,EAAkBL,GAC5C,GAAqB,iBAAVxB,EACT,MAAM,IAAIkC,UAAU,yCAGtB,MAA2B,oBAAhBC,aAA+BnC,aAAiBmC,YA6H7D,SAA0BZ,EAAMa,EAAOC,EAAYb,GAGjD,GAFAY,EAAME,WAEFD,EAAa,GAAKD,EAAME,WAAaD,EACvC,MAAM,IAAIZ,WAAW,6BAGvB,GAAIW,EAAME,WAAaD,GAAcb,GAAU,GAC7C,MAAM,IAAIC,WAAW,6BAIrBW,OADiBG,IAAfF,QAAuCE,IAAXf,EACtB,IAAIE,WAAWU,QACHG,IAAXf,EACD,IAAIE,WAAWU,EAAOC,GAEtB,IAAIX,WAAWU,EAAOC,EAAYb,GAGxCJ,EAAOC,qBAETE,EAAOa,GACFT,UAAYP,EAAOT,UAGxBY,EAAOiB,EAAcjB,EAAMa,GAE7B,OAAOb,EAvJEkB,CAAgBlB,EAAMvB,EAAO6B,EAAkBL,GAGnC,iBAAVxB,EAwFb,SAAqBuB,EAAMmB,EAAQC,GACT,iBAAbA,GAAsC,KAAbA,IAClCA,EAAW,QAGb,IAAKvB,EAAOwB,WAAWD,GACrB,MAAM,IAAIT,UAAU,8CAGtB,IAAIV,EAAwC,EAA/Bc,EAAWI,EAAQC,GAG5BE,GAFJtB,EAAOD,EAAaC,EAAMC,IAERsB,MAAMJ,EAAQC,GAE5BE,IAAWrB,IAIbD,EAAOA,EAAKwB,MAAM,EAAGF,IAGvB,OAAOtB,EA5GEyB,CAAWzB,EAAMvB,EAAO6B,GAsJnC,SAAqBN,EAAM0B,GACzB,GAAI7B,EAAO8B,SAASD,GAAM,CACxB,IAAIE,EAA4B,EAAtBC,EAAQH,EAAIzB,QAGtB,OAAoB,KAFpBD,EAAOD,EAAaC,EAAM4B,IAEjB3B,QAITyB,EAAII,KAAK9B,EAAM,EAAG,EAAG4B,GAHZ5B,EAOX,GAAI0B,EAAK,CACP,GAA4B,oBAAhBd,aACRc,EAAIK,kBAAkBnB,aAAgB,WAAYc,EACpD,MAA0B,iBAAfA,EAAIzB,SA+8CL+B,EA/8CkCN,EAAIzB,SAg9CrC+B,EA/8CFjC,EAAaC,EAAM,GAErBiB,EAAcjB,EAAM0B,GAG7B,GAAiB,WAAbA,EAAIO,MAAqBtC,EAAQ+B,EAAIQ,MACvC,OAAOjB,EAAcjB,EAAM0B,EAAIQ,MAw8CrC,IAAgBF,EAp8Cd,MAAM,IAAIrB,UAAU,sFA9KbwB,CAAWnC,EAAMvB,GA4B1B,SAAS2D,EAAYC,GACnB,GAAoB,iBAATA,EACT,MAAM,IAAI1B,UAAU,oCACf,GAAI0B,EAAO,EAChB,MAAM,IAAInC,WAAW,wCA4BzB,SAASO,EAAaT,EAAMqC,GAG1B,GAFAD,EAAWC,GACXrC,EAAOD,EAAaC,EAAMqC,EAAO,EAAI,EAAoB,EAAhBR,EAAQQ,KAC5CxC,EAAOC,oBACV,IAAK,IAAItC,EAAI,EAAGA,EAAI6E,IAAQ7E,EAC1BwC,EAAKxC,GAAK,EAGd,OAAOwC,EAwCT,SAASiB,EAAejB,EAAMa,GAC5B,IAAIZ,EAASY,EAAMZ,OAAS,EAAI,EAA4B,EAAxB4B,EAAQhB,EAAMZ,QAClDD,EAAOD,EAAaC,EAAMC,GAC1B,IAAK,IAAIzC,EAAI,EAAGA,EAAIyC,EAAQzC,GAAK,EAC/BwC,EAAKxC,GAAgB,IAAXqD,EAAMrD,GAElB,OAAOwC,EA+DT,SAAS6B,EAAS5B,GAGhB,GAAIA,GAAUL,IACZ,MAAM,IAAIM,WAAW,0DACaN,IAAa0C,SAAS,IAAM,UAEhE,OAAgB,EAATrC,EAsFT,SAASc,EAAYI,EAAQC,GAC3B,GAAIvB,EAAO8B,SAASR,GAClB,OAAOA,EAAOlB,OAEhB,GAA2B,oBAAhBW,aAA6D,mBAAvBA,YAAY2B,SACxD3B,YAAY2B,OAAOpB,IAAWA,aAAkBP,aACnD,OAAOO,EAAOJ,WAEM,iBAAXI,IACTA,EAAS,GAAKA,GAGhB,IAAIS,EAAMT,EAAOlB,OACjB,GAAY,IAAR2B,EAAW,OAAO,EAItB,IADA,IAAIY,GAAc,IAEhB,OAAQpB,GACN,IAAK,QACL,IAAK,SACL,IAAK,SACH,OAAOQ,EACT,IAAK,OACL,IAAK,QACL,UAAKZ,EACH,OAAOyB,EAAYtB,GAAQlB,OAC7B,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAa,EAAN2B,EACT,IAAK,MACH,OAAOA,IAAQ,EACjB,IAAK,SACH,OAAOc,EAAcvB,GAAQlB,OAC/B,QACE,GAAIuC,EAAa,OAAOC,EAAYtB,GAAQlB,OAC5CmB,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,GAMtB,SAASI,EAAcxB,EAAUyB,EAAOC,GACtC,IAAIN,GAAc,EAclB,SALcxB,IAAV6B,GAAuBA,EAAQ,KACjCA,EAAQ,GAINA,EAAQtC,KAAKN,OACf,MAAO,GAOT,SAJYe,IAAR8B,GAAqBA,EAAMvC,KAAKN,UAClC6C,EAAMvC,KAAKN,QAGT6C,GAAO,EACT,MAAO,GAOT,IAHAA,KAAS,KACTD,KAAW,GAGT,MAAO,GAKT,IAFKzB,IAAUA,EAAW,UAGxB,OAAQA,GACN,IAAK,MACH,OAAO2B,EAASxC,KAAMsC,EAAOC,GAE/B,IAAK,OACL,IAAK,QACH,OAAOE,EAAUzC,KAAMsC,EAAOC,GAEhC,IAAK,QACH,OAAOG,EAAW1C,KAAMsC,EAAOC,GAEjC,IAAK,SACL,IAAK,SACH,OAAOI,EAAY3C,KAAMsC,EAAOC,GAElC,IAAK,SACH,OAAOK,EAAY5C,KAAMsC,EAAOC,GAElC,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOM,EAAa7C,KAAMsC,EAAOC,GAEnC,QACE,GAAIN,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAYA,EAAW,IAAIuB,cAC3BH,GAAc,GAStB,SAASa,EAAMC,EAAGrE,EAAGrB,GACnB,IAAIJ,EAAI8F,EAAErE,GACVqE,EAAErE,GAAKqE,EAAE1F,GACT0F,EAAE1F,GAAKJ,EAmIT,SAAS+F,EAAsBxB,EAAQC,EAAKlB,EAAYM,EAAUoC,GAEhE,GAAsB,IAAlBzB,EAAO9B,OAAc,OAAQ,EAmBjC,GAhB0B,iBAAfa,GACTM,EAAWN,EACXA,EAAa,GACJA,EAAa,WACtBA,EAAa,WACJA,GAAc,aACvBA,GAAc,YAEhBA,GAAcA,EACV2C,MAAM3C,KAERA,EAAa0C,EAAM,EAAKzB,EAAO9B,OAAS,GAItCa,EAAa,IAAGA,EAAaiB,EAAO9B,OAASa,GAC7CA,GAAciB,EAAO9B,OAAQ,CAC/B,GAAIuD,EAAK,OAAQ,EACZ1C,EAAaiB,EAAO9B,OAAS,OAC7B,GAAIa,EAAa,EAAG,CACzB,IAAI0C,EACC,OAAQ,EADJ1C,EAAa,EAUxB,GALmB,iBAARkB,IACTA,EAAMnC,EAAOa,KAAKsB,EAAKZ,IAIrBvB,EAAO8B,SAASK,GAElB,OAAmB,IAAfA,EAAI/B,QACE,EAEHyD,EAAa3B,EAAQC,EAAKlB,EAAYM,EAAUoC,GAClD,GAAmB,iBAARxB,EAEhB,OADAA,GAAY,IACRnC,EAAOC,qBACiC,mBAAjCK,WAAWf,UAAUuE,QAC1BH,EACKrD,WAAWf,UAAUuE,QAAQhG,KAAKoE,EAAQC,EAAKlB,GAE/CX,WAAWf,UAAUwE,YAAYjG,KAAKoE,EAAQC,EAAKlB,GAGvD4C,EAAa3B,EAAQ,CAAEC,GAAOlB,EAAYM,EAAUoC,GAG7D,MAAM,IAAI7C,UAAU,wCAGtB,SAAS+C,EAAcG,EAAK7B,EAAKlB,EAAYM,EAAUoC,GACrD,IA0BIhG,EA1BAsG,EAAY,EACZC,EAAYF,EAAI5D,OAChB+D,EAAYhC,EAAI/B,OAEpB,QAAiBe,IAAbI,IAEe,UADjBA,EAAW6C,OAAO7C,GAAUuB,gBACY,UAAbvB,GACV,YAAbA,GAAuC,aAAbA,GAAyB,CACrD,GAAIyC,EAAI5D,OAAS,GAAK+B,EAAI/B,OAAS,EACjC,OAAQ,EAEV6D,EAAY,EACZC,GAAa,EACbC,GAAa,EACblD,GAAc,EAIlB,SAASoD,EAAMC,EAAK3G,GAClB,OAAkB,IAAdsG,EACKK,EAAI3G,GAEJ2G,EAAIC,aAAa5G,EAAIsG,GAKhC,GAAIN,EAAK,CACP,IAAIa,GAAc,EAClB,IAAK7G,EAAIsD,EAAYtD,EAAIuG,EAAWvG,IAClC,GAAI0G,EAAKL,EAAKrG,KAAO0G,EAAKlC,GAAqB,IAAhBqC,EAAoB,EAAI7G,EAAI6G,IAEzD,IADoB,IAAhBA,IAAmBA,EAAa7G,GAChCA,EAAI6G,EAAa,IAAML,EAAW,OAAOK,EAAaP,OAEtC,IAAhBO,IAAmB7G,GAAKA,EAAI6G,GAChCA,GAAc,OAKlB,IADIvD,EAAakD,EAAYD,IAAWjD,EAAaiD,EAAYC,GAC5DxG,EAAIsD,EAAYtD,GAAK,EAAGA,IAAK,CAEhC,IADA,IAAI8G,GAAQ,EACHC,EAAI,EAAGA,EAAIP,EAAWO,IAC7B,GAAIL,EAAKL,EAAKrG,EAAI+G,KAAOL,EAAKlC,EAAKuC,GAAI,CACrCD,GAAQ,EACR,MAGJ,GAAIA,EAAO,OAAO9G,EAItB,OAAQ,EAeV,SAASgH,EAAUL,EAAKhD,EAAQsD,EAAQxE,GACtCwE,EAASC,OAAOD,IAAW,EAC3B,IAAIE,EAAYR,EAAIlE,OAASwE,EACxBxE,GAGHA,EAASyE,OAAOzE,IACH0E,IACX1E,EAAS0E,GAJX1E,EAAS0E,EASX,IAAIC,EAASzD,EAAOlB,OACpB,GAAI2E,EAAS,GAAM,EAAG,MAAM,IAAIjE,UAAU,sBAEtCV,EAAS2E,EAAS,IACpB3E,EAAS2E,EAAS,GAEpB,IAAK,IAAIpH,EAAI,EAAGA,EAAIyC,IAAUzC,EAAG,CAC/B,IAAIqH,EAASC,SAAS3D,EAAO4D,OAAW,EAAJvH,EAAO,GAAI,IAC/C,GAAIiG,MAAMoB,GAAS,OAAOrH,EAC1B2G,EAAIM,EAASjH,GAAKqH,EAEpB,OAAOrH,EAGT,SAASwH,EAAWb,EAAKhD,EAAQsD,EAAQxE,GACvC,OAAOgF,EAAWxC,EAAYtB,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GAG3E,SAASiF,EAAYf,EAAKhD,EAAQsD,EAAQxE,GACxC,OAAOgF,EAq6BT,SAAuBE,GAErB,IADA,IAAIC,EAAY,GACP5H,EAAI,EAAGA,EAAI2H,EAAIlF,SAAUzC,EAEhC4H,EAAUC,KAAyB,IAApBF,EAAIG,WAAW9H,IAEhC,OAAO4H,EA36BWG,CAAapE,GAASgD,EAAKM,EAAQxE,GAGvD,SAASuF,EAAarB,EAAKhD,EAAQsD,EAAQxE,GACzC,OAAOiF,EAAWf,EAAKhD,EAAQsD,EAAQxE,GAGzC,SAASwF,EAAatB,EAAKhD,EAAQsD,EAAQxE,GACzC,OAAOgF,EAAWvC,EAAcvB,GAASgD,EAAKM,EAAQxE,GAGxD,SAASyF,EAAWvB,EAAKhD,EAAQsD,EAAQxE,GACvC,OAAOgF,EAk6BT,SAAyBE,EAAKQ,GAG5B,IAFA,IAAI9H,EAAG+H,EAAIC,EACPT,EAAY,GACP5H,EAAI,EAAGA,EAAI2H,EAAIlF,WACjB0F,GAAS,GAAK,KADanI,EAGhCK,EAAIsH,EAAIG,WAAW9H,GACnBoI,EAAK/H,GAAK,EACVgI,EAAKhI,EAAI,IACTuH,EAAUC,KAAKQ,GACfT,EAAUC,KAAKO,GAGjB,OAAOR,EA/6BWU,CAAe3E,EAAQgD,EAAIlE,OAASwE,GAASN,EAAKM,EAAQxE,GAkF9E,SAASkD,EAAagB,EAAKtB,EAAOC,GAChC,OAAc,IAAVD,GAAeC,IAAQqB,EAAIlE,OACtBT,EAAOuG,cAAc5B,GAErB3E,EAAOuG,cAAc5B,EAAI3C,MAAMqB,EAAOC,IAIjD,SAASE,EAAWmB,EAAKtB,EAAOC,GAC9BA,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAI3B,IAHA,IAAIoD,EAAM,GAEN1I,EAAIqF,EACDrF,EAAIsF,GAAK,CACd,IAQMqD,EAAYC,EAAWC,EAAYC,EARrCC,EAAYpC,EAAI3G,GAChBgJ,EAAY,KACZC,EAAoBF,EAAY,IAAQ,EACvCA,EAAY,IAAQ,EACpBA,EAAY,IAAQ,EACrB,EAEJ,GAAI/I,EAAIiJ,GAAoB3D,EAG1B,OAAQ2D,GACN,KAAK,EACCF,EAAY,MACdC,EAAYD,GAEd,MACF,KAAK,EAEyB,MAAV,KADlBJ,EAAahC,EAAI3G,EAAI,OAEnB8I,GAA6B,GAAZC,IAAqB,EAAoB,GAAbJ,GACzB,MAClBK,EAAYF,GAGhB,MACF,KAAK,EACHH,EAAahC,EAAI3G,EAAI,GACrB4I,EAAYjC,EAAI3G,EAAI,GACQ,MAAV,IAAb2I,IAAsD,MAAV,IAAZC,KACnCE,GAA6B,GAAZC,IAAoB,IAAoB,GAAbJ,IAAsB,EAAmB,GAAZC,GACrD,OAAUE,EAAgB,OAAUA,EAAgB,SACtEE,EAAYF,GAGhB,MACF,KAAK,EACHH,EAAahC,EAAI3G,EAAI,GACrB4I,EAAYjC,EAAI3G,EAAI,GACpB6I,EAAalC,EAAI3G,EAAI,GACO,MAAV,IAAb2I,IAAsD,MAAV,IAAZC,IAAsD,MAAV,IAAbC,KAClEC,GAA6B,GAAZC,IAAoB,IAAqB,GAAbJ,IAAsB,IAAmB,GAAZC,IAAqB,EAAoB,GAAbC,GAClF,OAAUC,EAAgB,UAC5CE,EAAYF,GAMJ,OAAdE,GAGFA,EAAY,MACZC,EAAmB,GACVD,EAAY,QAErBA,GAAa,MACbN,EAAIb,KAAKmB,IAAc,GAAK,KAAQ,OACpCA,EAAY,MAAqB,KAAZA,GAGvBN,EAAIb,KAAKmB,GACThJ,GAAKiJ,EAGP,OAQF,SAAgCC,GAC9B,IAAI9E,EAAM8E,EAAWzG,OACrB,GAAI2B,GAJqB,KAKvB,OAAOqC,OAAO0C,aAAaC,MAAM3C,OAAQyC,GAI3C,IAAIR,EAAM,GACN1I,EAAI,EACR,KAAOA,EAAIoE,GACTsE,GAAOjC,OAAO0C,aAAaC,MACzB3C,OACAyC,EAAWlF,MAAMhE,EAAGA,GAdC,OAiBzB,OAAO0I,EAvBAW,CAAsBX,GA98B/B5I,EAAQuC,OAASA,EACjBvC,EAAQwJ,WAoTR,SAAqB7G,IACdA,GAAUA,IACbA,EAAS,GAEX,OAAOJ,EAAOkH,OAAO9G,IAvTvB3C,EAAQ0J,kBAAoB,GA0B5BnH,EAAOC,yBAAqDkB,IAA/BiG,EAAOnH,oBAChCmH,EAAOnH,oBAQX,WACE,IACE,IAAI+D,EAAM,IAAI1D,WAAW,GAEzB,OADA0D,EAAIzD,UAAY,CAACA,UAAWD,WAAWf,UAAW8H,IAAK,WAAc,OAAO,KACvD,KAAdrD,EAAIqD,OACiB,mBAAjBrD,EAAIsD,UACuB,IAAlCtD,EAAIsD,SAAS,EAAG,GAAGpG,WACvB,MAAOqG,GACP,OAAO,GAfPC,GAKJ/J,EAAQsC,WAAaA,IAkErBC,EAAOyH,SAAW,KAGlBzH,EAAO0H,SAAW,SAAU1D,GAE1B,OADAA,EAAIzD,UAAYP,EAAOT,UAChByE,GA2BThE,EAAOa,KAAO,SAAUjC,EAAO6B,EAAkBL,GAC/C,OAAOS,EAAK,KAAMjC,EAAO6B,EAAkBL,IAGzCJ,EAAOC,sBACTD,EAAOT,UAAUgB,UAAYD,WAAWf,UACxCS,EAAOO,UAAYD,WACG,oBAAX5B,QAA0BA,OAAOiJ,SACxC3H,EAAOtB,OAAOiJ,WAAa3H,GAE7B3B,OAAOC,eAAe0B,EAAQtB,OAAOiJ,QAAS,CAC5C/I,MAAO,KACPgJ,cAAc,KAiCpB5H,EAAOkH,MAAQ,SAAU1E,EAAMqF,EAAMtG,GACnC,OArBF,SAAgBpB,EAAMqC,EAAMqF,EAAMtG,GAEhC,OADAgB,EAAWC,GACPA,GAAQ,EACHtC,EAAaC,EAAMqC,QAEfrB,IAAT0G,EAIyB,iBAAbtG,EACVrB,EAAaC,EAAMqC,GAAMqF,KAAKA,EAAMtG,GACpCrB,EAAaC,EAAMqC,GAAMqF,KAAKA,GAE7B3H,EAAaC,EAAMqC,GAQnB0E,CAAM,KAAM1E,EAAMqF,EAAMtG,IAiBjCvB,EAAOY,YAAc,SAAU4B,GAC7B,OAAO5B,EAAY,KAAM4B,IAK3BxC,EAAO8H,gBAAkB,SAAUtF,GACjC,OAAO5B,EAAY,KAAM4B,IAiH3BxC,EAAO8B,SAAW,SAAmB2B,GACnC,QAAe,MAALA,IAAaA,EAAEsE,YAG3B/H,EAAOgI,QAAU,SAAkBC,EAAGxE,GACpC,IAAKzD,EAAO8B,SAASmG,KAAOjI,EAAO8B,SAAS2B,GAC1C,MAAM,IAAI3C,UAAU,6BAGtB,GAAImH,IAAMxE,EAAG,OAAO,EAKpB,IAHA,IAAIyE,EAAID,EAAE7H,OACN+H,EAAI1E,EAAErD,OAEDzC,EAAI,EAAGoE,EAAMoE,KAAKC,IAAI8B,EAAGC,GAAIxK,EAAIoE,IAAOpE,EAC/C,GAAIsK,EAAEtK,KAAO8F,EAAE9F,GAAI,CACjBuK,EAAID,EAAEtK,GACNwK,EAAI1E,EAAE9F,GACN,MAIJ,OAAIuK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GAGTlI,EAAOwB,WAAa,SAAqBD,GACvC,OAAQ6C,OAAO7C,GAAUuB,eACvB,IAAK,MACL,IAAK,OACL,IAAK,QACL,IAAK,QACL,IAAK,SACL,IAAK,SACL,IAAK,SACL,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAO,EACT,QACE,OAAO,IAIb9C,EAAOoI,OAAS,SAAiBC,EAAMjI,GACrC,IAAKN,EAAQuI,GACX,MAAM,IAAIvH,UAAU,+CAGtB,GAAoB,IAAhBuH,EAAKjI,OACP,OAAOJ,EAAOkH,MAAM,GAGtB,IAAIvJ,EACJ,QAAewD,IAAXf,EAEF,IADAA,EAAS,EACJzC,EAAI,EAAGA,EAAI0K,EAAKjI,SAAUzC,EAC7ByC,GAAUiI,EAAK1K,GAAGyC,OAItB,IAAI8B,EAASlC,EAAOY,YAAYR,GAC5BkI,EAAM,EACV,IAAK3K,EAAI,EAAGA,EAAI0K,EAAKjI,SAAUzC,EAAG,CAChC,IAAI2G,EAAM+D,EAAK1K,GACf,IAAKqC,EAAO8B,SAASwC,GACnB,MAAM,IAAIxD,UAAU,+CAEtBwD,EAAIrC,KAAKC,EAAQoG,GACjBA,GAAOhE,EAAIlE,OAEb,OAAO8B,GA8CTlC,EAAOkB,WAAaA,EA0EpBlB,EAAOT,UAAUwI,WAAY,EAQ7B/H,EAAOT,UAAUgJ,OAAS,WACxB,IAAIxG,EAAMrB,KAAKN,OACf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAEvB,IAAK,IAAI1C,EAAI,EAAGA,EAAIoE,EAAKpE,GAAK,EAC5B6F,EAAK9C,KAAM/C,EAAGA,EAAI,GAEpB,OAAO+C,MAGTV,EAAOT,UAAUiJ,OAAS,WACxB,IAAIzG,EAAMrB,KAAKN,OACf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAEvB,IAAK,IAAI1C,EAAI,EAAGA,EAAIoE,EAAKpE,GAAK,EAC5B6F,EAAK9C,KAAM/C,EAAGA,EAAI,GAClB6F,EAAK9C,KAAM/C,EAAI,EAAGA,EAAI,GAExB,OAAO+C,MAGTV,EAAOT,UAAUkJ,OAAS,WACxB,IAAI1G,EAAMrB,KAAKN,OACf,GAAI2B,EAAM,GAAM,EACd,MAAM,IAAI1B,WAAW,6CAEvB,IAAK,IAAI1C,EAAI,EAAGA,EAAIoE,EAAKpE,GAAK,EAC5B6F,EAAK9C,KAAM/C,EAAGA,EAAI,GAClB6F,EAAK9C,KAAM/C,EAAI,EAAGA,EAAI,GACtB6F,EAAK9C,KAAM/C,EAAI,EAAGA,EAAI,GACtB6F,EAAK9C,KAAM/C,EAAI,EAAGA,EAAI,GAExB,OAAO+C,MAGTV,EAAOT,UAAUkD,SAAW,WAC1B,IAAIrC,EAAuB,EAAdM,KAAKN,OAClB,OAAe,IAAXA,EAAqB,GACA,IAArBsI,UAAUtI,OAAqB+C,EAAUzC,KAAM,EAAGN,GAC/C2C,EAAagE,MAAMrG,KAAMgI,YAGlC1I,EAAOT,UAAUoJ,OAAS,SAAiBlF,GACzC,IAAKzD,EAAO8B,SAAS2B,GAAI,MAAM,IAAI3C,UAAU,6BAC7C,OAAIJ,OAAS+C,GACsB,IAA5BzD,EAAOgI,QAAQtH,KAAM+C,IAG9BzD,EAAOT,UAAUqJ,QAAU,WACzB,IAAItD,EAAM,GACNuD,EAAMpL,EAAQ0J,kBAKlB,OAJIzG,KAAKN,OAAS,IAChBkF,EAAM5E,KAAK+B,SAAS,MAAO,EAAGoG,GAAKC,MAAM,SAASC,KAAK,KACnDrI,KAAKN,OAASyI,IAAKvD,GAAO,UAEzB,WAAaA,EAAM,KAG5BtF,EAAOT,UAAUyI,QAAU,SAAkBgB,EAAQhG,EAAOC,EAAKgG,EAAWC,GAC1E,IAAKlJ,EAAO8B,SAASkH,GACnB,MAAM,IAAIlI,UAAU,6BAgBtB,QAbcK,IAAV6B,IACFA,EAAQ,QAEE7B,IAAR8B,IACFA,EAAM+F,EAASA,EAAO5I,OAAS,QAEfe,IAAd8H,IACFA,EAAY,QAEE9H,IAAZ+H,IACFA,EAAUxI,KAAKN,QAGb4C,EAAQ,GAAKC,EAAM+F,EAAO5I,QAAU6I,EAAY,GAAKC,EAAUxI,KAAKN,OACtE,MAAM,IAAIC,WAAW,sBAGvB,GAAI4I,GAAaC,GAAWlG,GAASC,EACnC,OAAO,EAET,GAAIgG,GAAaC,EACf,OAAQ,EAEV,GAAIlG,GAASC,EACX,OAAO,EAQT,GAAIvC,OAASsI,EAAQ,OAAO,EAS5B,IAPA,IAAId,GAJJgB,KAAa,IADbD,KAAe,GAMXd,GAPJlF,KAAS,IADTD,KAAW,GASPjB,EAAMoE,KAAKC,IAAI8B,EAAGC,GAElBgB,EAAWzI,KAAKiB,MAAMsH,EAAWC,GACjCE,EAAaJ,EAAOrH,MAAMqB,EAAOC,GAE5BtF,EAAI,EAAGA,EAAIoE,IAAOpE,EACzB,GAAIwL,EAASxL,KAAOyL,EAAWzL,GAAI,CACjCuK,EAAIiB,EAASxL,GACbwK,EAAIiB,EAAWzL,GACf,MAIJ,OAAIuK,EAAIC,GAAW,EACfA,EAAID,EAAU,EACX,GA6HTlI,EAAOT,UAAU8J,SAAW,SAAmBlH,EAAKlB,EAAYM,GAC9D,OAAoD,IAA7Cb,KAAKoD,QAAQ3B,EAAKlB,EAAYM,IAGvCvB,EAAOT,UAAUuE,QAAU,SAAkB3B,EAAKlB,EAAYM,GAC5D,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAG/DvB,EAAOT,UAAUwE,YAAc,SAAsB5B,EAAKlB,EAAYM,GACpE,OAAOmC,EAAqBhD,KAAMyB,EAAKlB,EAAYM,GAAU,IAkD/DvB,EAAOT,UAAUmC,MAAQ,SAAgBJ,EAAQsD,EAAQxE,EAAQmB,GAE/D,QAAeJ,IAAXyD,EACFrD,EAAW,OACXnB,EAASM,KAAKN,OACdwE,EAAS,OAEJ,QAAezD,IAAXf,GAA0C,iBAAXwE,EACxCrD,EAAWqD,EACXxE,EAASM,KAAKN,OACdwE,EAAS,MAEJ,KAAI0E,SAAS1E,GAWlB,MAAM,IAAIjE,MACR,2EAXFiE,GAAkB,EACd0E,SAASlJ,IACXA,GAAkB,OACDe,IAAbI,IAAwBA,EAAW,UAEvCA,EAAWnB,EACXA,OAASe,GASb,IAAI2D,EAAYpE,KAAKN,OAASwE,EAG9B,SAFezD,IAAXf,GAAwBA,EAAS0E,KAAW1E,EAAS0E,GAEpDxD,EAAOlB,OAAS,IAAMA,EAAS,GAAKwE,EAAS,IAAOA,EAASlE,KAAKN,OACrE,MAAM,IAAIC,WAAW,0CAGlBkB,IAAUA,EAAW,QAG1B,IADA,IAAIoB,GAAc,IAEhB,OAAQpB,GACN,IAAK,MACH,OAAOoD,EAASjE,KAAMY,EAAQsD,EAAQxE,GAExC,IAAK,OACL,IAAK,QACH,OAAO+E,EAAUzE,KAAMY,EAAQsD,EAAQxE,GAEzC,IAAK,QACH,OAAOiF,EAAW3E,KAAMY,EAAQsD,EAAQxE,GAE1C,IAAK,SACL,IAAK,SACH,OAAOuF,EAAYjF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,SAEH,OAAOwF,EAAYlF,KAAMY,EAAQsD,EAAQxE,GAE3C,IAAK,OACL,IAAK,QACL,IAAK,UACL,IAAK,WACH,OAAOyF,EAAUnF,KAAMY,EAAQsD,EAAQxE,GAEzC,QACE,GAAIuC,EAAa,MAAM,IAAI7B,UAAU,qBAAuBS,GAC5DA,GAAY,GAAKA,GAAUuB,cAC3BH,GAAc,IAKtB3C,EAAOT,UAAUgK,OAAS,WACxB,MAAO,CACLnH,KAAM,SACNC,KAAMmH,MAAMjK,UAAUoC,MAAM7D,KAAK4C,KAAK+I,MAAQ/I,KAAM,KA4GxD,SAAS0C,EAAYkB,EAAKtB,EAAOC,GAC/B,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAItF,EAAIqF,EAAOrF,EAAIsF,IAAOtF,EAC7B+L,GAAOtF,OAAO0C,aAAsB,IAATxC,EAAI3G,IAEjC,OAAO+L,EAGT,SAASrG,EAAaiB,EAAKtB,EAAOC,GAChC,IAAIyG,EAAM,GACVzG,EAAMkD,KAAKC,IAAI9B,EAAIlE,OAAQ6C,GAE3B,IAAK,IAAItF,EAAIqF,EAAOrF,EAAIsF,IAAOtF,EAC7B+L,GAAOtF,OAAO0C,aAAaxC,EAAI3G,IAEjC,OAAO+L,EAGT,SAASxG,EAAUoB,EAAKtB,EAAOC,GAC7B,IAAIlB,EAAMuC,EAAIlE,SAET4C,GAASA,EAAQ,KAAGA,EAAQ,KAC5BC,GAAOA,EAAM,GAAKA,EAAMlB,KAAKkB,EAAMlB,GAGxC,IADA,IAAI4H,EAAM,GACDhM,EAAIqF,EAAOrF,EAAIsF,IAAOtF,EAC7BgM,GAAOC,EAAMtF,EAAI3G,IAEnB,OAAOgM,EAGT,SAASpG,EAAce,EAAKtB,EAAOC,GAGjC,IAFA,IAAI4G,EAAQvF,EAAI3C,MAAMqB,EAAOC,GACzBoD,EAAM,GACD1I,EAAI,EAAGA,EAAIkM,EAAMzJ,OAAQzC,GAAK,EACrC0I,GAAOjC,OAAO0C,aAAa+C,EAAMlM,GAAoB,IAAfkM,EAAMlM,EAAI,IAElD,OAAO0I,EA0CT,SAASyD,EAAalF,EAAQmF,EAAK3J,GACjC,GAAKwE,EAAS,GAAO,GAAKA,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBAC3D,GAAIuE,EAASmF,EAAM3J,EAAQ,MAAM,IAAIC,WAAW,yCA+JlD,SAAS2J,EAAU1F,EAAK1F,EAAOgG,EAAQmF,EAAKlB,EAAKzC,GAC/C,IAAKpG,EAAO8B,SAASwC,GAAM,MAAM,IAAIxD,UAAU,+CAC/C,GAAIlC,EAAQiK,GAAOjK,EAAQwH,EAAK,MAAM,IAAI/F,WAAW,qCACrD,GAAIuE,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBAkDtD,SAAS4J,EAAmB3F,EAAK1F,EAAOgG,EAAQsF,GAC1CtL,EAAQ,IAAGA,EAAQ,MAASA,EAAQ,GACxC,IAAK,IAAIjB,EAAI,EAAG+G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIjH,EAAI+G,IAAK/G,EAC7D2G,EAAIM,EAASjH,IAAMiB,EAAS,KAAS,GAAKsL,EAAevM,EAAI,EAAIA,MAClC,GAA5BuM,EAAevM,EAAI,EAAIA,GA8B9B,SAASwM,EAAmB7F,EAAK1F,EAAOgG,EAAQsF,GAC1CtL,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GAC5C,IAAK,IAAIjB,EAAI,EAAG+G,EAAIyB,KAAKC,IAAI9B,EAAIlE,OAASwE,EAAQ,GAAIjH,EAAI+G,IAAK/G,EAC7D2G,EAAIM,EAASjH,GAAMiB,IAAuC,GAA5BsL,EAAevM,EAAI,EAAIA,GAAU,IAmJnE,SAASyM,EAAc9F,EAAK1F,EAAOgG,EAAQmF,EAAKlB,EAAKzC,GACnD,GAAIxB,EAASmF,EAAMzF,EAAIlE,OAAQ,MAAM,IAAIC,WAAW,sBACpD,GAAIuE,EAAS,EAAG,MAAM,IAAIvE,WAAW,sBAGvC,SAASgK,EAAY/F,EAAK1F,EAAOgG,EAAQsF,EAAcI,GAKrD,OAJKA,GACHF,EAAa9F,EAAK1F,EAAOgG,EAAQ,GAEnC/E,EAAQ6B,MAAM4C,EAAK1F,EAAOgG,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EAWlB,SAAS2F,EAAajG,EAAK1F,EAAOgG,EAAQsF,EAAcI,GAKtD,OAJKA,GACHF,EAAa9F,EAAK1F,EAAOgG,EAAQ,GAEnC/E,EAAQ6B,MAAM4C,EAAK1F,EAAOgG,EAAQsF,EAAc,GAAI,GAC7CtF,EAAS,EA/clB5E,EAAOT,UAAUoC,MAAQ,SAAgBqB,EAAOC,GAC9C,IAoBIuH,EApBAzI,EAAMrB,KAAKN,OAqBf,IApBA4C,IAAUA,GAGE,GACVA,GAASjB,GACG,IAAGiB,EAAQ,GACdA,EAAQjB,IACjBiB,EAAQjB,IANVkB,OAAc9B,IAAR8B,EAAoBlB,IAAQkB,GASxB,GACRA,GAAOlB,GACG,IAAGkB,EAAM,GACVA,EAAMlB,IACfkB,EAAMlB,GAGJkB,EAAMD,IAAOC,EAAMD,GAGnBhD,EAAOC,qBACTuK,EAAS9J,KAAK4G,SAAStE,EAAOC,IACvB1C,UAAYP,EAAOT,cACrB,CACL,IAAIkL,EAAWxH,EAAMD,EACrBwH,EAAS,IAAIxK,EAAOyK,OAAUtJ,GAC9B,IAAK,IAAIxD,EAAI,EAAGA,EAAI8M,IAAY9M,EAC9B6M,EAAO7M,GAAK+C,KAAK/C,EAAIqF,GAIzB,OAAOwH,GAWTxK,EAAOT,UAAUmL,WAAa,SAAqB9F,EAAQ1D,EAAYoJ,GACrE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAHA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACNhN,EAAI,IACCA,EAAIuD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAASjH,GAAKgN,EAG5B,OAAOxI,GAGTnC,EAAOT,UAAUqL,WAAa,SAAqBhG,EAAQ1D,EAAYoJ,GACrE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GACHR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKvC,IAFA,IAAI+B,EAAMzB,KAAKkE,IAAW1D,GACtByJ,EAAM,EACHzJ,EAAa,IAAMyJ,GAAO,MAC/BxI,GAAOzB,KAAKkE,IAAW1D,GAAcyJ,EAGvC,OAAOxI,GAGTnC,EAAOT,UAAUsL,UAAY,SAAoBjG,EAAQ0F,GAEvD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,IAGd5E,EAAOT,UAAUuL,aAAe,SAAuBlG,EAAQ0F,GAE7D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCM,KAAKkE,GAAWlE,KAAKkE,EAAS,IAAM,GAG7C5E,EAAOT,UAAUgF,aAAe,SAAuBK,EAAQ0F,GAE7D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACnCM,KAAKkE,IAAW,EAAKlE,KAAKkE,EAAS,IAG7C5E,EAAOT,UAAUwL,aAAe,SAAuBnG,EAAQ0F,GAG7D,OAFKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,SAElCM,KAAKkE,GACTlE,KAAKkE,EAAS,IAAM,EACpBlE,KAAKkE,EAAS,IAAM,IACD,SAAnBlE,KAAKkE,EAAS,IAGrB5E,EAAOT,UAAUyL,aAAe,SAAuBpG,EAAQ0F,GAG7D,OAFKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAEpB,SAAfM,KAAKkE,IACTlE,KAAKkE,EAAS,IAAM,GACrBlE,KAAKkE,EAAS,IAAM,EACrBlE,KAAKkE,EAAS,KAGlB5E,EAAOT,UAAU0L,UAAY,SAAoBrG,EAAQ1D,EAAYoJ,GACnE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAHA,IAAI+B,EAAMzB,KAAKkE,GACX+F,EAAM,EACNhN,EAAI,IACCA,EAAIuD,IAAeyJ,GAAO,MACjCxI,GAAOzB,KAAKkE,EAASjH,GAAKgN,EAM5B,OAFIxI,IAFJwI,GAAO,OAESxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAEhCiB,GAGTnC,EAAOT,UAAU4L,UAAY,SAAoBvG,EAAQ1D,EAAYoJ,GACnE1F,GAAkB,EAClB1D,GAA0B,EACrBoJ,GAAUR,EAAYlF,EAAQ1D,EAAYR,KAAKN,QAKpD,IAHA,IAAIzC,EAAIuD,EACJyJ,EAAM,EACNxI,EAAMzB,KAAKkE,IAAWjH,GACnBA,EAAI,IAAMgN,GAAO,MACtBxI,GAAOzB,KAAKkE,IAAWjH,GAAKgN,EAM9B,OAFIxI,IAFJwI,GAAO,OAESxI,GAAOgE,KAAK+E,IAAI,EAAG,EAAIhK,IAEhCiB,GAGTnC,EAAOT,UAAU6L,SAAW,SAAmBxG,EAAQ0F,GAErD,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACtB,IAAfM,KAAKkE,IAC0B,GAA5B,IAAOlE,KAAKkE,GAAU,GADKlE,KAAKkE,IAI3C5E,EAAOT,UAAU8L,YAAc,SAAsBzG,EAAQ0F,GACtDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,GAAWlE,KAAKkE,EAAS,IAAM,EAC9C,OAAc,MAANzC,EAAsB,WAANA,EAAmBA,GAG7CnC,EAAOT,UAAU+L,YAAc,SAAsB1G,EAAQ0F,GACtDA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAC3C,IAAI+B,EAAMzB,KAAKkE,EAAS,GAAMlE,KAAKkE,IAAW,EAC9C,OAAc,MAANzC,EAAsB,WAANA,EAAmBA,GAG7CnC,EAAOT,UAAUgM,YAAc,SAAsB3G,EAAQ0F,GAG3D,OAFKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAEnCM,KAAKkE,GACVlE,KAAKkE,EAAS,IAAM,EACpBlE,KAAKkE,EAAS,IAAM,GACpBlE,KAAKkE,EAAS,IAAM,IAGzB5E,EAAOT,UAAUiM,YAAc,SAAsB5G,EAAQ0F,GAG3D,OAFKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QAEnCM,KAAKkE,IAAW,GACrBlE,KAAKkE,EAAS,IAAM,GACpBlE,KAAKkE,EAAS,IAAM,EACpBlE,KAAKkE,EAAS,IAGnB5E,EAAOT,UAAUkM,YAAc,SAAsB7G,EAAQ0F,GAE3D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOT,UAAUmM,YAAc,SAAsB9G,EAAQ0F,GAE3D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAG/C5E,EAAOT,UAAUoM,aAAe,SAAuB/G,EAAQ0F,GAE7D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAM,GAAI,IAG9C5E,EAAOT,UAAUqM,aAAe,SAAuBhH,EAAQ0F,GAE7D,OADKA,GAAUR,EAAYlF,EAAQ,EAAGlE,KAAKN,QACpCP,EAAQwE,KAAK3D,KAAMkE,GAAQ,EAAO,GAAI,IAS/C5E,EAAOT,UAAUsM,YAAc,SAAsBjN,EAAOgG,EAAQ1D,EAAYoJ,IAC9E1L,GAASA,EACTgG,GAAkB,EAClB1D,GAA0B,EACrBoJ,IAEHN,EAAStJ,KAAM9B,EAAOgG,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAIyJ,EAAM,EACNhN,EAAI,EAER,IADA+C,KAAKkE,GAAkB,IAARhG,IACNjB,EAAIuD,IAAeyJ,GAAO,MACjCjK,KAAKkE,EAASjH,GAAMiB,EAAQ+L,EAAO,IAGrC,OAAO/F,EAAS1D,GAGlBlB,EAAOT,UAAUuM,YAAc,SAAsBlN,EAAOgG,EAAQ1D,EAAYoJ,IAC9E1L,GAASA,EACTgG,GAAkB,EAClB1D,GAA0B,EACrBoJ,IAEHN,EAAStJ,KAAM9B,EAAOgG,EAAQ1D,EADfiF,KAAK+E,IAAI,EAAG,EAAIhK,GAAc,EACO,GAGtD,IAAIvD,EAAIuD,EAAa,EACjByJ,EAAM,EAEV,IADAjK,KAAKkE,EAASjH,GAAa,IAARiB,IACVjB,GAAK,IAAMgN,GAAO,MACzBjK,KAAKkE,EAASjH,GAAMiB,EAAQ+L,EAAO,IAGrC,OAAO/F,EAAS1D,GAGlBlB,EAAOT,UAAUwM,WAAa,SAAqBnN,EAAOgG,EAAQ0F,GAMhE,OALA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,IAAM,GACjD5E,EAAOC,sBAAqBrB,EAAQuH,KAAK6F,MAAMpN,IACpD8B,KAAKkE,GAAmB,IAARhG,EACTgG,EAAS,GAWlB5E,EAAOT,UAAU0M,cAAgB,SAAwBrN,EAAOgG,EAAQ0F,GAUtE,OATA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,MAAQ,GACpD5E,EAAOC,qBACTS,KAAKkE,GAAmB,IAARhG,EAChB8B,KAAKkE,EAAS,GAAMhG,IAAU,GAE9BqL,EAAkBvJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAU2M,cAAgB,SAAwBtN,EAAOgG,EAAQ0F,GAUtE,OATA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,MAAQ,GACpD5E,EAAOC,qBACTS,KAAKkE,GAAWhG,IAAU,EAC1B8B,KAAKkE,EAAS,GAAc,IAARhG,GAEpBqL,EAAkBvJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAUlB5E,EAAOT,UAAU4M,cAAgB,SAAwBvN,EAAOgG,EAAQ0F,GAYtE,OAXA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,WAAY,GACxD5E,EAAOC,qBACTS,KAAKkE,EAAS,GAAMhG,IAAU,GAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,GAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,EAC9B8B,KAAKkE,GAAmB,IAARhG,GAEhBuL,EAAkBzJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAU6M,cAAgB,SAAwBxN,EAAOgG,EAAQ0F,GAYtE,OAXA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,WAAY,GACxD5E,EAAOC,qBACTS,KAAKkE,GAAWhG,IAAU,GAC1B8B,KAAKkE,EAAS,GAAMhG,IAAU,GAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,EAC9B8B,KAAKkE,EAAS,GAAc,IAARhG,GAEpBuL,EAAkBzJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAU8M,WAAa,SAAqBzN,EAAOgG,EAAQ1D,EAAYoJ,GAG5E,GAFA1L,GAASA,EACTgG,GAAkB,GACb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GAEzC8I,EAAStJ,KAAM9B,EAAOgG,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI3O,EAAI,EACJgN,EAAM,EACN4B,EAAM,EAEV,IADA7L,KAAKkE,GAAkB,IAARhG,IACNjB,EAAIuD,IAAeyJ,GAAO,MAC7B/L,EAAQ,GAAa,IAAR2N,GAAsC,IAAzB7L,KAAKkE,EAASjH,EAAI,KAC9C4O,EAAM,GAER7L,KAAKkE,EAASjH,IAAOiB,EAAQ+L,GAAQ,GAAK4B,EAAM,IAGlD,OAAO3H,EAAS1D,GAGlBlB,EAAOT,UAAUiN,WAAa,SAAqB5N,EAAOgG,EAAQ1D,EAAYoJ,GAG5E,GAFA1L,GAASA,EACTgG,GAAkB,GACb0F,EAAU,CACb,IAAIgC,EAAQnG,KAAK+E,IAAI,EAAG,EAAIhK,EAAa,GAEzC8I,EAAStJ,KAAM9B,EAAOgG,EAAQ1D,EAAYoL,EAAQ,GAAIA,GAGxD,IAAI3O,EAAIuD,EAAa,EACjByJ,EAAM,EACN4B,EAAM,EAEV,IADA7L,KAAKkE,EAASjH,GAAa,IAARiB,IACVjB,GAAK,IAAMgN,GAAO,MACrB/L,EAAQ,GAAa,IAAR2N,GAAsC,IAAzB7L,KAAKkE,EAASjH,EAAI,KAC9C4O,EAAM,GAER7L,KAAKkE,EAASjH,IAAOiB,EAAQ+L,GAAQ,GAAK4B,EAAM,IAGlD,OAAO3H,EAAS1D,GAGlBlB,EAAOT,UAAUkN,UAAY,SAAoB7N,EAAOgG,EAAQ0F,GAO9D,OANA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,KAAO,KAClD5E,EAAOC,sBAAqBrB,EAAQuH,KAAK6F,MAAMpN,IAChDA,EAAQ,IAAGA,EAAQ,IAAOA,EAAQ,GACtC8B,KAAKkE,GAAmB,IAARhG,EACTgG,EAAS,GAGlB5E,EAAOT,UAAUmN,aAAe,SAAuB9N,EAAOgG,EAAQ0F,GAUpE,OATA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,OAAS,OACrD5E,EAAOC,qBACTS,KAAKkE,GAAmB,IAARhG,EAChB8B,KAAKkE,EAAS,GAAMhG,IAAU,GAE9BqL,EAAkBvJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAUoN,aAAe,SAAuB/N,EAAOgG,EAAQ0F,GAUpE,OATA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,OAAS,OACrD5E,EAAOC,qBACTS,KAAKkE,GAAWhG,IAAU,EAC1B8B,KAAKkE,EAAS,GAAc,IAARhG,GAEpBqL,EAAkBvJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAUqN,aAAe,SAAuBhO,EAAOgG,EAAQ0F,GAYpE,OAXA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,YAAa,YACzD5E,EAAOC,qBACTS,KAAKkE,GAAmB,IAARhG,EAChB8B,KAAKkE,EAAS,GAAMhG,IAAU,EAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,GAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,IAE9BuL,EAAkBzJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAGlB5E,EAAOT,UAAUsN,aAAe,SAAuBjO,EAAOgG,EAAQ0F,GAapE,OAZA1L,GAASA,EACTgG,GAAkB,EACb0F,GAAUN,EAAStJ,KAAM9B,EAAOgG,EAAQ,EAAG,YAAa,YACzDhG,EAAQ,IAAGA,EAAQ,WAAaA,EAAQ,GACxCoB,EAAOC,qBACTS,KAAKkE,GAAWhG,IAAU,GAC1B8B,KAAKkE,EAAS,GAAMhG,IAAU,GAC9B8B,KAAKkE,EAAS,GAAMhG,IAAU,EAC9B8B,KAAKkE,EAAS,GAAc,IAARhG,GAEpBuL,EAAkBzJ,KAAM9B,EAAOgG,GAAQ,GAElCA,EAAS,GAgBlB5E,EAAOT,UAAUuN,aAAe,SAAuBlO,EAAOgG,EAAQ0F,GACpE,OAAOD,EAAW3J,KAAM9B,EAAOgG,GAAQ,EAAM0F,IAG/CtK,EAAOT,UAAUwN,aAAe,SAAuBnO,EAAOgG,EAAQ0F,GACpE,OAAOD,EAAW3J,KAAM9B,EAAOgG,GAAQ,EAAO0F,IAWhDtK,EAAOT,UAAUyN,cAAgB,SAAwBpO,EAAOgG,EAAQ0F,GACtE,OAAOC,EAAY7J,KAAM9B,EAAOgG,GAAQ,EAAM0F,IAGhDtK,EAAOT,UAAU0N,cAAgB,SAAwBrO,EAAOgG,EAAQ0F,GACtE,OAAOC,EAAY7J,KAAM9B,EAAOgG,GAAQ,EAAO0F,IAIjDtK,EAAOT,UAAU0C,KAAO,SAAe+G,EAAQkE,EAAalK,EAAOC,GAQjE,GAPKD,IAAOA,EAAQ,GACfC,GAAe,IAARA,IAAWA,EAAMvC,KAAKN,QAC9B8M,GAAelE,EAAO5I,SAAQ8M,EAAclE,EAAO5I,QAClD8M,IAAaA,EAAc,GAC5BjK,EAAM,GAAKA,EAAMD,IAAOC,EAAMD,GAG9BC,IAAQD,EAAO,OAAO,EAC1B,GAAsB,IAAlBgG,EAAO5I,QAAgC,IAAhBM,KAAKN,OAAc,OAAO,EAGrD,GAAI8M,EAAc,EAChB,MAAM,IAAI7M,WAAW,6BAEvB,GAAI2C,EAAQ,GAAKA,GAAStC,KAAKN,OAAQ,MAAM,IAAIC,WAAW,6BAC5D,GAAI4C,EAAM,EAAG,MAAM,IAAI5C,WAAW,2BAG9B4C,EAAMvC,KAAKN,SAAQ6C,EAAMvC,KAAKN,QAC9B4I,EAAO5I,OAAS8M,EAAcjK,EAAMD,IACtCC,EAAM+F,EAAO5I,OAAS8M,EAAclK,GAGtC,IACIrF,EADAoE,EAAMkB,EAAMD,EAGhB,GAAItC,OAASsI,GAAUhG,EAAQkK,GAAeA,EAAcjK,EAE1D,IAAKtF,EAAIoE,EAAM,EAAGpE,GAAK,IAAKA,EAC1BqL,EAAOrL,EAAIuP,GAAexM,KAAK/C,EAAIqF,QAEhC,GAAIjB,EAAM,MAAS/B,EAAOC,oBAE/B,IAAKtC,EAAI,EAAGA,EAAIoE,IAAOpE,EACrBqL,EAAOrL,EAAIuP,GAAexM,KAAK/C,EAAIqF,QAGrC1C,WAAWf,UAAU4N,IAAIrP,KACvBkL,EACAtI,KAAK4G,SAAStE,EAAOA,EAAQjB,GAC7BmL,GAIJ,OAAOnL,GAOT/B,EAAOT,UAAUsI,KAAO,SAAe1F,EAAKa,EAAOC,EAAK1B,GAEtD,GAAmB,iBAARY,EAAkB,CAS3B,GARqB,iBAAVa,GACTzB,EAAWyB,EACXA,EAAQ,EACRC,EAAMvC,KAAKN,QACa,iBAAR6C,IAChB1B,EAAW0B,EACXA,EAAMvC,KAAKN,QAEM,IAAf+B,EAAI/B,OAAc,CACpB,IAAIgN,EAAOjL,EAAIsD,WAAW,GACtB2H,EAAO,MACTjL,EAAMiL,GAGV,QAAiBjM,IAAbI,GAA8C,iBAAbA,EACnC,MAAM,IAAIT,UAAU,6BAEtB,GAAwB,iBAAbS,IAA0BvB,EAAOwB,WAAWD,GACrD,MAAM,IAAIT,UAAU,qBAAuBS,OAErB,iBAARY,IAChBA,GAAY,KAId,GAAIa,EAAQ,GAAKtC,KAAKN,OAAS4C,GAAStC,KAAKN,OAAS6C,EACpD,MAAM,IAAI5C,WAAW,sBAGvB,GAAI4C,GAAOD,EACT,OAAOtC,KAQT,IAAI/C,EACJ,GANAqF,KAAkB,EAClBC,OAAc9B,IAAR8B,EAAoBvC,KAAKN,OAAS6C,IAAQ,EAE3Cd,IAAKA,EAAM,GAGG,iBAARA,EACT,IAAKxE,EAAIqF,EAAOrF,EAAIsF,IAAOtF,EACzB+C,KAAK/C,GAAKwE,MAEP,CACL,IAAI0H,EAAQ7J,EAAO8B,SAASK,GACxBA,EACAS,EAAY,IAAI5C,EAAOmC,EAAKZ,GAAUkB,YACtCV,EAAM8H,EAAMzJ,OAChB,IAAKzC,EAAI,EAAGA,EAAIsF,EAAMD,IAASrF,EAC7B+C,KAAK/C,EAAIqF,GAAS6G,EAAMlM,EAAIoE,GAIhC,OAAOrB,MAMT,IAAI2M,EAAoB,qBAmBxB,SAASzD,EAAOxK,GACd,OAAIA,EAAI,GAAW,IAAMA,EAAEqD,SAAS,IAC7BrD,EAAEqD,SAAS,IAGpB,SAASG,EAAatB,EAAQwE,GAE5B,IAAIa,EADJb,EAAQA,GAASwH,IAMjB,IAJA,IAAIlN,EAASkB,EAAOlB,OAChBmN,EAAgB,KAChB1D,EAAQ,GAEHlM,EAAI,EAAGA,EAAIyC,IAAUzC,EAAG,CAI/B,IAHAgJ,EAAYrF,EAAOmE,WAAW9H,IAGd,OAAUgJ,EAAY,MAAQ,CAE5C,IAAK4G,EAAe,CAElB,GAAI5G,EAAY,MAAQ,EAEjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SACK,GAAI7H,EAAI,IAAMyC,EAAQ,EAEtB0F,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C,SAIF+H,EAAgB5G,EAEhB,SAIF,GAAIA,EAAY,MAAQ,EACjBb,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAC9C+H,EAAgB5G,EAChB,SAIFA,EAAkE,OAArD4G,EAAgB,OAAU,GAAK5G,EAAY,YAC/C4G,IAEJzH,GAAS,IAAM,GAAG+D,EAAMrE,KAAK,IAAM,IAAM,KAMhD,GAHA+H,EAAgB,KAGZ5G,EAAY,IAAM,CACpB,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KAAKmB,QACN,GAAIA,EAAY,KAAO,CAC5B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KACJmB,GAAa,EAAM,IACP,GAAZA,EAAmB,UAEhB,GAAIA,EAAY,MAAS,CAC9B,IAAKb,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KACJmB,GAAa,GAAM,IACnBA,GAAa,EAAM,GAAO,IACd,GAAZA,EAAmB,SAEhB,MAAIA,EAAY,SASrB,MAAM,IAAIhG,MAAM,sBARhB,IAAKmF,GAAS,GAAK,EAAG,MACtB+D,EAAMrE,KACJmB,GAAa,GAAO,IACpBA,GAAa,GAAM,GAAO,IAC1BA,GAAa,EAAM,GAAO,IACd,GAAZA,EAAmB,MAOzB,OAAOkD,EA4BT,SAAShH,EAAeyC,GACtB,OAAO3F,EAAO6N,YAhIhB,SAAsBlI,GAIpB,IAFAA,EAUF,SAAqBA,GACnB,OAAIA,EAAImI,KAAanI,EAAImI,OAClBnI,EAAIoI,QAAQ,aAAc,IAZ3BC,CAAWrI,GAAKoI,QAAQL,EAAmB,KAEzCjN,OAAS,EAAG,MAAO,GAE3B,KAAOkF,EAAIlF,OAAS,GAAM,GACxBkF,GAAY,IAEd,OAAOA,EAuHmBsI,CAAYtI,IAGxC,SAASF,EAAYyI,EAAKC,EAAKlJ,EAAQxE,GACrC,IAAK,IAAIzC,EAAI,EAAGA,EAAIyC,KACbzC,EAAIiH,GAAUkJ,EAAI1N,QAAYzC,GAAKkQ,EAAIzN,UADhBzC,EAE5BmQ,EAAInQ,EAAIiH,GAAUiJ,EAAIlQ,GAExB,OAAOA,K,oQCvvDT,IAAIoQ,EAGJA,EAAK,WACJ,OAAOrN,KADH,GAIL,IAECqN,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACR,MAAOzG,GAEc,YAAlB,oBAAO0G,OAAP,cAAOA,WAAqBF,EAAIE,QAOrCvQ,EAAOD,QAAUsQ,G,cClBjB,IAOIG,EACAC,EARAC,EAAU1Q,EAAOD,QAAU,GAU/B,SAAS4Q,IACL,MAAM,IAAI1N,MAAM,mCAEpB,SAAS2N,IACL,MAAM,IAAI3N,MAAM,qCAsBpB,SAAS4N,EAAWC,GAChB,GAAIN,IAAqBO,WAErB,OAAOA,WAAWD,EAAK,GAG3B,IAAKN,IAAqBG,IAAqBH,IAAqBO,WAEhE,OADAP,EAAmBO,WACZA,WAAWD,EAAK,GAE3B,IAEI,OAAON,EAAiBM,EAAK,GAC/B,MAAMjH,GACJ,IAEI,OAAO2G,EAAiBpQ,KAAK,KAAM0Q,EAAK,GAC1C,MAAMjH,GAEJ,OAAO2G,EAAiBpQ,KAAK4C,KAAM8N,EAAK,MAvCnD,WACG,IAEQN,EADsB,mBAAfO,WACYA,WAEAJ,EAEzB,MAAO9G,GACL2G,EAAmBG,EAEvB,IAEQF,EADwB,mBAAjBO,aACcA,aAEAJ,EAE3B,MAAO/G,GACL4G,EAAqBG,GAjB5B,GAwED,IAEIK,EAFAC,EAAQ,GACRC,GAAW,EAEXC,GAAc,EAElB,SAASC,IACAF,GAAaF,IAGlBE,GAAW,EACPF,EAAavO,OACbwO,EAAQD,EAAavG,OAAOwG,GAE5BE,GAAc,EAEdF,EAAMxO,QACN4O,KAIR,SAASA,IACL,IAAIH,EAAJ,CAGA,IAAII,EAAUV,EAAWQ,GACzBF,GAAW,EAGX,IADA,IAAI9M,EAAM6M,EAAMxO,OACV2B,GAAK,CAGP,IAFA4M,EAAeC,EACfA,EAAQ,KACCE,EAAa/M,GACd4M,GACAA,EAAaG,GAAYI,MAGjCJ,GAAc,EACd/M,EAAM6M,EAAMxO,OAEhBuO,EAAe,KACfE,GAAW,EAnEf,SAAyBM,GACrB,GAAIhB,IAAuBO,aAEvB,OAAOA,aAAaS,GAGxB,IAAKhB,IAAuBG,IAAwBH,IAAuBO,aAEvE,OADAP,EAAqBO,aACdA,aAAaS,GAExB,IAEWhB,EAAmBgB,GAC5B,MAAO5H,GACL,IAEI,OAAO4G,EAAmBrQ,KAAK,KAAMqR,GACvC,MAAO5H,GAGL,OAAO4G,EAAmBrQ,KAAK4C,KAAMyO,KAgD7CC,CAAgBH,IAiBpB,SAASI,EAAKb,EAAKxN,GACfN,KAAK8N,IAAMA,EACX9N,KAAKM,MAAQA,EAYjB,SAASsO,KA5BTlB,EAAQmB,SAAW,SAAUf,GACzB,IAAIgB,EAAO,IAAIhG,MAAMd,UAAUtI,OAAS,GACxC,GAAIsI,UAAUtI,OAAS,EACnB,IAAK,IAAIzC,EAAI,EAAGA,EAAI+K,UAAUtI,OAAQzC,IAClC6R,EAAK7R,EAAI,GAAK+K,UAAU/K,GAGhCiR,EAAMpJ,KAAK,IAAI6J,EAAKb,EAAKgB,IACJ,IAAjBZ,EAAMxO,QAAiByO,GACvBN,EAAWS,IASnBK,EAAK9P,UAAU2P,IAAM,WACjBxO,KAAK8N,IAAIzH,MAAM,KAAMrG,KAAKM,QAE9BoN,EAAQqB,MAAQ,UAChBrB,EAAQsB,SAAU,EAClBtB,EAAQuB,IAAM,GACdvB,EAAQwB,KAAO,GACfxB,EAAQyB,QAAU,GAClBzB,EAAQ0B,SAAW,GAInB1B,EAAQ2B,GAAKT,EACblB,EAAQ4B,YAAcV,EACtBlB,EAAQ6B,KAAOX,EACflB,EAAQ8B,IAAMZ,EACdlB,EAAQ+B,eAAiBb,EACzBlB,EAAQgC,mBAAqBd,EAC7BlB,EAAQiC,KAAOf,EACflB,EAAQkC,gBAAkBhB,EAC1BlB,EAAQmC,oBAAsBjB,EAE9BlB,EAAQoC,UAAY,SAAUtS,GAAQ,MAAO,IAE7CkQ,EAAQqC,QAAU,SAAUvS,GACxB,MAAM,IAAIyC,MAAM,qCAGpByN,EAAQsC,IAAM,WAAc,MAAO,KACnCtC,EAAQuC,MAAQ,SAAUhN,GACtB,MAAM,IAAIhD,MAAM,mCAEpByN,EAAQwC,MAAQ,WAAa,OAAO,I,2DCvLpC,iB,g8CAOIhR,oBAAQ,IALXiR,iB,SAAAA,iBACAC,c,SAAAA,cACAC,a,SAAAA,aACAC,yB,SAAAA,yBACAC,yB,SAAAA,yBAEKC,UAAYtR,oBAAQ,IAEpBuR,eAAiBL,cAAc,wBAC/BM,iBAAmBN,cAAc,uBACjCO,SAAWP,cAAc,eAEzBQ,SAAW,SAAAC,GAChB,KAAMA,aAAiBjR,YAAciR,aAAiBxQ,aAAef,OAAO8B,SAASyP,IACpF,MAAM,IAAIzQ,UAAJ,8GAA6HyQ,GAA7H,MAGP,IAAMrP,EAASqP,aAAiBjR,WAAaiR,EAAQ,IAAIjR,WAAWiR,GAEpE,GAAMrP,GAAUA,EAAO9B,OAAS,EAAhC,CAIA,IAAMoR,EAAQ,SAACC,EAAQC,GACtBA,EAAU,cAAH,CACN9M,OAAQ,GACL8M,GAGJ,IAAK,IAAI/T,EAAI,EAAGA,EAAI8T,EAAOrR,OAAQzC,IAElC,GAAI+T,EAAQC,MAEX,GAAIF,EAAO9T,MAAQ+T,EAAQC,KAAKhU,GAAKuE,EAAOvE,EAAI+T,EAAQ9M,SACvD,OAAO,OAEF,GAAI6M,EAAO9T,KAAOuE,EAAOvE,EAAI+T,EAAQ9M,QAC3C,OAAO,EAIT,OAAO,GAGFgN,EAAc,SAACH,EAAQC,GAAT,OAAqBF,EAAMV,cAAcW,GAASC,IAEtE,GAAIF,EAAM,CAAC,IAAM,IAAM,MACtB,MAAO,CACNzH,IAAK,MACL8H,KAAM,cAIR,GAAIL,EAAM,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,KAAQ,CAQ5D,IACMM,EAA2B5P,EAAO6P,WAAU,SAACC,EAAIrU,GAAL,OAAWA,GAD1C,IAC2E,KAAduE,EAAOvE,IAAiC,KAAlBuE,EAAOvE,EAAI,IAAiC,KAAlBuE,EAAOvE,EAAI,IAAiC,KAAlBuE,EAAOvE,EAAI,MAC/JsU,EAAS/P,EAAOoF,SAFH,GAEwBwK,GAE3C,OAAIG,EAAOF,WAAU,SAACC,EAAIrU,GAAL,OAAyB,KAAdsU,EAAOtU,IAAiC,KAAlBsU,EAAOtU,EAAI,IAAiC,KAAlBsU,EAAOtU,EAAI,IAAiC,KAAlBsU,EAAOtU,EAAI,OAAgB,EAC7H,CACNoM,IAAK,OACL8H,KAAM,cAID,CACN9H,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,KACtB,MAAO,CACNzH,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IAC5C,MAAO,CACNmF,IAAK,OACL8H,KAAM,cAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,OACL8H,KAAM,cAKR,IACEL,EAAM,CAAC,GAAM,GAAM,GAAM,KAASA,EAAM,CAAC,GAAM,GAAM,EAAK,OAC3DA,EAAM,CAAC,GAAM,IAAO,CAAC5M,OAAQ,IAE7B,MAAO,CACNmF,IAAK,MACL8H,KAAM,qBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,EAAM,EAAM,EAAM,EAAM,KAC1D,MAAO,CACNzH,IAAK,MACL8H,KAAM,uBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,MACxBA,EAAM,CAAC,GAAM,IAAM,IAAM,GAAO,CAAC5M,OAAQ,KAAO4M,EAAM,CAAC,EAAM,EAAM,EAAM,GAAO,CAAC5M,OAAQ,MAE1F4M,EAAM,CAAC,EAAM,IAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,GAAO,CAAC5M,OAAQ,IAE3G,MAAO,CACNmF,IAAK,MACL8H,KAAM,oBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,EAAM,EAAM,EAAM,EAAM,MAChDA,EAAM,CAAC,GAAM,EAAM,IAAM,GAAO,CAAC5M,OAAQ,KAC1C4M,EAAM,CAAC,GAAM,EAAM,IAAM,GAAO,CAAC5M,OAAQ,KAEzC,MAAO,CACNmF,IAAK,MACL8H,KAAM,qBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,KACzBA,EAAM,CAAC,GAAM,EAAM,IAAM,GAAO,CAAC5M,OAAQ,IAEzC,MAAO,CACNmF,IAAK,MACL8H,KAAM,qBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,EAAM,GAAM,EAAM,EAAM,EAAM,IAAM,IAAM,IAAM,MAC5E,MAAO,CACNzH,IAAK,MACL8H,KAAM,yBAKR,GAAID,EAAY,mBACf,MAAO,CACN7H,IAAK,MACL8H,KAAM,wBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,KACzBA,EAAM,CAAC,GAAM,GAAM,EAAK,KAExB,MAAO,CACNzH,IAAK,MACL8H,KAAM,cAIR,GAAIL,EAAM,CAAC,GAAM,KAChB,MAAO,CACNzH,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,MACtB,MAAO,CACNzH,IAAK,MACL8H,KAAM,sBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,6BAMR,IAAMK,EAAY,CAAC,GAAM,GAAM,EAAK,GACpC,GAAIV,EAAMU,GAAY,CACrB,GACCV,EAAM,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,IAAM,KAAO,CAAC5M,OAAQ,KAEzL,MAAO,CACNmF,IAAK,OACL8H,KAAM,wBAKR,GAAIL,EAAML,eAAgB,CAACvM,OAAQ,KAClC,MAAO,CACNmF,IAAK,MACL8H,KAAM,2BAIR,GAAID,EAAY,kDAAmD,CAAChN,OAAQ,KAC3E,MAAO,CACNmF,IAAK,MACL8H,KAAM,2CAIR,GAAID,EAAY,yDAA0D,CAAChN,OAAQ,KAClF,MAAO,CACNmF,IAAK,MACL8H,KAAM,kDAIR,GAAID,EAAY,0DAA2D,CAAChN,OAAQ,KACnF,MAAO,CACNmF,IAAK,MACL8H,KAAM,mDAUR,IAEIzP,EAFA+P,EAAiB,EACjBC,GAAY,EAGhB,EAAG,CACF,IAAMxN,EAASuN,EAAiB,GAyBhC,GAvBKC,IACJA,EAAaZ,EAAMJ,iBAAkB,CAACxM,YAAY4M,EAAMH,SAAU,CAACzM,YAG/DxC,IACAwP,EAAY,QAAS,CAAChN,WACzBxC,EAAO,CACN2H,IAAK,OACL8H,KAAM,2EAEGD,EAAY,OAAQ,CAAChN,WAC/BxC,EAAO,CACN2H,IAAK,OACL8H,KAAM,6EAEGD,EAAY,MAAO,CAAChN,aAC9BxC,EAAO,CACN2H,IAAK,OACL8H,KAAM,uEAKLO,GAAahQ,EAChB,OAAOA,EAGR+P,EAAiBtB,iBAAiB3O,EAAQgQ,EAAWtN,SAC7CuN,GAAkB,GAG3B,GAAI/P,EACH,OAAOA,EAIT,GACCoP,EAAM,CAAC,GAAM,OACE,IAAdtP,EAAO,IAA4B,IAAdA,EAAO,IAA4B,IAAdA,EAAO,MACnC,IAAdA,EAAO,IAA4B,IAAdA,EAAO,IAA4B,IAAdA,EAAO,IAElD,MAAO,CACN6H,IAAK,MACL8H,KAAM,mBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IAAK+M,KAAM,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,QAC/FX,yBAAyB9O,GAEzB,MAAO,CACN6H,IAAK,MACL8H,KAAM,qBAIR,GACCL,EAAM,CAAC,GAAM,GAAM,IAAM,GAAM,GAAM,MACtB,IAAdtP,EAAO,IAA4B,IAAdA,EAAO,IAE7B,MAAO,CACN6H,IAAK,MACL8H,KAAM,gCAIR,GAAIL,EAAM,CAAC,GAAM,IAAM,IACtB,MAAO,CACNzH,IAAK,KACL8H,KAAM,oBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,MACtB,MAAO,CACNzH,IAAK,MACL8H,KAAM,uBAIR,GAAIL,EAAM,CAAC,GAAM,IAAM,IAAM,IAAM,GAAM,KACxC,MAAO,CACNzH,IAAK,KACL8H,KAAM,+BAIR,GAAIL,EAAM,CAAC,IAAM,IAChB,MAAO,CACNzH,IAAK,MACL8H,KAAM,iCAKR,GACCL,EAAM,CAAC,IAAM,IAAM,IAAM,KAAO,CAAC5M,OAAQ,KACzC4M,EAAM,CAAC,IAAM,IAAM,GAAM,KAAO,CAAC5M,OAAQ,KACzC4M,EAAM,CAAC,IAAM,IAAM,IAAM,KAAO,CAAC5M,OAAQ,KACzC4M,EAAM,CAAC,IAAM,IAAM,IAAM,KAAO,CAAC5M,OAAQ,IAEzC,MAAO,CACNmF,IAAK,MACL8H,KAAM,mBAQR,GACCL,EAAM,CAAC,IAAM,IAAM,IAAM,KAAO,CAAC5M,OAAQ,KAClB,IAAV,GAAZ1C,EAAO,KAA8C,IAAV,GAAZA,EAAO,KAA+C,IAAV,GAAbA,EAAO,MAAgD,IAAV,GAAbA,EAAO,KACrG,CAGD,IAAMmQ,EAAapB,yBAAyB/O,EAAQ,EAAG,IACvD,OAAQmQ,GACP,IAAK,OACJ,MAAO,CAACtI,IAAK,OAAQ8H,KAAM,cAC5B,IAAK,OACJ,MAAO,CAAC9H,IAAK,OAAQ8H,KAAM,uBAC5B,IAAK,OAAQ,IAAK,OACjB,MAAO,CAAC9H,IAAK,OAAQ8H,KAAM,cAC5B,IAAK,OAAQ,IAAK,OACjB,MAAO,CAAC9H,IAAK,OAAQ8H,KAAM,uBAC5B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,mBAC3B,IAAK,OAAQ,IAAK,OAAQ,IAAK,OAC9B,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,eAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,eAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,IAAK,OACJ,MAAO,CAAC9H,IAAK,MAAO8H,KAAM,aAC3B,QACC,OAAIQ,EAAWC,WAAW,MACrBD,EAAWC,WAAW,OAClB,CAACvI,IAAK,MAAO8H,KAAM,eAGpB,CAAC9H,IAAK,MAAO8H,KAAM,cAGpB,CAAC9H,IAAK,MAAO8H,KAAM,cAI7B,GAAIL,EAAM,CAAC,GAAM,GAAM,IAAM,MAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,cAKR,GAAIL,EAAM,CAAC,GAAM,GAAM,IAAM,MAAQ,CACpC,IAAMS,EAAS/P,EAAOoF,SAAS,EAAG,MAC5BiL,EAAQN,EAAOF,WAAU,SAACC,EAAIrU,EAAGqG,GAAR,OAA2B,KAAXA,EAAIrG,IAA8B,MAAfqG,EAAIrG,EAAI,MAE1E,IAAe,IAAX4U,EAAc,CACjB,IAAMC,EAAaD,EAAQ,EACrBE,EAAc,SAAArQ,GAAI,OAAI,mBAAIA,GAAMsQ,OAAM,SAAC1U,EAAGL,GAAJ,OAAUsU,EAAOO,EAAa7U,KAAOK,EAAEyH,WAAW,OAE9F,GAAIgN,EAAY,YACf,MAAO,CACN1I,IAAK,MACL8H,KAAM,oBAIR,GAAIY,EAAY,QACf,MAAO,CACN1I,IAAK,OACL8H,KAAM,eAOV,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAAQ,CACpC,GAAIA,EAAM,CAAC,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IACtC,MAAO,CACNmF,IAAK,MACL8H,KAAM,iBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,kBAKR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,eAMT,GAAIL,EAAM,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,MAAQ,CAGxE,IAAI5M,EAAS,GACb,EAAG,CACF,IAAM+N,EAAa5B,aAAa7O,EAAQ0C,EAAS,IACjD,GAAI4M,EAAM,CAAC,IAAM,EAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAM,IAAM,EAAM,IAAM,GAAM,GAAM,GAAM,KAAO,CAAC5M,WAAU,CAEtH,GAAI4M,EAAM,CAAC,GAAM,IAAM,IAAM,IAAM,GAAM,GAAM,IAAM,GAAM,IAAM,IAAM,EAAM,IAAM,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQA,EAAS,KAE7H,MAAO,CACNmF,IAAK,MACL8H,KAAM,kBAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,GAAM,IAAM,GAAM,GAAM,IAAM,GAAM,IAAM,IAAM,EAAM,IAAM,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQA,EAAS,KAE7H,MAAO,CACNmF,IAAK,MACL8H,KAAM,kBAIR,MAGDjN,GAAU+N,QACF/N,EAAS,IAAM1C,EAAO9B,QAG/B,MAAO,CACN2J,IAAK,MACL8H,KAAM,0BAIR,GACCL,EAAM,CAAC,EAAK,EAAK,EAAK,OACtBA,EAAM,CAAC,EAAK,EAAK,EAAK,MAEtB,MAAO,CACNzH,IAAK,MACL8H,KAAM,cAKR,IAAK,IAAI7O,EAAQ,EAAGA,EAAQ,GAAKA,EAASd,EAAO9B,OAAS,GAAK4C,IAAS,CACvE,GACCwO,EAAM,CAAC,GAAM,GAAM,IAAO,CAAC5M,OAAQ5B,KACnCwO,EAAM,CAAC,IAAM,KAAO,CAAC5M,OAAQ5B,EAAO2O,KAAM,CAAC,IAAM,OAEjD,MAAO,CACN5H,IAAK,MACL8H,KAAM,cAIR,GACCL,EAAM,CAAC,IAAM,KAAO,CAAC5M,OAAQ5B,EAAO2O,KAAM,CAAC,IAAM,OAEjD,MAAO,CACN5H,IAAK,MACL8H,KAAM,cAIR,GACCL,EAAM,CAAC,IAAM,KAAO,CAAC5M,OAAQ5B,EAAO2O,KAAM,CAAC,IAAM,OAEjD,MAAO,CACN5H,IAAK,MACL8H,KAAM,cAIR,GACCL,EAAM,CAAC,IAAM,KAAO,CAAC5M,OAAQ5B,EAAO2O,KAAM,CAAC,IAAM,OAEjD,MAAO,CACN5H,IAAK,MACL8H,KAAM,cAMT,GAAIL,EAAM,CAAC,GAAM,IAAM,IAAM,IAAM,GAAM,IAAM,GAAM,KAAO,CAAC5M,OAAQ,KACpE,MAAO,CACNmF,IAAK,OACL8H,KAAM,cAKR,GAAIL,EAAM,CAAC,GAAM,IAAM,IAAM,KAI5B,OAAIA,EAAM,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAO,CAAC5M,OAAQ,KACvD,CACNmF,IAAK,MACL8H,KAAM,aAKJL,EAAM,CAAC,EAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAO,CAAC5M,OAAQ,KACvD,CACNmF,IAAK,MACL8H,KAAM,aAKJL,EAAM,CAAC,IAAM,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,KAC3C,CACNmF,IAAK,MACL8H,KAAM,aAKJL,EAAM,CAAC,GAAM,IAAM,IAAM,IAAM,IAAM,GAAM,IAAO,CAAC5M,OAAQ,KACvD,CACNmF,IAAK,MACL8H,KAAM,aAKJL,EAAM,CAAC,EAAM,IAAM,IAAM,IAAM,GAAM,IAAM,KAAO,CAAC5M,OAAQ,KACvD,CACNmF,IAAK,MACL8H,KAAM,aAKD,CACN9H,IAAK,MACL8H,KAAM,mBAIR,GAAIL,EAAM,CAAC,IAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,OACL8H,KAAM,gBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,IAAM,MAC5B,MAAO,CACNzH,IAAK,KACL8H,KAAM,iBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,KACxC,MAAO,CACNzH,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,mBAIR,GAAIL,EAAM,CAAC,GAAM,KAChB,MAAO,CACNzH,IAAK,MACL8H,KAAM,4BAIR,IACgB,KAAd3P,EAAO,IAA6B,KAAdA,EAAO,KAC9BsP,EAAM,CAAC,GAAM,IAAO,CAAC5M,OAAQ,IAE7B,MAAO,CACNmF,IAAK,MACL8H,KAAM,iCAIR,GAAIL,EAAM,CAAC,IAAM,GAAM,IAAM,IAAM,MAClC,MAAO,CACNzH,IAAK,MACL8H,KAAM,mBAIR,GAAIL,EAAM,CAAC,EAAM,GAAM,IAAM,MAC5B,MAAO,CACNzH,IAAK,OACL8H,KAAM,oBAIR,GACCL,EAAM,CAAC,IAAM,GAAM,GAAM,OAExBA,EAAM,CAAC,EAAM,EAAM,EAAM,GAAO,CAAC5M,OAAQ,KACzC4M,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,KAG1C,MAAO,CACNmF,IAAK,OACL8H,KAAM,aAIR,GACCL,EAAM,CAAC,IAAM,GAAM,GAAM,OAExBA,EAAM,CAAC,EAAM,EAAM,EAAM,GAAO,CAAC5M,OAAQ,KACzC4M,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,KAG1C,MAAO,CACNmF,IAAK,QACL8H,KAAM,cAIR,GACCL,EAAM,CAAC,GAAM,IAAO,CAAC5M,OAAQ,OAE5B4M,EAAM,CAAC,EAAM,EAAM,GAAO,CAAC5M,OAAQ,KACnC4M,EAAM,CAAC,EAAM,EAAM,GAAO,CAAC5M,OAAQ,KACnC4M,EAAM,CAAC,EAAM,EAAM,GAAO,CAAC5M,OAAQ,KAGpC,MAAO,CACNmF,IAAK,MACL8H,KAAM,iCAIR,GAAIL,EAAM,CAAC,EAAM,EAAM,EAAM,EAAM,IAClC,MAAO,CACNzH,IAAK,MACL8H,KAAM,YAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,IAClC,MAAO,CACNzH,IAAK,MACL8H,KAAM,YAIR,GAAIL,EAAM,CAAC,EAAM,EAAM,EAAM,IAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,gBAIR,GAAIL,EAAM,CAAC,EAAM,EAAM,EAAM,IAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,gBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,eAIR,GAAIL,EAAM,CAAC,GAAM,KAChB,MAAO,CACNzH,IAAK,KACL8H,KAAM,0BAIR,GAAIL,EAAM,CAAC,IAAM,GAAM,IAAM,GAAM,GAAM,IACxC,MAAO,CACNzH,IAAK,KACL8H,KAAM,oBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,MAC5B,MAAO,CACNzH,IAAK,SACL8H,KAAM,yBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,kCAIR,GAAIL,EAAM,CAAC,GAAM,IAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,yCAIR,GACCL,EAAM,CAAC,GAAM,GAAM,GAAM,MACzBA,EAAM,CAAC,GAAM,GAAM,GAAM,KAEzB,MAAO,CACNzH,IAAK,MACL8H,KAAM,qCAKR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAM,GAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,IAAM,GAAM,IAAM,GAAM,GAAM,IAAM,IAAM,GAAM,IAAM,MAClI,MAAO,CACNzH,IAAK,MACL8H,KAAM,qBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAM,GAAM,IAAM,KAC9C,MAAO,CACNzH,IAAK,KACL8H,KAAM,8BAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,IAAM,MAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,qBAIR,GACCL,EAAM,CAAC,GAAM,OACbA,EAAM,CAAC,GAAM,MAEb,MAAO,CACNzH,IAAK,IACL8H,KAAM,0BAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KAC5B,MAAO,CACNzH,IAAK,KACL8H,KAAM,sBAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,GAAM,IAAM,IAAM,IAAM,GAAM,IAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,KAC1J,MAAO,CACNzH,IAAK,MACL8H,KAAM,qBAIR,GAAIL,EAAM,CAAC,EAAM,GAAM,GAAM,GAAM,EAAM,EAAM,EAAM,EAAM,GAAM,EAAM,EAAM,EAAM,EAAM,IACxF,MAAO,CACNzH,IAAK,MACL8H,KAAM,mBAIR,GAAIL,EAAM,CAAC,IAAO,CAAC5M,OAAQ,MAAQ4M,EAAM,CAAC,IAAO,CAAC5M,OAAQ,OAAS4M,EAAM,CAAC,IAAO,CAAC5M,OAAQ,OACzF,MAAO,CACNmF,IAAK,MACL8H,KAAM,cAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,KAC9C,MAAO,CACNzH,IAAK,QACL8H,KAAM,yBAIR,GAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,MAC5B,MAAO,CACNzH,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,EAAM,EAAM,EAAM,GAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,KAAQ,CAGpF,GAAIA,EAAM,CAAC,IAAM,IAAM,GAAM,IAAO,CAAC5M,OAAQ,KAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,IAAM,IAAO,CAAC5M,OAAQ,KAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,IAAM,IAAO,CAAC5M,OAAQ,KAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,aAIR,GAAIL,EAAM,CAAC,IAAM,IAAM,IAAM,IAAO,CAAC5M,OAAQ,KAC5C,MAAO,CACNmF,IAAK,MACL8H,KAAM,aAKT,OAAIL,EAAM,CAAC,GAAM,GAAM,GAAM,KACrB,CACNzH,IAAK,MACL8H,KAAM,cAIJD,EAAY,UACR,CACN7H,IAAK,MACL8H,KAAM,mBAIJL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,KAC7D,CACNmF,IAAK,OACL8H,KAAM,kCAIJL,EAAM,CAAC,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,GAAM,GAAM,GAAM,KACrE,CACNzH,IAAK,MACL8H,KAAM,aAIJL,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,MACrC,CACNmF,IAAK,MACL8H,KAAM,qBAKJL,EAAM,CAAC,GAAM,GAAM,MAQnBA,EAAM,CAAC,GAAM,GAAM,GAAM,KAPrB,CACNzH,IAAK,MACL8H,KAAM,oBAYJL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,KACjC,CACNzH,IAAK,MACL8H,KAAM,iBAIJL,EAAM,CAAC,IAAM,IAAM,GAAM,GAAM,EAAM,EAAM,EAAM,IAC7C,CACNzH,IAAK,MACL8H,KAAM,qBAIJL,EAAM,CAAC,IAAM,IAAM,IAAM,OAAUA,EAAM,CAAC,IAAM,IAAM,IAAM,MACxD,CACNzH,IAAK,OACL8H,KAAM,gCAKJL,EAAM,CAAC,GAAM,GAAM,GAAM,KACrB,CACNzH,IAAK,MACL8H,KAAM,eAIJL,EAAM,CAAC,GAAM,EAAM,EAAM,EAAM,EAAM,GAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,IAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,KACrH,CACNzH,IAAK,MACL8H,KAAM,6BAIJL,EAAM,CAAC,GAAM,IAAM,IAAM,IAAM,EAAM,EAAM,EAAM,EAAM,IAAM,GAAM,IAAM,IAAM,EAAM,EAAM,EAAM,IAC7F,CACNzH,IAAK,QACL8H,KAAM,6BAIJD,EAAY,uBACR,CACN7H,IAAK,MACL8H,KAAM,eAIJL,EAAM,CAAC,GAAM,MACT,CACNzH,IAAK,MACL8H,KAAM,2BAIHL,EAAM,CAAC,IAAM,GAAM,KAAUA,EAAM,CAAC,IAAM,GAAM,MAAWA,EAAM,CAAC,GAAM,GAAM,GAAM,IAAO,CAAC5M,OAAQ,IACjG,CACNmF,IAAK,MACL8H,KAAM,qBAIJL,EAAM,CAAC,GAAM,GAAM,GAAM,GAAM,GAAM,GAAM,EAAM,IAC7C,CACNzH,IAAK,QACL8H,KAAM,8BAIJL,EAAM,CAAC,GAAM,GAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,EAAM,GAAO,CAAC5M,OAAQ,IACrF,CACNmF,IAAK,MACL8H,KAAM,iCAHR,IAQDnU,OAAOD,QAAU6T,SAEjBjT,OAAOC,eAAegT,SAAU,eAAgB,CAAC1S,MAAO,OAExD0S,SAASsB,OAAS,SAAAC,gBAAc,OAAI,IAAIC,SAAQ,SAACC,QAASC,QAEzD,IAAMJ,OAASK,KAAK,UAALA,CAAgB,UAE/BJ,eAAe9C,GAAG,QAASiD,QAC3BH,eAAe5C,KAAK,YAAY,WAC/B,IAAMiD,EAAO,IAAIN,OAAOO,YAClBC,EAAQP,eAAexO,KAAK3G,OAAOD,QAAQ4V,eAAiBR,eAAexO,OACjF,IACC6O,EAAK5B,SAAWA,SAAS8B,GACxB,MAAOE,GACRN,OAAOM,GAGRT,eAAeU,QAAQH,GAEnBR,OAAOY,SACVT,QAAQH,OAAOY,SAASX,eAAgBK,GAAM,gBAE9CH,QAAQF,eAAeY,KAAKP,WAK/B7U,OAAOC,eAAegT,SAAU,aAAc,CAC7C9S,IAD6C,WAE5C,OAAO,IAAIkV,IAAIxC,UAAUyC,eAI3BtV,OAAOC,eAAegT,SAAU,YAAa,CAC5C9S,IAD4C,WAE3C,OAAO,IAAIkV,IAAIxC,UAAU0C,gB,6ZCxiC3B,IAAMC,EAASjU,EAAQ,GACjBkU,EAAUlU,EAAQ,IAClBmU,EAASnU,EAAQ,IACjBoU,EAAQpU,EAAQ,IAKtBwH,EAAO6M,iBAAiB,WAAW,YAAc,IAAX5R,EAAW,EAAXA,KACpCwR,EAAOK,iBAAiB7R,GAAM,SAACR,GAAD,OAASsS,YAAYtS,SAOrDgS,EAAOO,W,+VAAP,EACEN,UACAC,SACAM,MAAO,cACJL,M,ylDCrBLpU,EAAQ,GACR,IAWI0U,EAKAC,EAhBEjD,EAAW1R,EAAQ,GACnB4U,EAAQ5U,EAAQ,IAChB6U,EAAO7U,EAAQ,IACf8U,EAA6D,cAA/C9U,EAAQ,GAARA,CAAmC,QACjD+U,EAAW/U,EAAQ,IACnBgV,EAAgBhV,EAAQ,I,EACFA,EAAQ,IAA5BiV,E,EAAAA,IAAKC,E,EAAAA,WASTC,EAAM,KAENC,EAAU,GACVC,EAASL,EAEPM,EAAO,SAAC,EAAkE7O,GAAQ,IAAxE8O,EAAwE,EAAxEA,SAAUC,EAA8D,EAA9DA,MAA8D,IAAvDC,QAAW3D,QAAW4D,EAAiC,EAAjCA,SAAUC,EAAuB,EAAvBA,QAE/D,GADAT,EAAWS,GACNjB,EAoBHjO,EAAI0M,QAAQ,CAAEyC,QAAQ,QApBP,CACf,IAAMC,EAAOT,EAAQlB,QAAQwB,EAAUjP,GAEvCA,EAAIqP,SAAS,CAAEP,WAAUQ,OAAQ,yBAA0BD,SAAU,IAErED,EAAK,CACHG,kBADG,SACeC,GAChBtB,EAAUmB,SAAS,CACjBP,WACAC,QACAO,OAAQ,mBACRD,SAAUvP,KAAK0C,IAAI,GAAIgN,EAAU,IAAM,SAG1CC,MAAK,SAACC,GACPzB,EAAayB,EACb1P,EAAIqP,SAAS,CAAEP,WAAUQ,OAAQ,wBAAyBD,SAAU,IACpErP,EAAI0M,QAAQ,CAAEyC,QAAQ,SAOtBQ,EAAK,SAAC,EAAyC3P,GAAQ,MAA/C8O,EAA+C,EAA/CA,SAA+C,IAArCE,QAAWY,EAA0B,EAA1BA,OAAQzG,EAAkB,EAAlBA,KACzCqF,EAAI,IAAD,OAAKM,EAAL,iBAAsBc,EAAtB,sBAA0CzG,IAC7CnJ,EAAI0M,SAAQ,EAAAuB,EAAW0B,IAAGC,GAAd,UAAyBzG,MAGjC0G,EAAY,4CAAG,aAanB7P,GAbmB,iHACnB8O,EADmB,EACnBA,SADmB,IAEnBE,QACEc,EAHiB,EAGjBA,MAHiB,IAIjBzE,QACE0E,EALe,EAKfA,SACAC,EANe,EAMfA,SACAC,EAPe,EAOfA,UACAC,EARe,EAQfA,YARe,IASfC,YATe,SAcbC,EAda,4CAcO,WAAOC,GAAP,yGAClBC,EAAwB,iBAAVD,EAAqBA,EAAQA,EAAMtJ,KACjDwJ,EAAY,CAAC,UAAW,QAAQvN,SAASkN,GAC3C,kBAAMzD,QAAQC,WACdiC,EAAQ4B,UACRvU,EAAO,KALa,kBAQFuU,EAAU,GAAD,OAAIN,GAAa,IAAjB,YAAwBK,EAAxB,iBARP,eASD,KADfE,EARgB,yBAUpBhC,EAAI,IAAD,OAAKM,EAAL,mBAAwBwB,EAAxB,4BACHtQ,EAAIqP,SAAS,CAAEP,WAAUQ,OAAQ,4CAA6CD,SAAU,KACxFrT,EAAOwU,EAZa,8BAcdlW,MAAM,sBAdQ,6DAiBtBkU,EAAI,IAAD,OAAKM,EAAL,mBAAwBwB,EAAxB,6BAAiDP,IAC/B,iBAAVM,EAlBW,oBAmBhBI,EAAO,MAEPtC,EAAM4B,IAAaA,EAAS9D,WAAW,qBAAuB8D,EAAS9D,WAAW,wBAA0B8D,EAAS9D,WAAW,cAClIwE,EAAOV,GAGI,OAATU,EAzBgB,mCA0BEpC,EAAcL,MAAQW,EAAQX,OAA/B,UAAyCyC,EAAzC,YAAiDH,EAAjD,uBAAoEH,EAAO,MAAQ,KA1BpF,eA0BZO,EA1BY,iBA2BLA,EAAKC,cA3BA,QA2BlB3U,EA3BkB,gDA6BL2S,EAAQ4B,UAAR,UAAqBR,EAArB,YAAiCO,EAAjC,uBAAoDH,EAAO,MAAQ,KA7B9D,QA6BlBnU,EA7BkB,uCAgCpBA,EAAOqU,EAAMrU,KAhCO,QA2CxB,GAPAA,EAAO,IAAI/B,WAAW+B,QAGF,KADdD,EAAOkP,EAASjP,KAC2B,qBAAdD,EAAKyP,OACtCxP,EAAO2S,EAAQjB,OAAO1R,IAGpBiS,EAAY,CACd,GAAI+B,EACF,IACE/B,EAAW0B,GAAGiB,MAAMZ,GACpB,MAAOa,GACP7Q,EAAI2M,OAAOkE,EAAIzU,YAGnB6R,EAAW0B,GAAGmB,UAAd,UAA2Bd,GAAY,IAAvC,YAA8CM,EAA9C,gBAAkEtU,GAnD5C,IAsDpB,CAAC,QAAS,eAAWlB,GAAWkI,SAASkN,GAtDrB,kCAuDhBvB,EAAQoC,WAAR,UAAsBd,GAAa,IAAnC,YAA0CK,EAA1C,gBAA8DtU,GAvD9C,iCA0DjByQ,QAAQC,QAAQ1Q,IA1DC,0DAdP,sDA2EnBgE,EAAIqP,SAAS,CAAEP,WAAUQ,OAAQ,+BAAgCD,SAAU,IA3ExD,kBA6EX5C,QAAQuE,KAAsB,iBAAVlB,EAAqBA,EAAMmB,MAAM,KAAOnB,GAAOoB,IAAId,IA7E5D,OA8EjBpQ,EAAIqP,SAAS,CAAEP,WAAUQ,OAAQ,8BAA+BD,SAAU,IAC1ErP,EAAI0M,QAAQoD,GA/EK,kDAiFbzB,GAAe,gBAAe8C,cAOhCnR,EAAI2M,OAAO,KAAIvQ,YAxFA,0DAAH,wDA6FZgV,EAAgB,SAAC,EAAkCpR,GAAQ,IAArBqR,EAAqB,EAAxCrC,QAAWJ,OAClC5W,OAAOsZ,KAAKD,GACTE,QAAO,SAACC,GAAD,OAAQA,EAAEvF,WAAW,cAC5BwF,SAAQ,SAAC5Y,GACR6V,EAAIgD,YAAY7Y,EAAKwY,EAAQxY,OAEjC+V,EAAS,EAAH,GAAQA,EAAR,GAAmByC,QAEN,IAARrR,GACTA,EAAI0M,QAAQkC,IAIV+C,EAAa,SAAC,EAGjB3R,GAAQ,IAFT8O,EAES,EAFTA,SAES,IADTE,QAAkB4C,EACT,EADE9B,MAAe+B,EACjB,EADiBA,IAEpB/B,EAA2B,iBAAX8B,EAClBA,EACAA,EAAOV,KAAI,SAAC3Z,GAAD,MAAsB,iBAANA,EAAkBA,EAAIA,EAAEyE,QAAO0G,KAAK,KAEnE,IACE1C,EAAIqP,SAAS,CACXP,WAAUQ,OAAQ,mBAAoBD,SAAU,IAEtC,OAARX,GACFA,EAAIoD,OAENpD,EAAM,IAAIT,EAAW8D,aACjBC,KAAK,KAAMlC,EAAO+B,GAEtBT,EAAc,CAAEpC,QAAS,CAAEJ,OAD3BA,EAASL,KAETvO,EAAIqP,SAAS,CACXP,WAAUQ,OAAQ,kBAAmBD,SAAU,IAEjDrP,EAAI0M,UACJ,MAAOmE,GACP7Q,EAAI2M,OAAOkE,EAAIzU,cAIb6V,EAAY,SAAC,EAAqDjS,GAAQ,QAA3DgP,QAAWkD,EAAgD,EAAhDA,MAA6BC,EAAmB,EAAzC9G,QAAW+G,UAChD,IACE,IAAMC,EAAM/D,EAASL,EAAYS,EAAKwD,GACnB,WAAf,EAAOC,IACTzD,EAAI4D,aAAaH,EAAII,KAAMJ,EAAIK,IAAKL,EAAIM,MAAON,EAAIO,QAErDhE,EAAIiE,UAAU,MACd3S,EAAI0M,QAAQ0B,EAAKH,EAAYS,EAAKE,IAClCX,EAAW2E,MAAMP,GACjB,MAAOxB,GACP7Q,EAAI2M,OAAOkE,EAAIzU,cAIbyW,EAAS,SAAC,EAAkC7S,GAAQ,QAAxCgP,QAAW5F,EAA6B,EAA7BA,MAAO0J,EAAsB,EAAtBA,SAC5BC,EAAc,IAAI9E,EAAW+E,gBAAgB,gBAAiB,IAAKF,GACzEC,EAAYE,cAAc7J,GAC1B2J,EAAYG,SAASxE,GACrBqE,EAAYI,cACZlF,EAAW2E,MAAMG,GAEjB/S,EAAI0M,QAAQuB,EAAW0B,GAAGyD,SAAS,wBAG/BC,EAAS,SAAC,EAAwBrT,GAAQ,IAAnBkS,EAAmB,EAA9BlD,QAAWkD,MAC3B,IACE,IAAMG,EAAM/D,EAASL,EAAYS,EAAKwD,GAChCoB,EAAU,IAAIrF,EAAWsF,UAE/B,GAAK7E,EAAI8E,SAASF,GAIX,CACL,IAAMG,EAAOH,EAAQI,YACfC,EAAMF,EAAKG,eACXC,EAAMJ,EAAKK,UAEjB7F,EAAW2E,MAAMP,GAEjBrS,EAAI0M,QAAQ,CACVqH,oBAAqBF,EACrBG,OAAQV,EAAQW,WAAWC,0BAA0BL,GACrDM,kBAAmBV,EAAKW,YACxBC,oBAAqB,CAAC,EAAG,IAAK,IAAK,IAAIV,GACvCW,uBAAwBb,EAAKc,mBAf/B7F,EAAIoD,MACJ7D,EAAW2E,MAAMP,GACjBrS,EAAI2M,OAAO,uBAgBb,MAAOkE,GACP7Q,EAAI2M,OAAOkE,EAAIzU,cAIboY,EAAY,SAACC,EAAGzU,GACpB,IACc,OAAR0O,GACFA,EAAIoD,MAEN9R,EAAI0M,QAAQ,CAAEgI,YAAY,IAC1B,MAAO7D,GACP7Q,EAAI2M,OAAOkE,EAAIzU,cAgBnBhF,EAAQyW,iBAAmB,SAAC8G,EAAQC,GAClC,IAAM5U,EAAM,SAACsP,EAAQtT,GACnB4Y,EAAK,EAAD,GACCD,EADD,CAEFrF,SACAtT,WAGJgE,EAAI0M,QAAU1M,EAAIlH,KAAK,EAAM,WAC7BkH,EAAI2M,OAAS3M,EAAIlH,KAAK,EAAM,UAC5BkH,EAAIqP,SAAWrP,EAAIlH,KAAK,EAAM,YAE9BoV,EAAYlO,EAEZ,KACE,CACE6O,OACAc,KACAE,eACA8B,aACAP,gBACAa,YACAY,SACAQ,SACAmB,cACCG,EAAOE,QAAQF,EAAQ3U,GAC1B,MAAO6Q,GAEP7Q,EAAI2M,OAAOkE,EAAIzU,cAYnBhF,EAAQ2W,WAAa,SAAC+G,GACpBnG,EAAUmG,I,kQCjTZ,IAAIC,EAAW,SAAU3d,GACvB,aAEA,IAAI4d,EAAKhd,OAAOkB,UACZ+b,EAASD,EAAG7b,eAEZ+b,EAA4B,mBAAX7c,OAAwBA,OAAS,GAClD8c,EAAiBD,EAAQE,UAAY,aACrCC,EAAsBH,EAAQI,eAAiB,kBAC/CC,EAAoBL,EAAQ5c,aAAe,gBAE/C,SAASkd,EAAKC,EAASC,EAASC,EAAMC,GAEpC,IAAIC,EAAiBH,GAAWA,EAAQxc,qBAAqB4c,EAAYJ,EAAUI,EAC/EC,EAAY/d,OAAOY,OAAOid,EAAe3c,WACzC8c,EAAU,IAAIC,EAAQL,GAAe,IAMzC,OAFAG,EAAUG,QAkMZ,SAA0BT,EAASE,EAAMK,GACvC,IAAIG,EA3KuB,iBA6K3B,OAAO,SAAgBvG,EAAQzV,GAC7B,GA5KoB,cA4KhBgc,EACF,MAAM,IAAI7b,MAAM,gCAGlB,GA/KoB,cA+KhB6b,EAA6B,CAC/B,GAAe,UAAXvG,EACF,MAAMzV,EAKR,OAAOic,IAMT,IAHAJ,EAAQpG,OAASA,EACjBoG,EAAQ7b,IAAMA,IAED,CACX,IAAIkc,EAAWL,EAAQK,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUL,GACnD,GAAIM,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,GAIX,GAAuB,SAAnBN,EAAQpG,OAGVoG,EAAQS,KAAOT,EAAQU,MAAQV,EAAQ7b,SAElC,GAAuB,UAAnB6b,EAAQpG,OAAoB,CACrC,GA/MqB,mBA+MjBuG,EAEF,MADAA,EA7Mc,YA8MRH,EAAQ7b,IAGhB6b,EAAQW,kBAAkBX,EAAQ7b,SAEN,WAAnB6b,EAAQpG,QACjBoG,EAAQY,OAAO,SAAUZ,EAAQ7b,KAGnCgc,EAxNkB,YA0NlB,IAAIU,EAASC,EAASrB,EAASE,EAAMK,GACrC,GAAoB,WAAhBa,EAAO9a,KAAmB,CAO5B,GAJAoa,EAAQH,EAAQe,KA7NA,YAFK,iBAmOjBF,EAAO1c,MAAQqc,EACjB,SAGF,MAAO,CACLje,MAAOse,EAAO1c,IACd4c,KAAMf,EAAQe,MAGS,UAAhBF,EAAO9a,OAChBoa,EA3OgB,YA8OhBH,EAAQpG,OAAS,QACjBoG,EAAQ7b,IAAM0c,EAAO1c,OA1QP6c,CAAiBvB,EAASE,EAAMK,GAE7CD,EAcT,SAASe,EAASG,EAAIzb,EAAKrB,GACzB,IACE,MAAO,CAAE4B,KAAM,SAAU5B,IAAK8c,EAAGxf,KAAK+D,EAAKrB,IAC3C,MAAO0W,GACP,MAAO,CAAE9U,KAAM,QAAS5B,IAAK0W,IAhBjCzZ,EAAQoe,KAAOA,EAoBf,IAOIgB,EAAmB,GAMvB,SAASV,KACT,SAASoB,KACT,SAASC,KAIT,IAAIC,EAAoB,GACxBA,EAAkBjC,GAAkB,WAClC,OAAO9a,MAGT,IAAIgd,EAAWrf,OAAOsf,eAClBC,EAA0BF,GAAYA,EAASA,EAASG,EAAO,MAC/DD,GACAA,IAA4BvC,GAC5BC,EAAOxd,KAAK8f,EAAyBpC,KAGvCiC,EAAoBG,GAGtB,IAAIE,EAAKN,EAA2Bje,UAClC4c,EAAU5c,UAAYlB,OAAOY,OAAOwe,GAQtC,SAASM,EAAsBxe,GAC7B,CAAC,OAAQ,QAAS,UAAUuY,SAAQ,SAAS7B,GAC3C1W,EAAU0W,GAAU,SAASzV,GAC3B,OAAOE,KAAK6b,QAAQtG,EAAQzV,OAoClC,SAASwd,EAAc5B,GAgCrB,IAAI6B,EAgCJvd,KAAK6b,QA9BL,SAAiBtG,EAAQzV,GACvB,SAAS0d,IACP,OAAO,IAAIpL,SAAQ,SAASC,EAASC,IAnCzC,SAASmL,EAAOlI,EAAQzV,EAAKuS,EAASC,GACpC,IAAIkK,EAASC,EAASf,EAAUnG,GAASmG,EAAW5b,GACpD,GAAoB,UAAhB0c,EAAO9a,KAEJ,CACL,IAAIgc,EAASlB,EAAO1c,IAChB5B,EAAQwf,EAAOxf,MACnB,OAAIA,GACiB,WAAjB,EAAOA,IACP0c,EAAOxd,KAAKc,EAAO,WACdkU,QAAQC,QAAQnU,EAAMyf,SAASvI,MAAK,SAASlX,GAClDuf,EAAO,OAAQvf,EAAOmU,EAASC,MAC9B,SAASkE,GACViH,EAAO,QAASjH,EAAKnE,EAASC,MAI3BF,QAAQC,QAAQnU,GAAOkX,MAAK,SAASwI,GAI1CF,EAAOxf,MAAQ0f,EACfvL,EAAQqL,MACP,SAAS9K,GAGV,OAAO6K,EAAO,QAAS7K,EAAOP,EAASC,MAvBzCA,EAAOkK,EAAO1c,KAiCZ2d,CAAOlI,EAAQzV,EAAKuS,EAASC,MAIjC,OAAOiL,EAaLA,EAAkBA,EAAgBnI,KAChCoI,EAGAA,GACEA,KA+GV,SAAStB,EAAoBF,EAAUL,GACrC,IAAIpG,EAASyG,EAASjB,SAASY,EAAQpG,QACvC,QApSE9U,IAoSE8U,EAAsB,CAKxB,GAFAoG,EAAQK,SAAW,KAEI,UAAnBL,EAAQpG,OAAoB,CAE9B,GAAIyG,EAASjB,SAAT,SAGFY,EAAQpG,OAAS,SACjBoG,EAAQ7b,SA/SZW,EAgTIyb,EAAoBF,EAAUL,GAEP,UAAnBA,EAAQpG,QAGV,OAAO4G,EAIXR,EAAQpG,OAAS,QACjBoG,EAAQ7b,IAAM,IAAIM,UAChB,kDAGJ,OAAO+b,EAGT,IAAIK,EAASC,EAASlH,EAAQyG,EAASjB,SAAUY,EAAQ7b,KAEzD,GAAoB,UAAhB0c,EAAO9a,KAIT,OAHAia,EAAQpG,OAAS,QACjBoG,EAAQ7b,IAAM0c,EAAO1c,IACrB6b,EAAQK,SAAW,KACZG,EAGT,IAAI0B,EAAOrB,EAAO1c,IAElB,OAAM+d,EAOFA,EAAKnB,MAGPf,EAAQK,EAAS8B,YAAcD,EAAK3f,MAGpCyd,EAAQoC,KAAO/B,EAASgC,QAQD,WAAnBrC,EAAQpG,SACVoG,EAAQpG,OAAS,OACjBoG,EAAQ7b,SAnWVW,GA6WFkb,EAAQK,SAAW,KACZG,GANE0B,GA3BPlC,EAAQpG,OAAS,QACjBoG,EAAQ7b,IAAM,IAAIM,UAAU,oCAC5Bub,EAAQK,SAAW,KACZG,GAoDX,SAAS8B,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxBle,KAAKwe,WAAW1Z,KAAKqZ,GAGvB,SAASM,EAAcN,GACrB,IAAI3B,EAAS2B,EAAMO,YAAc,GACjClC,EAAO9a,KAAO,gBACP8a,EAAO1c,IACdqe,EAAMO,WAAalC,EAGrB,SAASZ,EAAQL,GAIfvb,KAAKwe,WAAa,CAAC,CAAEJ,OAAQ,SAC7B7C,EAAYnE,QAAQ6G,EAAcje,MAClCA,KAAK2e,OAAM,GA8Bb,SAASxB,EAAOyB,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAAS9D,GAC9B,GAAI+D,EACF,OAAOA,EAAezhB,KAAKwhB,GAG7B,GAA6B,mBAAlBA,EAASb,KAClB,OAAOa,EAGT,IAAK1b,MAAM0b,EAASlf,QAAS,CAC3B,IAAIzC,GAAK,EAAG8gB,EAAO,SAASA,IAC1B,OAAS9gB,EAAI2hB,EAASlf,QACpB,GAAIkb,EAAOxd,KAAKwhB,EAAU3hB,GAGxB,OAFA8gB,EAAK7f,MAAQ0gB,EAAS3hB,GACtB8gB,EAAKrB,MAAO,EACLqB,EAOX,OAHAA,EAAK7f,WAndTuC,EAodIsd,EAAKrB,MAAO,EAELqB,GAGT,OAAOA,EAAKA,KAAOA,GAKvB,MAAO,CAAEA,KAAMhC,GAIjB,SAASA,IACP,MAAO,CAAE7d,WAnePuC,EAmeyBic,MAAM,GA+MnC,OAxmBAG,EAAkBhe,UAAYue,EAAG0B,YAAchC,EAC/CA,EAA2BgC,YAAcjC,EACzCC,EAA2B5B,GACzB2B,EAAkBkC,YAAc,oBAYlChiB,EAAQiiB,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOH,YAClD,QAAOI,IACHA,IAASrC,GAG2B,uBAAnCqC,EAAKH,aAAeG,EAAK1hB,QAIhCT,EAAQoiB,KAAO,SAASF,GAUtB,OATIthB,OAAOyhB,eACTzhB,OAAOyhB,eAAeH,EAAQnC,IAE9BmC,EAAOpf,UAAYid,EACb5B,KAAqB+D,IACzBA,EAAO/D,GAAqB,sBAGhC+D,EAAOpgB,UAAYlB,OAAOY,OAAO6e,GAC1B6B,GAOTliB,EAAQsiB,MAAQ,SAASvf,GACvB,MAAO,CAAE6d,QAAS7d,IAsEpBud,EAAsBC,EAAcze,WACpCye,EAAcze,UAAUmc,GAAuB,WAC7C,OAAOhb,MAETjD,EAAQugB,cAAgBA,EAKxBvgB,EAAQuiB,MAAQ,SAASlE,EAASC,EAASC,EAAMC,GAC/C,IAAIgE,EAAO,IAAIjC,EACbnC,EAAKC,EAASC,EAASC,EAAMC,IAG/B,OAAOxe,EAAQiiB,oBAAoB3D,GAC/BkE,EACAA,EAAKxB,OAAO3I,MAAK,SAASsI,GACxB,OAAOA,EAAOhB,KAAOgB,EAAOxf,MAAQqhB,EAAKxB,WAuKjDV,EAAsBD,GAEtBA,EAAGlC,GAAqB,YAOxBkC,EAAGtC,GAAkB,WACnB,OAAO9a,MAGTod,EAAGrb,SAAW,WACZ,MAAO,sBAkCThF,EAAQka,KAAO,SAAStY,GACtB,IAAIsY,EAAO,GACX,IAAK,IAAIzY,KAAOG,EACdsY,EAAKnS,KAAKtG,GAMZ,OAJAyY,EAAKuI,UAIE,SAASzB,IACd,KAAO9G,EAAKvX,QAAQ,CAClB,IAAIlB,EAAMyY,EAAKwI,MACf,GAAIjhB,KAAOG,EAGT,OAFAof,EAAK7f,MAAQM,EACbuf,EAAKrB,MAAO,EACLqB,EAQX,OADAA,EAAKrB,MAAO,EACLqB,IAsCXhhB,EAAQogB,OAASA,EAMjBvB,EAAQ/c,UAAY,CAClBigB,YAAalD,EAEb+C,MAAO,SAASe,GAcd,GAbA1f,KAAK2f,KAAO,EACZ3f,KAAK+d,KAAO,EAGZ/d,KAAKoc,KAAOpc,KAAKqc,WA9ejB5b,EA+eAT,KAAK0c,MAAO,EACZ1c,KAAKgc,SAAW,KAEhBhc,KAAKuV,OAAS,OACdvV,KAAKF,SAnfLW,EAqfAT,KAAKwe,WAAWpH,QAAQqH,IAEnBiB,EACH,IAAK,IAAIliB,KAAQwC,KAEQ,MAAnBxC,EAAKoiB,OAAO,IACZhF,EAAOxd,KAAK4C,KAAMxC,KACjB0F,OAAO1F,EAAKyD,MAAM,MACrBjB,KAAKxC,QA7fXiD,IAmgBFof,KAAM,WACJ7f,KAAK0c,MAAO,EAEZ,IACIoD,EADY9f,KAAKwe,WAAW,GACLE,WAC3B,GAAwB,UAApBoB,EAAWpe,KACb,MAAMoe,EAAWhgB,IAGnB,OAAOE,KAAK+f,MAGdzD,kBAAmB,SAAS0D,GAC1B,GAAIhgB,KAAK0c,KACP,MAAMsD,EAGR,IAAIrE,EAAU3b,KACd,SAASigB,EAAOC,EAAKC,GAYnB,OAXA3D,EAAO9a,KAAO,QACd8a,EAAO1c,IAAMkgB,EACbrE,EAAQoC,KAAOmC,EAEXC,IAGFxE,EAAQpG,OAAS,OACjBoG,EAAQ7b,SA9hBZW,KAiiBY0f,EAGZ,IAAK,IAAIljB,EAAI+C,KAAKwe,WAAW9e,OAAS,EAAGzC,GAAK,IAAKA,EAAG,CACpD,IAAIkhB,EAAQne,KAAKwe,WAAWvhB,GACxBuf,EAAS2B,EAAMO,WAEnB,GAAqB,SAAjBP,EAAMC,OAIR,OAAO6B,EAAO,OAGhB,GAAI9B,EAAMC,QAAUpe,KAAK2f,KAAM,CAC7B,IAAIS,EAAWxF,EAAOxd,KAAK+gB,EAAO,YAC9BkC,EAAazF,EAAOxd,KAAK+gB,EAAO,cAEpC,GAAIiC,GAAYC,EAAY,CAC1B,GAAIrgB,KAAK2f,KAAOxB,EAAME,SACpB,OAAO4B,EAAO9B,EAAME,UAAU,GACzB,GAAIre,KAAK2f,KAAOxB,EAAMG,WAC3B,OAAO2B,EAAO9B,EAAMG,iBAGjB,GAAI8B,GACT,GAAIpgB,KAAK2f,KAAOxB,EAAME,SACpB,OAAO4B,EAAO9B,EAAME,UAAU,OAG3B,KAAIgC,EAMT,MAAM,IAAIpgB,MAAM,0CALhB,GAAID,KAAK2f,KAAOxB,EAAMG,WACpB,OAAO2B,EAAO9B,EAAMG,gBAU9B/B,OAAQ,SAAS7a,EAAM5B,GACrB,IAAK,IAAI7C,EAAI+C,KAAKwe,WAAW9e,OAAS,EAAGzC,GAAK,IAAKA,EAAG,CACpD,IAAIkhB,EAAQne,KAAKwe,WAAWvhB,GAC5B,GAAIkhB,EAAMC,QAAUpe,KAAK2f,MACrB/E,EAAOxd,KAAK+gB,EAAO,eACnBne,KAAK2f,KAAOxB,EAAMG,WAAY,CAChC,IAAIgC,EAAenC,EACnB,OAIAmC,IACU,UAAT5e,GACS,aAATA,IACD4e,EAAalC,QAAUte,GACvBA,GAAOwgB,EAAahC,aAGtBgC,EAAe,MAGjB,IAAI9D,EAAS8D,EAAeA,EAAa5B,WAAa,GAItD,OAHAlC,EAAO9a,KAAOA,EACd8a,EAAO1c,IAAMA,EAETwgB,GACFtgB,KAAKuV,OAAS,OACdvV,KAAK+d,KAAOuC,EAAahC,WAClBnC,GAGFnc,KAAKugB,SAAS/D,IAGvB+D,SAAU,SAAS/D,EAAQ+B,GACzB,GAAoB,UAAhB/B,EAAO9a,KACT,MAAM8a,EAAO1c,IAcf,MAXoB,UAAhB0c,EAAO9a,MACS,aAAhB8a,EAAO9a,KACT1B,KAAK+d,KAAOvB,EAAO1c,IACM,WAAhB0c,EAAO9a,MAChB1B,KAAK+f,KAAO/f,KAAKF,IAAM0c,EAAO1c,IAC9BE,KAAKuV,OAAS,SACdvV,KAAK+d,KAAO,OACa,WAAhBvB,EAAO9a,MAAqB6c,IACrCve,KAAK+d,KAAOQ,GAGPpC,GAGTqE,OAAQ,SAASlC,GACf,IAAK,IAAIrhB,EAAI+C,KAAKwe,WAAW9e,OAAS,EAAGzC,GAAK,IAAKA,EAAG,CACpD,IAAIkhB,EAAQne,KAAKwe,WAAWvhB,GAC5B,GAAIkhB,EAAMG,aAAeA,EAGvB,OAFAte,KAAKugB,SAASpC,EAAMO,WAAYP,EAAMI,UACtCE,EAAcN,GACPhC,IAKb,MAAS,SAASiC,GAChB,IAAK,IAAInhB,EAAI+C,KAAKwe,WAAW9e,OAAS,EAAGzC,GAAK,IAAKA,EAAG,CACpD,IAAIkhB,EAAQne,KAAKwe,WAAWvhB,GAC5B,GAAIkhB,EAAMC,SAAWA,EAAQ,CAC3B,IAAI5B,EAAS2B,EAAMO,WACnB,GAAoB,UAAhBlC,EAAO9a,KAAkB,CAC3B,IAAI+e,EAASjE,EAAO1c,IACpB2e,EAAcN,GAEhB,OAAOsC,GAMX,MAAM,IAAIxgB,MAAM,0BAGlBygB,cAAe,SAAS9B,EAAUd,EAAYE,GAa5C,OAZAhe,KAAKgc,SAAW,CACdjB,SAAUoC,EAAOyB,GACjBd,WAAYA,EACZE,QAASA,GAGS,SAAhBhe,KAAKuV,SAGPvV,KAAKF,SAvqBPW,GA0qBO0b,IAQJpf,EAvrBM,CA8rBK,WAAlB,EAAOC,GAAsBA,EAAOD,QAAU,IAGhD,IACE4jB,mBAAqBjG,EACrB,MAAOkG,GAUPtT,SAAS,IAAK,yBAAdA,CAAwCoN,M,kCCptB1C1d,EAAOD,QAAU,SAASC,GAoBzB,OAnBKA,EAAO6jB,kBACX7jB,EAAO8jB,UAAY,aACnB9jB,EAAO+jB,MAAQ,GAEV/jB,EAAOgkB,WAAUhkB,EAAOgkB,SAAW,IACxCrjB,OAAOC,eAAeZ,EAAQ,SAAU,CACvCa,YAAY,EACZC,IAAK,WACJ,OAAOd,EAAOE,KAGhBS,OAAOC,eAAeZ,EAAQ,KAAM,CACnCa,YAAY,EACZC,IAAK,WACJ,OAAOd,EAAOC,KAGhBD,EAAO6jB,gBAAkB,GAEnB7jB,I,6BClBRD,EAAQyD,WAuCR,SAAqBygB,GACnB,IAAIC,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAC3B,OAAuC,GAA9BE,EAAWC,GAAuB,EAAKA,GA1ClDtkB,EAAQ+P,YAiDR,SAAsBmU,GACpB,IAAIK,EAcArkB,EAbAikB,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAEvB5d,EAAM,IAAIie,EAVhB,SAAsBN,EAAKG,EAAUC,GACnC,OAAuC,GAA9BD,EAAWC,GAAuB,EAAKA,EAS9BG,CAAYP,EAAKG,EAAUC,IAEzCI,EAAU,EAGVpgB,EAAMggB,EAAkB,EACxBD,EAAW,EACXA,EAGJ,IAAKnkB,EAAI,EAAGA,EAAIoE,EAAKpE,GAAK,EACxBqkB,EACGI,EAAUT,EAAIlc,WAAW9H,KAAO,GAChCykB,EAAUT,EAAIlc,WAAW9H,EAAI,KAAO,GACpCykB,EAAUT,EAAIlc,WAAW9H,EAAI,KAAO,EACrCykB,EAAUT,EAAIlc,WAAW9H,EAAI,IAC/BqG,EAAIme,KAAcH,GAAO,GAAM,IAC/Bhe,EAAIme,KAAcH,GAAO,EAAK,IAC9Bhe,EAAIme,KAAmB,IAANH,EAGK,IAApBD,IACFC,EACGI,EAAUT,EAAIlc,WAAW9H,KAAO,EAChCykB,EAAUT,EAAIlc,WAAW9H,EAAI,KAAO,EACvCqG,EAAIme,KAAmB,IAANH,GAGK,IAApBD,IACFC,EACGI,EAAUT,EAAIlc,WAAW9H,KAAO,GAChCykB,EAAUT,EAAIlc,WAAW9H,EAAI,KAAO,EACpCykB,EAAUT,EAAIlc,WAAW9H,EAAI,KAAO,EACvCqG,EAAIme,KAAcH,GAAO,EAAK,IAC9Bhe,EAAIme,KAAmB,IAANH,GAGnB,OAAOhe,GA3FTvG,EAAQyI,cAkHR,SAAwBmc,GAQtB,IAPA,IAAIL,EACAjgB,EAAMsgB,EAAMjiB,OACZkiB,EAAavgB,EAAM,EACnBwgB,EAAQ,GAIH5kB,EAAI,EAAG6kB,EAAOzgB,EAAMugB,EAAY3kB,EAAI6kB,EAAM7kB,GAH9B,MAInB4kB,EAAM/c,KAAKid,EACTJ,EAAO1kB,EAAIA,EALM,MAKgB6kB,EAAOA,EAAQ7kB,EAL/B,QAUF,IAAf2kB,GACFN,EAAMK,EAAMtgB,EAAM,GAClBwgB,EAAM/c,KACJkd,EAAOV,GAAO,GACdU,EAAQV,GAAO,EAAK,IACpB,OAEsB,IAAfM,IACTN,GAAOK,EAAMtgB,EAAM,IAAM,GAAKsgB,EAAMtgB,EAAM,GAC1CwgB,EAAM/c,KACJkd,EAAOV,GAAO,IACdU,EAAQV,GAAO,EAAK,IACpBU,EAAQV,GAAO,EAAK,IACpB,MAIJ,OAAOO,EAAMxZ,KAAK,KA3IpB,IALA,IAAI2Z,EAAS,GACTN,EAAY,GACZH,EAA4B,oBAAf3hB,WAA6BA,WAAakJ,MAEvD4D,EAAO,mEACFzP,EAAI,EAAGoE,EAAMqL,EAAKhN,OAAQzC,EAAIoE,IAAOpE,EAC5C+kB,EAAO/kB,GAAKyP,EAAKzP,GACjBykB,EAAUhV,EAAK3H,WAAW9H,IAAMA,EAQlC,SAASkkB,EAASF,GAChB,IAAI5f,EAAM4f,EAAIvhB,OAEd,GAAI2B,EAAM,EAAI,EACZ,MAAM,IAAIpB,MAAM,kDAKlB,IAAImhB,EAAWH,EAAI7d,QAAQ,KAO3B,OANkB,IAAdge,IAAiBA,EAAW/f,GAMzB,CAAC+f,EAJcA,IAAa/f,EAC/B,EACA,EAAK+f,EAAW,GAsEtB,SAASW,EAAaJ,EAAOrf,EAAOC,GAGlC,IAFA,IAAI+e,EARoBW,EASpBC,EAAS,GACJjlB,EAAIqF,EAAOrF,EAAIsF,EAAKtF,GAAK,EAChCqkB,GACIK,EAAM1kB,IAAM,GAAM,WAClB0kB,EAAM1kB,EAAI,IAAM,EAAK,QACP,IAAf0kB,EAAM1kB,EAAI,IACbilB,EAAOpd,KAdFkd,GADiBC,EAeMX,IAdT,GAAK,IACxBU,EAAOC,GAAO,GAAK,IACnBD,EAAOC,GAAO,EAAI,IAClBD,EAAa,GAANC,IAaT,OAAOC,EAAO7Z,KAAK,IAjGrBqZ,EAAU,IAAI3c,WAAW,IAAM,GAC/B2c,EAAU,IAAI3c,WAAW,IAAM,I,cCnB/BhI,EAAQ4G,KAAO,SAAUnC,EAAQ0C,EAAQie,EAAMC,EAAMC,GACnD,IAAIxb,EAAGxJ,EACHilB,EAAiB,EAATD,EAAcD,EAAO,EAC7BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBE,GAAS,EACTxlB,EAAIklB,EAAQE,EAAS,EAAK,EAC1B9kB,EAAI4kB,GAAQ,EAAI,EAChBnjB,EAAIwC,EAAO0C,EAASjH,GAOxB,IALAA,GAAKM,EAELsJ,EAAI7H,GAAM,IAAOyjB,GAAU,EAC3BzjB,KAAQyjB,EACRA,GAASH,EACFG,EAAQ,EAAG5b,EAAS,IAAJA,EAAWrF,EAAO0C,EAASjH,GAAIA,GAAKM,EAAGklB,GAAS,GAKvE,IAHAplB,EAAIwJ,GAAM,IAAO4b,GAAU,EAC3B5b,KAAQ4b,EACRA,GAASL,EACFK,EAAQ,EAAGplB,EAAS,IAAJA,EAAWmE,EAAO0C,EAASjH,GAAIA,GAAKM,EAAGklB,GAAS,GAEvE,GAAU,IAAN5b,EACFA,EAAI,EAAI2b,MACH,IAAI3b,IAAM0b,EACf,OAAOllB,EAAIqlB,IAAsB9V,KAAd5N,GAAK,EAAI,GAE5B3B,GAAQoI,KAAK+E,IAAI,EAAG4X,GACpBvb,GAAQ2b,EAEV,OAAQxjB,GAAK,EAAI,GAAK3B,EAAIoI,KAAK+E,IAAI,EAAG3D,EAAIub,IAG5CrlB,EAAQiE,MAAQ,SAAUQ,EAAQtD,EAAOgG,EAAQie,EAAMC,EAAMC,GAC3D,IAAIxb,EAAGxJ,EAAGC,EACNglB,EAAiB,EAATD,EAAcD,EAAO,EAC7BG,GAAQ,GAAKD,GAAQ,EACrBE,EAAQD,GAAQ,EAChBI,EAAe,KAATP,EAAc3c,KAAK+E,IAAI,GAAI,IAAM/E,KAAK+E,IAAI,GAAI,IAAM,EAC1DvN,EAAIklB,EAAO,EAAKE,EAAS,EACzB9kB,EAAI4kB,EAAO,GAAK,EAChBnjB,EAAId,EAAQ,GAAgB,IAAVA,GAAe,EAAIA,EAAQ,EAAK,EAAI,EAmC1D,IAjCAA,EAAQuH,KAAKmd,IAAI1kB,GAEbgF,MAAMhF,IAAUA,IAAU0O,KAC5BvP,EAAI6F,MAAMhF,GAAS,EAAI,EACvB2I,EAAI0b,IAEJ1b,EAAIpB,KAAK6F,MAAM7F,KAAK0O,IAAIjW,GAASuH,KAAKod,KAClC3kB,GAASZ,EAAImI,KAAK+E,IAAI,GAAI3D,IAAM,IAClCA,IACAvJ,GAAK,IAGLY,GADE2I,EAAI2b,GAAS,EACNG,EAAKrlB,EAELqlB,EAAKld,KAAK+E,IAAI,EAAG,EAAIgY,IAEpBllB,GAAK,IACfuJ,IACAvJ,GAAK,GAGHuJ,EAAI2b,GAASD,GACfllB,EAAI,EACJwJ,EAAI0b,GACK1b,EAAI2b,GAAS,GACtBnlB,GAAMa,EAAQZ,EAAK,GAAKmI,KAAK+E,IAAI,EAAG4X,GACpCvb,GAAQ2b,IAERnlB,EAAIa,EAAQuH,KAAK+E,IAAI,EAAGgY,EAAQ,GAAK/c,KAAK+E,IAAI,EAAG4X,GACjDvb,EAAI,IAIDub,GAAQ,EAAG5gB,EAAO0C,EAASjH,GAAS,IAAJI,EAAUJ,GAAKM,EAAGF,GAAK,IAAK+kB,GAAQ,GAI3E,IAFAvb,EAAKA,GAAKub,EAAQ/kB,EAClBilB,GAAQF,EACDE,EAAO,EAAG9gB,EAAO0C,EAASjH,GAAS,IAAJ4J,EAAU5J,GAAKM,EAAGsJ,GAAK,IAAKyb,GAAQ,GAE1E9gB,EAAO0C,EAASjH,EAAIM,IAAU,IAAJyB,I,cClF5B,IAAI+C,EAAW,GAAGA,SAElB/E,EAAOD,QAAU+L,MAAM1J,SAAW,SAAUkE,GAC1C,MAA6B,kBAAtBvB,EAAS3E,KAAKkG,K,8BCHvB,Y,qVAEAvG,EAAQqT,cAAgB,SAAAxP,GAAM,OAAI,EAAIA,GAAQiW,KAAI,SAAAiM,GAAS,OAAIA,EAAU/d,WAAW,OAEpF,IAAMwL,EAA2B,SAACjQ,EAAOgC,EAAOC,GAC/C,OAAOmB,OAAO0C,aAAP,MAAA1C,OAAM,EAAiBpD,EAAMW,MAAMqB,EAAOC,MAGlDxF,EAAQsT,aAAe,SAAC7O,GAKvB,IAL8C,IAAf0C,EAAe,uDAAN,EACpCxF,EAAI8C,EAAO0C,GACX+F,EAAM,EACNhN,EAAI,IAECA,EAAI,GACZgN,GAAO,IACPvL,GAAK8C,EAAO0C,EAASjH,GAAKgN,EAG3B,OAAOvL,GAGR3B,EAAQuT,yBAA2B,SAAA9O,GAClC,GAAIA,EAAO9B,OAAS,IACnB,OAAO,EAQR,IALA,IAEIqjB,EAAM,IACNC,EAAe,EAEV/lB,EAAI,EAAGA,EAAI,IAAKA,IAAK,CAC7B,IAAMgmB,EAAOzhB,EAAOvE,GACpB8lB,GAAOE,EACPD,GARoB,IAQJC,EAKjB,IAAK,IAAIhmB,EAAI,IAAKA,EAAI,IAAKA,IAAK,CAC/B,IAAMgmB,EAAOzhB,EAAOvE,GACpB8lB,GAAOE,EACPD,GAhBoB,IAgBJC,EAGjB,IAAMC,EAAU3e,SAASgM,EAAyB/O,EAAQ,IAAK,KAAM,GAGrE,OAEC0hB,IAAYH,GAGZG,IAAaH,GAAOC,GAAgB,IAItCjmB,EAAQoT,iBAAmB,SAAC3O,EAAQ2hB,GAA+B,IAAhBC,EAAgB,uDAAN,EAE5D,GAAI9jB,GAAUA,EAAO8B,SAASI,GAC7B,OAAOA,EAAO4B,QAAQ9D,EAAOa,KAAKgjB,GAAgBC,GAenD,IAZA,IAAMC,EAAiB,SAAC7hB,EAAQ2H,EAAOma,GACtC,IAAK,IAAIrmB,EAAI,EAAGA,EAAIkM,EAAMzJ,OAAQzC,IACjC,GAAIkM,EAAMlM,KAAOuE,EAAO8hB,EAAarmB,GACpC,OAAO,EAIT,OAAO,GAIJsmB,EAAQ/hB,EAAO4B,QAAQ+f,EAAc,GAAIC,GACtCG,GAAS,GAAG,CAClB,GAAIF,EAAe7hB,EAAQ2hB,EAAeI,GACzC,OAAOA,EAGRA,EAAQ/hB,EAAO4B,QAAQ+f,EAAc,GAAII,EAAQ,GAGlD,OAAQ,GAGTxmB,EAAQwT,yBAA2BA,I,qDCpFnCvT,EAAOD,QAAU,CAChBkW,WAAY,CACX,MACA,MACA,OACA,MACA,OACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,KACA,MACA,KACA,MACA,MACA,MACA,MACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,MACA,MACA,MACA,MACA,OACA,MACA,MACA,MACA,OACA,OACA,QACA,MACA,MACA,MACA,MACA,MACA,KACA,KACA,SACA,MACA,MACA,MACA,MACA,MACA,KACA,MACA,IACA,KACA,MACA,MACA,MACA,QACA,MACA,OACA,OACA,OACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA,MACA,MACA,MACA,KACA,MACA,MACA,MACA,MACA,MACA,OACA,MACA,MACA,QACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,OAEDC,UAAW,CACV,aACA,YACA,YACA,aACA,aACA,oBACA,aACA,YACA,qBACA,4BACA,uBACA,0BACA,0CACA,iDACA,kDACA,0EACA,4EACA,oEACA,kBACA,oBACA,+BACA,mBACA,sBACA,8BACA,gCACA,6BACA,YACA,aACA,mBACA,aACA,kBACA,gBACA,iBACA,cACA,iBACA,iBACA,yBACA,aACA,aACA,aACA,YACA,aACA,YACA,YACA,kBACA,eACA,YACA,gBACA,YACA,kBACA,2BACA,gCACA,kBACA,mBACA,YACA,aACA,gCACA,WACA,WACA,eACA,cACA,yBACA,mBACA,wBACA,iCACA,wCACA,oCACA,oBACA,6BACA,oBACA,yBACA,qBACA,oBACA,oBACA,kBACA,aACA,wBACA,YACA,YACA,YACA,YACA,YACA,aACA,kBACA,iCACA,aACA,sBACA,aACA,sBACA,YACA,oBACA,mBACA,gBACA,oBACA,+BACA,cACA,4BACA,4BACA,cACA,yBACA,cACA,aACA,sBACA,mBACA,oBACA,oBACA,wBACA,uBACA,cACA,cACA,8B,cCrOFlW,EAAOD,QAoBP,SAAe6D,GACb,GAAsB,iBAAXA,EACT,OAAO,EAGT,IAAIwH,EAAQxH,EAAOwH,MAAMob,GACzB,IAAKpb,EACH,OAAO,EAGT,IAAIqb,EAA0Brb,EAAM,GACpC,IAAKqb,EACH,OAAO,EAGT,GAAIC,EAAkBC,KAAKF,IACvBG,EAAqBD,KAAKF,GAC5B,OAAO,EAGT,OAAO,GAhCT,IAAID,EAAsB,uBAEtBE,EAAoB,sCACpBE,EAAuB,sB,cCI3B,IAAMC,EAAW,SAACC,GAChB,IAAMC,EAAQD,EAAKlN,MAAM,MACzB,GAAiC,OAA7BmN,EAAM,GAAGC,UAAU,EAAG,GACxB,IAAK,IAAI/mB,EAAI,EAAGA,EAAI8mB,EAAMrkB,OAAQzC,GAAK,EACJ,OAA7B8mB,EAAM9mB,GAAG+mB,UAAU,EAAG,KACxBD,EAAM9mB,GAAK8mB,EAAM9mB,GAAGgE,MAAM,IAIhC,OAAO8iB,EAAM1b,KAAK,OAUpBrL,EAAOD,QAAU,SAAC6W,EAAYS,EAAb,GAMX,IAUA4P,EACAC,EACAC,EACAC,EACAC,EAnBJC,EAKI,EALJA,mBACAC,EAII,EAJJA,kBACAC,EAGI,EAHJA,kBACAC,EAEI,EAFJA,mBACAC,EACI,EADJA,kBAEMC,EAAKtQ,EAAIuQ,cAEbC,EAKEjR,EALFiR,UACAC,EAIElR,EAJFkR,SACAC,EAGEnR,EAHFmR,aACAC,EAEEpR,EAFFoR,SACAC,EACErR,EADFqR,WAEIC,EAAS,GAOTC,EAAe,SAACjnB,EAAOknB,GAAR,OACnBznB,OAAOsZ,KAAKrD,GACTsD,QAAO,SAACrQ,GAAD,OAAQA,EAAE+K,WAAF,UAAgBwT,EAAhB,OAA8BxR,EAAW/M,KAAO3I,KAC/D2Y,KAAI,SAAChQ,GAAD,OAAOA,EAAE5F,MAAMmkB,EAAO1lB,OAAS,MAAI,IAG5CilB,EAAGU,QACH,EAAG,CACD,GAAIV,EAAGW,gBAAgBT,GAAY,CACjC,IAAMU,EAAOZ,EAAGa,eACZC,EAAU,KAEd,GAAI7R,EAAW8R,WAAWH,GAAQ,EAAG,CACnC,IAAM7mB,EAAI6mB,EAAKI,QACTC,EAAKL,EAAKM,QACVC,EAAKP,EAAKQ,QAChBN,EAAU,GACV,IAAK,IAAIxoB,EAAI,EAAGA,EAAIyB,EAAGzB,GAAK,EAC1BwoB,EAAQ3gB,KAAK,CAAC8gB,EAAGI,SAAS/oB,GAAI6oB,EAAGE,SAAS/oB,KAQ9CgnB,EAAQ,CACNgC,WAAY,GACZC,KAAMvB,EAAGwB,YAAYtB,GACrBuB,WAAYzB,EAAG0B,WAAWxB,GAC1ByB,SAAU3B,EAAG4B,YAAY1B,GACzB2B,KAAM7B,EAAG8B,eAAe5B,GACxB6B,UAAWvB,EAAaR,EAAGgC,YAAa,MACxClB,WAEFP,EAAOpgB,KAAKmf,GAuBd,GArBIU,EAAGW,gBAAgBR,KACrBZ,EAAO,CACLH,MAAO,GACPmC,KAAMvB,EAAGwB,YAAYrB,GACrBsB,WAAYzB,EAAG0B,WAAWvB,GAC1BwB,SAAU3B,EAAG4B,YAAYzB,GACzB0B,KAAM7B,EAAG8B,eAAe3B,GACxB8B,SAAUjC,EAAGkC,kBAEf5C,EAAMgC,WAAWnhB,KAAKof,IAEpBS,EAAGW,gBAAgBP,KACrBZ,EAAW,CACT2C,MAAO,GACPZ,KAAMvB,EAAGwB,YAAYpB,GACrBqB,WAAYzB,EAAG0B,WAAWtB,GAC1BuB,SAAU3B,EAAG4B,YAAYxB,GACzByB,KAAM7B,EAAG8B,eAAe1B,IAE1Bb,EAAKH,MAAMjf,KAAKqf,IAEdQ,EAAGW,gBAAgBN,GAAW,CAChC,IAAM+B,EAAWpC,EAAGqC,wBACdC,EAAUtC,EAAGuC,gBACnB9C,EAAO,CACL+C,QAAS,GACTC,QAAS,GAETlB,KAAMvB,EAAGwB,YAAYnB,GACrBoB,WAAYzB,EAAG0B,WAAWrB,GAC1BsB,SAAU3B,EAAG4B,YAAYvB,GACzBwB,KAAM7B,EAAG8B,eAAezB,GAExBqC,aAAc1C,EAAG2C,gBACjBC,gBAAiB5C,EAAG6C,uBACpBC,UAAWtC,EAAa8B,EAAS,OACjCS,SAAU/C,EAAGgD,0BAEbC,QAASb,EAASa,QAClBC,UAAWd,EAASc,UACpBC,cAAef,EAASe,cACxBC,aAAchB,EAASgB,aACvBC,SAAUjB,EAASiB,SACnBC,aAAclB,EAASkB,aACvBC,UAAWnB,EAASoB,UACpBC,QAASrB,EAASqB,QAClBC,UAAWtB,EAASsB,WAEtB,IAAMC,EAAK,IAAI1U,EAAW2U,mBAAmB5D,GAC7C,GACEP,EAAKgD,QAAQtiB,KAAK,CAChBohB,KAAMoC,EAAGnC,cACTC,WAAYkC,EAAGjC,qBAEViC,EAAGE,QACZ5U,EAAW6U,QAAQH,GACnBnE,EAAS2C,MAAMhiB,KAAKsf,GAQtB,GAAIO,EAAGW,gBAAgBL,GAAa,CAClCZ,EAAS,CACP+C,QAAS,GACTvP,MAAO,KACPqO,KAAMvB,EAAGwB,YAAYlB,GACrBmB,WAAYzB,EAAG0B,WAAWpB,GAC1BqB,SAAU3B,EAAG4B,YAAYtB,GACzBuB,KAAM7B,EAAG8B,eAAexB,GACxByD,iBAAkB/D,EAAGgE,sBACrBC,eAAgBjE,EAAGkE,oBACnBC,aAAcnE,EAAGoE,mBAEnB3E,EAAK+C,QAAQriB,KAAKuf,GAClB,IAAM2E,EAAK,IAAIpV,EAAWqV,eAAetE,GACzC,GACEN,EAAO+C,QAAQtiB,KAAK,CAClBohB,KAAM8C,EAAG7C,cACTC,WAAY4C,EAAG3C,qBAEV2C,EAAGR,eAGP7D,EAAG6D,KAAKvD,IAGjB,OAFArR,EAAW6U,QAAQ9D,GAEZ,CACLuB,KAAM7R,EAAI8R,cACV+C,KAA6B,MAAvB5E,EAA6BT,EAASxP,EAAI8U,eAAiB,KACjEC,IAA2B,MAAtB7E,EAA4BlQ,EAAIgV,aAAe,KACpDC,IAA2B,MAAtB9E,EAA4BnQ,EAAIkV,aAAe,KACpDC,KAA6B,MAAvB/E,EAA6BpQ,EAAIoV,cAAgB,KACvDC,IAA2B,MAAtBhF,EAA4BrQ,EAAIsV,aAAe,KACpDvD,WAAY/R,EAAIuV,eAChB1E,SACA2E,IAAK1E,EAAa9Q,EAAIyV,iBAAkB,OACxCtS,IAAK2N,EAAa9Q,EAAImD,MAAO,OAC7BrI,QAASkF,EAAI0V,a,kQCtMjB,IAAMC,EAAa9qB,EAAQ,IAE3BlC,EAAOD,QAAU,SAACyB,GAChB,IAAMyQ,EAAM,GAYZ,MAViC,oBAAtBgb,kBACThb,EAAIvN,KAAO,YACFsoB,IACT/a,EAAIvN,KAAO,WACgB,YAAlB,oBAAO6L,OAAP,cAAOA,SAChB0B,EAAIvN,KAAO,UACiB,iBAAnB,IAAOgM,EAAP,cAAOA,MAChBuB,EAAIvN,KAAO,aAGM,IAARlD,EACFyQ,EAGFA,EAAIzQ,M,mRCCbxB,EAAOD,QAnBP,WAEI,MAAsB,oBAAXwQ,QAAoD,WAA1B,EAAOA,OAAOG,UAAgD,aAAxBH,OAAOG,QAAQhM,cAKnE,IAAZgM,GAAuD,WAA5B,EAAOA,EAAQ0B,YAA2B1B,EAAQ0B,SAAS8a,WAKxE,YAArB,oBAAOC,UAAP,cAAOA,aAAyD,iBAAxBA,UAAUC,WAA0BD,UAAUC,UAAUhnB,QAAQ,aAAe,M,03BCb/H,IAAMinB,EAAMnrB,EAAQ,IACd0R,EAAW1R,EAAQ,GASzBlC,EAAOD,QAAU,SAAC6W,EAAYS,EAAKwD,GACjC,IAAMjU,EAAMtE,EAAOa,KAAK2I,MAAM3I,K,+VAAN,IAAgB0X,EAAhB,CAAuBnY,OAAQ/B,OAAOsZ,KAAKY,GAAOnY,WACpEgC,EAAOkP,EAAShN,GAClB0mB,EAAgB,EAChB3oB,EAAO,KACP4oB,EAAM,KACNC,EAAI,EACJC,EAAI,EAOR,GAAI/oB,GAAsB,cAAdA,EAAKyP,KAAsB,CACrC,IAAMuZ,EAASL,EAAIM,OAAO/mB,GAC1BjC,EAAOiS,EAAWgX,QAAQF,EAAO/oB,KAAKjC,OAASE,WAAWirB,mBAC1DjX,EAAWkX,OAAOre,IAAIie,EAAO/oB,KAAMA,GACnC6oB,EAAIE,EAAOtS,MACXqS,EAAIC,EAAOrS,OACXiS,EAAgB,MACX,CACL,IAAMtS,EAAMpE,EAAWgX,QAAQhnB,EAAIlE,OAASE,WAAWirB,mBACvDjX,EAAWkX,OAAOre,IAAI7I,EAAKoU,GAC3BuS,EAAM3W,EAAWmX,YAAY/S,EAAKpU,EAAIlE,QACY,IAA9CkU,EAAWoS,SAASuE,EAAO,GAAQ,QAKrC3W,EAAWoX,SAAST,EAAO,GAAQ,IAAK,OATrC,QAWIzhB,MAAM,GAAG3B,KAAK,GACpB0P,KAAI,SAACoU,EAAGC,GAAJ,OACHtX,EAAWoS,SAASuE,EAAa,EAANW,EAAU,UAbpC,GAWJV,EAXI,KAWDC,EAXC,KA4BP,OALa,OAAT9oB,EACF0S,EAAI8W,SAASZ,GAEblW,EAAI8W,SAASxpB,EAAM6oB,EAAGC,EAAGH,EAAeE,EAAIF,GAE9B,OAAT3oB,EAAgB4oB,EAAM5oB,K,wCCnD/B,IAAIypB,EAASlsB,EAAQ,IACjByrB,EAASzrB,EAAQ,IAErBlC,EAAOD,QAAU,CACfquB,OAAQA,EACRT,OAAQA,I,iBCbV,YAQA,SAASU,EAAWC,GACnBtrB,KAAKwB,OAAS8pB,EAAQ3pB,KACtB3B,KAAKoY,MAAQkT,EAAQlT,MACrBpY,KAAKqY,OAASiT,EAAQjT,OACtBrY,KAAK4hB,WAAa5hB,KAAKoY,MAAM,EAC7BpY,KAAKurB,QAAUvrB,KAAKqY,QAAQ,EAAErY,KAAKoY,MAAMpY,KAAK4hB,YAC9C5hB,KAAKwrB,eAAiB,GAEtBxrB,KAAK2B,KAAO,GAEZ3B,KAAKyrB,KAAO,KACZzrB,KAAK0rB,SAAW,EAChB1rB,KAAKkE,OAAS,GACdlE,KAAK2rB,SAAW3rB,KAAKurB,QAAQvrB,KAAKkE,OAClClE,KAAK4rB,OAAS,EACd5rB,KAAK6rB,MAAQ,GACb7rB,KAAK8rB,SAAW,EAChB9rB,KAAK+rB,GAAK,EACV/rB,KAAKgsB,GAAK,EACVhsB,KAAKisB,OAAS,EACdjsB,KAAKksB,gBAAkB,EAGxBb,EAAWxsB,UAAUusB,OAAS,WAC7B,IAAIe,EAAa,IAAI7sB,EAAOU,KAAKkE,OAAOlE,KAAKurB,SAC7CvrB,KAAK4H,IAAM,EACXukB,EAAWnrB,MAAMhB,KAAKyrB,KAAKzrB,KAAK4H,IAAI,GAAG5H,KAAK4H,KAAK,EACjDukB,EAAW1gB,cAAczL,KAAK2rB,SAAS3rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EAC3DukB,EAAW1gB,cAAczL,KAAK0rB,SAAS1rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EAC3DukB,EAAW1gB,cAAczL,KAAKkE,OAAOlE,KAAK4H,KAAK5H,KAAK4H,KAAK,EAEzDukB,EAAW1gB,cAAczL,KAAKwrB,eAAexrB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACjEukB,EAAW1gB,cAAczL,KAAKoY,MAAMpY,KAAK4H,KAAK5H,KAAK4H,KAAK,EACxDukB,EAAWjgB,cAAclM,KAAKqY,OAAOrY,KAAK4H,KAAK5H,KAAK4H,KAAK,EACzDukB,EAAW5gB,cAAcvL,KAAK4rB,OAAO5rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACzDukB,EAAW5gB,cAAcvL,KAAK6rB,MAAM7rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACxDukB,EAAW1gB,cAAczL,KAAK8rB,SAAS9rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EAC3DukB,EAAW1gB,cAAczL,KAAKurB,QAAQvrB,KAAK4H,KAAK5H,KAAK4H,KAAK,EAC1DukB,EAAW1gB,cAAczL,KAAK+rB,GAAG/rB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACrDukB,EAAW1gB,cAAczL,KAAKgsB,GAAGhsB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACrDukB,EAAW1gB,cAAczL,KAAKisB,OAAOjsB,KAAK4H,KAAK5H,KAAK4H,KAAK,EACzDukB,EAAW1gB,cAAczL,KAAKksB,gBAAgBlsB,KAAK4H,KAAK5H,KAAK4H,KAAK,EAKlE,IAHA,IAAI3K,EAAE,EACFmvB,EAAW,EAAEpsB,KAAKoY,MAAMpY,KAAK4hB,WAExBna,EAAI,EAAGA,EAAGzH,KAAKqY,OAAQ5Q,IAAI,CACnC,IAAK,IAAID,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAI,CACnC,IAAIzI,EAAIiB,KAAK4H,IAAIH,EAAE2kB,EAAW,EAAF5kB,EAC5BvK,IACAkvB,EAAWptB,GAAIiB,KAAKwB,OAAOvE,KAC3BkvB,EAAWptB,EAAE,GAAKiB,KAAKwB,OAAOvE,KAC9BkvB,EAAWptB,EAAE,GAAMiB,KAAKwB,OAAOvE,KAEhC,GAAG+C,KAAK4hB,WAAW,EAAE,CACpB,IAAIyK,EAAarsB,KAAK4H,IAAIH,EAAE2kB,EAAoB,EAAXpsB,KAAKoY,MAC1C+T,EAAWhlB,KAAK,EAAEklB,EAAWA,EAAWrsB,KAAK4hB,aAI/C,OAAOuK,GAGRnvB,EAAOD,QAAU,SAASuuB,EAASgB,GAIjC,YAHuB,IAAZA,IAAyBA,EAAU,KAGvC,CACL3qB,KAHY,IAAI0pB,EAAWC,GACXF,SAGhBhT,MAAOkT,EAAQlT,MACfC,OAAQiT,EAAQjT,W,yCC9EpB,YAOA,SAASkU,EAAW/qB,EAAOgrB,GAMzB,GALAxsB,KAAK4H,IAAM,EACX5H,KAAKwB,OAASA,EACdxB,KAAKwsB,gBAAkBA,EACvBxsB,KAAKysB,WAAY,EACjBzsB,KAAKyrB,KAAOzrB,KAAKwB,OAAOO,SAAS,QAAS,EAAG/B,KAAK4H,KAAO,GACxC,MAAb5H,KAAKyrB,KAAc,MAAM,IAAIxrB,MAAM,oBACvCD,KAAK0sB,cACL1sB,KAAK2sB,YAGPJ,EAAW1tB,UAAU6tB,YAAc,WAiCjC,GAhCA1sB,KAAK2rB,SAAW3rB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC9C5H,KAAK4H,KAAO,EACZ5H,KAAK0rB,SAAW1rB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC9C5H,KAAK4H,KAAO,EACZ5H,KAAKkE,OAASlE,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC5C5H,KAAK4H,KAAO,EACZ5H,KAAK4sB,WAAa5sB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAChD5H,KAAK4H,KAAO,EACZ5H,KAAKoY,MAAQpY,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC3C5H,KAAK4H,KAAO,EACZ5H,KAAKqY,OAASrY,KAAKwB,OAAOqJ,YAAY7K,KAAK4H,KAC3C5H,KAAK4H,KAAO,EACZ5H,KAAK4rB,OAAS5rB,KAAKwB,OAAO4I,aAAapK,KAAK4H,KAC5C5H,KAAK4H,KAAO,EACZ5H,KAAK6rB,MAAQ7rB,KAAKwB,OAAO4I,aAAapK,KAAK4H,KAC3C5H,KAAK4H,KAAO,EACZ5H,KAAK8rB,SAAW9rB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC9C5H,KAAK4H,KAAO,EACZ5H,KAAK6sB,QAAU7sB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC7C5H,KAAK4H,KAAO,EACZ5H,KAAK+rB,GAAK/rB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KACxC5H,KAAK4H,KAAO,EACZ5H,KAAKgsB,GAAKhsB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KACxC5H,KAAK4H,KAAO,EACZ5H,KAAKisB,OAASjsB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC5C5H,KAAK4H,KAAO,EACZ5H,KAAKksB,gBAAkBlsB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KACrD5H,KAAK4H,KAAO,EAEM,KAAf5H,KAAK6rB,OAAgB7rB,KAAKwsB,gBAC3BxsB,KAAK6rB,MAAQ,IAEX7rB,KAAK6rB,MAAQ,GAAI,CACnB,IAAIxqB,EAAsB,IAAhBrB,KAAKisB,OAAe,GAAKjsB,KAAK6rB,MAAQ7rB,KAAKisB,OACrDjsB,KAAK8sB,QAAU,IAAIhkB,MAAMzH,GACzB,IAAK,IAAIpE,EAAI,EAAGA,EAAIoE,EAAKpE,IAAK,CAC5B,IAAI8vB,EAAO/sB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAClColB,EAAQhtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnCqlB,EAAMjtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACjCslB,EAAOltB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACtC5H,KAAK8sB,QAAQ7vB,GAAK,CAChBgwB,IAAKA,EACLD,MAAOA,EACPD,KAAMA,EACNG,KAAMA,IAITltB,KAAKqY,OAAS,IACfrY,KAAKqY,SAAW,EAChBrY,KAAKysB,WAAY,IAKrBF,EAAW1tB,UAAU8tB,UAAY,WAC7B,IAAIQ,EAAO,MAAQntB,KAAK6rB,MACpBxqB,EAAMrB,KAAKoY,MAAQpY,KAAKqY,OAAS,EACrCrY,KAAK2B,KAAO,IAAIrC,EAAO+B,GACvBrB,KAAKmtB,MAGTZ,EAAW1tB,UAAUuuB,KAAO,WAC1B,IAAIC,EAAO5nB,KAAK6nB,KAAKttB,KAAKoY,MAAQ,GAC9Bha,EAAOivB,EAAK,EACZ5lB,EAAIzH,KAAKqY,QAAU,EAAIrY,KAAKqY,OAAS,GAAKrY,KAAKqY,OACnD,IAAS5Q,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CAEzC,IADA,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAI6lB,EAAM7lB,IAGxB,IAFA,IAAIzE,EAAI/C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/B4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAM,EAAF5Q,EAAI,EAClCvK,EAAI,EAAGA,EAAI,GACb,EAAFuK,EAAIvK,EAAE+C,KAAKoY,MADOnb,IAAK,CAExB,IAAIwwB,EAAMztB,KAAK8sB,QAAU/pB,GAAI,EAAE9F,EAAI,GAEnC+C,KAAK2B,KAAK6rB,EAAW,EAAFvwB,GAAO,EAC1B+C,KAAK2B,KAAK6rB,EAAW,EAAFvwB,EAAM,GAAKwwB,EAAIV,KAClC/sB,KAAK2B,KAAK6rB,EAAW,EAAFvwB,EAAM,GAAKwwB,EAAIT,MAClChtB,KAAK2B,KAAK6rB,EAAW,EAAFvwB,EAAM,GAAKwwB,EAAIR,IAQ5B,GAAR7uB,IACF4B,KAAK4H,KAAM,EAAIxJ,KAKrBmuB,EAAW1tB,UAAU6uB,KAAO,WAExB,GAAoB,GAAjB1tB,KAAK8rB,SAAc,KAuET6B,EAAT,SAAsBC,GAClB,IAAIH,EAAMztB,KAAK8sB,QAAQc,GACvB5tB,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIV,KAC9B/sB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIT,MAC9BhtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIR,IAC9BO,GAAU,GA5EdxtB,KAAK2B,KAAKwF,KAAK,KAMf,IAJA,IAAIqmB,EAAW,EACXzJ,EAAQ/jB,KAAKysB,UAAUzsB,KAAKqY,OAAO,EAAE,EACrCwV,GAAa,EAEXL,EAASxtB,KAAK2B,KAAKjC,QAAO,CAC5B,IAAI6H,EAAIvH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/B7E,EAAI/C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAEnC,GAAQ,GAALL,EAAO,CACN,GAAQ,GAALxE,EAAO,CACH/C,KAAKysB,UACJ1I,IAEAA,IAEJyJ,EAAWzJ,EAAM/jB,KAAKoY,MAAM,EAC5ByV,GAAa,EACb,SACE,GAAQ,GAAL9qB,EACL,MACE,GAAO,GAAJA,EAAM,CAEX,IAAIyE,EAAIxH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/BH,EAAIzH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAChC5H,KAAKysB,UACJ1I,GAAOtc,EAEPsc,GAAOtc,EAGX+lB,GAAY/lB,EAAEzH,KAAKoY,MAAM,EAAI,EAAF5Q,MAC1B,CAED,IADA,IAAIlK,EAAI0C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC3B3K,EAAE,EAAEA,EAAE8F,EAAE9F,IAER0wB,EAAavwB,KAAK4C,KADlB6tB,EAC6B,GAAJvwB,GAEI,IAAJA,IAAW,GAG/B,EAAJL,GAAWA,EAAE,EAAI8F,IAClBzF,EAAI0C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,QAGnCimB,GAAcA,EAGS,IAApB9qB,EAAE,GAAM,EAAK,IAChB/C,KAAK4H,YAKb,IAAS3K,EAAI,EAAGA,EAAIsK,EAAGtK,IAEf0wB,EAAavwB,KAAK4C,KADlB6tB,EAC6B,GAAJ9qB,GAEI,IAAJA,IAAW,GAExC8qB,GAAcA,OAmB5B,KAAIR,EAAO5nB,KAAK6nB,KAAKttB,KAAKoY,MAAM,GAC5Bha,EAAOivB,EAAK,EAChB,IAAS5lB,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CACzC,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EAClD,IAASD,EAAI,EAAGA,EAAI6lB,EAAM7lB,IAAK,CACzBzE,EAAI/C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/B4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAM,EAAF5Q,EAAI,EAD3C,IAGIsmB,EAAS/qB,GAAG,EACZgrB,EAAU,GAAFhrB,EAER0qB,EAAMztB,KAAK8sB,QAAQgB,GAOvB,GANA9tB,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIV,KAC9B/sB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIT,MAC9BhtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIR,IAGzB,EAAFzlB,EAAI,GAAGxH,KAAKoY,MAAM,MAErBqV,EAAMztB,KAAK8sB,QAAQiB,GAEnB/tB,KAAK2B,KAAK6rB,EAAS,GAAK,EACxBxtB,KAAK2B,KAAK6rB,EAAS,EAAI,GAAKC,EAAIV,KAChC/sB,KAAK2B,KAAK6rB,EAAS,EAAI,GAAKC,EAAIT,MAChChtB,KAAK2B,KAAK6rB,EAAS,EAAI,GAAKC,EAAIR,IAItB,GAAR7uB,IACF4B,KAAK4H,KAAM,EAAIxJ,MAQzBmuB,EAAW1tB,UAAUmvB,KAAO,WAExB,GAAoB,GAAjBhuB,KAAK8rB,SAAc,KAsDT6B,EAAT,SAAsBC,GAClB,IAAIH,EAAMztB,KAAK8sB,QAAQc,GACvB5tB,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIV,KAC9B/sB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIT,MAC9BhtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIR,IAC9BO,GAAU,GA3DdxtB,KAAK2B,KAAKwF,KAAK,KAKf,IAHA,IAAIqmB,EAAW,EACXzJ,EAAQ/jB,KAAKysB,UAAUzsB,KAAKqY,OAAO,EAAE,EAEnCmV,EAASxtB,KAAK2B,KAAKjC,QAAO,CAC5B,IAAI6H,EAAIvH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/B7E,EAAI/C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAEnC,GAAQ,GAALL,EAAO,CACN,GAAQ,GAALxE,EAAO,CACH/C,KAAKysB,UACJ1I,IAEAA,IAEJyJ,EAAWzJ,EAAM/jB,KAAKoY,MAAM,EAC5B,SACE,GAAQ,GAALrV,EACL,MACE,GAAO,GAAJA,EAAM,CAEX,IAAIyE,EAAIxH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/BH,EAAIzH,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAChC5H,KAAKysB,UACJ1I,GAAOtc,EAEPsc,GAAOtc,EAGX+lB,GAAY/lB,EAAEzH,KAAKoY,MAAM,EAAI,EAAF5Q,MAC1B,CACD,IAAI,IAAIvK,EAAE,EAAEA,EAAE8F,EAAE9F,IAAI,CAChB,IAAIK,EAAI0C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnC+lB,EAAavwB,KAAK4C,KAAM1C,IAEvB,EAAFyF,GACC/C,KAAK4H,YAMb,IAAS3K,EAAI,EAAGA,EAAIsK,EAAGtK,IACnB0wB,EAAavwB,KAAK4C,KAAM+C,QAkBpC,KAAI3E,EAAO4B,KAAKoY,MAAQ,EACxB,IAAS3Q,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CACvC,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EAClD,IAASD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAC7BzE,EAAI/C,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAC/B4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EACvC,GAAIzE,EAAI/C,KAAK8sB,QAAQptB,OAAQ,CACzB,IAAI+tB,EAAMztB,KAAK8sB,QAAQ/pB,GAEvB/C,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIV,KAC9B/sB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIT,MAC9BhtB,KAAK2B,KAAK6rB,EAAW,GAAKC,EAAIR,SAG9BjtB,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAK,IAC1BxtB,KAAK2B,KAAK6rB,EAAW,GAAK,IAC1BxtB,KAAK2B,KAAK6rB,EAAW,GAAK,IAGtB,GAARpvB,IACA4B,KAAK4H,KAAQ,EAAIxJ,MAMjCmuB,EAAW1tB,UAAUovB,MAAQ,WAG3B,IAFA,IAAIC,EAAOluB,KAAKoY,MAAQ,EACU+V,EAArB5pB,SAAS,QAAS,GACtBkD,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CAEzC,IADA,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAEnC,IAAI4mB,EAAIpuB,KAAKwB,OAAO4I,aAAapK,KAAK4H,KACtC5H,KAAK4H,KAAK,EACV,IAAImlB,GAAQqB,EAAID,GAAQA,EAAO,IAAM,EACjCnB,GAASoB,GAAK,EAAID,GAASA,EAAO,IAAM,EACxClB,GAAOmB,GAAK,GAAKD,GAAQA,EAAO,IAAM,EACtCE,EAASD,GAAG,GAAI,IAAK,EAErBZ,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EAEvCxH,KAAK2B,KAAK6rB,GAAYa,EACtBruB,KAAK2B,KAAK6rB,EAAW,GAAKT,EAC1B/sB,KAAK2B,KAAK6rB,EAAW,GAAKR,EAC1BhtB,KAAK2B,KAAK6rB,EAAW,GAAKP,EAG5BjtB,KAAK4H,KAAOsmB,IAIhB3B,EAAW1tB,UAAUyvB,MAAQ,WAC3B,IAAIJ,EAAQluB,KAAKoY,MAAQ,EAAG,EAE5BpY,KAAKuuB,QAAU,MACfvuB,KAAKwuB,UAAY,IACjBxuB,KAAKyuB,SAAU,GACfzuB,KAAK0uB,MAAQ,EAEO,GAAjB1uB,KAAK8rB,WACN9rB,KAAKuuB,QAAUvuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC7C5H,KAAK4H,KAAK,EACV5H,KAAKwuB,UAAYxuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC/C5H,KAAK4H,KAAK,EACV5H,KAAKyuB,SAAWzuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC9C5H,KAAK4H,KAAK,EACV5H,KAAK0uB,MAAQ1uB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC3C5H,KAAK4H,KAAK,GAKZ,IADA,IAAItJ,EAAG,CAAC,EAAE,EAAE,GACHrB,EAAE,EAAEA,EAAE,GAAGA,IACX+C,KAAKuuB,SAAStxB,EAAG,GAAMqB,EAAG,KAC1B0B,KAAKwuB,WAAWvxB,EAAG,GAAMqB,EAAG,KAC5B0B,KAAKyuB,UAAUxxB,EAAG,GAAMqB,EAAG,KAElCA,EAAG,IAAIA,EAAG,GAAIA,EAAG,IAAIA,EAAG,GAAIA,EAAG,GAAG,EAAEA,EAAG,GAAIA,EAAG,IAAI,EAAGA,EAAG,IAAI,EAE5D,IAAK,IAAImJ,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CAEzC,IADA,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAEnC,IAAI4mB,EAAIpuB,KAAKwB,OAAO4I,aAAapK,KAAK4H,KACtC5H,KAAK4H,KAAK,EAEV,IAAImlB,GAAQqB,EAAEpuB,KAAKyuB,WAAWnwB,EAAG,GAC7B0uB,GAASoB,EAAEpuB,KAAKwuB,YAAYlwB,EAAG,GAC/B2uB,GAAOmB,EAAEpuB,KAAKuuB,UAAUjwB,EAAG,GAE3BkvB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EAEvCxH,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKT,EAC1B/sB,KAAK2B,KAAK6rB,EAAW,GAAKR,EAC1BhtB,KAAK2B,KAAK6rB,EAAW,GAAKP,EAG5BjtB,KAAK4H,KAAOsmB,IAIhB3B,EAAW1tB,UAAU8vB,MAAQ,WAC3B,IAAK,IAAIlnB,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAAK,CAEzC,IADA,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAEnC,IAAIulB,EAAO/sB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAClColB,EAAQhtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnCqlB,EAAMjtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACjC4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EACvCxH,KAAK2B,KAAK6rB,GAAY,EACtBxtB,KAAK2B,KAAK6rB,EAAW,GAAKT,EAC1B/sB,KAAK2B,KAAK6rB,EAAW,GAAKR,EAC1BhtB,KAAK2B,KAAK6rB,EAAW,GAAKP,EAG5BjtB,KAAK4H,KAAQ5H,KAAKoY,MAAQ,IAS9BmU,EAAW1tB,UAAU+vB,MAAQ,WAE3B,GAAoB,GAAjB5uB,KAAK8rB,SAAc,CACpB9rB,KAAKuuB,QAAUvuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC7C5H,KAAK4H,KAAK,EACV5H,KAAKwuB,UAAYxuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC/C5H,KAAK4H,KAAK,EACV5H,KAAKyuB,SAAWzuB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC9C5H,KAAK4H,KAAK,EACV5H,KAAK0uB,MAAQ1uB,KAAKwB,OAAO6I,aAAarK,KAAK4H,KAC3C5H,KAAK4H,KAAK,EACR,IAAK,IAAIH,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAElC,IADA,IAAI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAEjC,IAAI6mB,EAAQruB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnCmlB,EAAO/sB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAClColB,EAAQhtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnCqlB,EAAMjtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACjC4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EACvCxH,KAAK2B,KAAK6rB,GAAYa,EACtBruB,KAAK2B,KAAK6rB,EAAW,GAAKT,EAC1B/sB,KAAK2B,KAAK6rB,EAAW,GAAKR,EAC1BhtB,KAAK2B,KAAK6rB,EAAW,GAAKP,QAKlC,IAASxlB,EAAIzH,KAAKqY,OAAS,EAAG5Q,GAAK,EAAGA,IAElC,IADI8lB,EAAOvtB,KAAKysB,UAAYhlB,EAAIzH,KAAKqY,OAAS,EAAI5Q,EACzCD,EAAI,EAAGA,EAAIxH,KAAKoY,MAAO5Q,IAAK,CAE7BulB,EAAO/sB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OAClColB,EAAQhtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnCqlB,EAAMjtB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACjCymB,EAAQruB,KAAKwB,OAAO2I,UAAUnK,KAAK4H,OACnC4lB,EAAWD,EAAOvtB,KAAKoY,MAAQ,EAAQ,EAAJ5Q,EACvCxH,KAAK2B,KAAK6rB,GAAYa,EACtBruB,KAAK2B,KAAK6rB,EAAW,GAAKT,EAC1B/sB,KAAK2B,KAAK6rB,EAAW,GAAKR,EAC1BhtB,KAAK2B,KAAK6rB,EAAW,GAAKP,IAWxCV,EAAW1tB,UAAUgwB,QAAU,WAC7B,OAAO7uB,KAAK2B,MAGd3E,EAAOD,QAAU,SAAS+xB,GAExB,OADc,IAAIvC,EAAWuC,M,wCC/d/B,IAAMC,EAAM7vB,EAAQ,IAEpBlC,EAAOD,QAAU,CACfiyB,sBAAuBD,EAAIE,aAC3BC,wBAAyB,GACzB5K,mBAAoB,IACpBC,kBAAmB,IACnBC,kBAAmB,IACnBC,mBAAoB,IACpBC,kBAAmB,M,cCTrB1nB,EAAOD,QAAU,CACfoyB,SAAU,IACVC,SAAU,IACVC,UAAW,IACXC,KAAM,IACNC,cAAe,IACfC,uBAAwB,IACxBP,aAAc,IACdQ,YAAa,IACbC,YAAa,IACbC,YAAa,IACbC,YAAa,KACbC,YAAa,KACbC,gBAAiB,O,yBChBfjb,GAAU,EAEd9X,EAAQ8X,QAAUA,EAElB9X,EAAQqX,WAAa,SAAC2b,GACpBlb,EAAUkb,GAGZhzB,EAAQoX,IAAM,sCAAIrF,EAAJ,yBAAIA,EAAJ,uBAAc+F,EAAUmb,QAAQ7b,IAAI9N,MAAM,EAAMyI,GAAQ,O,kQCRtE9R,EAAOD,QAAU,SAAC6X,EAAUjP,GAC1B,QAAoC,IAAzBe,EAAOupB,cAA+B,CAO/C,GANAtqB,EAAIqP,SAAS,CAAEC,OAAQ,yBAA0BD,SAAU,IAC3DtO,EAAOwpB,cAActb,QAKmB,IAA7BlO,EAAOypB,mBAA4D,YAAvB,oBAAOC,YAAP,cAAOA,cAC5D1pB,EAAOupB,cAAgBvpB,EAAOypB,sBACzB,SAAuC,IAA5BzpB,EAAO2pB,iBAGvB,MAAMpwB,MAAM,gCAFZyG,EAAOupB,cAAgBvpB,EAAO2pB,iBAIhC1qB,EAAIqP,SAAS,CAAEC,OAAQ,yBAA0BD,SAAU,IAE7D,OAAOtO,EAAOupB,iB,iCCjBhBjzB,EAAOD,QAAUmC,EAAQ,IAAUoxB,Y,iBCAnC;CAAyF,WAAa,aAAa,SAASC,EAAExtB,GAAG,MAAMA,EAAG,IAAI5E,OAAE,EAAO8sB,GAAE,EAAOmD,EAAE,oBAAqBxuB,YAAY,oBAAqB4wB,aAAa,oBAAqBC,aAAa,oBAAqBC,SAAS,SAASC,EAAE5tB,EAAEwE,GAAGvH,KAAKujB,MAAM,iBAAkBhc,EAAEA,EAAE,EAAEvH,KAAK3C,EAAE,EAAE2C,KAAKwB,OAAOuB,aAAaqrB,EAAExuB,WAAWkJ,OAAO/F,EAAE,IAAKqrB,EAAExuB,WAAWkJ,OAAO,OAAO,EAAE9I,KAAKwB,OAAO9B,QAAQM,KAAKujB,OAAOgN,EAAEtwB,MAAM,kBAAkBD,KAAKwB,OAAO9B,QAAQM,KAAKujB,OAAOvjB,KAAK4wB,IAAID,EAAE9xB,UAAU+xB,EAAE,WAAW,IAAkBrpB,EAAdxE,EAAE/C,KAAKwB,OAASlE,EAAEyF,EAAErD,OAAOnC,EAAE,IAAK6wB,EAAExuB,WAAWkJ,OAAOxL,GAAG,GAAG,GAAG8wB,EAAE7wB,EAAEkP,IAAI1J,QAAQ,IAAIwE,EAAE,EAAEA,EAAEjK,IAAIiK,EAAEhK,EAAEgK,GAAGxE,EAAEwE,GAAG,OAAOvH,KAAKwB,OAAOjE,GAC9qBozB,EAAE9xB,UAAUtB,EAAE,SAASwF,EAAEwE,EAAEjK,GAAG,IAA+C6Z,EAA3C5Z,EAAEyC,KAAKwB,OAAOqF,EAAE7G,KAAKujB,MAAMqN,EAAE5wB,KAAK3C,EAAEgQ,EAAE9P,EAAEsJ,GAAoG,GAA/FvJ,GAAG,EAAEiK,IAAIxE,EAAE,EAAEwE,GAAGspB,EAAI,IAAF9tB,IAAQ,GAAG8tB,EAAE9tB,IAAI,EAAE,MAAM,GAAG8tB,EAAE9tB,IAAI,GAAG,MAAM,EAAE8tB,EAAE9tB,IAAI,GAAG,OAAO,GAAGwE,EAAEspB,EAAE9tB,IAAI,EAAEwE,GAAM,EAAEA,EAAEqpB,EAAEvjB,EAAEA,GAAG9F,EAAExE,EAAE6tB,GAAGrpB,OAAO,IAAI4P,EAAE,EAAEA,EAAE5P,IAAI4P,EAAE9J,EAAEA,GAAG,EAAEtK,GAAGwE,EAAE4P,EAAE,EAAE,EAAE,KAAMyZ,IAAIA,EAAE,EAAErzB,EAAEsJ,KAAKgqB,EAAExjB,GAAGA,EAAE,EAAExG,IAAItJ,EAAEmC,SAASnC,EAAEyC,KAAK4wB,MAAMrzB,EAAEsJ,GAAGwG,EAAErN,KAAKwB,OAAOjE,EAAEyC,KAAK3C,EAAEuzB,EAAE5wB,KAAKujB,MAAM1c,GAAG8pB,EAAE9xB,UAAU2hB,OAAO,WAAW,IAA+BljB,EAA3ByF,EAAE/C,KAAKwB,OAAO+F,EAAEvH,KAAKujB,MAA0F,OAAlF,EAAEvjB,KAAK3C,IAAI0F,EAAEwE,KAAK,EAAEvH,KAAK3C,EAAE0F,EAAEwE,GAAGspB,EAAE9tB,EAAEwE,IAAIA,KAAK6mB,EAAE9wB,EAAEyF,EAAE6D,SAAS,EAAEW,IAAIxE,EAAErD,OAAO6H,EAAEjK,EAAEyF,GAAUzF,GAC1e,IAAqCwzB,EAAjCC,EAAG,IAAK3C,EAAExuB,WAAWkJ,OAAO,KAAO,IAAIgoB,EAAE,EAAE,IAAIA,IAAIA,EAAE,CAAC,IAAI,IAAQE,EAAJC,EAAEH,EAAOI,EAAG,EAAED,EAAEA,IAAI,EAAEA,EAAEA,KAAK,EAAED,IAAK,EAAEA,GAAM,EAAFC,IAAMC,EAAGH,EAAGD,IAAIE,GAAIE,EAAG,OAAO,EAAE,IAAIL,EAAEE,EAAG,SAASI,EAAGpuB,EAAEwE,EAAEjK,GAAG,IAAIC,EAAEsJ,EAAE,iBAAkBU,EAAEA,EAAEA,EAAE,EAAEqpB,EAAE,iBAAkBtzB,EAAEA,EAAEyF,EAAErD,OAAY,IAALnC,GAAG,EAAMsJ,EAAI,EAAF+pB,EAAI/pB,MAAMU,EAAEhK,EAAEA,IAAI,EAAE6zB,EAAW,KAAR7zB,EAAEwF,EAAEwE,KAAS,IAAIV,EAAE+pB,GAAG,EAAE/pB,IAAIU,GAAG,EAAsLhK,GAA1BA,GAA1BA,GAA1BA,GAA1BA,GAA1BA,GAA1BA,GAAxBA,EAAEA,IAAI,EAAE6zB,EAAW,KAAR7zB,EAAEwF,EAAEwE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,QAAe,EAAE6pB,EAAa,KAAV7zB,EAAEwF,EAAEwE,EAAE,KAAS,OAAS,WAAFhK,KAAgB,EACnhB,IAAI8zB,EAAG,CAAC,EAAE,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAC/e,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,SAAS,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAC9e,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,SAAS,WAAW,WAAW,WAAW,SAAS,WAAW,WAAW,WAAW,UAAU,WAAW,WAC9e,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAC/e,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,SAAS,WAAW,WAAW,WAAW,SAAS,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAC9e,SAAS,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,UAAU,WAAW,WAAW,WAAW,WAAWD,EAAEhD,EAAE,IAAIqC,YAAYY,GAAIA,EAAG,SAASC,KAAO,SAASC,EAAGxuB,GAAG/C,KAAKwB,OAAO,IAAK4sB,EAAEoC,YAAY1nB,OAAO,EAAE/F,GAAG/C,KAAKN,OAAO,EACH,SAAS8xB,EAAEzuB,GAAG,IAA8C8D,EAAE+pB,EAAEvjB,EAAE8J,EAAEsT,EAAEptB,EAAEU,EAAEgB,EAAE7B,EAAEwB,EAA5D6I,EAAExE,EAAErD,OAAOpC,EAAE,EAAEC,EAAE4G,OAAOstB,kBAAsC,IAAI1yB,EAAE,EAAEA,EAAEwI,IAAIxI,EAAEgE,EAAEhE,GAAGzB,IAAIA,EAAEyF,EAAEhE,IAAIgE,EAAEhE,GAAGxB,IAAIA,EAAEwF,EAAEhE,IAAkD,IAA9C8H,EAAE,GAAGvJ,EAAEszB,EAAE,IAAKxC,EAAEqC,YAAY3nB,OAAOjC,GAAGwG,EAAE,EAAE8J,EAAE,EAAMsT,EAAE,EAAEpd,GAAG/P,GAAG,CAAC,IAAIyB,EAAE,EAAEA,EAAEwI,IAAIxI,EAAE,GAAGgE,EAAEhE,KAAKsO,EAAE,CAAS,IAARhQ,EAAE,EAAEU,EAAEoZ,EAAMja,EAAE,EAAEA,EAAEmQ,IAAInQ,EAAEG,EAAEA,GAAG,EAAI,EAAFU,EAAIA,IAAI,EAAY,IAAVW,EAAE2O,GAAG,GAAGtO,EAAM7B,EAAEG,EAAEH,EAAE2J,EAAE3J,GAAGutB,EAAEmG,EAAE1zB,GAAGwB,IAAIyY,IAAI9J,EAAE8J,IAAI,EAAEsT,IAAI,EAAE,MAAM,CAACmG,EAAEtzB,EAAEC,GAAI,SAASm0B,EAAG3uB,EAAEwE,GAAGvH,KAAKmX,EAAEwa,EAAG3xB,KAAK4xB,EAAE,EAAE5xB,KAAK6Q,MAAMud,GAAGrrB,aAAa+F,MAAM,IAAIlJ,WAAWmD,GAAGA,EAAE/C,KAAK+C,EAAE,EAAEwE,IAAIA,EAAEsqB,OAAO7xB,KAAK4xB,EAAErqB,EAAEsqB,MAAM,iBAAkBtqB,EAAEuqB,kBAAkB9xB,KAAKmX,EAAE5P,EAAEuqB,iBAAiBvqB,EAAEwqB,eAAe/xB,KAAKuH,EAAE6mB,GAAG7mB,EAAEwqB,wBAAwBjpB,MAAM,IAAIlJ,WAAW2H,EAAEwqB,cAAcxqB,EAAEwqB,cAAc,iBAAkBxqB,EAAEyqB,cAAchyB,KAAK+C,EAAEwE,EAAEyqB,cAAchyB,KAAKuH,IAAIvH,KAAKuH,EAAE,IAAK6mB,EAAExuB,WAAWkJ,OAAO,QAD/sByoB,EAAG1yB,UAAUozB,UAAU,SAASlvB,GAAG,OAAO,IAAIA,EAAE,GAAG,EAAE,IAAIwuB,EAAG1yB,UAAUiG,KAAK,SAAS/B,EAAEwE,GAAG,IAAIjK,EAAEC,EAAgBqzB,EAAd/pB,EAAE7G,KAAKwB,OAA0C,IAAjClE,EAAE0C,KAAKN,OAAOmH,EAAE7G,KAAKN,UAAU6H,EAAMV,EAAE7G,KAAKN,UAAUqD,EAAE,EAAEzF,IAAMC,EAAEyC,KAAKiyB,UAAU30B,GAAGuJ,EAAEvJ,GAAGuJ,EAAEtJ,KAAGqzB,EAAE/pB,EAAEvJ,GAAGuJ,EAAEvJ,GAAGuJ,EAAEtJ,GAAGsJ,EAAEtJ,GAAGqzB,EAAEA,EAAE/pB,EAAEvJ,EAAE,GAAGuJ,EAAEvJ,EAAE,GAAGuJ,EAAEtJ,EAAE,GAAGsJ,EAAEtJ,EAAE,GAAGqzB,EAAEtzB,EAAEC,EAAa,OAAOyC,KAAKN,QACtnB6xB,EAAG1yB,UAAU4gB,IAAI,WAAW,IAAI1c,EAAEwE,EAAgBhK,EAAEsJ,EAAE+pB,EAAlBtzB,EAAE0C,KAAKwB,OAAoF,IAAvE+F,EAAEjK,EAAE,GAAGyF,EAAEzF,EAAE,GAAG0C,KAAKN,QAAQ,EAAEpC,EAAE,GAAGA,EAAE0C,KAAKN,QAAQpC,EAAE,GAAGA,EAAE0C,KAAKN,OAAO,GAAOkxB,EAAE,KAAK/pB,EAAE,EAAE+pB,EAAE,IAAQ5wB,KAAKN,UAAamH,EAAE,EAAE7G,KAAKN,QAAQpC,EAAEuJ,EAAE,GAAGvJ,EAAEuJ,KAAKA,GAAG,GAAMvJ,EAAEuJ,GAAGvJ,EAAEszB,KAAGrzB,EAAED,EAAEszB,GAAGtzB,EAAEszB,GAAGtzB,EAAEuJ,GAAGvJ,EAAEuJ,GAAGtJ,EAAEA,EAAED,EAAEszB,EAAE,GAAGtzB,EAAEszB,EAAE,GAAGtzB,EAAEuJ,EAAE,GAAGvJ,EAAEuJ,EAAE,GAAGtJ,EAAaqzB,EAAE/pB,EAAE,MAAM,CAAC0c,MAAMxgB,EAAE7E,MAAMqJ,EAAE7H,OAAOM,KAAKN,SAAquB,IAAwCwyB,EAApCP,EAAG,EAAEQ,EAAG,CAACC,KAAK,EAAEtB,EAAE,EAAE3yB,EAAEwzB,EAAGU,EAAE,GAAGC,EAAG,GAChlC,IAAIJ,EAAE,EAAE,IAAIA,EAAEA,IAAI,QAAOjH,GAAG,KAAK,KAAKiH,EAAEI,EAAGxtB,KAAK,CAACotB,EAAE,GAAG,IAAI,MAAM,KAAK,KAAKA,EAAEI,EAAGxtB,KAAK,CAACotB,EAAE,IAAI,IAAI,IAAI,MAAM,KAAK,KAAKA,EAAEI,EAAGxtB,KAAK,CAACotB,EAAE,IAAI,EAAE,IAAI,MAAM,KAAK,KAAKA,EAAEI,EAAGxtB,KAAK,CAACotB,EAAE,IAAI,IAAI,IAAI,MAAM,QAAQ3B,EAAE,oBAAoB2B,GAMvN,SAASK,EAAGxvB,EAAEwE,GAAGvH,KAAKN,OAAOqD,EAAE/C,KAAKwyB,EAAEjrB,EALtCmqB,EAAG7yB,UAAU4rB,EAAE,WAAW,IAAI1nB,EAAEwE,EAAEjK,EAAEC,EAAEsJ,EAAE7G,KAAK6Q,MAAM,OAAO7Q,KAAKmX,GAAG,KAAK,EAAM,IAAJ7Z,EAAE,EAAMC,EAAEsJ,EAAEnH,OAAOpC,EAAEC,GAAG,CAA0D,IAAgB4Z,EAAIsT,EAAIptB,EAApBuzB,EAA7DrpB,EAAE6mB,EAAEvnB,EAAED,SAAStJ,EAAEA,EAAE,OAAOuJ,EAAE5F,MAAM3D,EAAEA,EAAE,OAA2B+P,GAApB/P,GAAGiK,EAAE7H,UAAqBnC,EAAcQ,EAAEI,EAAEY,EAAEZ,EAAEjB,EAAE8C,KAAKuH,EAAE7I,EAAEsB,KAAK+C,EAAE,GAAGqrB,EAAE,CAAC,IAAIlxB,EAAE,IAAI0C,WAAWI,KAAKuH,EAAE/F,QAAQtE,EAAEwC,QAAQhB,EAAEkyB,EAAElxB,OAAO,GAAGxC,EAAE,IAAI0C,WAAW1C,EAAEwC,QAAQ,GAAGxC,EAAEuP,IAAIzM,KAAKuH,GAA8G,GAA3G4P,EAAE9J,EAAE,EAAE,EAAEnQ,EAAEwB,KAAO,EAAFyY,EAAe9Z,EAAK,QAAhBotB,EAAEmG,EAAElxB,QAAkB,MAAMxC,EAAEwB,KAAO,IAAF+rB,EAAMvtB,EAAEwB,KAAK+rB,IAAI,EAAE,IAAIvtB,EAAEwB,KAAO,IAAFrB,EAAMH,EAAEwB,KAAKrB,IAAI,EAAE,IAAO+wB,EAAElxB,EAAEuP,IAAImkB,EAAElyB,GAAGA,GAAGkyB,EAAElxB,OAAOxC,EAAEA,EAAE0J,SAAS,EAAElI,OAAO,CAAK,IAAJX,EAAE,EAAMgB,EAAE6xB,EAAElxB,OAAO3B,EAAEgB,IAAIhB,EAAEb,EAAEwB,KAC1fkyB,EAAE7yB,GAAGb,EAAEwC,OAAOhB,EAAEsB,KAAK+C,EAAErE,EAAEsB,KAAKuH,EAAErK,EAAE,MAAM,KAAK,EAAE,IAAI8B,EAAE,IAAI2xB,EAAEvC,EAAE,IAAIxuB,WAAWI,KAAKuH,EAAE/F,QAAQxB,KAAKuH,EAAEvH,KAAK+C,GAAG/D,EAAEzB,EAAE,EAAE,EAAE0tB,GAAGjsB,EAAEzB,EAAE,EAAE,EAAE0tB,GAAG,IAAiBT,EAAEiI,EAAEjrB,EAAjBkrB,EAAEC,EAAG3yB,KAAK6G,GAAa,IAAJ2jB,EAAE,EAAMiI,EAAEC,EAAEhzB,OAAO8qB,EAAEiI,EAAEjI,IAAI,GAAGhjB,EAAEkrB,EAAElI,GAAGmG,EAAE9xB,UAAUtB,EAAE8I,MAAMrH,EAAEszB,EAAG9qB,IAAI,IAAIA,EAAExI,EAAEzB,EAAEm1B,IAAIlI,GAAGkI,IAAIlI,GAAGS,GAAGjsB,EAAEzB,EAAEm1B,IAAIlI,GAAG,GAAGxrB,EAAEzB,EAAEm1B,IAAIlI,GAAGkI,IAAIlI,GAAGS,QAAQ,GAAG,MAAMzjB,EAAE,MAAMxH,KAAKuH,EAAEvI,EAAEwhB,SAASxgB,KAAK+C,EAAE/C,KAAKuH,EAAE7H,OAAO,MAAM,KAAKiyB,EAAG,IAA2DiB,EAAEC,EAAEL,EAAEH,EAAES,EAAsDC,EAAGC,EAAGC,EAAGC,EAAGC,EACveC,EAAGC,EAAEC,EAAGC,EAAEC,EAD4VC,EAAE,IAAI9C,EAAEvC,EAAE,IAAIxuB,WAAWI,KAAKuH,EAAE/F,QAAQxB,KAAKuH,EAAEvH,KAAK+C,GAAa2wB,EAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAmBC,EAAG7qB,MAAM,IACzY,IAA7F8pB,EAAEjB,EAAG8B,EAAEl2B,EAAE,EAAE,EAAE0tB,GAAGwI,EAAEl2B,EAAEq1B,EAAE,EAAE3H,GAAG4H,EAAEF,EAAG3yB,KAAK6G,GAAoBmsB,EAAGY,EAApBb,EAAGc,EAAG7zB,KAAKkyB,EAAE,KAA8BgB,EAAGU,EAAnBX,EAAGY,EAAG7zB,KAAKwxB,EAAE,IAAiBgB,EAAE,IAAI,IAAIA,GAAG,IAAIO,EAAGP,EAAE,GAAGA,KAAK,IAAIH,EAAE,GAAG,EAAEA,GAAG,IAAIY,EAAGZ,EAAE,GAAGA,KAAK,IAAiD5qB,EAAEqsB,EAAEC,EAAEC,EAAoCC,EAAErC,EAAzFsC,EAAG1B,EAAE2B,GAAG9B,EAAE+B,GAAE,IAAKhG,EAAEqC,YAAY3nB,OAAOorB,EAAGC,IAAaE,GAAE,IAAKjG,EAAEqC,YAAY3nB,OAAO,KAASwrB,GAAE,IAAKlG,EAAExuB,WAAWkJ,OAAO,IAAI,IAAIrB,EAAEqsB,EAAE,EAAErsB,EAAEysB,EAAGzsB,IAAI2sB,GAAEN,KAAKf,EAAGtrB,GAAG,IAAIA,EAAE,EAAEA,EAAE0sB,GAAG1sB,IAAI2sB,GAAEN,KAAKb,EAAGxrB,GAAG,IAAI2mB,EAAO,IAAJ3mB,EAAE,EAAMusB,EAAGM,GAAE50B,OAAO+H,EAAEusB,IAAKvsB,EAAE6sB,GAAE7sB,GAAG,EAAQ,IAANA,EAAEwsB,EAAE,EAAMD,EAAGI,GAAE10B,OAAO+H,EAAEusB,EAAGvsB,GAAGqsB,EAAE,CAAC,IAAIA,EAAE,EAAErsB,EAAEqsB,EAAEE,GAAII,GAAE3sB,EAAEqsB,KAAKM,GAAE3sB,KAAKqsB,GAAO,GAAJC,EAAED,EAAK,IAAIM,GAAE3sB,GAAG,GAAG,EAAEssB,EAAE,KAAK,EAAEA,KAAKM,GAAEJ,KAC3f,EAAEK,GAAE,UAAU,KAAK,EAAEP,IAAGnC,EAAE,IAAImC,EAAEA,EAAE,KAAMA,EAAE,GAAGnC,EAAEmC,IAAInC,EAAEmC,EAAE,GAAG,IAAInC,GAAGyC,GAAEJ,KAAK,GAAGI,GAAEJ,KAAKrC,EAAE,EAAE0C,GAAE,QAAQD,GAAEJ,KAAK,GAAGI,GAAEJ,KAAKrC,EAAE,GAAG0C,GAAE,OAAOP,GAAGnC,OAAO,GAAGyC,GAAEJ,KAAKG,GAAE3sB,GAAG6sB,GAAEF,GAAE3sB,MAAU,IAAJssB,EAAQ,KAAK,EAAEA,KAAKM,GAAEJ,KAAKG,GAAE3sB,GAAG6sB,GAAEF,GAAE3sB,WAAW,KAAK,EAAEssB,IAAGnC,EAAE,EAAEmC,EAAEA,EAAE,GAAIA,EAAE,GAAGnC,EAAEmC,IAAInC,EAAEmC,EAAE,GAAGM,GAAEJ,KAAK,GAAGI,GAAEJ,KAAKrC,EAAE,EAAE0C,GAAE,MAAMP,GAAGnC,EAA8C,IAA5C7uB,EAAEqrB,EAAEiG,GAAEztB,SAAS,EAAEqtB,GAAGI,GAAEpzB,MAAM,EAAEgzB,GAAGd,EAAGU,EAAGS,GAAE,GAAOf,EAAE,EAAE,GAAGA,EAAEA,IAAII,EAAGJ,GAAGJ,EAAGO,EAAGH,IAAI,IAAIT,EAAE,GAAG,EAAEA,GAAG,IAAIa,EAAGb,EAAE,GAAGA,KAAwD,IAAnDM,EAAGQ,EAAGT,GAAIM,EAAEl2B,EAAEi1B,EAAE,IAAI,EAAEvH,GAAGwI,EAAEl2B,EAAE80B,EAAE,EAAE,EAAEpH,GAAGwI,EAAEl2B,EAAEu1B,EAAE,EAAE,EAAE7H,GAAOsI,EAAE,EAAEA,EAAET,EAAES,IAAIE,EAAEl2B,EAAEo2B,EAAGJ,GAAG,EAAEtI,GAAO,IAAJsI,EAAE,EAAMC,EAAGzwB,EAAErD,OAAO6zB,EAAEC,EAAGD,IAAI,GAAGF,EACzftwB,EAAEwwB,GAAGE,EAAEl2B,EAAE61B,EAAGC,GAAGF,EAAGE,GAAGpI,GAAG,IAAIoI,EAAE,CAAK,OAAJE,IAAWF,GAAG,KAAK,GAAGC,EAAG,EAAE,MAAM,KAAK,GAAGA,EAAG,EAAE,MAAM,KAAK,GAAGA,EAAG,EAAE,MAAM,QAAQ/C,EAAE,iBAAiB8C,GAAGI,EAAEl2B,EAAEwF,EAAEwwB,GAAGD,EAAGrI,GAAG,IAA0BsJ,GAAEC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAA1CC,GAAG,CAAC/B,EAAGD,GAAIiC,GAAG,CAAC9B,EAAGD,GAAmE,IAAxC0B,GAAGI,GAAG,GAAGH,GAAGG,GAAG,GAAGF,GAAGG,GAAG,GAAGF,GAAGE,GAAG,GAAGT,GAAE,EAAMC,GAAG3B,EAAEnzB,OAAO60B,GAAEC,KAAKD,GAAE,GAAGE,GAAG5B,EAAE0B,IAAGd,EAAEl2B,EAAEo3B,GAAGF,IAAIG,GAAGH,IAAIxJ,GAAG,IAAIwJ,GAAGhB,EAAEl2B,EAAEs1B,IAAI0B,IAAG1B,IAAI0B,IAAGtJ,GAAGyJ,GAAG7B,IAAI0B,IAAGd,EAAEl2B,EAAEs3B,GAAGH,IAAII,GAAGJ,IAAIzJ,GAAGwI,EAAEl2B,EAAEs1B,IAAI0B,IAAG1B,IAAI0B,IAAGtJ,QAAQ,GAAG,MAAMwJ,GAAG,MAAMz0B,KAAKuH,EAAEksB,EAAEjT,SAASxgB,KAAK+C,EAAE/C,KAAKuH,EAAE7H,OAAO,MAAM,QAAQ6wB,EAAE,4BAA4B,OAAOvwB,KAAKuH,GAE3e,IAAI0tB,EAAG,WAAW,SAASlyB,EAAEwE,GAAG,QAAO0jB,GAAG,KAAK,IAAI1jB,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,EAAE,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IACxfA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,IAAIA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,GAAG,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,KAAK,KAAKA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,KAAK,MAAMA,EAAE,MAAM,CAAC,IAAIA,EAAE,IAAI,GAAG,QAAQgpB,EAAE,mBAAmBhpB,IAAI,IAASjK,EAAEC,EAAPgK,EAAE,GAAO,IAAIjK,EAAE,EAAE,KAAKA,EAAEA,IAAIC,EAAEwF,EAAEzF,GAAGiK,EAAEjK,GAAGC,EAAE,IAAI,GAAGA,EAAE,IACpf,GAAGA,EAAE,GAAG,OAAOgK,EAFR,GAEa2tB,EAAG9G,EAAE,IAAIqC,YAAYwE,GAAIA,EAC7C,SAAStC,EAAG5vB,EAAEwE,GAAG,SAASjK,EAAEiK,EAAEjK,GAAG,IAAmBuJ,EAAkEwG,EAE0Dod,EAAEtT,EAF7IpU,EAAEwE,EAAEirB,EAAEj1B,EAAE,GAAGqzB,EAAE,EAAsE,OAAlE/pB,EAAEquB,EAAG3tB,EAAE7H,QAAQnC,EAAEqzB,KAAO,MAAF/pB,EAAQtJ,EAAEqzB,KAAK/pB,GAAG,GAAG,IAAItJ,EAAEqzB,KAAK/pB,GAAG,IAAgBokB,GAAG,KAAK,IAAIloB,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,GAAGA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,GAAGA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,EAAE,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,GAAG,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,GAAG,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,EAAEtK,EAAE,GAAG,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,GAAG,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,GAAG,GAAG,MAAM,KAAK,IAAIA,EAAEsK,EAAE,CAAC,GAAGtK,EACpf,GAAG,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,GAAG,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,KAAKA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,IAAI,GAAG,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,GAAG,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,GAAG,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,IAAI,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,IAAI,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,IAAI,MAAM,KAAK,MAAMA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,IAAI,MAAM,KAAK,OAAOA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,KAAK,IAAI,MAAM,KAAK,OACnfA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,MAAM,IAAI,MAAM,KAAK,OAAOA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,MAAM,IAAI,MAAM,KAAK,OAAOA,EAAEsK,EAAE,CAAC,GAAGtK,EAAE,MAAM,IAAI,MAAM,QAAQwtB,EAAE,oBAAwE,IAApD1pB,EAAEwG,EAAE9P,EAAEqzB,KAAK/pB,EAAE,GAAGtJ,EAAEqzB,KAAK/pB,EAAE,GAAGtJ,EAAEqzB,KAAK/pB,EAAE,GAAW4jB,EAAE,EAAMtT,EAAE5Z,EAAEmC,OAAO+qB,EAAEtT,IAAIsT,EAAEvtB,EAAEwB,KAAKnB,EAAEktB,GAAGiI,EAAEn1B,EAAE,MAAMitB,EAAEjtB,EAAE,MAAMyB,EAAEuI,EAAE7H,OAAOpC,EAAE,EAAEyB,EAAE,KAAK,IAAIxB,EAAEsJ,EAAE+pB,EAAEvjB,EAAE8J,EAAO9Z,EAAEU,EAAEgB,EAAoHyI,EAA7HijB,EAAE,GAASvtB,EAAEkxB,EAAE,IAAIoC,YAAY,EAAEjpB,EAAE7H,QAAQ,GAAGhB,EAAE,EAAEM,EAAE,EAAE0zB,EAAE,IAAKtE,EAAEqC,YAAY3nB,OAAO,KAAK0hB,EAAE,IAAK4D,EAAEqC,YAAY3nB,OAAO,IAAI2pB,EAAE1vB,EAAE6uB,EAAI,IAAIxD,EAAE,CAAC,IAAIwC,EAAE,EAAE,KAAKA,GAAG8B,EAAE9B,KAAK,EAAE,IAAIA,EAAE,EAAE,IAAIA,GAAGpG,EAAEoG,KAAK,EAAe,IAAb8B,EAAE,KAAK,EAAEn1B,EAAE,EAAMsJ,EAAEU,EAAE7H,OAAOnC,EAAEsJ,IAAItJ,EAAE,CAC9e,IAD+eqzB,EAAEzZ,EAAE,EAC/e9J,EAAE,EAAEujB,EAAEvjB,GAAG9P,EAAEqzB,IAAI/pB,IAAI+pB,EAAEzZ,EAAEA,GAAG,EAAE5P,EAAEhK,EAAEqzB,GAA8B,GAA3BnG,EAAEtT,KAAKhZ,IAAIssB,EAAEtT,GAAG,IAAI9Z,EAAEotB,EAAEtT,KAAQ,EAAEnY,KAAK,CAAC,KAAK,EAAE3B,EAAEqC,QAAQ,MAAMnC,EAAEF,EAAE,IAAIA,EAAE83B,QAAQ,GAAG53B,EAAE,GAAGsJ,EAAE,CAAgB,IAAf9H,GAAGzB,EAAEyB,GAAG,GAAG6xB,EAAE,EAAMvjB,EAAExG,EAAEtJ,EAAEqzB,EAAEvjB,IAAIujB,EAAEppB,EAAED,EAAEhK,EAAEqzB,GAAG1zB,EAAEwB,KAAK8I,IAAIkrB,EAAElrB,GAAG,MAAM,EAAEnK,EAAEqC,QAAQ3B,EAAEq3B,EAAG7tB,EAAEhK,EAAEF,GAAG0B,EAAEA,EAAEW,OAAO3B,EAAE2B,QAAQ8H,EAAED,EAAEhK,EAAE,GAAGL,EAAEwB,KAAK8I,IAAIkrB,EAAElrB,GAAGlK,EAAES,EAAE,IAAIT,EAAEyB,GAAG,GAAGhB,EAAE2B,OAAO+yB,EAAE1zB,EAAEhB,EAAET,EAAES,EAAE,IAAIgB,EAAEzB,EAAEyB,GAAG,IAAIyI,EAAED,EAAEhK,GAAGL,EAAEwB,KAAK8I,IAAIkrB,EAAElrB,IAAInK,EAAEyH,KAAKvH,GAAmC,OAAhCL,EAAEwB,KAAK,IAAIg0B,EAAE,OAAO3vB,EAAEmvB,EAAEQ,EAAE3vB,EAAEyuB,EAAEhH,EAAS4D,EAAElxB,EAAE0J,SAAS,EAAElI,GAAGxB,EACtZ,SAASk4B,EAAGryB,EAAEwE,EAAEjK,GAAG,IAAIC,EAAEsJ,EAAMwG,EAAE8J,EAAEsT,EAAEptB,EAAVuzB,EAAE,EAAU7yB,EAAEgF,EAAErD,OAAOyX,EAAE,EAAE9Z,EAAEC,EAAEoC,OAAO6H,EAAE,KAAK4P,EAAE9Z,EAAE8Z,IAAI,CAAgB,GAAf5Z,EAAED,EAAED,EAAE8Z,EAAE,GAAG9J,EAAE,EAAK,EAAEujB,EAAE,CAAC,IAAInG,EAAEmG,EAAE,EAAEnG,EAAEA,IAAI,GAAG1nB,EAAExF,EAAEktB,EAAE,KAAK1nB,EAAEwE,EAAEkjB,EAAE,GAAG,SAASljB,EAAE8F,EAAEujB,EAAE,KAAK,IAAIvjB,GAAG9F,EAAE8F,EAAEtP,GAAGgF,EAAExF,EAAE8P,KAAKtK,EAAEwE,EAAE8F,MAAMA,EAAiB,GAAfA,EAAEujB,IAAI/pB,EAAEtJ,EAAEqzB,EAAEvjB,GAAM,MAAMA,EAAE,MAAM,OAAO,IAAIklB,EAAG3B,EAAErpB,EAAEV,GACxP,SAASgtB,EAAG9wB,EAAEwE,GAAG,IAA2DqpB,EAAEvjB,EAAE8J,EAAEsT,EAAEptB,EAA/DC,EAAEyF,EAAErD,OAAOnC,EAAE,IAAIg0B,EAAG,KAAK1qB,EAAE,IAAKunB,EAAExuB,WAAWkJ,OAAOxL,GAAa,IAAI8wB,EAAE,IAAI3D,EAAE,EAAEA,EAAEntB,EAAEmtB,IAAI5jB,EAAE4jB,GAAG,EAAE,IAAIA,EAAE,EAAEA,EAAEntB,IAAImtB,EAAE,EAAE1nB,EAAE0nB,IAAIltB,EAAEuH,KAAK2lB,EAAE1nB,EAAE0nB,IAAgE,GAA5DmG,EAAE9nB,MAAMvL,EAAEmC,OAAO,GAAG2N,EAAE,IAAK+gB,EAAEqC,YAAY3nB,OAAOvL,EAAEmC,OAAO,GAAM,IAAIkxB,EAAElxB,OAAO,OAAOmH,EAAEtJ,EAAEkiB,MAAM8D,OAAO,EAAE1c,EAAM,IAAJ4jB,EAAE,EAAMptB,EAAEE,EAAEmC,OAAO,EAAE+qB,EAAEptB,IAAIotB,EAAEmG,EAAEnG,GAAGltB,EAAEkiB,MAAMpS,EAAEod,GAAGmG,EAAEnG,GAAGvsB,MAA6B,IAAvBiZ,EAC5T,SAAYpU,EAAEwE,EAAEjK,GAAG,SAASC,EAAEwF,GAAG,IAAIzF,EAAEmtB,EAAE1nB,GAAG1F,EAAE0F,IAAIzF,IAAIiK,GAAGhK,EAAEwF,EAAE,GAAGxF,EAAEwF,EAAE,MAAMsK,EAAE/P,KAAKD,EAAE0F,GAAG,IAAoJ7F,EAAEwB,EAAEM,EAAE0zB,EAAElI,EAAxJ3jB,EAAE,IAAKunB,EAAEoC,YAAY1nB,OAAOxL,GAAGszB,EAAE,IAAKxC,EAAExuB,WAAWkJ,OAAOxL,GAAG+P,EAAE,IAAK+gB,EAAExuB,WAAWkJ,OAAOvB,GAAG4P,EAAErO,MAAMxL,GAAGmtB,EAAE3hB,MAAMxL,GAAGD,EAAEyL,MAAMxL,GAAGS,GAAG,GAAGT,GAAGiK,EAAExI,EAAE,GAAGzB,EAAE,EAAqB,IAATuJ,EAAEvJ,EAAE,GAAGiK,EAAM7I,EAAE,EAAEA,EAAEpB,IAAIoB,EAAEX,EAAEgB,EAAE6xB,EAAElyB,GAAG,GAAGkyB,EAAElyB,GAAG,EAAEX,GAAGgB,GAAGhB,IAAI,EAAE8I,EAAEvJ,EAAE,EAAEoB,IAAImI,EAAEvJ,EAAE,EAAEoB,GAAG,EAAE,GAAG6I,EAA8C,IAA5CV,EAAE,GAAG+pB,EAAE,GAAGzZ,EAAE,GAAGrO,MAAMjC,EAAE,IAAI4jB,EAAE,GAAG3hB,MAAMjC,EAAE,IAAQnI,EAAE,EAAEA,EAAEpB,IAAIoB,EAAEmI,EAAEnI,GAAG,EAAEmI,EAAEnI,EAAE,GAAGkyB,EAAElyB,KAAKmI,EAAEnI,GAAG,EAAEmI,EAAEnI,EAAE,GAAGkyB,EAAElyB,IAAIyY,EAAEzY,GAAGoK,MAAMjC,EAAEnI,IAAI+rB,EAAE/rB,GAAGoK,MAAMjC,EAAEnI,IAAI,IAAIxB,EAAE,EAAEA,EAAEqK,IAAIrK,EAAEmQ,EAAEnQ,GAAGI,EAAE,IAAI0B,EAAE,EAAEA,EAAE6H,EAAEvJ,EAAE,KAAK0B,EAAEmY,EAAE7Z,EAC3f,GAAG0B,GAAG+D,EAAE/D,GAAGyrB,EAAEntB,EAAE,GAAG0B,GAAGA,EAAE,IAAI9B,EAAE,EAAEA,EAAEI,IAAIJ,EAAEG,EAAEH,GAAG,EAAgC,IAA9B,IAAI0zB,EAAEtzB,EAAE,OAAO+P,EAAE,KAAKhQ,EAAEC,EAAE,IAAQoB,EAAEpB,EAAE,EAAE,GAAGoB,IAAIA,EAAE,CAAgB,IAAfg0B,EAAEx1B,EAAE,EAAEstB,EAAEntB,EAAEqB,EAAE,GAAOM,EAAE,EAAEA,EAAE6H,EAAEnI,GAAGM,KAAI0zB,EAAEvb,EAAEzY,EAAE,GAAG8rB,GAAGrT,EAAEzY,EAAE,GAAG8rB,EAAE,IAAKznB,EAAE7F,IAAIia,EAAEzY,GAAGM,GAAG0zB,EAAEjI,EAAE/rB,GAAGM,GAAGuI,EAAEijB,GAAG,IAAIrT,EAAEzY,GAAGM,GAAG+D,EAAE7F,GAAGutB,EAAE/rB,GAAGM,GAAG9B,IAAIA,GAAGG,EAAEqB,GAAG,EAAE,IAAIkyB,EAAElyB,IAAInB,EAAEmB,GAAG,OAAO2O,EAFwEgoB,CAAGhoB,EAAEA,EAAE3N,OAAO6H,GAAGkjB,EAAE,EAAMptB,EAAEuzB,EAAElxB,OAAO+qB,EAAEptB,IAAIotB,EAAE5jB,EAAE+pB,EAAEnG,GAAGlH,OAAOpM,EAAEsT,GAAG,OAAO5jB,EAGpY,SAAS+sB,EAAG7wB,GAAG,IAAwD6tB,EAAEvjB,EAAE8J,EAAEsT,EAA1DljB,EAAE,IAAK6mB,EAAEoC,YAAY1nB,OAAO/F,EAAErD,QAAQpC,EAAE,GAAGC,EAAE,GAAGsJ,EAAE,EAAc,IAAJ+pB,EAAE,EAAMvjB,EAAEtK,EAAErD,OAAOkxB,EAAEvjB,EAAEujB,IAAItzB,EAAEyF,EAAE6tB,IAAgB,GAAH,EAARtzB,EAAEyF,EAAE6tB,KAAa,IAAJA,EAAE,EAAMvjB,EAAE,GAAGujB,GAAGvjB,EAAEujB,IAAIrzB,EAAEqzB,GAAG/pB,EAAEA,GAAQ,EAALvJ,EAAEszB,GAAK/pB,IAAI,EAAM,IAAJ+pB,EAAE,EAAMvjB,EAAEtK,EAAErD,OAAOkxB,EAAEvjB,EAAEujB,IAAmC,IAA9B/pB,EAAEtJ,EAAEwF,EAAE6tB,IAAIrzB,EAAEwF,EAAE6tB,KAAK,EAAEzZ,EAAE5P,EAAEqpB,GAAG,EAAMnG,EAAE1nB,EAAE6tB,GAAGzZ,EAAEsT,EAAEtT,IAAI5P,EAAEqpB,GAAGrpB,EAAEqpB,IAAI,EAAI,EAAF/pB,EAAIA,KAAK,EAAE,OAAOU,EAAG,SAAS+tB,EAAGvyB,EAAEwE,GAAGvH,KAAK6Q,MAAM9N,EAAE/C,KAAK+C,EAAE/C,KAAK1C,EAAE,EAAE0C,KAAKqN,EAAE,GAAG9F,IAAIA,EAAEguB,QAAQv1B,KAAKqN,EAAE9F,EAAEguB,OAAO,iBAAkBhuB,EAAEiuB,WAAWx1B,KAAKw1B,SAASjuB,EAAEiuB,UAAU,iBAAkBjuB,EAAEkuB,UAAUz1B,KAAKwqB,EAAEjjB,EAAEkuB,SAASluB,EAAEmuB,iBAAiB11B,KAAK9C,EAAEqK,EAAEmuB,iBAAiB11B,KAAK9C,IAAI8C,KAAK9C,EAAE,IACxhBo4B,EAAGz2B,UAAU4rB,EAAE,WAAW,IAAI1nB,EAAEwE,EAAEjK,EAAEC,EAAEsJ,EAAE+pB,EAAEvjB,EAAE8J,EAAEsT,EAAE,IAAK2D,EAAExuB,WAAWkJ,OAAO,OAAOzL,EAAE,EAAEU,EAAEiC,KAAK6Q,MAAM9R,EAAEiB,KAAK1C,EAAEJ,EAAE8C,KAAKw1B,SAAS92B,EAAEsB,KAAKwqB,EAA+O,GAA7OC,EAAEptB,KAAK,GAAGotB,EAAEptB,KAAK,IAAIotB,EAAEptB,KAAK,EAAE0F,EAAE,EAAE/C,KAAKqN,EAAEsoB,QAAQ5yB,GAAG6yB,GAAI51B,KAAKqN,EAAEwoB,WAAW9yB,GAAG+yB,GAAI91B,KAAKqN,EAAE0oB,QAAQhzB,GAAGizB,GAAIvL,EAAEptB,KAAK0F,EAAEwE,GAAG0uB,KAAKC,IAAID,KAAKC,OAAO,IAAID,MAAM,IAAI,EAAExL,EAAEptB,KAAO,IAAFkK,EAAMkjB,EAAEptB,KAAKkK,IAAI,EAAE,IAAIkjB,EAAEptB,KAAKkK,IAAI,GAAG,IAAIkjB,EAAEptB,KAAKkK,IAAI,GAAG,IAAIkjB,EAAEptB,KAAK,EAAEotB,EAAEptB,KAAK84B,EAAMn2B,KAAKqN,EAAEsoB,QAAQx3B,EAAE,CAAK,IAAJkP,EAAE,EAAM8J,EAAEja,EAAEwC,OAAO2N,EAAE8J,IAAI9J,EAAoB,KAAlBujB,EAAE1zB,EAAE6H,WAAWsI,MAAWod,EAAEptB,KAAKuzB,IAAI,EAAE,KAAKnG,EAAEptB,KAAO,IAAFuzB,EAAMnG,EAAEptB,KAAK,EAAE,GAAG2C,KAAKqN,EAAEooB,QAAQ,CAClf,IADmfpoB,EACrf,EAAM8J,EAAEzY,EAAEgB,OAAO2N,EAAE8J,IAAI9J,EAAoB,KAAlBujB,EAAElyB,EAAEqG,WAAWsI,MAAWod,EAAEptB,KAAKuzB,IAAI,EAAE,KAAKnG,EAAEptB,KAAO,IAAFuzB,EAAMnG,EAAEptB,KAAK,EAC3B,OAD6B2C,KAAKqN,EAAE0oB,QAAQz4B,EAAY,MAAV6zB,EAAG1G,EAAE,EAAEptB,GAASotB,EAAEptB,KAAO,IAAFC,EAAMmtB,EAAEptB,KAAKC,IAAI,EAAE,KAAK0C,KAAK9C,EAAE60B,aAAatH,EAAEzqB,KAAK9C,EAAE80B,YAAY30B,EAAqBotB,GAAnB5jB,EAAE,IAAI6qB,EAAG3zB,EAAEiC,KAAK9C,IAAOutB,IAAIptB,EAAEwJ,EAAE9D,EAAEqrB,IAAI/wB,EAAE,EAAEotB,EAAEjpB,OAAOhB,YAAYR,KAAKuH,EAAE,IAAI3H,WAAWvC,EAAE,GAAG2C,KAAKuH,EAAEkF,IAAI,IAAI7M,WAAW6qB,EAAEjpB,SAASipB,EAAEzqB,KAAKuH,GAAGkjB,EAAE,IAAI7qB,WAAW6qB,EAAEjpB,SAASjE,EAAE4zB,EAAGpzB,EAAEI,EAAEA,GAAGssB,EAAEptB,KAAO,IAAFE,EAAMktB,EAAEptB,KAAKE,IAAI,EAAE,IAAIktB,EAAEptB,KAAKE,IAAI,GAAG,IAAIktB,EAAEptB,KAAKE,IAAI,GAAG,IAAI4Z,EAAEpZ,EAAE2B,OAAO+qB,EAAEptB,KAAO,IAAF8Z,EAAMsT,EAAEptB,KAAK8Z,IAAI,EAAE,IAAIsT,EAAEptB,KAAK8Z,IAAI,GAAG,IAAIsT,EAAEptB,KACrf8Z,IAAI,GAAG,IAAInX,KAAK1C,EAAEyB,EAAEqvB,GAAG/wB,EAAEotB,EAAE/qB,SAASM,KAAKuH,EAAEkjB,EAAEA,EAAE7jB,SAAS,EAAEvJ,IAAWotB,GAAG,IAAI0L,EAAG,IAAIH,EAAG,EAAEJ,EAAG,EAAEE,EAAG,GAAG,SAASM,EAAErzB,EAAEwE,GAAmQ,OAAhQvH,KAAKtC,EAAE,GAAGsC,KAAKjB,EAAE,MAAMiB,KAAK6G,EAAE7G,KAAKgE,EAAEhE,KAAK1C,EAAE0C,KAAKhB,EAAE,EAAEgB,KAAK6Q,MAAMud,EAAE,IAAIxuB,WAAWmD,GAAGA,EAAE/C,KAAK0yB,GAAE,EAAG1yB,KAAKuwB,EAAE8F,EAAGr2B,KAAKo0B,GAAE,GAAM7sB,IAAKA,EAAE,MAAIA,EAAEgc,QAAQvjB,KAAK1C,EAAEiK,EAAEgc,OAAOhc,EAAE+uB,aAAat2B,KAAKjB,EAAEwI,EAAE+uB,YAAY/uB,EAAEgvB,aAAav2B,KAAKuwB,EAAEhpB,EAAEgvB,YAAYhvB,EAAEivB,SAASx2B,KAAKo0B,EAAE7sB,EAAEivB,SAAex2B,KAAKuwB,GAAG,KAAKkG,EAAGz2B,KAAK+C,EAAE,MAAM/C,KAAKuH,EAAE,IAAK6mB,EAAExuB,WAAWkJ,OAAO,MAAM9I,KAAKjB,EAAE,KAAK,MAAM,KAAKs3B,EAAGr2B,KAAK+C,EAAE,EAAE/C,KAAKuH,EAAE,IAAK6mB,EAAExuB,WAAWkJ,OAAO9I,KAAKjB,GAAGiB,KAAK4wB,EAAE5wB,KAAKoxB,EAAEpxB,KAAK6yB,EAAE7yB,KAAK8zB,EAAE9zB,KAAKjC,EAAEiC,KAAKu0B,EAAE,MAAM,QAAQhE,EAAEtwB,MAAM,0BACxkB,IAAIw2B,EAAG,EAAEJ,EAAG,EACZD,EAAEv3B,UAAU5B,EAAE,WAAW,MAAM+C,KAAK0yB,GAAG,CAAC,IAAI3vB,EAAE2zB,GAAE12B,KAAK,GAA0B,OAArB,EAAF+C,IAAM/C,KAAK0yB,EAAEzH,GAAGloB,KAAK,GAAY,KAAK,EAAE,IAAIwE,EAAEvH,KAAK6Q,MAAMvT,EAAE0C,KAAK1C,EAAEC,EAAEyC,KAAKuH,EAAEV,EAAE7G,KAAK+C,EAAE6tB,EAAErpB,EAAE7H,OAAO2N,EAAElP,EAAMssB,EAAEltB,EAAEmC,OAAOrC,EAAEc,EAAqS,OAAnS6B,KAAK6G,EAAE7G,KAAKgE,EAAE,EAAE1G,EAAE,GAAGszB,GAAGL,EAAEtwB,MAAM,2CAA2CoN,EAAE9F,EAAEjK,KAAKiK,EAAEjK,MAAM,EAAEA,EAAE,GAAGszB,GAAGL,EAAEtwB,MAAM,4CAA+DoN,MAAjB9F,EAAEjK,KAAKiK,EAAEjK,MAAM,IAAUizB,EAAEtwB,MAAM,qDAAqD3C,EAAE+P,EAAE9F,EAAE7H,QAAQ6wB,EAAEtwB,MAAM,2BAAkCD,KAAKuwB,GAAG,KAAKkG,EAAG,KAAK5vB,EAAEwG,EAAE9P,EAAEmC,QAAQ,CAClf,GAAL2N,GADwfhQ,EAC5fotB,EAAE5jB,EAAUunB,EAAE7wB,EAAEkP,IAAIlF,EAAEX,SAAStJ,EAAEA,EAAED,GAAGwJ,GAAGA,GAAGxJ,EAAEC,GAAGD,OAAO,KAAKA,KAAKE,EAAEsJ,KAAKU,EAAEjK,KAAK0C,KAAK+C,EAAE8D,EAAEtJ,EAAEyC,KAAK4wB,IAAI/pB,EAAE7G,KAAK+C,EAAE,MAAM,KAAKszB,EAAG,KAAKxvB,EAAEwG,EAAE9P,EAAEmC,QAAQnC,EAAEyC,KAAK4wB,EAAE,CAACxC,EAAE,IAAI,MAAM,QAAQmC,EAAEtwB,MAAM,yBAAyB,GAAGmuB,EAAE7wB,EAAEkP,IAAIlF,EAAEX,SAAStJ,EAAEA,EAAE+P,GAAGxG,GAAGA,GAAGwG,EAAE/P,GAAG+P,OAAO,KAAKA,KAAK9P,EAAEsJ,KAAKU,EAAEjK,KAAK0C,KAAK1C,EAAEA,EAAE0C,KAAK+C,EAAE8D,EAAE7G,KAAKuH,EAAEhK,EAAE,MAAM,KAAK,EAAEyC,KAAKjC,EAAE44B,GAAGC,IAAI,MAAM,KAAK,EAAM,IAAsF53B,EAAI0zB,EAAIlI,EAAwBgI,EAAlHz0B,EAAE24B,GAAE12B,KAAK,GAAG,IAAIjB,EAAE23B,GAAE12B,KAAK,GAAG,EAAE9C,EAAEw5B,GAAE12B,KAAK,GAAG,EAAEtB,EAAE,IAAK0vB,EAAExuB,WAAWkJ,OAAO+tB,EAAGn3B,QAAoB+yB,EAAEt0B,EAAEqJ,EAAErJ,EAAEs1B,EAAEt1B,EAAEy0B,EAAEz0B,EAAE00B,EAAE10B,EAAxH,IAA8H00B,EAAE,EAAEA,EAAE31B,IAAI21B,EAAEn0B,EAAEm4B,EAAGhE,IAAI6D,GAAE12B,KAAK,GAAG,IAAIouB,EACtf,IADyfyE,EAC3f31B,EAAMA,EAAEwB,EAAEgB,OAAOmzB,EAAE31B,IAAI21B,EAAEn0B,EAAEm4B,EAAGhE,IAAI,EAA6C,IAA3C7zB,EAAEwyB,EAAE9yB,GAAG+zB,EAAE,IAAKrE,EAAExuB,WAAWkJ,OAAO/K,EAAEgB,GAAG8zB,EAAE,EAAML,EAAEz0B,EAAEgB,EAAE8zB,EAAEL,GAAG,OAAOhrB,EAAEsvB,GAAG92B,KAAKhB,IAAM,KAAK,GAAG,IAAI4zB,EAAE,EAAE8D,GAAE12B,KAAK,GAAG4yB,KAAKH,EAAEI,KAAKY,EAAE,MAAM,KAAK,GAAG,IAAIb,EAAE,EAAE8D,GAAE12B,KAAK,GAAG4yB,KAAKH,EAAEI,KAAK,EAAEY,EAAE,EAAE,MAAM,KAAK,GAAG,IAAIb,EAAE,GAAG8D,GAAE12B,KAAK,GAAG4yB,KAAKH,EAAEI,KAAK,EAAEY,EAAE,EAAE,MAAM,QAAQA,EAAEhB,EAAEI,KAAKrrB,EAAEkrB,EAAIlB,EAAFpD,EAAIqE,EAAE7rB,SAAS,EAAE7I,GAAM00B,EAAExxB,MAAM,EAAElD,IAAIysB,EAAIgH,EAAFpD,EAAIqE,EAAE7rB,SAAS7I,GAAM00B,EAAExxB,MAAMlD,IAAIiC,KAAKjC,EAAE20B,EAAElI,GAAG,MAAM,QAAQ+F,EAAEtwB,MAAM,kBAAkB8C,KAAK,OAAO/C,KAAK6yB,KAC1a,IACkFkE,EAAEC,EADhFC,EAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAIJ,EAAGzI,EAAE,IAAIoC,YAAYyG,GAAIA,EAAGC,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKC,EAAG/I,EAAE,IAAIoC,YAAY0G,GAAIA,EAAGE,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAGC,EAAGjJ,EAAE,IAAIxuB,WAAWw3B,GAAIA,EAAGE,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,OAAOC,EAAGnJ,EAAE,IAAIoC,YAAY8G,GAAIA,EAAGE,EAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAClf,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIC,EAAGrJ,EAAE,IAAIxuB,WAAW43B,GAAIA,EAAGE,EAAG,IAAKtJ,EAAExuB,WAAWkJ,OAAO,KAAc,IAAJiuB,EAAE,EAAMC,EAAGU,EAAGh4B,OAAOq3B,EAAEC,IAAKD,EAAEW,EAAGX,GAAG,KAAKA,EAAE,EAAE,KAAKA,EAAE,EAAE,KAAKA,EAAE,EAAE,EAAE,IAA6CY,EAAGC,GAA5CjB,GAAGnF,EAAEkG,GAAIG,GAAG,IAAKzJ,EAAExuB,WAAWkJ,OAAO,IAAe,IAAL6uB,EAAG,EAAMC,GAAGC,GAAGn4B,OAAOi4B,EAAGC,KAAKD,EAAGE,GAAGF,GAAI,EAAE,IAAIf,GAAGpF,EAAEqG,IAAI,SAASnB,GAAE3zB,EAAEwE,GAAG,IAAI,IAA2C4P,EAAvC7Z,EAAEyF,EAAEiB,EAAEzG,EAAEwF,EAAE8D,EAAEA,EAAE9D,EAAE8N,MAAM+f,EAAE7tB,EAAEzF,EAAE+P,EAAExG,EAAEnH,OAASnC,EAAEgK,GAAGqpB,GAAGvjB,GAAGkjB,EAAEtwB,MAAM,2BAA2B3C,GAAGuJ,EAAE+pB,MAAMrzB,EAAEA,GAAG,EAAuC,OAArC4Z,EAAE7Z,GAAG,GAAGiK,GAAG,EAAExE,EAAEiB,EAAE1G,IAAIiK,EAAExE,EAAE8D,EAAEtJ,EAAEgK,EAAExE,EAAEzF,EAAEszB,EAASzZ,EAC/a,SAAS2f,GAAG/zB,EAAEwE,GAAG,IAAI,IAAyDlK,EAAEU,EAAvDT,EAAEyF,EAAEiB,EAAEzG,EAAEwF,EAAE8D,EAAEA,EAAE9D,EAAE8N,MAAM+f,EAAE7tB,EAAEzF,EAAE+P,EAAExG,EAAEnH,OAAOyX,EAAE5P,EAAE,GAAGkjB,EAAEljB,EAAE,GAAOhK,EAAEktB,KAAKmG,GAAGvjB,IAAI/P,GAAGuJ,EAAE+pB,MAAMrzB,EAAEA,GAAG,EAA2F,OAAzEQ,GAAhBV,EAAE8Z,EAAE7Z,GAAG,GAAGmtB,GAAG,MAAS,IAAKltB,GAAGgzB,EAAEtwB,MAAM,wBAAwBlC,IAAIgF,EAAEiB,EAAE1G,GAAGS,EAAEgF,EAAE8D,EAAEtJ,EAAEQ,EAAEgF,EAAEzF,EAAEszB,EAAW,MAAFvzB,EAMpB,SAASy6B,GAAG/0B,GAAG/C,KAAK6Q,MAAM9N,EAAE/C,KAAK1C,EAAE,EAAE0C,KAAK2wB,EAAE,GAAG3wB,KAAKixB,GAAE,EAG8L,SAAS8G,GAAGh1B,GAAG,GAAG,iBAAkBA,EAAE,CAAC,IAAkBzF,EAAEC,EAAhBgK,EAAExE,EAAE6T,MAAM,IAAY,IAAJtZ,EAAE,EAAMC,EAAEgK,EAAE7H,OAAOpC,EAAEC,EAAED,IAAIiK,EAAEjK,IAAuB,IAAnBiK,EAAEjK,GAAGyH,WAAW,MAAU,EAAEhC,EAAEwE,EAAE,IAAI,IAAuB4P,EAAnBtQ,EAAE,EAAE+pB,EAAE,EAAEvjB,EAAEtK,EAAErD,OAAS+qB,EAAE,EAAE,EAAEpd,GAAG,CAAiBA,GAAhB8J,EAAE,KAAK9J,EAAE,KAAKA,EAAO,GAAaujB,GAAV/pB,GAAG9D,EAAE0nB,aAAkBtT,GAAGtQ,GAAG,MAAM+pB,GAAG,MAAM,OAAOA,GAAG,GAAG/pB,KAAK,EAAG,SAASmxB,GAAGj1B,EAAEwE,GAAG,IAAIjK,EAAEC,EAAwH,OAAtHyC,KAAK6Q,MAAM9N,EAAE/C,KAAK1C,EAAE,GAAKiK,IAAKA,EAAE,MAAIA,EAAEgc,QAAQvjB,KAAK1C,EAAEiK,EAAEgc,OAAOhc,EAAE0wB,SAASj4B,KAAKo2B,EAAE7uB,EAAE0wB,SAAQ36B,EAAEyF,EAAE/C,KAAK1C,KAAKC,EAAEwF,EAAE/C,KAAK1C,KAAc,GAAFA,GAAM,KAAK46B,GAAGl4B,KAAKuV,OAAO2iB,GAAG,MAAM,QAAQ3H,EAAEtwB,MAAM,mCAAmC,KAAM3C,GAAG,GAAGC,GAAG,IAAIgzB,EAAEtwB,MAAM,yBAAyB3C,GAAG,GAAGC,GAAG,KAAO,GAAFA,GAAMgzB,EAAEtwB,MAAM,gCAAgCD,KAAKq0B,EAAE,IAAI+B,EAAErzB,EAAE,CAACwgB,MAAMvjB,KAAK1C,EAAEg5B,WAAW/uB,EAAE+uB,WAAWC,WAAWhvB,EAAEgvB,WAAWC,OAAOjvB,EAAEivB,SARlmCJ,EAAEv3B,UAAUd,EAAE,SAASgF,EAAEwE,GAAG,IAAIjK,EAAE0C,KAAKuH,EAAEhK,EAAEyC,KAAK+C,EAAE/C,KAAK+zB,EAAEhxB,EAAE,IAAI,IAAmB6tB,EAAEvjB,EAAE8J,EAAEsT,EAArB5jB,EAAEvJ,EAAEoC,OAAO,IAAY,OAAOkxB,EAAEkG,GAAG92B,KAAK+C,KAAK,GAAG,IAAI6tB,EAAErzB,GAAGsJ,IAAI7G,KAAK+C,EAAExF,EAAED,EAAE0C,KAAK4wB,IAAIrzB,EAAEyC,KAAK+C,GAAGzF,EAAEC,KAAKqzB,OAAyI,IAA1HnG,EAAE0M,EAAV9pB,EAAEujB,EAAE,KAAY,EAAEyG,EAAGhqB,KAAKod,GAAGiM,GAAE12B,KAAKq3B,EAAGhqB,KAAKujB,EAAEkG,GAAG92B,KAAKuH,GAAG4P,EAAEogB,EAAG3G,GAAG,EAAE6G,EAAG7G,KAAKzZ,GAAGuf,GAAE12B,KAAKy3B,EAAG7G,KAAKrzB,GAAGsJ,IAAI7G,KAAK+C,EAAExF,EAAED,EAAE0C,KAAK4wB,IAAIrzB,EAAEyC,KAAK+C,GAAQ0nB,KAAKntB,EAAEC,GAAGD,EAAEC,IAAI4Z,GAAG,KAAK,GAAGnX,KAAK6G,GAAG7G,KAAK6G,GAAG,EAAE7G,KAAK1C,IAAI0C,KAAK+C,EAAExF,GAChX64B,EAAEv3B,UAAU01B,EAAE,SAASxxB,EAAEwE,GAAG,IAAIjK,EAAE0C,KAAKuH,EAAEhK,EAAEyC,KAAK+C,EAAE/C,KAAK+zB,EAAEhxB,EAAE,IAAI,IAAe6tB,EAAEvjB,EAAE8J,EAAEsT,EAAjB5jB,EAAEvJ,EAAEoC,OAAe,OAAOkxB,EAAEkG,GAAG92B,KAAK+C,KAAK,GAAG,IAAI6tB,EAAErzB,GAAGsJ,IAAeA,GAAXvJ,EAAE0C,KAAK4wB,KAAQlxB,QAAQpC,EAAEC,KAAKqzB,OAAmI,IAApHnG,EAAE0M,EAAV9pB,EAAEujB,EAAE,KAAY,EAAEyG,EAAGhqB,KAAKod,GAAGiM,GAAE12B,KAAKq3B,EAAGhqB,KAAKujB,EAAEkG,GAAG92B,KAAKuH,GAAG4P,EAAEogB,EAAG3G,GAAG,EAAE6G,EAAG7G,KAAKzZ,GAAGuf,GAAE12B,KAAKy3B,EAAG7G,KAAKrzB,EAAEktB,EAAE5jB,IAAeA,GAAXvJ,EAAE0C,KAAK4wB,KAAQlxB,QAAa+qB,KAAKntB,EAAEC,GAAGD,EAAEC,IAAI4Z,GAAG,KAAK,GAAGnX,KAAK6G,GAAG7G,KAAK6G,GAAG,EAAE7G,KAAK1C,IAAI0C,KAAK+C,EAAExF,GAC/V64B,EAAEv3B,UAAU+xB,EAAE,WAAW,IAA4DtzB,EAAEC,EAA1DwF,EAAE,IAAKqrB,EAAExuB,WAAWkJ,OAAO9I,KAAK+C,EAAE,OAAOwE,EAAEvH,KAAK+C,EAAE,MAAU8D,EAAE7G,KAAKuH,EAAE,GAAG6mB,EAAErrB,EAAE0J,IAAI5F,EAAED,SAAS,MAAM7D,EAAErD,cAAkB,IAAJpC,EAAE,EAAMC,EAAEwF,EAAErD,OAAOpC,EAAEC,IAAID,EAAEyF,EAAEzF,GAAGuJ,EAAEvJ,EAAE,OAAuC,GAAhC0C,KAAKtC,EAAEoH,KAAK/B,GAAG/C,KAAKhB,GAAG+D,EAAErD,OAAU0uB,EAAEvnB,EAAE4F,IAAI5F,EAAED,SAASW,EAAEA,EAAE,aAAa,IAAIjK,EAAE,EAAE,MAAMA,IAAIA,EAAEuJ,EAAEvJ,GAAGuJ,EAAEU,EAAEjK,GAAgB,OAAb0C,KAAK+C,EAAE,MAAa8D,GACpTuvB,EAAEv3B,UAAUuyB,EAAE,SAASruB,GAAG,IAAIwE,EAAmCV,EAAE+pB,EAAnCtzB,EAAE0C,KAAK6Q,MAAMnR,OAAOM,KAAK1C,EAAE,EAAE,EAAQ+P,EAAErN,KAAK6Q,MAAMsG,EAAEnX,KAAKuH,EAA8M,OAA5MxE,IAAI,iBAAkBA,EAAEqrB,IAAI9wB,EAAEyF,EAAEqrB,GAAG,iBAAkBrrB,EAAE6vB,IAAIt1B,GAAGyF,EAAE6vB,IAAI,EAAEt1B,EAA+CuJ,GAAd+pB,GAA3BvjB,EAAE3N,OAAOM,KAAK1C,GAAG0C,KAAK+zB,EAAE,GAAY,EAAP,IAAU,GAAM5c,EAAEzX,OAAOyX,EAAEzX,OAAOkxB,EAAEzZ,EAAEzX,QAAQ,EAAGmH,EAAEsQ,EAAEzX,OAAOpC,EAAE8wB,GAAG7mB,EAAE,IAAI3H,WAAWiH,IAAK4F,IAAI0K,GAAI5P,EAAE4P,EAASnX,KAAKuH,EAAEA,GACrT6uB,EAAEv3B,UAAUg0B,EAAE,WAAW,IAA0Bt1B,EAAoDqzB,EAAEvjB,EAAE8J,EAAEsT,EAAhF1nB,EAAE,EAAEwE,EAAEvH,KAAKuH,EAAEjK,EAAE0C,KAAKtC,EAAImJ,EAAE,IAAKunB,EAAExuB,WAAWkJ,OAAO9I,KAAKhB,GAAGgB,KAAK+C,EAAE,QAAgB,GAAG,IAAIzF,EAAEoC,OAAO,OAAO0uB,EAAEpuB,KAAKuH,EAAEX,SAAS,MAAM5G,KAAK+C,GAAG/C,KAAKuH,EAAEtG,MAAM,MAAMjB,KAAK+C,GAAO,IAAJ6tB,EAAE,EAAMvjB,EAAE/P,EAAEoC,OAAOkxB,EAAEvjB,IAAIujB,EAAc,IAAJzZ,EAAE,EAAMsT,GAAfltB,EAAED,EAAEszB,IAAelxB,OAAOyX,EAAEsT,IAAItT,EAAEtQ,EAAE9D,KAAKxF,EAAE4Z,GAAW,IAARyZ,EAAE,MAAUvjB,EAAErN,KAAK+C,EAAE6tB,EAAEvjB,IAAIujB,EAAE/pB,EAAE9D,KAAKwE,EAAEqpB,GAAa,OAAV5wB,KAAKtC,EAAE,GAAUsC,KAAKwB,OAAOqF,GACjVuvB,EAAEv3B,UAAUi1B,EAAE,WAAW,IAAI/wB,EAAEwE,EAAEvH,KAAK+C,EAAkI,OAAhIqrB,EAAEpuB,KAAKo0B,GAAGrxB,EAAE,IAAInD,WAAW2H,IAAKkF,IAAIzM,KAAKuH,EAAEX,SAAS,EAAEW,IAAKxE,EAAE/C,KAAKuH,EAAEX,SAAS,EAAEW,IAAIvH,KAAKuH,EAAE7H,OAAO6H,IAAIvH,KAAKuH,EAAE7H,OAAO6H,GAAGxE,EAAE/C,KAAKuH,GAAUvH,KAAKwB,OAAOuB,GAC3L+0B,GAAGj5B,UAAU5B,EAAE,WAAW,IAAI,IAAI8F,EAAE/C,KAAK6Q,MAAMnR,OAAOM,KAAK1C,EAAEyF,GAAG,CAAC,IAAqB8D,EAAoBxJ,EAArCkK,EAAE,IAAI+pB,EAAGh0B,EAAEa,EAAEZ,EAAEY,EAAMyyB,EAAEzyB,EAAEkP,EAAElP,EAAEgZ,EAAEhZ,EAAEssB,EAAEtsB,EAAMJ,EAAEI,EAAEY,EAAEiB,KAAK6Q,MAAM3T,EAAE8C,KAAK1C,EAA0G,OAAxGiK,EAAEkrB,EAAE1zB,EAAE7B,KAAKqK,EAAEksB,EAAE10B,EAAE7B,MAAM,KAAKqK,EAAEkrB,GAAG,MAAMlrB,EAAEksB,IAAIlD,EAAEtwB,MAAM,0BAA0BsH,EAAEkrB,EAAE,IAAIlrB,EAAEksB,IAAIlsB,EAAE0jB,EAAElsB,EAAE7B,KAAYqK,EAAE0jB,GAAG,KAAK,EAAE,MAAM,QAAQsF,EAAEtwB,MAAM,+BAA+BsH,EAAE0jB,IAA6I,GAAzI1jB,EAAE7I,EAAEK,EAAE7B,KAAKG,EAAE0B,EAAE7B,KAAK6B,EAAE7B,MAAM,EAAE6B,EAAE7B,MAAM,GAAG6B,EAAE7B,MAAM,GAAGqK,EAAEwvB,EAAE,IAAId,KAAK,IAAI54B,GAAGkK,EAAEypB,GAAGjyB,EAAE7B,KAAKqK,EAAEwpB,GAAGhyB,EAAE7B,KAAK,GAAO,EAAJqK,EAAE7I,KAAO6I,EAAEmvB,EAAE33B,EAAE7B,KAAK6B,EAAE7B,MAAM,EAAEA,GAAGqK,EAAEmvB,GAAM,GAAGnvB,EAAE7I,EAAEk3B,GAAI,CAAM,IAALnL,EAAE,GAAOtT,EAAE,EAAE,GAAG9J,EAAEtO,EAAE7B,OAAOutB,EAAEtT,KACnfzT,OAAO0C,aAAaiH,GAAG9F,EAAE/J,KAAKitB,EAAEpiB,KAAK,IAAI,GAAG,GAAGd,EAAE7I,EAAEo3B,GAAI,CAAM,IAALrL,EAAE,GAAOtT,EAAE,EAAE,GAAG9J,EAAEtO,EAAE7B,OAAOutB,EAAEtT,KAAKzT,OAAO0C,aAAaiH,GAAG9F,EAAEijB,EAAEC,EAAEpiB,KAAK,IAAI,GAAGd,EAAE7I,EAAEs3B,KAAMzuB,EAAE+sB,EAAY,MAAVnD,EAAGpyB,EAAE,EAAE7B,GAASqK,EAAE+sB,KAAKv1B,EAAE7B,KAAK6B,EAAE7B,MAAM,IAAIqzB,EAAEtwB,MAAM,0BAA0B3C,EAAEyB,EAAEA,EAAEW,OAAO,GAAGX,EAAEA,EAAEW,OAAO,IAAI,EAAEX,EAAEA,EAAEW,OAAO,IAAI,GAAGX,EAAEA,EAAEW,OAAO,IAAI,GAAGX,EAAEW,OAAOxC,EAAE,EAAE,EAAE,IAAII,IAAIszB,EAAEtzB,GAAGC,EAAE,IAAI64B,EAAEr3B,EAAE,CAACwkB,MAAMrmB,EAAEo5B,WAAW1F,IAAIrpB,EAAE5F,KAAKkF,EAAEtJ,EAAEN,IAAIC,EAAEK,EAAED,EAAEiK,EAAEurB,EAAE/0B,GAAGgB,EAAE7B,KAAK6B,EAAE7B,MAAM,EAAE6B,EAAE7B,MAAM,GAAG6B,EAAE7B,MAAM,MAAM,EAAEi0B,EAAGtqB,EAAE1I,EAAEA,KAAKJ,GAAGwyB,EAAEtwB,MAAM,8BAA8BkxB,EAAGtqB,EAAE1I,EAAEA,GAAG4D,SAAS,IAAI,QACpfhE,EAAEgE,SAAS,MAAMwF,EAAE8rB,EAAE/1B,GAAGyB,EAAE7B,KAAK6B,EAAE7B,MAAM,EAAE6B,EAAE7B,MAAM,GAAG6B,EAAE7B,MAAM,MAAM,GAAY,WAAT2J,EAAEnH,UAAqBpC,GAAGizB,EAAEtwB,MAAM,wBAAiC,WAAT4G,EAAEnH,QAAmB,MAAMpC,IAAI0C,KAAK2wB,EAAE7rB,KAAKyC,GAAGvH,KAAK1C,EAAEJ,EAAE8C,KAAKixB,EAAEhG,EAAE,IAAajsB,EAAE0zB,EAAUlrB,EAArB9I,EAAEsB,KAAK2wB,EAAMnG,EAAE,EAAEiI,EAAE,EAAQ,IAAJzzB,EAAE,EAAM0zB,EAAEh0B,EAAEgB,OAAOV,EAAE0zB,IAAI1zB,EAAEyzB,GAAG/zB,EAAEM,GAAG2C,KAAKjC,OAAO,GAAG0uB,EAAuB,IAApB5mB,EAAE,IAAI5H,WAAW6yB,GAAOzzB,EAAE,EAAEA,EAAE0zB,IAAI1zB,EAAEwI,EAAEiF,IAAI/N,EAAEM,GAAG2C,KAAK6oB,GAAGA,GAAG9rB,EAAEM,GAAG2C,KAAKjC,WAAW,CAAM,IAAL8H,EAAE,GAAOxI,EAAE,EAAEA,EAAE0zB,IAAI1zB,EAAEwI,EAAExI,GAAGN,EAAEM,GAAG2C,KAAK6F,EAAEsB,MAAMjK,UAAU6I,OAAOrB,MAAM,GAAGmB,GAAG,OAAOA,GAC/awwB,GAAGn5B,UAAU5B,EAAE,WAAW,IAAiBsK,EAAbxE,EAAE/C,KAAK6Q,MAAqK,OAA3JtJ,EAAEvH,KAAKq0B,EAAEp3B,IAAI+C,KAAK1C,EAAE0C,KAAKq0B,EAAE/2B,EAAE0C,KAAKo2B,KAAOrzB,EAAE/C,KAAK1C,MAAM,GAAGyF,EAAE/C,KAAK1C,MAAM,GAAGyF,EAAE/C,KAAK1C,MAAM,EAAEyF,EAAE/C,KAAK1C,QAAQ,IAAMy6B,GAAGxwB,IAAIgpB,EAAEtwB,MAAM,+BAAsCsH,GAAG,IAAI2wB,GAAG,EAAE,SAASC,GAAGp1B,EAAEwE,GAAGvH,KAAK6Q,MAAM9N,EAAE/C,KAAKuH,EAAE,IAAK6mB,EAAExuB,WAAWkJ,OAAO,OAAO9I,KAAKmX,EAAEihB,GAAGj6B,EAAE,IAASZ,EAALD,EAAE,GAAmF,IAAIC,KAA9EgK,IAAKA,EAAE,KAAM,iBAAkBA,EAAEuqB,kBAAgB9xB,KAAKmX,EAAE5P,EAAEuqB,iBAAyBvqB,EAAEjK,EAAEC,GAAGgK,EAAEhK,GAAGD,EAAEy0B,aAAa/xB,KAAKuH,EAAEvH,KAAK6wB,EAAE,IAAIa,EAAG1xB,KAAK6Q,MAAMvT,GAAG,IAAI86B,GAAGjG,EAE5D,SAASkG,GAAGt1B,EAAEwE,GAAG,IAAIjK,EAA8B,OAA5BA,EAAG,IAAI66B,GAAGp1B,GAAI0nB,IAAIljB,IAAIA,EAAE,IAAWA,EAAE0sB,EAAE32B,EAAEg7B,GAAGh7B,GAC5d,SAASi7B,GAAGx1B,EAAEwE,GAAG,IAAIjK,EAAiD,OAA/CyF,EAAE6D,SAAS7D,EAAE9B,MAAM3D,EAAG,IAAI06B,GAAGj1B,GAAI9F,IAAIsK,IAAIA,EAAE,IAAWA,EAAEixB,SAASl7B,EAAEg7B,GAAGh7B,GAA8F,SAASm7B,GAAG11B,EAAEwE,GAAG,IAAIjK,EAAiD,OAA/CyF,EAAE6D,SAAS7D,EAAE9B,MAAM3D,EAAG,IAAIg4B,EAAGvyB,GAAI0nB,IAAIljB,IAAIA,EAAE,IAAWA,EAAE0sB,EAAE32B,EAAEg7B,GAAGh7B,GAA8F,SAASo7B,GAAG31B,EAAEwE,GAAG,IAAIjK,EAAiD,OAA/CyF,EAAE6D,SAAS7D,EAAE9B,MAAM3D,EAAG,IAAIw6B,GAAG/0B,GAAI9F,IAAIsK,IAAIA,EAAE,IAAWA,EAAE0sB,EAAE32B,EAAEg7B,GAAGh7B,GACrc,SAASg7B,GAAGv1B,GAAG,IAA2BzF,EAAEC,EAAzBgK,EAAE,IAAIjI,EAAOyD,EAAErD,QAAgB,IAAJpC,EAAE,EAAMC,EAAEwF,EAAErD,OAAOpC,EAAEC,IAAID,EAAEiK,EAAEjK,GAAGyF,EAAEzF,GAAG,OAAOiK,EAH1F4wB,GAAGt5B,UAAU4rB,EAAE,WAAW,IAAI1nB,EAAEwE,EAAEjK,EAAEC,EAAEsJ,EAAE+pB,EAAEvjB,EAAE8J,EAAE,EAAgB,OAAd9J,EAAErN,KAAKuH,EAAExE,EAAEm1B,IAAa,KAAKA,GAAG3wB,EAAE9B,KAAKkzB,MAAMlzB,KAAK0O,IAAI,OAAO,EAAE,MAAM,QAAQoc,EAAEtwB,MAAM,+BAAiD,OAAlB3C,EAAEiK,GAAG,EAAExE,EAAEsK,EAAE8J,KAAK7Z,EAASyF,GAAG,KAAKm1B,GAAG,OAAOl4B,KAAKmX,GAAG,KAAKihB,GAAGhG,KAAKvrB,EAAE,EAAE,MAAM,KAAKuxB,GAAGtH,EAAEjqB,EAAE,EAAE,MAAM,KAAKuxB,GAAGj6B,EAAE0I,EAAE,EAAE,MAAM,QAAQ0pB,EAAEtwB,MAAM,iCAAiC,MAAM,QAAQswB,EAAEtwB,MAAM,+BACzM,OADwO1C,EAAEsJ,GAAG,EAAE,EAAEwG,EAAE8J,KAAK5Z,EAAE,IAAI,IAAID,EAAEC,GAAG,GAAGqzB,EAAEmH,GAAG/3B,KAAK6Q,OAAO7Q,KAAK6wB,EAAE9tB,EAAEoU,EAAeA,GAAb9J,EAAErN,KAAK6wB,EAAEpG,KAAQ/qB,OAAO0uB,KAAI/gB,EAAE,IAAIzN,WAAWyN,EAAE7L,SAAU9B,QACnfyX,EAAE,IAAInX,KAAKuH,EAAE,IAAI3H,WAAWyN,EAAE3N,OAAO,GAAGM,KAAKuH,EAAEkF,IAAIY,GAAGA,EAAErN,KAAKuH,GAAG8F,EAAEA,EAAEzG,SAAS,EAAEuQ,EAAE,IAAI9J,EAAE8J,KAAKyZ,GAAG,GAAG,IAAIvjB,EAAE8J,KAAKyZ,GAAG,GAAG,IAAIvjB,EAAE8J,KAAKyZ,GAAG,EAAE,IAAIvjB,EAAE8J,KAAO,IAAFyZ,EAAavjB,GAAGtQ,EAAQ67B,QAAwJ,SAAY71B,EAAEwE,EAAEjK,GAAGoQ,EAAQmB,UAAS,WAAW,IAAItR,EAAEsJ,EAAE,IAAIA,EAAEwxB,GAAGt1B,EAAEzF,GAAG,MAAMszB,GAAGrzB,EAAEqzB,EAAErpB,EAAEhK,EAAEsJ,OAAnO9J,EAAQ87B,YAAYR,GAAGt7B,EAAQ+7B,QAA+Q,SAAY/1B,EAAEwE,EAAEjK,GAAGoQ,EAAQmB,UAAS,WAAW,IAAItR,EAAEsJ,EAAE,IAAIA,EAAE0xB,GAAGx1B,EAAEzF,GAAG,MAAMszB,GAAGrzB,EAAEqzB,EAAErpB,EAAEhK,EAAEsJ,OAA1V9J,EAAQg8B,YAAYR,GAAGx7B,EAAQ+Y,KAC1J,SAAY/S,EAAEwE,EAAEjK,GAAGoQ,EAAQmB,UAAS,WAAW,IAAItR,EAAEsJ,EAAE,IAAIA,EAAE4xB,GAAG11B,EAAEzF,GAAG,MAAMszB,GAAGrzB,EAAEqzB,EAAErpB,EAAEhK,EAAEsJ,OAD4E9J,EAAQi8B,SAASP,GAAG17B,EAAQsW,OACV,SAAYtQ,EAAEwE,EAAEjK,GAAGoQ,EAAQmB,UAAS,WAAW,IAAItR,EAAEsJ,EAAE,IAAIA,EAAE6xB,GAAG31B,EAAEzF,GAAG,MAAMszB,GAAGrzB,EAAEqzB,EAAErpB,EAAEhK,EAAEsJ,OADlE9J,EAAQuzB,WAAWoI,KAE3Nt7B,KAAK4C,Q,mDCpD3Ed,EAAQ,IAA1BuN,E,EAAAA,IAAK3O,E,EAAAA,IAAKm7B,E,EAAAA,IAElBj8B,EAAOD,QAAU,CACfmZ,UAAWpY,EACX4Y,WAAYjK,EACZysB,YAAaD,EACbE,WAAY,SAAC/iB,GAAD,OACVtY,EAAIsY,GAAMhB,MAAK,SAAC6V,GAAD,YAAoB,IAANA,Q,ygBCe7BmO,EAtBEC,E,WACF,aAA2D,IAA/CC,EAA+C,uDAAtC,eAAgBC,EAAsB,uDAAV,SAAU,UACvDv5B,KAAKu5B,UAAYA,EACjBv5B,KAAKw5B,KAAO,IAAIpnB,SAAQ,SAACC,EAASC,GAC9B,IAAMmnB,EAAUC,UAAUC,KAAKL,EAAQ,GACvCG,EAAQG,QAAU,kBAAMtnB,EAAOmnB,EAAQ7mB,QACvC6mB,EAAQI,UAAY,kBAAMxnB,EAAQonB,EAAQ/b,SAE1C+b,EAAQK,gBAAkB,WACtBL,EAAQ/b,OAAOqc,kBAAkBR,O,6DAI/B73B,EAAMs4B,GAAU,WAC1B,OAAOh6B,KAAKw5B,KAAKpkB,MAAK,SAAAkiB,GAAE,OAAI,IAAIllB,SAAQ,SAACC,EAASC,GAC9C,IAAM2nB,EAAc3C,EAAG2C,YAAY,EAAKV,UAAW73B,GACnDu4B,EAAYC,WAAa,kBAAM7nB,KAC/B4nB,EAAYE,QAAUF,EAAYL,QAAU,kBAAMtnB,EAAO2nB,EAAYrnB,QACrEonB,EAASC,EAAYG,YAAY,EAAKb,uB,gCAKlD,SAASc,IAGL,OAFKjB,IACDA,EAAQ,IAAIC,GACTD,EAEX,SAASt7B,EAAIU,GAAgC,IACrC87B,EADUlB,EAA2B,uDAAnBiB,IAEtB,OAAOjB,EAAMmB,cAAc,YAAY,SAAAnB,GACnCkB,EAAMlB,EAAMt7B,IAAIU,MACjB4W,MAAK,kBAAMklB,EAAI5c,UAEtB,SAASjR,EAAIjO,EAAKN,GAAkC,IAA3Bk7B,EAA2B,uDAAnBiB,IAC7B,OAAOjB,EAAMmB,cAAc,aAAa,SAAAnB,GACpCA,EAAMoB,IAAIt8B,EAAOM,MAGzB,SAASy6B,EAAIz6B,GAAgC,IAA3B46B,EAA2B,uDAAnBiB,IACtB,OAAOjB,EAAMmB,cAAc,aAAa,SAAAnB,GACpCA,EAAMqB,OAAOj8B,MAGrB,SAASk8B,IAAiC,IAA3BtB,EAA2B,uDAAnBiB,IACnB,OAAOjB,EAAMmB,cAAc,aAAa,SAAAnB,GACpCA,EAAMsB,WAGd,SAASzjB,IAAgC,IAA3BmiB,EAA2B,uDAAnBiB,IACZpjB,EAAO,GACb,OAAOmiB,EAAMmB,cAAc,YAAY,SAAAnB,IAGlCA,EAAMuB,eAAiBvB,EAAMwB,YAAYx9B,KAAKg8B,GAAOS,UAAY,WACzD75B,KAAK0d,SAEVzG,EAAKnS,KAAK9E,KAAK0d,OAAOlf,KACtBwB,KAAK0d,OAAOmd,gBAEjBzlB,MAAK,kBAAM6B", "file": "worker.min.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 4);\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar isArray = require('isarray')\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - <PERSON><PERSON><PERSON> has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined\n  ? global.TYPED_ARRAY_SUPPORT\n  : typedArraySupport()\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nexports.kMaxLength = kMaxLength()\n\nfunction typedArraySupport () {\n  try {\n    var arr = new Uint8Array(1)\n    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}\n    return arr.foo() === 42 && // typed array instances can be augmented\n        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`\n        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`\n  } catch (e) {\n    return false\n  }\n}\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length)\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length)\n    }\n    that.length = length\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype\n  return arr\n}\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n}\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype\n  Buffer.__proto__ = Uint8Array\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) {\n    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97\n    Object.defineProperty(Buffer, Symbol.species, {\n      value: null,\n      configurable: true\n    })\n  }\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n}\n\nfunction allocUnsafe (that, size) {\n  assertSize(size)\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n}\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0\n  that = createBuffer(that, length)\n\n  var actual = that.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual)\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  that = createBuffer(that, length)\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array)\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset)\n  } else {\n    array = new Uint8Array(array, byteOffset, length)\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array\n    that.__proto__ = Buffer.prototype\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array)\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    that = createBuffer(that, len)\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len)\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos)\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string\n  }\n\n  var len = string.length\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')\n    if (this.length > max) str += ' ... '\n  }\n  return '<Buffer ' + str + '>'\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0\n    if (isFinite(length)) {\n      length = length | 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i])\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end)\n    newBuf.__proto__ = Buffer.prototype\n  } else {\n    var sliceLen = end - start\n    newBuf = new Buffer(sliceLen, undefined)\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start]\n    }\n  }\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  byteLength = byteLength | 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 1] = (value >>> 8)\n    this[offset] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n  } else {\n    objectWriteUInt16(this, value, offset, true)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8)\n    this[offset + 1] = (value & 0xff)\n  } else {\n    objectWriteUInt16(this, value, offset, false)\n  }\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff)\n    this[offset + 1] = (value >>> 8)\n    this[offset + 2] = (value >>> 16)\n    this[offset + 3] = (value >>> 24)\n  } else {\n    objectWriteUInt32(this, value, offset, true)\n  }\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset | 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24)\n    this[offset + 1] = (value >>> 16)\n    this[offset + 2] = (value >>> 8)\n    this[offset + 3] = (value & 0xff)\n  } else {\n    objectWriteUInt32(this, value, offset, false)\n  }\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n  var i\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start]\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if (code < 256) {\n        val = code\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString())\n    var len = bytes.length\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "'use strict';\nconst {\n\tmultiByteIndexOf,\n\tstringToBytes,\n\treadUInt64LE,\n\ttarHeaderChecksumMatches,\n\tuint8ArrayUtf8ByteString\n} = require('./util');\nconst supported = require('./supported');\n\nconst xpiZipFilename = stringToBytes('META-INF/mozilla.rsa');\nconst oxmlContentTypes = stringToBytes('[Content_Types].xml');\nconst oxmlRels = stringToBytes('_rels/.rels');\n\nconst fileType = input => {\n\tif (!(input instanceof Uint8Array || input instanceof ArrayBuffer || Buffer.isBuffer(input))) {\n\t\tthrow new TypeError(`Expected the \\`input\\` argument to be of type \\`Uint8Array\\` or \\`<PERSON>uffer\\` or \\`ArrayBuffer\\`, got \\`${typeof input}\\``);\n\t}\n\n\tconst buffer = input instanceof Uint8Array ? input : new Uint8Array(input);\n\n\tif (!(buffer && buffer.length > 1)) {\n\t\treturn;\n\t}\n\n\tconst check = (header, options) => {\n\t\toptions = {\n\t\t\toffset: 0,\n\t\t\t...options\n\t\t};\n\n\t\tfor (let i = 0; i < header.length; i++) {\n\t\t\t// If a bitmask is set\n\t\t\tif (options.mask) {\n\t\t\t\t// If header doesn't equal `buf` with bits masked off\n\t\t\t\tif (header[i] !== (options.mask[i] & buffer[i + options.offset])) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t} else if (header[i] !== buffer[i + options.offset]) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t};\n\n\tconst checkString = (header, options) => check(stringToBytes(header), options);\n\n\tif (check([0xFF, 0xD8, 0xFF])) {\n\t\treturn {\n\t\t\text: 'jpg',\n\t\t\tmime: 'image/jpeg'\n\t\t};\n\t}\n\n\tif (check([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\t// APNG format (https://wiki.mozilla.org/APNG_Specification)\n\t\t// 1. Find the first IDAT (image data) chunk (49 44 41 54)\n\t\t// 2. Check if there is an \"acTL\" chunk before the IDAT one (61 63 54 4C)\n\n\t\t// Offset calculated as follows:\n\t\t// - 8 bytes: PNG signature\n\t\t// - 4 (length) + 4 (chunk type) + 13 (chunk data) + 4 (CRC): IHDR chunk\n\t\tconst startIndex = 33;\n\t\tconst firstImageDataChunkIndex = buffer.findIndex((el, i) => i >= startIndex && buffer[i] === 0x49 && buffer[i + 1] === 0x44 && buffer[i + 2] === 0x41 && buffer[i + 3] === 0x54);\n\t\tconst sliced = buffer.subarray(startIndex, firstImageDataChunkIndex);\n\n\t\tif (sliced.findIndex((el, i) => sliced[i] === 0x61 && sliced[i + 1] === 0x63 && sliced[i + 2] === 0x54 && sliced[i + 3] === 0x4C) >= 0) {\n\t\t\treturn {\n\t\t\t\text: 'apng',\n\t\t\t\tmime: 'image/apng'\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\text: 'png',\n\t\t\tmime: 'image/png'\n\t\t};\n\t}\n\n\tif (check([0x47, 0x49, 0x46])) {\n\t\treturn {\n\t\t\text: 'gif',\n\t\t\tmime: 'image/gif'\n\t\t};\n\t}\n\n\tif (check([0x57, 0x45, 0x42, 0x50], {offset: 8})) {\n\t\treturn {\n\t\t\text: 'webp',\n\t\t\tmime: 'image/webp'\n\t\t};\n\t}\n\n\tif (check([0x46, 0x4C, 0x49, 0x46])) {\n\t\treturn {\n\t\t\text: 'flif',\n\t\t\tmime: 'image/flif'\n\t\t};\n\t}\n\n\t// `cr2`, `orf`, and `arw` need to be before `tif` check\n\tif (\n\t\t(check([0x49, 0x49, 0x2A, 0x0]) || check([0x4D, 0x4D, 0x0, 0x2A])) &&\n\t\tcheck([0x43, 0x52], {offset: 8})\n\t) {\n\t\treturn {\n\t\t\text: 'cr2',\n\t\t\tmime: 'image/x-canon-cr2'\n\t\t};\n\t}\n\n\tif (check([0x49, 0x49, 0x52, 0x4F, 0x08, 0x00, 0x00, 0x00, 0x18])) {\n\t\treturn {\n\t\t\text: 'orf',\n\t\t\tmime: 'image/x-olympus-orf'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x49, 0x49, 0x2A, 0x00]) &&\n\t\t(check([0x10, 0xFB, 0x86, 0x01], {offset: 4}) || check([0x08, 0x00, 0x00, 0x00], {offset: 4})) &&\n\t\t// This pattern differentiates ARW from other TIFF-ish file types:\n\t\tcheck([0x00, 0xFE, 0x00, 0x04, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x03, 0x01], {offset: 9})\n\t) {\n\t\treturn {\n\t\t\text: 'arw',\n\t\t\tmime: 'image/x-sony-arw'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x49, 0x49, 0x2A, 0x00, 0x08, 0x00, 0x00, 0x00]) &&\n\t\t(check([0x2D, 0x00, 0xFE, 0x00], {offset: 8}) ||\n\t\tcheck([0x27, 0x00, 0xFE, 0x00], {offset: 8}))\n\t) {\n\t\treturn {\n\t\t\text: 'dng',\n\t\t\tmime: 'image/x-adobe-dng'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x49, 0x49, 0x2A, 0x00]) &&\n\t\tcheck([0x1C, 0x00, 0xFE, 0x00], {offset: 8})\n\t) {\n\t\treturn {\n\t\t\text: 'nef',\n\t\t\tmime: 'image/x-nikon-nef'\n\t\t};\n\t}\n\n\tif (check([0x49, 0x49, 0x55, 0x00, 0x18, 0x00, 0x00, 0x00, 0x88, 0xE7, 0x74, 0xD8])) {\n\t\treturn {\n\t\t\text: 'rw2',\n\t\t\tmime: 'image/x-panasonic-rw2'\n\t\t};\n\t}\n\n\t// `raf` is here just to keep all the raw image detectors together.\n\tif (checkString('FUJIFILMCCD-RAW')) {\n\t\treturn {\n\t\t\text: 'raf',\n\t\t\tmime: 'image/x-fujifilm-raf'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x49, 0x49, 0x2A, 0x0]) ||\n\t\tcheck([0x4D, 0x4D, 0x0, 0x2A])\n\t) {\n\t\treturn {\n\t\t\text: 'tif',\n\t\t\tmime: 'image/tiff'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4D])) {\n\t\treturn {\n\t\t\text: 'bmp',\n\t\t\tmime: 'image/bmp'\n\t\t};\n\t}\n\n\tif (check([0x49, 0x49, 0xBC])) {\n\t\treturn {\n\t\t\text: 'jxr',\n\t\t\tmime: 'image/vnd.ms-photo'\n\t\t};\n\t}\n\n\tif (check([0x38, 0x42, 0x50, 0x53])) {\n\t\treturn {\n\t\t\text: 'psd',\n\t\t\tmime: 'image/vnd.adobe.photoshop'\n\t\t};\n\t}\n\n\t// Zip-based file formats\n\t// Need to be before the `zip` check\n\tconst zipHeader = [0x50, 0x4B, 0x3, 0x4];\n\tif (check(zipHeader)) {\n\t\tif (\n\t\t\tcheck([0x6D, 0x69, 0x6D, 0x65, 0x74, 0x79, 0x70, 0x65, 0x61, 0x70, 0x70, 0x6C, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x2F, 0x65, 0x70, 0x75, 0x62, 0x2B, 0x7A, 0x69, 0x70], {offset: 30})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'epub',\n\t\t\t\tmime: 'application/epub+zip'\n\t\t\t};\n\t\t}\n\n\t\t// Assumes signed `.xpi` from addons.mozilla.org\n\t\tif (check(xpiZipFilename, {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'xpi',\n\t\t\t\tmime: 'application/x-xpinstall'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.text', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'odt',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.text'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.spreadsheet', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'ods',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.spreadsheet'\n\t\t\t};\n\t\t}\n\n\t\tif (checkString('mimetypeapplication/vnd.oasis.opendocument.presentation', {offset: 30})) {\n\t\t\treturn {\n\t\t\t\text: 'odp',\n\t\t\t\tmime: 'application/vnd.oasis.opendocument.presentation'\n\t\t\t};\n\t\t}\n\n\t\t// The docx, xlsx and pptx file types extend the Office Open XML file format:\n\t\t// https://en.wikipedia.org/wiki/Office_Open_XML_file_formats\n\t\t// We look for:\n\t\t// - one entry named '[Content_Types].xml' or '_rels/.rels',\n\t\t// - one entry indicating specific type of file.\n\t\t// MS Office, OpenOffice and LibreOffice may put the parts in different order, so the check should not rely on it.\n\t\tlet zipHeaderIndex = 0; // The first zip header was already found at index 0\n\t\tlet oxmlFound = false;\n\t\tlet type;\n\n\t\tdo {\n\t\t\tconst offset = zipHeaderIndex + 30;\n\n\t\t\tif (!oxmlFound) {\n\t\t\t\toxmlFound = (check(oxmlContentTypes, {offset}) || check(oxmlRels, {offset}));\n\t\t\t}\n\n\t\t\tif (!type) {\n\t\t\t\tif (checkString('word/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'docx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n\t\t\t\t\t};\n\t\t\t\t} else if (checkString('ppt/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'pptx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'\n\t\t\t\t\t};\n\t\t\t\t} else if (checkString('xl/', {offset})) {\n\t\t\t\t\ttype = {\n\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (oxmlFound && type) {\n\t\t\t\treturn type;\n\t\t\t}\n\n\t\t\tzipHeaderIndex = multiByteIndexOf(buffer, zipHeader, offset);\n\t\t} while (zipHeaderIndex >= 0);\n\n\t\t// No more zip parts available in the buffer, but maybe we are almost certain about the type?\n\t\tif (type) {\n\t\t\treturn type;\n\t\t}\n\t}\n\n\tif (\n\t\tcheck([0x50, 0x4B]) &&\n\t\t(buffer[2] === 0x3 || buffer[2] === 0x5 || buffer[2] === 0x7) &&\n\t\t(buffer[3] === 0x4 || buffer[3] === 0x6 || buffer[3] === 0x8)\n\t) {\n\t\treturn {\n\t\t\text: 'zip',\n\t\t\tmime: 'application/zip'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x30, 0x30, 0x30, 0x30, 0x30, 0x30], {offset: 148, mask: [0xF8, 0xF8, 0xF8, 0xF8, 0xF8, 0xF8]}) && // Valid tar checksum\n\t\ttarHeaderChecksumMatches(buffer)\n\t) {\n\t\treturn {\n\t\t\text: 'tar',\n\t\t\tmime: 'application/x-tar'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x52, 0x61, 0x72, 0x21, 0x1A, 0x7]) &&\n\t\t(buffer[6] === 0x0 || buffer[6] === 0x1)\n\t) {\n\t\treturn {\n\t\t\text: 'rar',\n\t\t\tmime: 'application/x-rar-compressed'\n\t\t};\n\t}\n\n\tif (check([0x1F, 0x8B, 0x8])) {\n\t\treturn {\n\t\t\text: 'gz',\n\t\t\tmime: 'application/gzip'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x5A, 0x68])) {\n\t\treturn {\n\t\t\text: 'bz2',\n\t\t\tmime: 'application/x-bzip2'\n\t\t};\n\t}\n\n\tif (check([0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C])) {\n\t\treturn {\n\t\t\text: '7z',\n\t\t\tmime: 'application/x-7z-compressed'\n\t\t};\n\t}\n\n\tif (check([0x78, 0x01])) {\n\t\treturn {\n\t\t\text: 'dmg',\n\t\t\tmime: 'application/x-apple-diskimage'\n\t\t};\n\t}\n\n\t// `mov` format variants\n\tif (\n\t\tcheck([0x66, 0x72, 0x65, 0x65], {offset: 4}) || // `free`\n\t\tcheck([0x6D, 0x64, 0x61, 0x74], {offset: 4}) || // `mdat` MJPEG\n\t\tcheck([0x6D, 0x6F, 0x6F, 0x76], {offset: 4}) || // `moov`\n\t\tcheck([0x77, 0x69, 0x64, 0x65], {offset: 4}) // `wide`\n\t) {\n\t\treturn {\n\t\t\text: 'mov',\n\t\t\tmime: 'video/quicktime'\n\t\t};\n\t}\n\n\t// File Type Box (https://en.wikipedia.org/wiki/ISO_base_media_file_format)\n\t// It's not required to be first, but it's recommended to be. Almost all ISO base media files start with `ftyp` box.\n\t// `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n\t// Here we check for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n\tif (\n\t\tcheck([0x66, 0x74, 0x79, 0x70], {offset: 4}) && // `ftyp`\n\t\t(buffer[8] & 0x60) !== 0x00 && (buffer[9] & 0x60) !== 0x00 && (buffer[10] & 0x60) !== 0x00 && (buffer[11] & 0x60) !== 0x00 // Brand major\n\t) {\n\t\t// They all can have MIME `video/mp4` except `application/mp4` special-case which is hard to detect.\n\t\t// For some cases, we're specific, everything else falls to `video/mp4` with `mp4` extension.\n\t\tconst brandMajor = uint8ArrayUtf8ByteString(buffer, 8, 12);\n\t\tswitch (brandMajor) {\n\t\t\tcase 'mif1':\n\t\t\t\treturn {ext: 'heic', mime: 'image/heif'};\n\t\t\tcase 'msf1':\n\t\t\t\treturn {ext: 'heic', mime: 'image/heif-sequence'};\n\t\t\tcase 'heic': case 'heix':\n\t\t\t\treturn {ext: 'heic', mime: 'image/heic'};\n\t\t\tcase 'hevc': case 'hevx':\n\t\t\t\treturn {ext: 'heic', mime: 'image/heic-sequence'};\n\t\t\tcase 'qt  ':\n\t\t\t\treturn {ext: 'mov', mime: 'video/quicktime'};\n\t\t\tcase 'M4V ': case 'M4VH': case 'M4VP':\n\t\t\t\treturn {ext: 'm4v', mime: 'video/x-m4v'};\n\t\t\tcase 'M4P ':\n\t\t\t\treturn {ext: 'm4p', mime: 'video/mp4'};\n\t\t\tcase 'M4B ':\n\t\t\t\treturn {ext: 'm4b', mime: 'audio/mp4'};\n\t\t\tcase 'M4A ':\n\t\t\t\treturn {ext: 'm4a', mime: 'audio/x-m4a'};\n\t\t\tcase 'F4V ':\n\t\t\t\treturn {ext: 'f4v', mime: 'video/mp4'};\n\t\t\tcase 'F4P ':\n\t\t\t\treturn {ext: 'f4p', mime: 'video/mp4'};\n\t\t\tcase 'F4A ':\n\t\t\t\treturn {ext: 'f4a', mime: 'audio/mp4'};\n\t\t\tcase 'F4B ':\n\t\t\t\treturn {ext: 'f4b', mime: 'audio/mp4'};\n\t\t\tdefault:\n\t\t\t\tif (brandMajor.startsWith('3g')) {\n\t\t\t\t\tif (brandMajor.startsWith('3g2')) {\n\t\t\t\t\t\treturn {ext: '3g2', mime: 'video/3gpp2'};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {ext: '3gp', mime: 'video/3gpp'};\n\t\t\t\t}\n\n\t\t\t\treturn {ext: 'mp4', mime: 'video/mp4'};\n\t\t}\n\t}\n\n\tif (check([0x4D, 0x54, 0x68, 0x64])) {\n\t\treturn {\n\t\t\text: 'mid',\n\t\t\tmime: 'audio/midi'\n\t\t};\n\t}\n\n\t// https://github.com/threatstack/libmagic/blob/master/magic/Magdir/matroska\n\tif (check([0x1A, 0x45, 0xDF, 0xA3])) {\n\t\tconst sliced = buffer.subarray(4, 4 + 4096);\n\t\tconst idPos = sliced.findIndex((el, i, arr) => arr[i] === 0x42 && arr[i + 1] === 0x82);\n\n\t\tif (idPos !== -1) {\n\t\t\tconst docTypePos = idPos + 3;\n\t\t\tconst findDocType = type => [...type].every((c, i) => sliced[docTypePos + i] === c.charCodeAt(0));\n\n\t\t\tif (findDocType('matroska')) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mkv',\n\t\t\t\t\tmime: 'video/x-matroska'\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (findDocType('webm')) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'webm',\n\t\t\t\t\tmime: 'video/webm'\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t// RIFF file format which might be AVI, WAV, QCP, etc\n\tif (check([0x52, 0x49, 0x46, 0x46])) {\n\t\tif (check([0x41, 0x56, 0x49], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'avi',\n\t\t\t\tmime: 'video/vnd.avi'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x57, 0x41, 0x56, 0x45], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'wav',\n\t\t\t\tmime: 'audio/vnd.wave'\n\t\t\t};\n\t\t}\n\n\t\t// QLCM, QCP file\n\t\tif (check([0x51, 0x4C, 0x43, 0x4D], {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'qcp',\n\t\t\t\tmime: 'audio/qcelp'\n\t\t\t};\n\t\t}\n\t}\n\n\t// ASF_Header_Object first 80 bytes\n\tif (check([0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11, 0xA6, 0xD9])) {\n\t\t// Search for header should be in first 1KB of file.\n\n\t\tlet offset = 30;\n\t\tdo {\n\t\t\tconst objectSize = readUInt64LE(buffer, offset + 16);\n\t\t\tif (check([0x91, 0x07, 0xDC, 0xB7, 0xB7, 0xA9, 0xCF, 0x11, 0x8E, 0xE6, 0x00, 0xC0, 0x0C, 0x20, 0x53, 0x65], {offset})) {\n\t\t\t\t// Sync on Stream-Properties-Object (B7DC0791-A9B7-11CF-8EE6-00C00C205365)\n\t\t\t\tif (check([0x40, 0x9E, 0x69, 0xF8, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B], {offset: offset + 24})) {\n\t\t\t\t\t// Found audio:\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'wma',\n\t\t\t\t\t\tmime: 'audio/x-ms-wma'\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (check([0xC0, 0xEF, 0x19, 0xBC, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B], {offset: offset + 24})) {\n\t\t\t\t\t// Found video:\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'wmv',\n\t\t\t\t\t\tmime: 'video/x-ms-asf'\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\toffset += objectSize;\n\t\t} while (offset + 24 <= buffer.length);\n\n\t\t// Default to ASF generic extension\n\t\treturn {\n\t\t\text: 'asf',\n\t\t\tmime: 'application/vnd.ms-asf'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x0, 0x0, 0x1, 0xBA]) ||\n\t\tcheck([0x0, 0x0, 0x1, 0xB3])\n\t) {\n\t\treturn {\n\t\t\text: 'mpg',\n\t\t\tmime: 'video/mpeg'\n\t\t};\n\t}\n\n\t// Check for MPEG header at different starting offsets\n\tfor (let start = 0; start < 2 && start < (buffer.length - 16); start++) {\n\t\tif (\n\t\t\tcheck([0x49, 0x44, 0x33], {offset: start}) || // ID3 header\n\t\t\tcheck([0xFF, 0xE2], {offset: start, mask: [0xFF, 0xE6]}) // MPEG 1 or 2 Layer 3 header\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp3',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xE4], {offset: start, mask: [0xFF, 0xE6]}) // MPEG 1 or 2 Layer 2 header\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp2',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xF8], {offset: start, mask: [0xFF, 0xFC]}) // MPEG 2 layer 0 using ADTS\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp2',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tcheck([0xFF, 0xF0], {offset: start, mask: [0xFF, 0xFC]}) // MPEG 4 layer 0 using ADTS\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mp4',\n\t\t\t\tmime: 'audio/mpeg'\n\t\t\t};\n\t\t}\n\t}\n\n\t// Needs to be before `ogg` check\n\tif (check([0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64], {offset: 28})) {\n\t\treturn {\n\t\t\text: 'opus',\n\t\t\tmime: 'audio/opus'\n\t\t};\n\t}\n\n\t// If 'OggS' in first  bytes, then OGG container\n\tif (check([0x4F, 0x67, 0x67, 0x53])) {\n\t\t// This is a OGG container\n\n\t\t// If ' theora' in header.\n\t\tif (check([0x80, 0x74, 0x68, 0x65, 0x6F, 0x72, 0x61], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogv',\n\t\t\t\tmime: 'video/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// If '\\x01video' in header.\n\t\tif (check([0x01, 0x76, 0x69, 0x64, 0x65, 0x6F, 0x00], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogm',\n\t\t\t\tmime: 'video/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// If ' FLAC' in header  https://xiph.org/flac/faq.html\n\t\tif (check([0x7F, 0x46, 0x4C, 0x41, 0x43], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'oga',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// 'Speex  ' in header https://en.wikipedia.org/wiki/Speex\n\t\tif (check([0x53, 0x70, 0x65, 0x65, 0x78, 0x20, 0x20], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'spx',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// If '\\x01vorbis' in header\n\t\tif (check([0x01, 0x76, 0x6F, 0x72, 0x62, 0x69, 0x73], {offset: 28})) {\n\t\t\treturn {\n\t\t\t\text: 'ogg',\n\t\t\t\tmime: 'audio/ogg'\n\t\t\t};\n\t\t}\n\n\t\t// Default OGG container https://www.iana.org/assignments/media-types/application/ogg\n\t\treturn {\n\t\t\text: 'ogx',\n\t\t\tmime: 'application/ogg'\n\t\t};\n\t}\n\n\tif (check([0x66, 0x4C, 0x61, 0x43])) {\n\t\treturn {\n\t\t\text: 'flac',\n\t\t\tmime: 'audio/x-flac'\n\t\t};\n\t}\n\n\tif (check([0x4D, 0x41, 0x43, 0x20])) { // 'MAC '\n\t\treturn {\n\t\t\text: 'ape',\n\t\t\tmime: 'audio/ape'\n\t\t};\n\t}\n\n\tif (check([0x77, 0x76, 0x70, 0x6B])) { // 'wvpk'\n\t\treturn {\n\t\t\text: 'wv',\n\t\t\tmime: 'audio/wavpack'\n\t\t};\n\t}\n\n\tif (check([0x23, 0x21, 0x41, 0x4D, 0x52, 0x0A])) {\n\t\treturn {\n\t\t\text: 'amr',\n\t\t\tmime: 'audio/amr'\n\t\t};\n\t}\n\n\tif (check([0x25, 0x50, 0x44, 0x46])) {\n\t\treturn {\n\t\t\text: 'pdf',\n\t\t\tmime: 'application/pdf'\n\t\t};\n\t}\n\n\tif (check([0x4D, 0x5A])) {\n\t\treturn {\n\t\t\text: 'exe',\n\t\t\tmime: 'application/x-msdownload'\n\t\t};\n\t}\n\n\tif (\n\t\t(buffer[0] === 0x43 || buffer[0] === 0x46) &&\n\t\tcheck([0x57, 0x53], {offset: 1})\n\t) {\n\t\treturn {\n\t\t\text: 'swf',\n\t\t\tmime: 'application/x-shockwave-flash'\n\t\t};\n\t}\n\n\tif (check([0x7B, 0x5C, 0x72, 0x74, 0x66])) {\n\t\treturn {\n\t\t\text: 'rtf',\n\t\t\tmime: 'application/rtf'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x61, 0x73, 0x6D])) {\n\t\treturn {\n\t\t\text: 'wasm',\n\t\t\tmime: 'application/wasm'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x77, 0x4F, 0x46, 0x46]) &&\n\t\t(\n\t\t\tcheck([0x00, 0x01, 0x00, 0x00], {offset: 4}) ||\n\t\t\tcheck([0x4F, 0x54, 0x54, 0x4F], {offset: 4})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'woff',\n\t\t\tmime: 'font/woff'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x77, 0x4F, 0x46, 0x32]) &&\n\t\t(\n\t\t\tcheck([0x00, 0x01, 0x00, 0x00], {offset: 4}) ||\n\t\t\tcheck([0x4F, 0x54, 0x54, 0x4F], {offset: 4})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'woff2',\n\t\t\tmime: 'font/woff2'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x4C, 0x50], {offset: 34}) &&\n\t\t(\n\t\t\tcheck([0x00, 0x00, 0x01], {offset: 8}) ||\n\t\t\tcheck([0x01, 0x00, 0x02], {offset: 8}) ||\n\t\t\tcheck([0x02, 0x00, 0x02], {offset: 8})\n\t\t)\n\t) {\n\t\treturn {\n\t\t\text: 'eot',\n\t\t\tmime: 'application/vnd.ms-fontobject'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x01, 0x00, 0x00, 0x00])) {\n\t\treturn {\n\t\t\text: 'ttf',\n\t\t\tmime: 'font/ttf'\n\t\t};\n\t}\n\n\tif (check([0x4F, 0x54, 0x54, 0x4F, 0x00])) {\n\t\treturn {\n\t\t\text: 'otf',\n\t\t\tmime: 'font/otf'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x01, 0x00])) {\n\t\treturn {\n\t\t\text: 'ico',\n\t\t\tmime: 'image/x-icon'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x02, 0x00])) {\n\t\treturn {\n\t\t\text: 'cur',\n\t\t\tmime: 'image/x-icon'\n\t\t};\n\t}\n\n\tif (check([0x46, 0x4C, 0x56, 0x01])) {\n\t\treturn {\n\t\t\text: 'flv',\n\t\t\tmime: 'video/x-flv'\n\t\t};\n\t}\n\n\tif (check([0x25, 0x21])) {\n\t\treturn {\n\t\t\text: 'ps',\n\t\t\tmime: 'application/postscript'\n\t\t};\n\t}\n\n\tif (check([0xFD, 0x37, 0x7A, 0x58, 0x5A, 0x00])) {\n\t\treturn {\n\t\t\text: 'xz',\n\t\t\tmime: 'application/x-xz'\n\t\t};\n\t}\n\n\tif (check([0x53, 0x51, 0x4C, 0x69])) {\n\t\treturn {\n\t\t\text: 'sqlite',\n\t\t\tmime: 'application/x-sqlite3'\n\t\t};\n\t}\n\n\tif (check([0x4E, 0x45, 0x53, 0x1A])) {\n\t\treturn {\n\t\t\text: 'nes',\n\t\t\tmime: 'application/x-nintendo-nes-rom'\n\t\t};\n\t}\n\n\tif (check([0x43, 0x72, 0x32, 0x34])) {\n\t\treturn {\n\t\t\text: 'crx',\n\t\t\tmime: 'application/x-google-chrome-extension'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x4D, 0x53, 0x43, 0x46]) ||\n\t\tcheck([0x49, 0x53, 0x63, 0x28])\n\t) {\n\t\treturn {\n\t\t\text: 'cab',\n\t\t\tmime: 'application/vnd.ms-cab-compressed'\n\t\t};\n\t}\n\n\t// Needs to be before `ar` check\n\tif (check([0x21, 0x3C, 0x61, 0x72, 0x63, 0x68, 0x3E, 0x0A, 0x64, 0x65, 0x62, 0x69, 0x61, 0x6E, 0x2D, 0x62, 0x69, 0x6E, 0x61, 0x72, 0x79])) {\n\t\treturn {\n\t\t\text: 'deb',\n\t\t\tmime: 'application/x-deb'\n\t\t};\n\t}\n\n\tif (check([0x21, 0x3C, 0x61, 0x72, 0x63, 0x68, 0x3E])) {\n\t\treturn {\n\t\t\text: 'ar',\n\t\t\tmime: 'application/x-unix-archive'\n\t\t};\n\t}\n\n\tif (check([0xED, 0xAB, 0xEE, 0xDB])) {\n\t\treturn {\n\t\t\text: 'rpm',\n\t\t\tmime: 'application/x-rpm'\n\t\t};\n\t}\n\n\tif (\n\t\tcheck([0x1F, 0xA0]) ||\n\t\tcheck([0x1F, 0x9D])\n\t) {\n\t\treturn {\n\t\t\text: 'Z',\n\t\t\tmime: 'application/x-compress'\n\t\t};\n\t}\n\n\tif (check([0x4C, 0x5A, 0x49, 0x50])) {\n\t\treturn {\n\t\t\text: 'lz',\n\t\t\tmime: 'application/x-lzip'\n\t\t};\n\t}\n\n\tif (check([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3E])) {\n\t\treturn {\n\t\t\text: 'msi',\n\t\t\tmime: 'application/x-msi'\n\t\t};\n\t}\n\n\tif (check([0x06, 0x0E, 0x2B, 0x34, 0x02, 0x05, 0x01, 0x01, 0x0D, 0x01, 0x02, 0x01, 0x01, 0x02])) {\n\t\treturn {\n\t\t\text: 'mxf',\n\t\t\tmime: 'application/mxf'\n\t\t};\n\t}\n\n\tif (check([0x47], {offset: 4}) && (check([0x47], {offset: 192}) || check([0x47], {offset: 196}))) {\n\t\treturn {\n\t\t\text: 'mts',\n\t\t\tmime: 'video/mp2t'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4C, 0x45, 0x4E, 0x44, 0x45, 0x52])) {\n\t\treturn {\n\t\t\text: 'blend',\n\t\t\tmime: 'application/x-blender'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x50, 0x47, 0xFB])) {\n\t\treturn {\n\t\t\text: 'bpg',\n\t\t\tmime: 'image/bpg'\n\t\t};\n\t}\n\n\tif (check([0x00, 0x00, 0x00, 0x0C, 0x6A, 0x50, 0x20, 0x20, 0x0D, 0x0A, 0x87, 0x0A])) {\n\t\t// JPEG-2000 family\n\n\t\tif (check([0x6A, 0x70, 0x32, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jp2',\n\t\t\t\tmime: 'image/jp2'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6A, 0x70, 0x78, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jpx',\n\t\t\t\tmime: 'image/jpx'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6A, 0x70, 0x6D, 0x20], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'jpm',\n\t\t\t\tmime: 'image/jpm'\n\t\t\t};\n\t\t}\n\n\t\tif (check([0x6D, 0x6A, 0x70, 0x32], {offset: 20})) {\n\t\t\treturn {\n\t\t\t\text: 'mj2',\n\t\t\t\tmime: 'image/mj2'\n\t\t\t};\n\t\t}\n\t}\n\n\tif (check([0x46, 0x4F, 0x52, 0x4D])) {\n\t\treturn {\n\t\t\text: 'aif',\n\t\t\tmime: 'audio/aiff'\n\t\t};\n\t}\n\n\tif (checkString('<?xml ')) {\n\t\treturn {\n\t\t\text: 'xml',\n\t\t\tmime: 'application/xml'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x4F, 0x4F, 0x4B, 0x4D, 0x4F, 0x42, 0x49], {offset: 60})) {\n\t\treturn {\n\t\t\text: 'mobi',\n\t\t\tmime: 'application/x-mobipocket-ebook'\n\t\t};\n\t}\n\n\tif (check([0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\treturn {\n\t\t\text: 'ktx',\n\t\t\tmime: 'image/ktx'\n\t\t};\n\t}\n\n\tif (check([0x44, 0x49, 0x43, 0x4D], {offset: 128})) {\n\t\treturn {\n\t\t\text: 'dcm',\n\t\t\tmime: 'application/dicom'\n\t\t};\n\t}\n\n\t// Musepack, SV7\n\tif (check([0x4D, 0x50, 0x2B])) {\n\t\treturn {\n\t\t\text: 'mpc',\n\t\t\tmime: 'audio/x-musepack'\n\t\t};\n\t}\n\n\t// Musepack, SV8\n\tif (check([0x4D, 0x50, 0x43, 0x4B])) {\n\t\treturn {\n\t\t\text: 'mpc',\n\t\t\tmime: 'audio/x-musepack'\n\t\t};\n\t}\n\n\tif (check([0x42, 0x45, 0x47, 0x49, 0x4E, 0x3A])) {\n\t\treturn {\n\t\t\text: 'ics',\n\t\t\tmime: 'text/calendar'\n\t\t};\n\t}\n\n\tif (check([0x67, 0x6C, 0x54, 0x46, 0x02, 0x00, 0x00, 0x00])) {\n\t\treturn {\n\t\t\text: 'glb',\n\t\t\tmime: 'model/gltf-binary'\n\t\t};\n\t}\n\n\tif (check([0xD4, 0xC3, 0xB2, 0xA1]) || check([0xA1, 0xB2, 0xC3, 0xD4])) {\n\t\treturn {\n\t\t\text: 'pcap',\n\t\t\tmime: 'application/vnd.tcpdump.pcap'\n\t\t};\n\t}\n\n\t// Sony DSD Stream File (DSF)\n\tif (check([0x44, 0x53, 0x44, 0x20])) {\n\t\treturn {\n\t\t\text: 'dsf',\n\t\t\tmime: 'audio/x-dsf' // Non-standard\n\t\t};\n\t}\n\n\tif (check([0x4C, 0x00, 0x00, 0x00, 0x01, 0x14, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46])) {\n\t\treturn {\n\t\t\text: 'lnk',\n\t\t\tmime: 'application/x.ms.shortcut' // Invented by us\n\t\t};\n\t}\n\n\tif (check([0x62, 0x6F, 0x6F, 0x6B, 0x00, 0x00, 0x00, 0x00, 0x6D, 0x61, 0x72, 0x6B, 0x00, 0x00, 0x00, 0x00])) {\n\t\treturn {\n\t\t\text: 'alias',\n\t\t\tmime: 'application/x.apple.alias' // Invented by us\n\t\t};\n\t}\n\n\tif (checkString('Creative Voice File')) {\n\t\treturn {\n\t\t\text: 'voc',\n\t\t\tmime: 'audio/x-voc'\n\t\t};\n\t}\n\n\tif (check([0x0B, 0x77])) {\n\t\treturn {\n\t\t\text: 'ac3',\n\t\t\tmime: 'audio/vnd.dolby.dd-raw'\n\t\t};\n\t}\n\n\tif ((check([0x7E, 0x10, 0x04]) || check([0x7E, 0x18, 0x04])) && check([0x30, 0x4D, 0x49, 0x45], {offset: 4})) {\n\t\treturn {\n\t\t\text: 'mie',\n\t\t\tmime: 'application/x-mie'\n\t\t};\n\t}\n\n\tif (check([0x41, 0x52, 0x52, 0x4F, 0x57, 0x31, 0x00, 0x00])) {\n\t\treturn {\n\t\t\text: 'arrow',\n\t\t\tmime: 'application/x-apache-arrow'\n\t\t};\n\t}\n\n\tif (check([0x27, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], {offset: 2})) {\n\t\treturn {\n\t\t\text: 'shp',\n\t\t\tmime: 'application/x-esri-shape'\n\t\t};\n\t}\n};\n\nmodule.exports = fileType;\n\nObject.defineProperty(fileType, 'minimumBytes', {value: 4100});\n\nfileType.stream = readableStream => new Promise((resolve, reject) => {\n\t// Using `eval` to work around issues when bundling with Webpack\n\tconst stream = eval('require')('stream'); // eslint-disable-line no-eval\n\n\treadableStream.on('error', reject);\n\treadableStream.once('readable', () => {\n\t\tconst pass = new stream.PassThrough();\n\t\tconst chunk = readableStream.read(module.exports.minimumBytes) || readableStream.read();\n\t\ttry {\n\t\t\tpass.fileType = fileType(chunk);\n\t\t} catch (error) {\n\t\t\treject(error);\n\t\t}\n\n\t\treadableStream.unshift(chunk);\n\n\t\tif (stream.pipeline) {\n\t\t\tresolve(stream.pipeline(readableStream, pass, () => {}));\n\t\t} else {\n\t\t\tresolve(readableStream.pipe(pass));\n\t\t}\n\t});\n});\n\nObject.defineProperty(fileType, 'extensions', {\n\tget() {\n\t\treturn new Set(supported.extensions);\n\t}\n});\n\nObject.defineProperty(fileType, 'mimeTypes', {\n\tget() {\n\t\treturn new Set(supported.mimeTypes);\n\t}\n});\n", "/**\n *\n * Browser worker scripts\n *\n * @fileoverview Browser worker implementation\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\n\nconst worker = require('..');\nconst getCore = require('./getCore');\nconst gunzip = require('./gunzip');\nconst cache = require('./cache');\n\n/*\n * register message handler\n */\nglobal.addEventListener('message', ({ data }) => {\n  worker.dispatchHandlers(data, (obj) => postMessage(obj));\n});\n\n/*\n * getCore is a sync function to load and return\n * TesseractCore.\n */\nworker.setAdapter({\n  getCore,\n  gunzip,\n  fetch: () => {},\n  ...cache,\n});\n", "/**\n *\n * Worker script for browser and node\n *\n * @fileoverview Worker script for browser and node\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst fileType = require('file-type');\nconst isURL = require('is-url');\nconst dump = require('./utils/dump');\nconst isWebWorker = require('../utils/getEnvironment')('type') === 'webworker';\nconst setImage = require('./utils/setImage');\nconst defaultParams = require('./constants/defaultParams');\nconst { log, setLogging } = require('../utils/log');\n\n/*\n * Tesseract Module returned by TesseractCore.\n */\nlet TessModule;\n/*\n * TessearctBaseAPI instance\n */\nlet api = null;\nlet latestJob;\nlet adapter = {};\nlet params = defaultParams;\n\nconst load = ({ workerId, jobId, payload: { options: { corePath, logging } } }, res) => {\n  setLogging(logging);\n  if (!TessModule) {\n    const Core = adapter.getCore(corePath, res);\n\n    res.progress({ workerId, status: 'initializing tesseract', progress: 0 });\n\n    Core({\n      TesseractProgress(percent) {\n        latestJob.progress({\n          workerId,\n          jobId,\n          status: 'recognizing text',\n          progress: Math.max(0, (percent - 30) / 70),\n        });\n      },\n    }).then((tessModule) => {\n      TessModule = tessModule;\n      res.progress({ workerId, status: 'initialized tesseract', progress: 1 });\n      res.resolve({ loaded: true });\n    });\n  } else {\n    res.resolve({ loaded: true });\n  }\n};\n\nconst FS = ({ workerId, payload: { method, args } }, res) => {\n  log(`[${workerId}]: FS.${method} with args ${args}`);\n  res.resolve(TessModule.FS[method](...args));\n};\n\nconst loadLanguage = async ({\n  workerId,\n  payload: {\n    langs,\n    options: {\n      langPath,\n      dataPath,\n      cachePath,\n      cacheMethod,\n      gzip = true,\n    },\n  },\n},\n  res) => {\n  const loadAndGunzipFile = async (_lang) => {\n    const lang = typeof _lang === 'string' ? _lang : _lang.code;\n    const readCache = ['refresh', 'none'].includes(cacheMethod)\n      ? () => Promise.resolve()\n      : adapter.readCache;\n    let data = null;\n\n    try {\n      const _data = await readCache(`${cachePath || '.'}/${lang}.traineddata`);\n      if (typeof _data !== 'undefined') {\n        log(`[${workerId}]: Load ${lang}.traineddata from cache`);\n        res.progress({ workerId, status: 'loading language traineddata (from cache)', progress: 0.5 });\n        data = _data;\n      } else {\n        throw Error('Not found in cache');\n      }\n    } catch (e) {\n      log(`[${workerId}]: Load ${lang}.traineddata from ${langPath}`);\n      if (typeof _lang === 'string') {\n        let path = null;\n\n        if (isURL(langPath) || langPath.startsWith('moz-extension://') || langPath.startsWith('chrome-extension://') || langPath.startsWith('file://')) { /** When langPath is an URL */\n          path = langPath;\n        }\n\n        if (path !== null) {\n          const resp = await (isWebWorker ? fetch : adapter.fetch)(`${path}/${lang}.traineddata${gzip ? '.gz' : ''}`);\n          data = await resp.arrayBuffer();\n        } else {\n          data = await adapter.readCache(`${langPath}/${lang}.traineddata${gzip ? '.gz' : ''}`);\n        }\n      } else {\n        data = _lang.data; // eslint-disable-line\n      }\n    }\n\n    data = new Uint8Array(data);\n\n    const type = fileType(data);\n    if (typeof type !== 'undefined' && type.mime === 'application/gzip') {\n      data = adapter.gunzip(data);\n    }\n\n    if (TessModule) {\n      if (dataPath) {\n        try {\n          TessModule.FS.mkdir(dataPath);\n        } catch (err) {\n          res.reject(err.toString());\n        }\n      }\n      TessModule.FS.writeFile(`${dataPath || '.'}/${lang}.traineddata`, data);\n    }\n\n    if (['write', 'refresh', undefined].includes(cacheMethod)) {\n      await adapter.writeCache(`${cachePath || '.'}/${lang}.traineddata`, data);\n    }\n\n    return Promise.resolve(data);\n  };\n\n  res.progress({ workerId, status: 'loading language traineddata', progress: 0 });\n  try {\n    await Promise.all((typeof langs === 'string' ? langs.split('+') : langs).map(loadAndGunzipFile));\n    res.progress({ workerId, status: 'loaded language traineddata', progress: 1 });\n    res.resolve(langs);\n  } catch (err) {\n    if (isWebWorker && err instanceof DOMException) {\n      /*\n       * For some reason google chrome throw DOMException in loadLang,\n       * while other browser is OK, for now we ignore this exception\n       * and hopefully to find the root cause one day.\n       */\n    } else {\n      res.reject(err.toString());\n    }\n  }\n};\n\nconst setParameters = ({ payload: { params: _params } }, res) => {\n  Object.keys(_params)\n    .filter((k) => !k.startsWith('tessjs_'))\n    .forEach((key) => {\n      api.SetVariable(key, _params[key]);\n    });\n  params = { ...params, ..._params };\n\n  if (typeof res !== 'undefined') {\n    res.resolve(params);\n  }\n};\n\nconst initialize = ({\n  workerId,\n  payload: { langs: _langs, oem },\n}, res) => {\n  const langs = (typeof _langs === 'string')\n    ? _langs\n    : _langs.map((l) => ((typeof l === 'string') ? l : l.data)).join('+');\n\n  try {\n    res.progress({\n      workerId, status: 'initializing api', progress: 0,\n    });\n    if (api !== null) {\n      api.End();\n    }\n    api = new TessModule.TessBaseAPI();\n    api.Init(null, langs, oem);\n    params = defaultParams;\n    setParameters({ payload: { params } });\n    res.progress({\n      workerId, status: 'initialized api', progress: 1,\n    });\n    res.resolve();\n  } catch (err) {\n    res.reject(err.toString());\n  }\n};\n\nconst recognize = ({ payload: { image, options: { rectangle: rec } } }, res) => {\n  try {\n    const ptr = setImage(TessModule, api, image);\n    if (typeof rec === 'object') {\n      api.SetRectangle(rec.left, rec.top, rec.width, rec.height);\n    }\n    api.Recognize(null);\n    res.resolve(dump(TessModule, api, params));\n    TessModule._free(ptr);\n  } catch (err) {\n    res.reject(err.toString());\n  }\n};\n\nconst getPDF = ({ payload: { title, textonly } }, res) => {\n  const pdfRenderer = new TessModule.TessPDFRenderer('tesseract-ocr', '/', textonly);\n  pdfRenderer.BeginDocument(title);\n  pdfRenderer.AddImage(api);\n  pdfRenderer.EndDocument();\n  TessModule._free(pdfRenderer);\n\n  res.resolve(TessModule.FS.readFile('/tesseract-ocr.pdf'));\n};\n\nconst detect = ({ payload: { image } }, res) => {\n  try {\n    const ptr = setImage(TessModule, api, image);\n    const results = new TessModule.OSResults();\n\n    if (!api.DetectOS(results)) {\n      api.End();\n      TessModule._free(ptr);\n      res.reject('Failed to detect OS');\n    } else {\n      const best = results.best_result;\n      const oid = best.orientation_id;\n      const sid = best.script_id;\n\n      TessModule._free(ptr);\n\n      res.resolve({\n        tesseract_script_id: sid,\n        script: results.unicharset.get_script_from_script_id(sid),\n        script_confidence: best.sconfidence,\n        orientation_degrees: [0, 270, 180, 90][oid],\n        orientation_confidence: best.oconfidence,\n      });\n    }\n  } catch (err) {\n    res.reject(err.toString());\n  }\n};\n\nconst terminate = (_, res) => {\n  try {\n    if (api !== null) {\n      api.End();\n    }\n    res.resolve({ terminated: true });\n  } catch (err) {\n    res.reject(err.toString());\n  }\n};\n\n/**\n * dispatchHandlers\n *\n * @name dispatchHandlers\n * @function worker data handler\n * @access public\n * @param {object} data\n * @param {string} data.jobId - unique job id\n * @param {string} data.action - action of the job, only recognize and detect for now\n * @param {object} data.payload - data for the job\n * @param {function} send - trigger job to work\n */\nexports.dispatchHandlers = (packet, send) => {\n  const res = (status, data) => {\n    send({\n      ...packet,\n      status,\n      data,\n    });\n  };\n  res.resolve = res.bind(this, 'resolve');\n  res.reject = res.bind(this, 'reject');\n  res.progress = res.bind(this, 'progress');\n\n  latestJob = res;\n\n  try {\n    ({\n      load,\n      FS,\n      loadLanguage,\n      initialize,\n      setParameters,\n      recognize,\n      getPDF,\n      detect,\n      terminate,\n    })[packet.action](packet, res);\n  } catch (err) {\n    /** Prepare exception to travel through postMessage */\n    res.reject(err.toString());\n  }\n};\n\n/**\n * setAdapter\n *\n * @name setAdapter\n * @function\n * @access public\n * @param {object} adapter - implementation of the worker, different in browser and node environment\n */\nexports.setAdapter = (_adapter) => {\n  adapter = _adapter;\n};\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunctionPrototype[toStringTagSymbol] =\n    GeneratorFunction.displayName = \"GeneratorFunction\";\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      prototype[method] = function(arg) {\n        return this._invoke(method, arg);\n      };\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      if (!(toStringTagSymbol in genFun)) {\n        genFun[toStringTagSymbol] = \"GeneratorFunction\";\n      }\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return Promise.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return Promise.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new Promise(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList) {\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList)\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  Gp[toStringTagSymbol] = \"Generator\";\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n", "module.exports = function(module) {\n\tif (!module.webpackPolyfill) {\n\t\tmodule.deprecate = function() {};\n\t\tmodule.paths = [];\n\t\t// module.parent = undefined by default\n\t\tif (!module.children) module.children = [];\n\t\tObject.defineProperty(module, \"loaded\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.l;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(module, \"id\", {\n\t\t\tenumerable: true,\n\t\t\tget: function() {\n\t\t\t\treturn module.i;\n\t\t\t}\n\t\t});\n\t\tmodule.webpackPolyfill = 1;\n\t}\n\treturn module;\n};\n", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(\n      uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)\n    ))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "exports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n", "'use strict';\n\nexports.stringToBytes = string => [...string].map(character => character.charCodeAt(0));\n\nconst uint8ArrayUtf8ByteString = (array, start, end) => {\n\treturn String.fromCharCode(...array.slice(start, end));\n};\n\nexports.readUInt64LE = (buffer, offset = 0) => {\n\tlet n = buffer[offset];\n\tlet mul = 1;\n\tlet i = 0;\n\n\twhile (++i < 8) {\n\t\tmul *= 0x100;\n\t\tn += buffer[offset + i] * mul;\n\t}\n\n\treturn n;\n};\n\nexports.tarHeaderChecksumMatches = buffer => { // Does not check if checksum field characters are valid\n\tif (buffer.length < 512) { // `tar` header size, cannot compute checksum without it\n\t\treturn false;\n\t}\n\n\tconst MASK_8TH_BIT = 0x80;\n\n\tlet sum = 256; // Intitalize sum, with 256 as sum of 8 spaces in checksum field\n\tlet signedBitSum = 0; // Initialize signed bit sum\n\n\tfor (let i = 0; i < 148; i++) {\n\t\tconst byte = buffer[i];\n\t\tsum += byte;\n\t\tsignedBitSum += byte & MASK_8TH_BIT; // Add signed bit to signed bit sum\n\t}\n\n\t// Skip checksum field\n\n\tfor (let i = 156; i < 512; i++) {\n\t\tconst byte = buffer[i];\n\t\tsum += byte;\n\t\tsignedBitSum += byte & MASK_8TH_BIT; // Add signed bit to signed bit sum\n\t}\n\n\tconst readSum = parseInt(uint8ArrayUtf8ByteString(buffer, 148, 154), 8); // Read sum in header\n\n\t// Some implementations compute checksum incorrectly using signed bytes\n\treturn (\n\t\t// Checksum in header equals the sum we calculated\n\t\treadSum === sum ||\n\n\t\t// Checksum in header equals sum we calculated plus signed-to-unsigned delta\n\t\treadSum === (sum - (signedBitSum << 1))\n\t);\n};\n\nexports.multiByteIndexOf = (buffer, bytesToSearch, startAt = 0) => {\n\t// `Buffer#indexOf()` can search for multiple bytes\n\tif (Buffer && Buffer.isBuffer(buffer)) {\n\t\treturn buffer.indexOf(Buffer.from(bytesToSearch), startAt);\n\t}\n\n\tconst nextBytesMatch = (buffer, bytes, startIndex) => {\n\t\tfor (let i = 1; i < bytes.length; i++) {\n\t\t\tif (bytes[i] !== buffer[startIndex + i]) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t// `Uint8Array#indexOf()` can search for only a single byte\n\tlet index = buffer.indexOf(bytesToSearch[0], startAt);\n\twhile (index >= 0) {\n\t\tif (nextBytesMatch(buffer, bytesToSearch, index)) {\n\t\t\treturn index;\n\t\t}\n\n\t\tindex = buffer.indexOf(bytesToSearch[0], index + 1);\n\t}\n\n\treturn -1;\n};\n\nexports.uint8ArrayUtf8ByteString = uint8ArrayUtf8ByteString;\n", "'use strict';\n\nmodule.exports = {\n\textensions: [\n\t\t'jpg',\n\t\t'png',\n\t\t'apng',\n\t\t'gif',\n\t\t'webp',\n\t\t'flif',\n\t\t'cr2',\n\t\t'orf',\n\t\t'arw',\n\t\t'dng',\n\t\t'nef',\n\t\t'rw2',\n\t\t'raf',\n\t\t'tif',\n\t\t'bmp',\n\t\t'jxr',\n\t\t'psd',\n\t\t'zip',\n\t\t'tar',\n\t\t'rar',\n\t\t'gz',\n\t\t'bz2',\n\t\t'7z',\n\t\t'dmg',\n\t\t'mp4',\n\t\t'mid',\n\t\t'mkv',\n\t\t'webm',\n\t\t'mov',\n\t\t'avi',\n\t\t'mpg',\n\t\t'mp2',\n\t\t'mp3',\n\t\t'm4a',\n\t\t'oga',\n\t\t'ogg',\n\t\t'ogv',\n\t\t'opus',\n\t\t'flac',\n\t\t'wav',\n\t\t'spx',\n\t\t'amr',\n\t\t'pdf',\n\t\t'epub',\n\t\t'exe',\n\t\t'swf',\n\t\t'rtf',\n\t\t'wasm',\n\t\t'woff',\n\t\t'woff2',\n\t\t'eot',\n\t\t'ttf',\n\t\t'otf',\n\t\t'ico',\n\t\t'flv',\n\t\t'ps',\n\t\t'xz',\n\t\t'sqlite',\n\t\t'nes',\n\t\t'crx',\n\t\t'xpi',\n\t\t'cab',\n\t\t'deb',\n\t\t'ar',\n\t\t'rpm',\n\t\t'Z',\n\t\t'lz',\n\t\t'msi',\n\t\t'mxf',\n\t\t'mts',\n\t\t'blend',\n\t\t'bpg',\n\t\t'docx',\n\t\t'pptx',\n\t\t'xlsx',\n\t\t'3gp',\n\t\t'3g2',\n\t\t'jp2',\n\t\t'jpm',\n\t\t'jpx',\n\t\t'mj2',\n\t\t'aif',\n\t\t'qcp',\n\t\t'odt',\n\t\t'ods',\n\t\t'odp',\n\t\t'xml',\n\t\t'mobi',\n\t\t'heic',\n\t\t'cur',\n\t\t'ktx',\n\t\t'ape',\n\t\t'wv',\n\t\t'wmv',\n\t\t'wma',\n\t\t'dcm',\n\t\t'ics',\n\t\t'glb',\n\t\t'pcap',\n\t\t'dsf',\n\t\t'lnk',\n\t\t'alias',\n\t\t'voc',\n\t\t'ac3',\n\t\t'm4v',\n\t\t'm4p',\n\t\t'm4b',\n\t\t'f4v',\n\t\t'f4p',\n\t\t'f4b',\n\t\t'f4a',\n\t\t'mie',\n\t\t'asf',\n\t\t'ogm',\n\t\t'ogx',\n\t\t'mpc',\n\t\t'arrow',\n\t\t'shp'\n\t],\n\tmimeTypes: [\n\t\t'image/jpeg',\n\t\t'image/png',\n\t\t'image/gif',\n\t\t'image/webp',\n\t\t'image/flif',\n\t\t'image/x-canon-cr2',\n\t\t'image/tiff',\n\t\t'image/bmp',\n\t\t'image/vnd.ms-photo',\n\t\t'image/vnd.adobe.photoshop',\n\t\t'application/epub+zip',\n\t\t'application/x-xpinstall',\n\t\t'application/vnd.oasis.opendocument.text',\n\t\t'application/vnd.oasis.opendocument.spreadsheet',\n\t\t'application/vnd.oasis.opendocument.presentation',\n\t\t'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n\t\t'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n\t\t'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t\t'application/zip',\n\t\t'application/x-tar',\n\t\t'application/x-rar-compressed',\n\t\t'application/gzip',\n\t\t'application/x-bzip2',\n\t\t'application/x-7z-compressed',\n\t\t'application/x-apple-diskimage',\n\t\t'application/x-apache-arrow',\n\t\t'video/mp4',\n\t\t'audio/midi',\n\t\t'video/x-matroska',\n\t\t'video/webm',\n\t\t'video/quicktime',\n\t\t'video/vnd.avi',\n\t\t'audio/vnd.wave',\n\t\t'audio/qcelp',\n\t\t'audio/x-ms-wma',\n\t\t'video/x-ms-asf',\n\t\t'application/vnd.ms-asf',\n\t\t'video/mpeg',\n\t\t'video/3gpp',\n\t\t'audio/mpeg',\n\t\t'audio/mp4', // RFC 4337\n\t\t'audio/opus',\n\t\t'video/ogg',\n\t\t'audio/ogg',\n\t\t'application/ogg',\n\t\t'audio/x-flac',\n\t\t'audio/ape',\n\t\t'audio/wavpack',\n\t\t'audio/amr',\n\t\t'application/pdf',\n\t\t'application/x-msdownload',\n\t\t'application/x-shockwave-flash',\n\t\t'application/rtf',\n\t\t'application/wasm',\n\t\t'font/woff',\n\t\t'font/woff2',\n\t\t'application/vnd.ms-fontobject',\n\t\t'font/ttf',\n\t\t'font/otf',\n\t\t'image/x-icon',\n\t\t'video/x-flv',\n\t\t'application/postscript',\n\t\t'application/x-xz',\n\t\t'application/x-sqlite3',\n\t\t'application/x-nintendo-nes-rom',\n\t\t'application/x-google-chrome-extension',\n\t\t'application/vnd.ms-cab-compressed',\n\t\t'application/x-deb',\n\t\t'application/x-unix-archive',\n\t\t'application/x-rpm',\n\t\t'application/x-compress',\n\t\t'application/x-lzip',\n\t\t'application/x-msi',\n\t\t'application/x-mie',\n\t\t'application/mxf',\n\t\t'video/mp2t',\n\t\t'application/x-blender',\n\t\t'image/bpg',\n\t\t'image/jp2',\n\t\t'image/jpx',\n\t\t'image/jpm',\n\t\t'image/mj2',\n\t\t'audio/aiff',\n\t\t'application/xml',\n\t\t'application/x-mobipocket-ebook',\n\t\t'image/heif',\n\t\t'image/heif-sequence',\n\t\t'image/heic',\n\t\t'image/heic-sequence',\n\t\t'image/ktx',\n\t\t'application/dicom',\n\t\t'audio/x-musepack',\n\t\t'text/calendar',\n\t\t'model/gltf-binary',\n\t\t'application/vnd.tcpdump.pcap',\n\t\t'audio/x-dsf', // Non-standard\n\t\t'application/x.ms.shortcut', // Invented by us\n\t\t'application/x.apple.alias', // Invented by us\n\t\t'audio/x-voc',\n\t\t'audio/vnd.dolby.dd-raw',\n\t\t'audio/x-m4a',\n\t\t'image/apng',\n\t\t'image/x-olympus-orf',\n\t\t'image/x-sony-arw',\n\t\t'image/x-adobe-dng',\n\t\t'image/x-nikon-nef',\n\t\t'image/x-panasonic-rw2',\n\t\t'image/x-fujifilm-raf',\n\t\t'video/x-m4v',\n\t\t'video/3gpp2',\n\t\t'application/x-esri-shape'\n\t]\n};\n", "\n/**\n * Expose `isUrl`.\n */\n\nmodule.exports = isUrl;\n\n/**\n * RegExps.\n * A URL must match #1 and then at least one of #2/#3.\n * Use two levels of REs to avoid REDOS.\n */\n\nvar protocolAndDomainRE = /^(?:\\w+:)?\\/\\/(\\S+)$/;\n\nvar localhostDomainRE = /^localhost[\\:?\\d]*(?:[^\\:?\\d]\\S*)?$/\nvar nonLocalhostDomainRE = /^[^\\s\\.]+\\.\\S{2,}$/;\n\n/**\n * Loosely validate a URL `string`.\n *\n * @param {String} string\n * @return {Boolean}\n */\n\nfunction isUrl(string){\n  if (typeof string !== 'string') {\n    return false;\n  }\n\n  var match = string.match(protocolAndDomainRE);\n  if (!match) {\n    return false;\n  }\n\n  var everythingAfterProtocol = match[1];\n  if (!everythingAfterProtocol) {\n    return false;\n  }\n\n  if (localhostDomainRE.test(everythingAfterProtocol) ||\n      nonLocalhostDomainRE.test(everythingAfterProtocol)) {\n    return true;\n  }\n\n  return false;\n}\n", "/**\n *\n * Dump data to a big JSON tree\n *\n * @fileoverview dump data to JSON tree\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\n\n/**\n * deindent\n *\n * The generated HOCR is excessively indented, so\n * we get rid of that indentation\n *\n * @name deindent\n * @function deindent string\n * @access public\n */\nconst deindent = (html) => {\n  const lines = html.split('\\n');\n  if (lines[0].substring(0, 2) === '  ') {\n    for (let i = 0; i < lines.length; i += 1) {\n      if (lines[i].substring(0, 2) === '  ') {\n        lines[i] = lines[i].slice(2);\n      }\n    }\n  }\n  return lines.join('\\n');\n};\n\n/**\n * dump\n *\n * @name dump\n * @function dump recognition result to a JSON object\n * @access public\n */\nmodule.exports = (TessModule, api, {\n  tessjs_create_hocr,\n  tessjs_create_tsv,\n  tessjs_create_box,\n  tessjs_create_unlv,\n  tessjs_create_osd,\n}) => {\n  const ri = api.GetIterator();\n  const {\n    RIL_BLOCK,\n    RIL_PARA,\n    RIL_TEXTLINE,\n    RIL_WORD,\n    RIL_SYMBOL,\n  } = TessModule;\n  const blocks = [];\n  let block;\n  let para;\n  let textline;\n  let word;\n  let symbol;\n\n  const enumToString = (value, prefix) => (\n    Object.keys(TessModule)\n      .filter((e) => (e.startsWith(`${prefix}_`) && TessModule[e] === value))\n      .map((e) => e.slice(prefix.length + 1))[0]\n  );\n\n  ri.Begin();\n  do {\n    if (ri.IsAtBeginningOf(RIL_BLOCK)) {\n      const poly = ri.BlockPolygon();\n      let polygon = null;\n      // BlockPolygon() returns null when automatic page segmentation is off\n      if (TessModule.getPointer(poly) > 0) {\n        const n = poly.get_n();\n        const px = poly.get_x();\n        const py = poly.get_y();\n        polygon = [];\n        for (let i = 0; i < n; i += 1) {\n          polygon.push([px.getValue(i), py.getValue(i)]);\n        }\n        /*\n         * TODO: find out why _ptaDestroy doesn't work\n         */\n        // TessModule._ptaDestroy(TessModule.getPointer(poly));\n      }\n\n      block = {\n        paragraphs: [],\n        text: ri.GetUTF8Text(RIL_BLOCK),\n        confidence: ri.Confidence(RIL_BLOCK),\n        baseline: ri.getBaseline(RIL_BLOCK),\n        bbox: ri.getBoundingBox(RIL_BLOCK),\n        blocktype: enumToString(ri.BlockType(), 'PT'),\n        polygon,\n      };\n      blocks.push(block);\n    }\n    if (ri.IsAtBeginningOf(RIL_PARA)) {\n      para = {\n        lines: [],\n        text: ri.GetUTF8Text(RIL_PARA),\n        confidence: ri.Confidence(RIL_PARA),\n        baseline: ri.getBaseline(RIL_PARA),\n        bbox: ri.getBoundingBox(RIL_PARA),\n        is_ltr: !!ri.ParagraphIsLtr(),\n      };\n      block.paragraphs.push(para);\n    }\n    if (ri.IsAtBeginningOf(RIL_TEXTLINE)) {\n      textline = {\n        words: [],\n        text: ri.GetUTF8Text(RIL_TEXTLINE),\n        confidence: ri.Confidence(RIL_TEXTLINE),\n        baseline: ri.getBaseline(RIL_TEXTLINE),\n        bbox: ri.getBoundingBox(RIL_TEXTLINE),\n      };\n      para.lines.push(textline);\n    }\n    if (ri.IsAtBeginningOf(RIL_WORD)) {\n      const fontInfo = ri.getWordFontAttributes();\n      const wordDir = ri.WordDirection();\n      word = {\n        symbols: [],\n        choices: [],\n\n        text: ri.GetUTF8Text(RIL_WORD),\n        confidence: ri.Confidence(RIL_WORD),\n        baseline: ri.getBaseline(RIL_WORD),\n        bbox: ri.getBoundingBox(RIL_WORD),\n\n        is_numeric: !!ri.WordIsNumeric(),\n        in_dictionary: !!ri.WordIsFromDictionary(),\n        direction: enumToString(wordDir, 'DIR'),\n        language: ri.WordRecognitionLanguage(),\n\n        is_bold: fontInfo.is_bold,\n        is_italic: fontInfo.is_italic,\n        is_underlined: fontInfo.is_underlined,\n        is_monospace: fontInfo.is_monospace,\n        is_serif: fontInfo.is_serif,\n        is_smallcaps: fontInfo.is_smallcaps,\n        font_size: fontInfo.pointsize,\n        font_id: fontInfo.font_id,\n        font_name: fontInfo.font_name,\n      };\n      const wc = new TessModule.WordChoiceIterator(ri);\n      do {\n        word.choices.push({\n          text: wc.GetUTF8Text(),\n          confidence: wc.Confidence(),\n        });\n      } while (wc.Next());\n      TessModule.destroy(wc);\n      textline.words.push(word);\n    }\n\n    // let image = null;\n    // var pix = ri.GetBinaryImage(TessModule.RIL_SYMBOL)\n    // var image = pix2array(pix);\n    // // for some reason it seems that things stop working if you destroy pics\n    // TessModule._pixDestroy(TessModule.getPointer(pix));\n    if (ri.IsAtBeginningOf(RIL_SYMBOL)) {\n      symbol = {\n        choices: [],\n        image: null,\n        text: ri.GetUTF8Text(RIL_SYMBOL),\n        confidence: ri.Confidence(RIL_SYMBOL),\n        baseline: ri.getBaseline(RIL_SYMBOL),\n        bbox: ri.getBoundingBox(RIL_SYMBOL),\n        is_superscript: !!ri.SymbolIsSuperscript(),\n        is_subscript: !!ri.SymbolIsSubscript(),\n        is_dropcap: !!ri.SymbolIsDropcap(),\n      };\n      word.symbols.push(symbol);\n      const ci = new TessModule.ChoiceIterator(ri);\n      do {\n        symbol.choices.push({\n          text: ci.GetUTF8Text(),\n          confidence: ci.Confidence(),\n        });\n      } while (ci.Next());\n      // TessModule.destroy(i);\n    }\n  } while (ri.Next(RIL_SYMBOL));\n  TessModule.destroy(ri);\n\n  return {\n    text: api.GetUTF8Text(),\n    hocr: tessjs_create_hocr === '1' ? deindent(api.GetHOCRText()) : null,\n    tsv: tessjs_create_tsv === '1' ? api.GetTSVText() : null,\n    box: tessjs_create_box === '1' ? api.GetBoxText() : null,\n    unlv: tessjs_create_unlv === '1' ? api.GetUNLVText() : null,\n    osd: tessjs_create_osd === '1' ? api.GetOsdText() : null,\n    confidence: api.MeanTextConf(),\n    blocks,\n    psm: enumToString(api.GetPageSegMode(), 'PSM'),\n    oem: enumToString(api.oem(), 'OEM'),\n    version: api.Version(),\n  };\n};\n", "const isElectron = require('is-electron');\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (isElectron()) {\n    env.type = 'electron';\n  } else if (typeof window === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n", "// https://github.com/electron/electron/issues/2288\nfunction isElectron() {\n    // Renderer process\n    if (typeof window !== 'undefined' && typeof window.process === 'object' && window.process.type === 'renderer') {\n        return true;\n    }\n\n    // Main process\n    if (typeof process !== 'undefined' && typeof process.versions === 'object' && !!process.versions.electron) {\n        return true;\n    }\n\n    // Detect the user agent when the `nodeIntegration` option is set to true\n    if (typeof navigator === 'object' && typeof navigator.userAgent === 'string' && navigator.userAgent.indexOf('Electron') >= 0) {\n        return true;\n    }\n\n    return false;\n}\n\nmodule.exports = isElectron;\n", "const bmp = require('bmp-js');\nconst fileType = require('file-type');\n\n/**\n * setImage\n *\n * @name setImage\n * @function set image in tesseract for recognition\n * @access public\n */\nmodule.exports = (TessModule, api, image) => {\n  const buf = Buffer.from(Array.from({ ...image, length: Object.keys(image).length }));\n  const type = fileType(buf);\n  let bytesPerPixel = 0;\n  let data = null;\n  let pix = null;\n  let w = 0;\n  let h = 0;\n\n  /*\n   * Although leptonica should support reading bmp, there is a bug of \"compressed BMP files\".\n   * As there is no solution, we need to use bmp-js for now.\n   * @see https://groups.google.com/forum/#!topic/tesseract-ocr/4mPD9zTxdxE\n   */\n  if (type && type.mime === 'image/bmp') {\n    const bmpBuf = bmp.decode(buf);\n    data = TessModule._malloc(bmpBuf.data.length * Uint8Array.BYTES_PER_ELEMENT);\n    TessModule.HEAPU8.set(bmpBuf.data, data);\n    w = bmpBuf.width;\n    h = bmpBuf.height;\n    bytesPerPixel = 4;\n  } else {\n    const ptr = TessModule._malloc(buf.length * Uint8Array.BYTES_PER_ELEMENT);\n    TessModule.HEAPU8.set(buf, ptr);\n    pix = TessModule._pixReadMem(ptr, buf.length);\n    if (TessModule.getValue(pix + (7 * 4), 'i32') === 0) {\n      /*\n       * Set a yres default value to prevent warning from tesseract\n       * See kMinCredibleResolution in tesseract/src/ccstruct/publictypes.h\n       */\n      TessModule.setValue(pix + (7 * 4), 300, 'i32');\n    }\n    [w, h] = Array(2).fill(0)\n      .map((v, idx) => (\n        TessModule.getValue(pix + (idx * 4), 'i32')\n      ));\n  }\n\n  /*\n   * As some image format (ex. bmp) is not supported natiely by tesseract,\n   * sometimes it will not return pix directly, but data and bytesPerPixel\n   * for another SetImage usage.\n   *\n   */\n  if (data === null) {\n    api.SetImage(pix);\n  } else {\n    api.SetImage(data, w, h, bytesPerPixel, w * bytesPerPixel);\n  }\n  return data === null ? pix : data;\n};\n", "/**\n * <AUTHOR>\n *\n * support 1bit 4bit 8bit 24bit decode\n * encode with 24bit\n * \n */\n\nvar encode = require('./lib/encoder'),\n    decode = require('./lib/decoder');\n\nmodule.exports = {\n  encode: encode,\n  decode: decode\n};\n", "/**\n * <AUTHOR>\n *\n * BMP format encoder,encode 24bit BMP\n * Not support quality compression\n *\n */\n\nfunction BmpEncoder(imgData){\n\tthis.buffer = imgData.data;\n\tthis.width = imgData.width;\n\tthis.height = imgData.height;\n\tthis.extraBytes = this.width%4;\n\tthis.rgbSize = this.height*(3*this.width+this.extraBytes);\n\tthis.headerInfoSize = 40;\n\n\tthis.data = [];\n\t/******************header***********************/\n\tthis.flag = \"BM\";\n\tthis.reserved = 0;\n\tthis.offset = 54;\n\tthis.fileSize = this.rgbSize+this.offset;\n\tthis.planes = 1;\n\tthis.bitPP = 24;\n\tthis.compress = 0;\n\tthis.hr = 0;\n\tthis.vr = 0;\n\tthis.colors = 0;\n\tthis.importantColors = 0;\n}\n\nBmpEncoder.prototype.encode = function() {\n\tvar tempBuffer = new Buffer(this.offset+this.rgbSize);\n\tthis.pos = 0;\n\ttempBuffer.write(this.flag,this.pos,2);this.pos+=2;\n\ttempBuffer.writeUInt32LE(this.fileSize,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.reserved,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.offset,this.pos);this.pos+=4;\n\n\ttempBuffer.writeUInt32LE(this.headerInfoSize,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.width,this.pos);this.pos+=4;\n\ttempBuffer.writeInt32LE(-this.height,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt16LE(this.planes,this.pos);this.pos+=2;\n\ttempBuffer.writeUInt16LE(this.bitPP,this.pos);this.pos+=2;\n\ttempBuffer.writeUInt32LE(this.compress,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.rgbSize,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.hr,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.vr,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.colors,this.pos);this.pos+=4;\n\ttempBuffer.writeUInt32LE(this.importantColors,this.pos);this.pos+=4;\n\n\tvar i=0;\n\tvar rowBytes = 3*this.width+this.extraBytes;\n\n\tfor (var y = 0; y <this.height; y++){\n\t\tfor (var x = 0; x < this.width; x++){\n\t\t\tvar p = this.pos+y*rowBytes+x*3;\n\t\t\ti++;//a\n\t\t\ttempBuffer[p]= this.buffer[i++];//b\n\t\t\ttempBuffer[p+1] = this.buffer[i++];//g\n\t\t\ttempBuffer[p+2]  = this.buffer[i++];//r\n\t\t}\n\t\tif(this.extraBytes>0){\n\t\t\tvar fillOffset = this.pos+y*rowBytes+this.width*3;\n\t\t\ttempBuffer.fill(0,fillOffset,fillOffset+this.extraBytes);\n\t\t}\n\t}\n\n\treturn tempBuffer;\n};\n\nmodule.exports = function(imgData, quality) {\n  if (typeof quality === 'undefined') quality = 100;\n \tvar encoder = new BmpEncoder(imgData);\n\tvar data = encoder.encode();\n  return {\n    data: data,\n    width: imgData.width,\n    height: imgData.height\n  };\n};\n", "/**\n * <AUTHOR>\n *\n * Bmp format decoder,support 1bit 4bit 8bit 24bit bmp\n *\n */\n\nfunction BmpDecoder(buffer,is_with_alpha) {\n  this.pos = 0;\n  this.buffer = buffer;\n  this.is_with_alpha = !!is_with_alpha;\n  this.bottom_up = true;\n  this.flag = this.buffer.toString(\"utf-8\", 0, this.pos += 2);\n  if (this.flag != \"BM\") throw new Error(\"Invalid BMP File\");\n  this.parseHeader();\n  this.parseRGBA();\n}\n\nBmpDecoder.prototype.parseHeader = function() {\n  this.fileSize = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.reserved = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.offset = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.headerSize = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.width = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.height = this.buffer.readInt32LE(this.pos);\n  this.pos += 4;\n  this.planes = this.buffer.readUInt16LE(this.pos);\n  this.pos += 2;\n  this.bitPP = this.buffer.readUInt16LE(this.pos);\n  this.pos += 2;\n  this.compress = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.rawSize = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.hr = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.vr = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.colors = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n  this.importantColors = this.buffer.readUInt32LE(this.pos);\n  this.pos += 4;\n\n  if(this.bitPP === 16 && this.is_with_alpha){\n    this.bitPP = 15\n  }\n  if (this.bitPP < 15) {\n    var len = this.colors === 0 ? 1 << this.bitPP : this.colors;\n    this.palette = new Array(len);\n    for (var i = 0; i < len; i++) {\n      var blue = this.buffer.readUInt8(this.pos++);\n      var green = this.buffer.readUInt8(this.pos++);\n      var red = this.buffer.readUInt8(this.pos++);\n      var quad = this.buffer.readUInt8(this.pos++);\n      this.palette[i] = {\n        red: red,\n        green: green,\n        blue: blue,\n        quad: quad\n      };\n    }\n  }\n  if(this.height < 0) {\n    this.height *= -1;\n    this.bottom_up = false;\n  }\n\n}\n\nBmpDecoder.prototype.parseRGBA = function() {\n    var bitn = \"bit\" + this.bitPP;\n    var len = this.width * this.height * 4;\n    this.data = new Buffer(len);\n    this[bitn]();\n};\n\nBmpDecoder.prototype.bit1 = function() {\n  var xlen = Math.ceil(this.width / 8);\n  var mode = xlen%4;\n  var y = this.height >= 0 ? this.height - 1 : -this.height\n  for (var y = this.height - 1; y >= 0; y--) {\n    var line = this.bottom_up ? y : this.height - 1 - y\n    for (var x = 0; x < xlen; x++) {\n      var b = this.buffer.readUInt8(this.pos++);\n      var location = line * this.width * 4 + x*8*4;\n      for (var i = 0; i < 8; i++) {\n        if(x*8+i<this.width){\n          var rgb = this.palette[((b>>(7-i))&0x1)];\n\n          this.data[location+i*4] = 0;\n          this.data[location+i*4 + 1] = rgb.blue;\n          this.data[location+i*4 + 2] = rgb.green;\n          this.data[location+i*4 + 3] = rgb.red;\n\n        }else{\n          break;\n        }\n      }\n    }\n\n    if (mode != 0){\n      this.pos+=(4 - mode);\n    }\n  }\n};\n\nBmpDecoder.prototype.bit4 = function() {\n    //RLE-4\n    if(this.compress == 2){\n        this.data.fill(0xff);\n\n        var location = 0;\n        var lines = this.bottom_up?this.height-1:0;\n        var low_nibble = false;//for all count of pixel\n\n        while(location<this.data.length){\n            var a = this.buffer.readUInt8(this.pos++);\n            var b = this.buffer.readUInt8(this.pos++);\n            //absolute mode\n            if(a == 0){\n                if(b == 0){//line end\n                    if(this.bottom_up){\n                        lines--;\n                    }else{\n                        lines++;\n                    }\n                    location = lines*this.width*4;\n                    low_nibble = false;\n                    continue;\n                }else if(b == 1){//image end\n                    break;\n                }else if(b ==2){\n                    //offset x,y\n                    var x = this.buffer.readUInt8(this.pos++);\n                    var y = this.buffer.readUInt8(this.pos++);\n                    if(this.bottom_up){\n                        lines-=y;\n                    }else{\n                        lines+=y;\n                    }\n\n                    location +=(y*this.width*4+x*4);\n                }else{\n                    var c = this.buffer.readUInt8(this.pos++);\n                    for(var i=0;i<b;i++){\n                        if (low_nibble) {\n                            setPixelData.call(this, (c & 0x0f));\n                        } else {\n                            setPixelData.call(this, (c & 0xf0)>>4);\n                        }\n\n                        if ((i & 1) && (i+1 < b)){\n                            c = this.buffer.readUInt8(this.pos++);\n                        }\n\n                        low_nibble = !low_nibble;\n                    }\n\n                    if ((((b+1) >> 1) & 1 ) == 1){\n                        this.pos++\n                    }\n                }\n\n            }else{//encoded mode\n                for (var i = 0; i < a; i++) {\n                    if (low_nibble) {\n                        setPixelData.call(this, (b & 0x0f));\n                    } else {\n                        setPixelData.call(this, (b & 0xf0)>>4);\n                    }\n                    low_nibble = !low_nibble;\n                }\n            }\n\n        }\n\n\n\n\n        function setPixelData(rgbIndex){\n            var rgb = this.palette[rgbIndex];\n            this.data[location] = 0;\n            this.data[location + 1] = rgb.blue;\n            this.data[location + 2] = rgb.green;\n            this.data[location + 3] = rgb.red;\n            location+=4;\n        }\n    }else{\n\n      var xlen = Math.ceil(this.width/2);\n      var mode = xlen%4;\n      for (var y = this.height - 1; y >= 0; y--) {\n        var line = this.bottom_up ? y : this.height - 1 - y\n        for (var x = 0; x < xlen; x++) {\n          var b = this.buffer.readUInt8(this.pos++);\n          var location = line * this.width * 4 + x*2*4;\n\n          var before = b>>4;\n          var after = b&0x0F;\n\n          var rgb = this.palette[before];\n          this.data[location] = 0;\n          this.data[location + 1] = rgb.blue;\n          this.data[location + 2] = rgb.green;\n          this.data[location + 3] = rgb.red;\n\n\n          if(x*2+1>=this.width)break;\n\n          rgb = this.palette[after];\n\n          this.data[location+4] = 0;\n          this.data[location+4 + 1] = rgb.blue;\n          this.data[location+4 + 2] = rgb.green;\n          this.data[location+4 + 3] = rgb.red;\n\n        }\n\n        if (mode != 0){\n          this.pos+=(4 - mode);\n        }\n      }\n\n    }\n\n};\n\nBmpDecoder.prototype.bit8 = function() {\n    //RLE-8\n    if(this.compress == 1){\n        this.data.fill(0xff);\n\n        var location = 0;\n        var lines = this.bottom_up?this.height-1:0;\n\n        while(location<this.data.length){\n            var a = this.buffer.readUInt8(this.pos++);\n            var b = this.buffer.readUInt8(this.pos++);\n            //absolute mode\n            if(a == 0){\n                if(b == 0){//line end\n                    if(this.bottom_up){\n                        lines--;\n                    }else{\n                        lines++;\n                    }\n                    location = lines*this.width*4;\n                    continue;\n                }else if(b == 1){//image end\n                    break;\n                }else if(b ==2){\n                    //offset x,y\n                    var x = this.buffer.readUInt8(this.pos++);\n                    var y = this.buffer.readUInt8(this.pos++);\n                    if(this.bottom_up){\n                        lines-=y;\n                    }else{\n                        lines+=y;\n                    }\n\n                    location +=(y*this.width*4+x*4);\n                }else{\n                    for(var i=0;i<b;i++){\n                        var c = this.buffer.readUInt8(this.pos++);\n                        setPixelData.call(this, c);\n                    }\n                    if(b&1 == 1){\n                        this.pos++;\n                    }\n\n                }\n\n            }else{//encoded mode\n                for (var i = 0; i < a; i++) {\n                    setPixelData.call(this, b);\n                }\n            }\n\n        }\n\n\n\n\n        function setPixelData(rgbIndex){\n            var rgb = this.palette[rgbIndex];\n            this.data[location] = 0;\n            this.data[location + 1] = rgb.blue;\n            this.data[location + 2] = rgb.green;\n            this.data[location + 3] = rgb.red;\n            location+=4;\n        }\n    }else {\n        var mode = this.width % 4;\n        for (var y = this.height - 1; y >= 0; y--) {\n            var line = this.bottom_up ? y : this.height - 1 - y\n            for (var x = 0; x < this.width; x++) {\n                var b = this.buffer.readUInt8(this.pos++);\n                var location = line * this.width * 4 + x * 4;\n                if (b < this.palette.length) {\n                    var rgb = this.palette[b];\n\n                    this.data[location] = 0;\n                    this.data[location + 1] = rgb.blue;\n                    this.data[location + 2] = rgb.green;\n                    this.data[location + 3] = rgb.red;\n\n                } else {\n                    this.data[location] = 0;\n                    this.data[location + 1] = 0xFF;\n                    this.data[location + 2] = 0xFF;\n                    this.data[location + 3] = 0xFF;\n                }\n            }\n            if (mode != 0) {\n                this.pos += (4 - mode);\n            }\n        }\n    }\n};\n\nBmpDecoder.prototype.bit15 = function() {\n  var dif_w =this.width % 3;\n  var _11111 = parseInt(\"11111\", 2),_1_5 = _11111;\n  for (var y = this.height - 1; y >= 0; y--) {\n    var line = this.bottom_up ? y : this.height - 1 - y\n    for (var x = 0; x < this.width; x++) {\n\n      var B = this.buffer.readUInt16LE(this.pos);\n      this.pos+=2;\n      var blue = (B & _1_5) / _1_5 * 255 | 0;\n      var green = (B >> 5 & _1_5 ) / _1_5 * 255 | 0;\n      var red = (B >> 10 & _1_5) / _1_5 * 255 | 0;\n      var alpha = (B>>15)?0xFF:0x00;\n\n      var location = line * this.width * 4 + x * 4;\n\n      this.data[location] = alpha;\n      this.data[location + 1] = blue;\n      this.data[location + 2] = green;\n      this.data[location + 3] = red;\n    }\n    //skip extra bytes\n    this.pos += dif_w;\n  }\n};\n\nBmpDecoder.prototype.bit16 = function() {\n  var dif_w =(this.width % 2)*2;\n  //default xrgb555\n  this.maskRed = 0x7C00;\n  this.maskGreen = 0x3E0;\n  this.maskBlue =0x1F;\n  this.mask0 = 0;\n\n  if(this.compress == 3){\n    this.maskRed = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.maskGreen = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.maskBlue = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.mask0 = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n  }\n\n\n  var ns=[0,0,0];\n  for (var i=0;i<16;i++){\n    if ((this.maskRed>>i)&0x01) ns[0]++;\n    if ((this.maskGreen>>i)&0x01) ns[1]++;\n    if ((this.maskBlue>>i)&0x01) ns[2]++;\n  }\n  ns[1]+=ns[0]; ns[2]+=ns[1];\tns[0]=8-ns[0]; ns[1]-=8; ns[2]-=8;\n\n  for (var y = this.height - 1; y >= 0; y--) {\n    var line = this.bottom_up ? y : this.height - 1 - y;\n    for (var x = 0; x < this.width; x++) {\n\n      var B = this.buffer.readUInt16LE(this.pos);\n      this.pos+=2;\n\n      var blue = (B&this.maskBlue)<<ns[0];\n      var green = (B&this.maskGreen)>>ns[1];\n      var red = (B&this.maskRed)>>ns[2];\n\n      var location = line * this.width * 4 + x * 4;\n\n      this.data[location] = 0;\n      this.data[location + 1] = blue;\n      this.data[location + 2] = green;\n      this.data[location + 3] = red;\n    }\n    //skip extra bytes\n    this.pos += dif_w;\n  }\n};\n\nBmpDecoder.prototype.bit24 = function() {\n  for (var y = this.height - 1; y >= 0; y--) {\n    var line = this.bottom_up ? y : this.height - 1 - y\n    for (var x = 0; x < this.width; x++) {\n      //Little Endian rgb\n      var blue = this.buffer.readUInt8(this.pos++);\n      var green = this.buffer.readUInt8(this.pos++);\n      var red = this.buffer.readUInt8(this.pos++);\n      var location = line * this.width * 4 + x * 4;\n      this.data[location] = 0;\n      this.data[location + 1] = blue;\n      this.data[location + 2] = green;\n      this.data[location + 3] = red;\n    }\n    //skip extra bytes\n    this.pos += (this.width % 4);\n  }\n\n};\n\n/**\n * add 32bit decode func\n * <AUTHOR>\n */\nBmpDecoder.prototype.bit32 = function() {\n  //BI_BITFIELDS\n  if(this.compress == 3){\n    this.maskRed = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.maskGreen = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.maskBlue = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n    this.mask0 = this.buffer.readUInt32LE(this.pos);\n    this.pos+=4;\n      for (var y = this.height - 1; y >= 0; y--) {\n          var line = this.bottom_up ? y : this.height - 1 - y;\n          for (var x = 0; x < this.width; x++) {\n              //Little Endian rgba\n              var alpha = this.buffer.readUInt8(this.pos++);\n              var blue = this.buffer.readUInt8(this.pos++);\n              var green = this.buffer.readUInt8(this.pos++);\n              var red = this.buffer.readUInt8(this.pos++);\n              var location = line * this.width * 4 + x * 4;\n              this.data[location] = alpha;\n              this.data[location + 1] = blue;\n              this.data[location + 2] = green;\n              this.data[location + 3] = red;\n          }\n      }\n\n  }else{\n      for (var y = this.height - 1; y >= 0; y--) {\n          var line = this.bottom_up ? y : this.height - 1 - y;\n          for (var x = 0; x < this.width; x++) {\n              //Little Endian argb\n              var blue = this.buffer.readUInt8(this.pos++);\n              var green = this.buffer.readUInt8(this.pos++);\n              var red = this.buffer.readUInt8(this.pos++);\n              var alpha = this.buffer.readUInt8(this.pos++);\n              var location = line * this.width * 4 + x * 4;\n              this.data[location] = alpha;\n              this.data[location + 1] = blue;\n              this.data[location + 2] = green;\n              this.data[location + 3] = red;\n          }\n      }\n\n  }\n\n\n\n\n};\n\nBmpDecoder.prototype.getData = function() {\n  return this.data;\n};\n\nmodule.exports = function(bmpData) {\n  var decoder = new BmpDecoder(bmpData);\n  return decoder;\n};\n", "/*\n * default params for tesseract.js\n */\nconst PSM = require('../../constants/PSM');\n\nmodule.exports = {\n  tessedit_pageseg_mode: PSM.SINGLE_BLOCK,\n  tessedit_char_whitelist: '',\n  tessjs_create_hocr: '1',\n  tessjs_create_tsv: '1',\n  tessjs_create_box: '0',\n  tessjs_create_unlv: '0',\n  tessjs_create_osd: '0',\n};\n", "/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n};\n", "let logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n", "module.exports = (corePath, res) => {\n  if (typeof global.TesseractCore === 'undefined') {\n    res.progress({ status: 'loading tesseract core', progress: 0 });\n    global.importScripts(corePath);\n    /*\n     * Depending on whether the browser supports WebAssembly,\n     * the version of the TesseractCore will be different.\n     */\n    if (typeof global.TesseractCoreWASM !== 'undefined' && typeof WebAssembly === 'object') {\n      global.TesseractCore = global.TesseractCoreWASM;\n    } else if (typeof global.TesseractCoreASM !== 'undefined') {\n      global.TesseractCore = global.TesseractCoreASM;\n    } else {\n      throw Error('Failed to load TesseractCore');\n    }\n    res.progress({ status: 'loading tesseract core', progress: 1 });\n  }\n  return global.TesseractCore;\n};\n", "module.exports = require('zlibjs').gunzipSync;\n", "/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';function q(b){throw b;}var t=void 0,v=!0;var B=\"undefined\"!==typeof Uint8Array&&\"undefined\"!==typeof Uint16Array&&\"undefined\"!==typeof Uint32Array&&\"undefined\"!==typeof DataView;function G(b,a){this.index=\"number\"===typeof a?a:0;this.m=0;this.buffer=b instanceof(B?Uint8Array:Array)?b:new (B?Uint8Array:Array)(32768);2*this.buffer.length<=this.index&&q(Error(\"invalid index\"));this.buffer.length<=this.index&&this.f()}G.prototype.f=function(){var b=this.buffer,a,c=b.length,d=new (B?Uint8Array:Array)(c<<1);if(B)d.set(b);else for(a=0;a<c;++a)d[a]=b[a];return this.buffer=d};\nG.prototype.d=function(b,a,c){var d=this.buffer,e=this.index,f=this.m,g=d[e],k;c&&1<a&&(b=8<a?(I[b&255]<<24|I[b>>>8&255]<<16|I[b>>>16&255]<<8|I[b>>>24&255])>>32-a:I[b]>>8-a);if(8>a+f)g=g<<a|b,f+=a;else for(k=0;k<a;++k)g=g<<1|b>>a-k-1&1,8===++f&&(f=0,d[e++]=I[g],g=0,e===d.length&&(d=this.f()));d[e]=g;this.buffer=d;this.m=f;this.index=e};G.prototype.finish=function(){var b=this.buffer,a=this.index,c;0<this.m&&(b[a]<<=8-this.m,b[a]=I[b[a]],a++);B?c=b.subarray(0,a):(b.length=a,c=b);return c};\nvar aa=new (B?Uint8Array:Array)(256),L;for(L=0;256>L;++L){for(var R=L,ba=R,ca=7,R=R>>>1;R;R>>>=1)ba<<=1,ba|=R&1,--ca;aa[L]=(ba<<ca&255)>>>0}var I=aa;function ha(b,a,c){var d,e=\"number\"===typeof a?a:a=0,f=\"number\"===typeof c?c:b.length;d=-1;for(e=f&7;e--;++a)d=d>>>8^S[(d^b[a])&255];for(e=f>>3;e--;a+=8)d=d>>>8^S[(d^b[a])&255],d=d>>>8^S[(d^b[a+1])&255],d=d>>>8^S[(d^b[a+2])&255],d=d>>>8^S[(d^b[a+3])&255],d=d>>>8^S[(d^b[a+4])&255],d=d>>>8^S[(d^b[a+5])&255],d=d>>>8^S[(d^b[a+6])&255],d=d>>>8^S[(d^b[a+7])&255];return(d^4294967295)>>>0}\nvar ia=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,\n2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,\n2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,\n2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,\n3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,\n936918E3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],S=B?new Uint32Array(ia):ia;function ja(){};function ka(b){this.buffer=new (B?Uint16Array:Array)(2*b);this.length=0}ka.prototype.getParent=function(b){return 2*((b-2)/4|0)};ka.prototype.push=function(b,a){var c,d,e=this.buffer,f;c=this.length;e[this.length++]=a;for(e[this.length++]=b;0<c;)if(d=this.getParent(c),e[c]>e[d])f=e[c],e[c]=e[d],e[d]=f,f=e[c+1],e[c+1]=e[d+1],e[d+1]=f,c=d;else break;return this.length};\nka.prototype.pop=function(){var b,a,c=this.buffer,d,e,f;a=c[0];b=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(f=0;;){e=2*f+2;if(e>=this.length)break;e+2<this.length&&c[e+2]>c[e]&&(e+=2);if(c[e]>c[f])d=c[f],c[f]=c[e],c[e]=d,d=c[f+1],c[f+1]=c[e+1],c[e+1]=d;else break;f=e}return{index:b,value:a,length:this.length}};function T(b){var a=b.length,c=0,d=Number.POSITIVE_INFINITY,e,f,g,k,h,m,r,p,l,n;for(p=0;p<a;++p)b[p]>c&&(c=b[p]),b[p]<d&&(d=b[p]);e=1<<c;f=new (B?Uint32Array:Array)(e);g=1;k=0;for(h=2;g<=c;){for(p=0;p<a;++p)if(b[p]===g){m=0;r=k;for(l=0;l<g;++l)m=m<<1|r&1,r>>=1;n=g<<16|p;for(l=m;l<e;l+=h)f[l]=n;++k}++g;k<<=1;h<<=1}return[f,c,d]};function na(b,a){this.k=oa;this.F=0;this.input=B&&b instanceof Array?new Uint8Array(b):b;this.b=0;a&&(a.lazy&&(this.F=a.lazy),\"number\"===typeof a.compressionType&&(this.k=a.compressionType),a.outputBuffer&&(this.a=B&&a.outputBuffer instanceof Array?new Uint8Array(a.outputBuffer):a.outputBuffer),\"number\"===typeof a.outputIndex&&(this.b=a.outputIndex));this.a||(this.a=new (B?Uint8Array:Array)(32768))}var oa=2,pa={NONE:0,L:1,t:oa,X:3},qa=[],U;\nfor(U=0;288>U;U++)switch(v){case 143>=U:qa.push([U+48,8]);break;case 255>=U:qa.push([U-144+400,9]);break;case 279>=U:qa.push([U-256+0,7]);break;case 287>=U:qa.push([U-280+192,8]);break;default:q(\"invalid literal: \"+U)}\nna.prototype.h=function(){var b,a,c,d,e=this.input;switch(this.k){case 0:c=0;for(d=e.length;c<d;){a=B?e.subarray(c,c+65535):e.slice(c,c+65535);c+=a.length;var f=a,g=c===d,k=t,h=t,m=t,r=t,p=t,l=this.a,n=this.b;if(B){for(l=new Uint8Array(this.a.buffer);l.length<=n+f.length+5;)l=new Uint8Array(l.length<<1);l.set(this.a)}k=g?1:0;l[n++]=k|0;h=f.length;m=~h+65536&65535;l[n++]=h&255;l[n++]=h>>>8&255;l[n++]=m&255;l[n++]=m>>>8&255;if(B)l.set(f,n),n+=f.length,l=l.subarray(0,n);else{r=0;for(p=f.length;r<p;++r)l[n++]=\nf[r];l.length=n}this.b=n;this.a=l}break;case 1:var s=new G(B?new Uint8Array(this.a.buffer):this.a,this.b);s.d(1,1,v);s.d(1,2,v);var u=ra(this,e),w,C,x;w=0;for(C=u.length;w<C;w++)if(x=u[w],G.prototype.d.apply(s,qa[x]),256<x)s.d(u[++w],u[++w],v),s.d(u[++w],5),s.d(u[++w],u[++w],v);else if(256===x)break;this.a=s.finish();this.b=this.a.length;break;case oa:var D=new G(B?new Uint8Array(this.a.buffer):this.a,this.b),M,z,N,X,Y,qb=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],da,Fa,ea,Ga,la,ta=Array(19),\nHa,Z,ma,E,Ia;M=oa;D.d(1,1,v);D.d(M,2,v);z=ra(this,e);da=sa(this.U,15);Fa=ua(da);ea=sa(this.T,7);Ga=ua(ea);for(N=286;257<N&&0===da[N-1];N--);for(X=30;1<X&&0===ea[X-1];X--);var Ja=N,Ka=X,K=new (B?Uint32Array:Array)(Ja+Ka),y,O,A,fa,J=new (B?Uint32Array:Array)(316),H,F,P=new (B?Uint8Array:Array)(19);for(y=O=0;y<Ja;y++)K[O++]=da[y];for(y=0;y<Ka;y++)K[O++]=ea[y];if(!B){y=0;for(fa=P.length;y<fa;++y)P[y]=0}y=H=0;for(fa=K.length;y<fa;y+=O){for(O=1;y+O<fa&&K[y+O]===K[y];++O);A=O;if(0===K[y])if(3>A)for(;0<A--;)J[H++]=\n0,P[0]++;else for(;0<A;)F=138>A?A:138,F>A-3&&F<A&&(F=A-3),10>=F?(J[H++]=17,J[H++]=F-3,P[17]++):(J[H++]=18,J[H++]=F-11,P[18]++),A-=F;else if(J[H++]=K[y],P[K[y]]++,A--,3>A)for(;0<A--;)J[H++]=K[y],P[K[y]]++;else for(;0<A;)F=6>A?A:6,F>A-3&&F<A&&(F=A-3),J[H++]=16,J[H++]=F-3,P[16]++,A-=F}b=B?J.subarray(0,H):J.slice(0,H);la=sa(P,7);for(E=0;19>E;E++)ta[E]=la[qb[E]];for(Y=19;4<Y&&0===ta[Y-1];Y--);Ha=ua(la);D.d(N-257,5,v);D.d(X-1,5,v);D.d(Y-4,4,v);for(E=0;E<Y;E++)D.d(ta[E],3,v);E=0;for(Ia=b.length;E<Ia;E++)if(Z=\nb[E],D.d(Ha[Z],la[Z],v),16<=Z){E++;switch(Z){case 16:ma=2;break;case 17:ma=3;break;case 18:ma=7;break;default:q(\"invalid code: \"+Z)}D.d(b[E],ma,v)}var La=[Fa,da],Ma=[Ga,ea],Q,Na,ga,wa,Oa,Pa,Qa,Ra;Oa=La[0];Pa=La[1];Qa=Ma[0];Ra=Ma[1];Q=0;for(Na=z.length;Q<Na;++Q)if(ga=z[Q],D.d(Oa[ga],Pa[ga],v),256<ga)D.d(z[++Q],z[++Q],v),wa=z[++Q],D.d(Qa[wa],Ra[wa],v),D.d(z[++Q],z[++Q],v);else if(256===ga)break;this.a=D.finish();this.b=this.a.length;break;default:q(\"invalid compression type\")}return this.a};\nfunction va(b,a){this.length=b;this.N=a}\nvar xa=function(){function b(a){switch(v){case 3===a:return[257,a-3,0];case 4===a:return[258,a-4,0];case 5===a:return[259,a-5,0];case 6===a:return[260,a-6,0];case 7===a:return[261,a-7,0];case 8===a:return[262,a-8,0];case 9===a:return[263,a-9,0];case 10===a:return[264,a-10,0];case 12>=a:return[265,a-11,1];case 14>=a:return[266,a-13,1];case 16>=a:return[267,a-15,1];case 18>=a:return[268,a-17,1];case 22>=a:return[269,a-19,2];case 26>=a:return[270,a-23,2];case 30>=a:return[271,a-27,2];case 34>=a:return[272,\na-31,2];case 42>=a:return[273,a-35,3];case 50>=a:return[274,a-43,3];case 58>=a:return[275,a-51,3];case 66>=a:return[276,a-59,3];case 82>=a:return[277,a-67,4];case 98>=a:return[278,a-83,4];case 114>=a:return[279,a-99,4];case 130>=a:return[280,a-115,4];case 162>=a:return[281,a-131,5];case 194>=a:return[282,a-163,5];case 226>=a:return[283,a-195,5];case 257>=a:return[284,a-227,5];case 258===a:return[285,a-258,0];default:q(\"invalid length: \"+a)}}var a=[],c,d;for(c=3;258>=c;c++)d=b(c),a[c]=d[2]<<24|d[1]<<\n16|d[0];return a}(),ya=B?new Uint32Array(xa):xa;\nfunction ra(b,a){function c(a,c){var b=a.N,d=[],f=0,e;e=ya[a.length];d[f++]=e&65535;d[f++]=e>>16&255;d[f++]=e>>24;var g;switch(v){case 1===b:g=[0,b-1,0];break;case 2===b:g=[1,b-2,0];break;case 3===b:g=[2,b-3,0];break;case 4===b:g=[3,b-4,0];break;case 6>=b:g=[4,b-5,1];break;case 8>=b:g=[5,b-7,1];break;case 12>=b:g=[6,b-9,2];break;case 16>=b:g=[7,b-13,2];break;case 24>=b:g=[8,b-17,3];break;case 32>=b:g=[9,b-25,3];break;case 48>=b:g=[10,b-33,4];break;case 64>=b:g=[11,b-49,4];break;case 96>=b:g=[12,b-\n65,5];break;case 128>=b:g=[13,b-97,5];break;case 192>=b:g=[14,b-129,6];break;case 256>=b:g=[15,b-193,6];break;case 384>=b:g=[16,b-257,7];break;case 512>=b:g=[17,b-385,7];break;case 768>=b:g=[18,b-513,8];break;case 1024>=b:g=[19,b-769,8];break;case 1536>=b:g=[20,b-1025,9];break;case 2048>=b:g=[21,b-1537,9];break;case 3072>=b:g=[22,b-2049,10];break;case 4096>=b:g=[23,b-3073,10];break;case 6144>=b:g=[24,b-4097,11];break;case 8192>=b:g=[25,b-6145,11];break;case 12288>=b:g=[26,b-8193,12];break;case 16384>=\nb:g=[27,b-12289,12];break;case 24576>=b:g=[28,b-16385,13];break;case 32768>=b:g=[29,b-24577,13];break;default:q(\"invalid distance\")}e=g;d[f++]=e[0];d[f++]=e[1];d[f++]=e[2];var h,k;h=0;for(k=d.length;h<k;++h)l[n++]=d[h];u[d[0]]++;w[d[3]]++;s=a.length+c-1;p=null}var d,e,f,g,k,h={},m,r,p,l=B?new Uint16Array(2*a.length):[],n=0,s=0,u=new (B?Uint32Array:Array)(286),w=new (B?Uint32Array:Array)(30),C=b.F,x;if(!B){for(f=0;285>=f;)u[f++]=0;for(f=0;29>=f;)w[f++]=0}u[256]=1;d=0;for(e=a.length;d<e;++d){f=k=0;\nfor(g=3;f<g&&d+f!==e;++f)k=k<<8|a[d+f];h[k]===t&&(h[k]=[]);m=h[k];if(!(0<s--)){for(;0<m.length&&32768<d-m[0];)m.shift();if(d+3>=e){p&&c(p,-1);f=0;for(g=e-d;f<g;++f)x=a[d+f],l[n++]=x,++u[x];break}0<m.length?(r=za(a,d,m),p?p.length<r.length?(x=a[d-1],l[n++]=x,++u[x],c(r,0)):c(p,-1):r.length<C?p=r:c(r,0)):p?c(p,-1):(x=a[d],l[n++]=x,++u[x])}m.push(d)}l[n++]=256;u[256]++;b.U=u;b.T=w;return B?l.subarray(0,n):l}\nfunction za(b,a,c){var d,e,f=0,g,k,h,m,r=b.length;k=0;m=c.length;a:for(;k<m;k++){d=c[m-k-1];g=3;if(3<f){for(h=f;3<h;h--)if(b[d+h-1]!==b[a+h-1])continue a;g=f}for(;258>g&&a+g<r&&b[d+g]===b[a+g];)++g;g>f&&(e=d,f=g);if(258===g)break}return new va(f,a-e)}\nfunction sa(b,a){var c=b.length,d=new ka(572),e=new (B?Uint8Array:Array)(c),f,g,k,h,m;if(!B)for(h=0;h<c;h++)e[h]=0;for(h=0;h<c;++h)0<b[h]&&d.push(h,b[h]);f=Array(d.length/2);g=new (B?Uint32Array:Array)(d.length/2);if(1===f.length)return e[d.pop().index]=1,e;h=0;for(m=d.length/2;h<m;++h)f[h]=d.pop(),g[h]=f[h].value;k=Aa(g,g.length,a);h=0;for(m=f.length;h<m;++h)e[f[h].index]=k[h];return e}\nfunction Aa(b,a,c){function d(b){var c=h[b][m[b]];c===a?(d(b+1),d(b+1)):--g[c];++m[b]}var e=new (B?Uint16Array:Array)(c),f=new (B?Uint8Array:Array)(c),g=new (B?Uint8Array:Array)(a),k=Array(c),h=Array(c),m=Array(c),r=(1<<c)-a,p=1<<c-1,l,n,s,u,w;e[c-1]=a;for(n=0;n<c;++n)r<p?f[n]=0:(f[n]=1,r-=p),r<<=1,e[c-2-n]=(e[c-1-n]/2|0)+a;e[0]=f[0];k[0]=Array(e[0]);h[0]=Array(e[0]);for(n=1;n<c;++n)e[n]>2*e[n-1]+f[n]&&(e[n]=2*e[n-1]+f[n]),k[n]=Array(e[n]),h[n]=Array(e[n]);for(l=0;l<a;++l)g[l]=c;for(s=0;s<e[c-1];++s)k[c-\n1][s]=b[s],h[c-1][s]=s;for(l=0;l<c;++l)m[l]=0;1===f[c-1]&&(--g[0],++m[c-1]);for(n=c-2;0<=n;--n){u=l=0;w=m[n+1];for(s=0;s<e[n];s++)u=k[n+1][w]+k[n+1][w+1],u>b[l]?(k[n][s]=u,h[n][s]=a,w+=2):(k[n][s]=b[l],h[n][s]=l,++l);m[n]=0;1===f[n]&&d(n)}return g}\nfunction ua(b){var a=new (B?Uint16Array:Array)(b.length),c=[],d=[],e=0,f,g,k,h;f=0;for(g=b.length;f<g;f++)c[b[f]]=(c[b[f]]|0)+1;f=1;for(g=16;f<=g;f++)d[f]=e,e+=c[f]|0,e<<=1;f=0;for(g=b.length;f<g;f++){e=d[b[f]];d[b[f]]+=1;k=a[f]=0;for(h=b[f];k<h;k++)a[f]=a[f]<<1|e&1,e>>>=1}return a};function Ba(b,a){this.input=b;this.b=this.c=0;this.g={};a&&(a.flags&&(this.g=a.flags),\"string\"===typeof a.filename&&(this.filename=a.filename),\"string\"===typeof a.comment&&(this.w=a.comment),a.deflateOptions&&(this.l=a.deflateOptions));this.l||(this.l={})}\nBa.prototype.h=function(){var b,a,c,d,e,f,g,k,h=new (B?Uint8Array:Array)(32768),m=0,r=this.input,p=this.c,l=this.filename,n=this.w;h[m++]=31;h[m++]=139;h[m++]=8;b=0;this.g.fname&&(b|=Ca);this.g.fcomment&&(b|=Da);this.g.fhcrc&&(b|=Ea);h[m++]=b;a=(Date.now?Date.now():+new Date)/1E3|0;h[m++]=a&255;h[m++]=a>>>8&255;h[m++]=a>>>16&255;h[m++]=a>>>24&255;h[m++]=0;h[m++]=Sa;if(this.g.fname!==t){g=0;for(k=l.length;g<k;++g)f=l.charCodeAt(g),255<f&&(h[m++]=f>>>8&255),h[m++]=f&255;h[m++]=0}if(this.g.comment){g=\n0;for(k=n.length;g<k;++g)f=n.charCodeAt(g),255<f&&(h[m++]=f>>>8&255),h[m++]=f&255;h[m++]=0}this.g.fhcrc&&(c=ha(h,0,m)&65535,h[m++]=c&255,h[m++]=c>>>8&255);this.l.outputBuffer=h;this.l.outputIndex=m;e=new na(r,this.l);h=e.h();m=e.b;B&&(m+8>h.buffer.byteLength?(this.a=new Uint8Array(m+8),this.a.set(new Uint8Array(h.buffer)),h=this.a):h=new Uint8Array(h.buffer));d=ha(r,t,t);h[m++]=d&255;h[m++]=d>>>8&255;h[m++]=d>>>16&255;h[m++]=d>>>24&255;k=r.length;h[m++]=k&255;h[m++]=k>>>8&255;h[m++]=k>>>16&255;h[m++]=\nk>>>24&255;this.c=p;B&&m<h.length&&(this.a=h=h.subarray(0,m));return h};var Sa=255,Ea=2,Ca=8,Da=16;function V(b,a){this.o=[];this.p=32768;this.e=this.j=this.c=this.s=0;this.input=B?new Uint8Array(b):b;this.u=!1;this.q=Ta;this.K=!1;if(a||!(a={}))a.index&&(this.c=a.index),a.bufferSize&&(this.p=a.bufferSize),a.bufferType&&(this.q=a.bufferType),a.resize&&(this.K=a.resize);switch(this.q){case Ua:this.b=32768;this.a=new (B?Uint8Array:Array)(32768+this.p+258);break;case Ta:this.b=0;this.a=new (B?Uint8Array:Array)(this.p);this.f=this.S;this.z=this.O;this.r=this.Q;break;default:q(Error(\"invalid inflate mode\"))}}\nvar Ua=0,Ta=1;\nV.prototype.i=function(){for(;!this.u;){var b=W(this,3);b&1&&(this.u=v);b>>>=1;switch(b){case 0:var a=this.input,c=this.c,d=this.a,e=this.b,f=a.length,g=t,k=t,h=d.length,m=t;this.e=this.j=0;c+1>=f&&q(Error(\"invalid uncompressed block header: LEN\"));g=a[c++]|a[c++]<<8;c+1>=f&&q(Error(\"invalid uncompressed block header: NLEN\"));k=a[c++]|a[c++]<<8;g===~k&&q(Error(\"invalid uncompressed block header: length verify\"));c+g>a.length&&q(Error(\"input buffer is broken\"));switch(this.q){case Ua:for(;e+g>d.length;){m=\nh-e;g-=m;if(B)d.set(a.subarray(c,c+m),e),e+=m,c+=m;else for(;m--;)d[e++]=a[c++];this.b=e;d=this.f();e=this.b}break;case Ta:for(;e+g>d.length;)d=this.f({B:2});break;default:q(Error(\"invalid inflate mode\"))}if(B)d.set(a.subarray(c,c+g),e),e+=g,c+=g;else for(;g--;)d[e++]=a[c++];this.c=c;this.b=e;this.a=d;break;case 1:this.r(Va,Wa);break;case 2:for(var r=W(this,5)+257,p=W(this,5)+1,l=W(this,4)+4,n=new (B?Uint8Array:Array)(Xa.length),s=t,u=t,w=t,C=t,x=t,D=t,M=t,z=t,N=t,z=0;z<l;++z)n[Xa[z]]=W(this,3);if(!B){z=\nl;for(l=n.length;z<l;++z)n[Xa[z]]=0}s=T(n);C=new (B?Uint8Array:Array)(r+p);z=0;for(N=r+p;z<N;)switch(x=Ya(this,s),x){case 16:for(M=3+W(this,2);M--;)C[z++]=D;break;case 17:for(M=3+W(this,3);M--;)C[z++]=0;D=0;break;case 18:for(M=11+W(this,7);M--;)C[z++]=0;D=0;break;default:D=C[z++]=x}u=B?T(C.subarray(0,r)):T(C.slice(0,r));w=B?T(C.subarray(r)):T(C.slice(r));this.r(u,w);break;default:q(Error(\"unknown BTYPE: \"+b))}}return this.z()};\nvar Za=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Xa=B?new Uint16Array(Za):Za,$a=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],ab=B?new Uint16Array($a):$a,bb=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],cb=B?new Uint8Array(bb):bb,db=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],eb=B?new Uint16Array(db):db,fb=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,\n10,11,11,12,12,13,13],gb=B?new Uint8Array(fb):fb,hb=new (B?Uint8Array:Array)(288),$,ib;$=0;for(ib=hb.length;$<ib;++$)hb[$]=143>=$?8:255>=$?9:279>=$?7:8;var Va=T(hb),jb=new (B?Uint8Array:Array)(30),kb,lb;kb=0;for(lb=jb.length;kb<lb;++kb)jb[kb]=5;var Wa=T(jb);function W(b,a){for(var c=b.j,d=b.e,e=b.input,f=b.c,g=e.length,k;d<a;)f>=g&&q(Error(\"input buffer is broken\")),c|=e[f++]<<d,d+=8;k=c&(1<<a)-1;b.j=c>>>a;b.e=d-a;b.c=f;return k}\nfunction Ya(b,a){for(var c=b.j,d=b.e,e=b.input,f=b.c,g=e.length,k=a[0],h=a[1],m,r;d<h&&!(f>=g);)c|=e[f++]<<d,d+=8;m=k[c&(1<<h)-1];r=m>>>16;r>d&&q(Error(\"invalid code length: \"+r));b.j=c>>r;b.e=d-r;b.c=f;return m&65535}\nV.prototype.r=function(b,a){var c=this.a,d=this.b;this.A=b;for(var e=c.length-258,f,g,k,h;256!==(f=Ya(this,b));)if(256>f)d>=e&&(this.b=d,c=this.f(),d=this.b),c[d++]=f;else{g=f-257;h=ab[g];0<cb[g]&&(h+=W(this,cb[g]));f=Ya(this,a);k=eb[f];0<gb[f]&&(k+=W(this,gb[f]));d>=e&&(this.b=d,c=this.f(),d=this.b);for(;h--;)c[d]=c[d++-k]}for(;8<=this.e;)this.e-=8,this.c--;this.b=d};\nV.prototype.Q=function(b,a){var c=this.a,d=this.b;this.A=b;for(var e=c.length,f,g,k,h;256!==(f=Ya(this,b));)if(256>f)d>=e&&(c=this.f(),e=c.length),c[d++]=f;else{g=f-257;h=ab[g];0<cb[g]&&(h+=W(this,cb[g]));f=Ya(this,a);k=eb[f];0<gb[f]&&(k+=W(this,gb[f]));d+h>e&&(c=this.f(),e=c.length);for(;h--;)c[d]=c[d++-k]}for(;8<=this.e;)this.e-=8,this.c--;this.b=d};\nV.prototype.f=function(){var b=new (B?Uint8Array:Array)(this.b-32768),a=this.b-32768,c,d,e=this.a;if(B)b.set(e.subarray(32768,b.length));else{c=0;for(d=b.length;c<d;++c)b[c]=e[c+32768]}this.o.push(b);this.s+=b.length;if(B)e.set(e.subarray(a,a+32768));else for(c=0;32768>c;++c)e[c]=e[a+c];this.b=32768;return e};\nV.prototype.S=function(b){var a,c=this.input.length/this.c+1|0,d,e,f,g=this.input,k=this.a;b&&(\"number\"===typeof b.B&&(c=b.B),\"number\"===typeof b.M&&(c+=b.M));2>c?(d=(g.length-this.c)/this.A[2],f=258*(d/2)|0,e=f<k.length?k.length+f:k.length<<1):e=k.length*c;B?(a=new Uint8Array(e),a.set(k)):a=k;return this.a=a};\nV.prototype.z=function(){var b=0,a=this.a,c=this.o,d,e=new (B?Uint8Array:Array)(this.s+(this.b-32768)),f,g,k,h;if(0===c.length)return B?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);f=0;for(g=c.length;f<g;++f){d=c[f];k=0;for(h=d.length;k<h;++k)e[b++]=d[k]}f=32768;for(g=this.b;f<g;++f)e[b++]=a[f];this.o=[];return this.buffer=e};\nV.prototype.O=function(){var b,a=this.b;B?this.K?(b=new Uint8Array(a),b.set(this.a.subarray(0,a))):b=this.a.subarray(0,a):(this.a.length>a&&(this.a.length=a),b=this.a);return this.buffer=b};function mb(b){this.input=b;this.c=0;this.G=[];this.R=!1}\nmb.prototype.i=function(){for(var b=this.input.length;this.c<b;){var a=new ja,c=t,d=t,e=t,f=t,g=t,k=t,h=t,m=t,r=t,p=this.input,l=this.c;a.C=p[l++];a.D=p[l++];(31!==a.C||139!==a.D)&&q(Error(\"invalid file signature:\"+a.C+\",\"+a.D));a.v=p[l++];switch(a.v){case 8:break;default:q(Error(\"unknown compression method: \"+a.v))}a.n=p[l++];m=p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24;a.$=new Date(1E3*m);a.ba=p[l++];a.aa=p[l++];0<(a.n&4)&&(a.W=p[l++]|p[l++]<<8,l+=a.W);if(0<(a.n&Ca)){h=[];for(k=0;0<(g=p[l++]);)h[k++]=\nString.fromCharCode(g);a.name=h.join(\"\")}if(0<(a.n&Da)){h=[];for(k=0;0<(g=p[l++]);)h[k++]=String.fromCharCode(g);a.w=h.join(\"\")}0<(a.n&Ea)&&(a.P=ha(p,0,l)&65535,a.P!==(p[l++]|p[l++]<<8)&&q(Error(\"invalid header crc16\")));c=p[p.length-4]|p[p.length-3]<<8|p[p.length-2]<<16|p[p.length-1]<<24;p.length-l-4-4<512*c&&(f=c);d=new V(p,{index:l,bufferSize:f});a.data=e=d.i();l=d.c;a.Y=r=(p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24)>>>0;ha(e,t,t)!==r&&q(Error(\"invalid CRC-32 checksum: 0x\"+ha(e,t,t).toString(16)+\" / 0x\"+\nr.toString(16)));a.Z=c=(p[l++]|p[l++]<<8|p[l++]<<16|p[l++]<<24)>>>0;(e.length&4294967295)!==c&&q(Error(\"invalid input size: \"+(e.length&4294967295)+\" / \"+c));this.G.push(a);this.c=l}this.R=v;var n=this.G,s,u,w=0,C=0,x;s=0;for(u=n.length;s<u;++s)C+=n[s].data.length;if(B){x=new Uint8Array(C);for(s=0;s<u;++s)x.set(n[s].data,w),w+=n[s].data.length}else{x=[];for(s=0;s<u;++s)x[s]=n[s].data;x=Array.prototype.concat.apply([],x)}return x};function nb(b){if(\"string\"===typeof b){var a=b.split(\"\"),c,d;c=0;for(d=a.length;c<d;c++)a[c]=(a[c].charCodeAt(0)&255)>>>0;b=a}for(var e=1,f=0,g=b.length,k,h=0;0<g;){k=1024<g?1024:g;g-=k;do e+=b[h++],f+=e;while(--k);e%=65521;f%=65521}return(f<<16|e)>>>0};function ob(b,a){var c,d;this.input=b;this.c=0;if(a||!(a={}))a.index&&(this.c=a.index),a.verify&&(this.V=a.verify);c=b[this.c++];d=b[this.c++];switch(c&15){case pb:this.method=pb;break;default:q(Error(\"unsupported compression method\"))}0!==((c<<8)+d)%31&&q(Error(\"invalid fcheck flag:\"+((c<<8)+d)%31));d&32&&q(Error(\"fdict flag is not supported\"));this.J=new V(b,{index:this.c,bufferSize:a.bufferSize,bufferType:a.bufferType,resize:a.resize})}\nob.prototype.i=function(){var b=this.input,a,c;a=this.J.i();this.c=this.J.c;this.V&&(c=(b[this.c++]<<24|b[this.c++]<<16|b[this.c++]<<8|b[this.c++])>>>0,c!==nb(a)&&q(Error(\"invalid adler-32 checksum\")));return a};var pb=8;function rb(b,a){this.input=b;this.a=new (B?Uint8Array:Array)(32768);this.k=sb.t;var c={},d;if((a||!(a={}))&&\"number\"===typeof a.compressionType)this.k=a.compressionType;for(d in a)c[d]=a[d];c.outputBuffer=this.a;this.I=new na(this.input,c)}var sb=pa;\nrb.prototype.h=function(){var b,a,c,d,e,f,g,k=0;g=this.a;b=pb;switch(b){case pb:a=Math.LOG2E*Math.log(32768)-8;break;default:q(Error(\"invalid compression method\"))}c=a<<4|b;g[k++]=c;switch(b){case pb:switch(this.k){case sb.NONE:e=0;break;case sb.L:e=1;break;case sb.t:e=2;break;default:q(Error(\"unsupported compression type\"))}break;default:q(Error(\"invalid compression method\"))}d=e<<6|0;g[k++]=d|31-(256*c+d)%31;f=nb(this.input);this.I.b=k;g=this.I.h();k=g.length;B&&(g=new Uint8Array(g.buffer),g.length<=\nk+4&&(this.a=new Uint8Array(g.length+4),this.a.set(g),g=this.a),g=g.subarray(0,k+4));g[k++]=f>>24&255;g[k++]=f>>16&255;g[k++]=f>>8&255;g[k++]=f&255;return g};exports.deflate=tb;exports.deflateSync=ub;exports.inflate=vb;exports.inflateSync=wb;exports.gzip=xb;exports.gzipSync=yb;exports.gunzip=zb;exports.gunzipSync=Ab;function tb(b,a,c){process.nextTick(function(){var d,e;try{e=ub(b,c)}catch(f){d=f}a(d,e)})}function ub(b,a){var c;c=(new rb(b)).h();a||(a={});return a.H?c:Bb(c)}function vb(b,a,c){process.nextTick(function(){var d,e;try{e=wb(b,c)}catch(f){d=f}a(d,e)})}\nfunction wb(b,a){var c;b.subarray=b.slice;c=(new ob(b)).i();a||(a={});return a.noBuffer?c:Bb(c)}function xb(b,a,c){process.nextTick(function(){var d,e;try{e=yb(b,c)}catch(f){d=f}a(d,e)})}function yb(b,a){var c;b.subarray=b.slice;c=(new Ba(b)).h();a||(a={});return a.H?c:Bb(c)}function zb(b,a,c){process.nextTick(function(){var d,e;try{e=Ab(b,c)}catch(f){d=f}a(d,e)})}function Ab(b,a){var c;b.subarray=b.slice;c=(new mb(b)).i();a||(a={});return a.H?c:Bb(c)}\nfunction Bb(b){var a=new Buffer(b.length),c,d;c=0;for(d=b.length;c<d;++c)a[c]=b[c];return a};}).call(this);\n", "const { set, get, del } = require('idb-keyval');\n\nmodule.exports = {\n  readCache: get,\n  writeCache: set,\n  deleteCache: del,\n  checkCache: (path) => (\n    get(path).then((v) => typeof v !== 'undefined')\n  ),\n};\n", "class Store {\r\n    constructor(dbName = 'keyval-store', storeName = 'keyval') {\r\n        this.storeName = storeName;\r\n        this._dbp = new Promise((resolve, reject) => {\r\n            const openreq = indexedDB.open(dbName, 1);\r\n            openreq.onerror = () => reject(openreq.error);\r\n            openreq.onsuccess = () => resolve(openreq.result);\r\n            // First time setup: create an empty object store\r\n            openreq.onupgradeneeded = () => {\r\n                openreq.result.createObjectStore(storeName);\r\n            };\r\n        });\r\n    }\r\n    _withIDBStore(type, callback) {\r\n        return this._dbp.then(db => new Promise((resolve, reject) => {\r\n            const transaction = db.transaction(this.storeName, type);\r\n            transaction.oncomplete = () => resolve();\r\n            transaction.onabort = transaction.onerror = () => reject(transaction.error);\r\n            callback(transaction.objectStore(this.storeName));\r\n        }));\r\n    }\r\n}\r\nlet store;\r\nfunction getDefaultStore() {\r\n    if (!store)\r\n        store = new Store();\r\n    return store;\r\n}\r\nfunction get(key, store = getDefaultStore()) {\r\n    let req;\r\n    return store._withIDBStore('readonly', store => {\r\n        req = store.get(key);\r\n    }).then(() => req.result);\r\n}\r\nfunction set(key, value, store = getDefaultStore()) {\r\n    return store._withIDBStore('readwrite', store => {\r\n        store.put(value, key);\r\n    });\r\n}\r\nfunction del(key, store = getDefaultStore()) {\r\n    return store._withIDBStore('readwrite', store => {\r\n        store.delete(key);\r\n    });\r\n}\r\nfunction clear(store = getDefaultStore()) {\r\n    return store._withIDBStore('readwrite', store => {\r\n        store.clear();\r\n    });\r\n}\r\nfunction keys(store = getDefaultStore()) {\r\n    const keys = [];\r\n    return store._withIDBStore('readonly', store => {\r\n        // This would be store.getAllKeys(), but it isn't supported by Edge or Safari.\r\n        // And openKeyCursor isn't supported by Safari.\r\n        (store.openKeyCursor || store.openCursor).call(store).onsuccess = function () {\r\n            if (!this.result)\r\n                return;\r\n            keys.push(this.result.key);\r\n            this.result.continue();\r\n        };\r\n    }).then(() => keys);\r\n}\n\nexport { Store, get, set, del, clear, keys };\n"], "sourceRoot": ""}