!function(t){var e={};function r(i){if(e[i])return e[i].exports;var n=e[i]={i:i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=t,r.c=e,r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(i,n,function(e){return t[e]}.bind(null,n));return i},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=4)}([function(t,e,r){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var i=r(8),n=r(9),o=r(10);function a(){return f.TYPED_ARRAY_SUPPORT?**********:**********}function s(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=f.prototype:(null===t&&(t=new f(e)),t.length=e),t}function f(t,e,r){if(!(f.TYPED_ARRAY_SUPPORT||this instanceof f))return new f(t,e,r);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return u(this,t,e,r)}function u(t,e,r,i){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,r,i){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(i||0))throw new RangeError("'length' is out of bounds");e=void 0===r&&void 0===i?new Uint8Array(e):void 0===i?new Uint8Array(e,r):new Uint8Array(e,r,i);f.TYPED_ARRAY_SUPPORT?(t=e).__proto__=f.prototype:t=p(t,e);return t}(t,e,r,i):"string"==typeof e?function(t,e,r){"string"==typeof r&&""!==r||(r="utf8");if(!f.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var i=0|d(e,r),n=(t=s(t,i)).write(e,r);n!==i&&(t=t.slice(0,n));return t}(t,e,r):function(t,e){if(f.isBuffer(e)){var r=0|l(e.length);return 0===(t=s(t,r)).length||e.copy(t,0,0,r),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(i=e.length)!=i?s(t,0):p(t,e);if("Buffer"===e.type&&o(e.data))return p(t,e.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function h(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function c(t,e){if(h(e),t=s(t,e<0?0:0|l(e)),!f.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}function p(t,e){var r=e.length<0?0:0|l(e.length);t=s(t,r);for(var i=0;i<r;i+=1)t[i]=255&e[i];return t}function l(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function d(t,e){if(f.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var r=t.length;if(0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return N(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return Y(t).length;default:if(i)return N(t).length;e=(""+e).toLowerCase(),i=!0}}function m(t,e,r){var i=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return T(this,e,r);case"utf8":case"utf-8":return k(this,e,r);case"ascii":return U(this,e,r);case"latin1":case"binary":return I(this,e,r);case"base64":return S(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return O(this,e,r);default:if(i)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),i=!0}}function g(t,e,r){var i=t[e];t[e]=t[r],t[r]=i}function y(t,e,r,i,n){if(0===t.length)return-1;if("string"==typeof r?(i=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=n?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(n)return-1;r=t.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof e&&(e=f.from(e,i)),f.isBuffer(e))return 0===e.length?-1:b(t,e,r,i,n);if("number"==typeof e)return e&=255,f.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):b(t,[e],r,i,n);throw new TypeError("val must be string, number or Buffer")}function b(t,e,r,i,n){var o,a=1,s=t.length,f=e.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(t.length<2||e.length<2)return-1;a=2,s/=2,f/=2,r/=2}function u(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(n){var h=-1;for(o=r;o<s;o++)if(u(t,o)===u(e,-1===h?0:o-h)){if(-1===h&&(h=o),o-h+1===f)return h*a}else-1!==h&&(o-=o-h),h=-1}else for(r+f>s&&(r=s-f),o=r;o>=0;o--){for(var c=!0,p=0;p<f;p++)if(u(t,o+p)!==u(e,p)){c=!1;break}if(c)return o}return-1}function v(t,e,r,i){r=Number(r)||0;var n=t.length-r;i?(i=Number(i))>n&&(i=n):i=n;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");i>o/2&&(i=o/2);for(var a=0;a<i;++a){var s=parseInt(e.substr(2*a,2),16);if(isNaN(s))return a;t[r+a]=s}return a}function w(t,e,r,i){return F(N(e,t.length-r),t,r,i)}function x(t,e,r,i){return F(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(e),t,r,i)}function A(t,e,r,i){return x(t,e,r,i)}function _(t,e,r,i){return F(Y(e),t,r,i)}function E(t,e,r,i){return F(function(t,e){for(var r,i,n,o=[],a=0;a<t.length&&!((e-=2)<0);++a)r=t.charCodeAt(a),i=r>>8,n=r%256,o.push(n),o.push(i);return o}(e,t.length-r),t,r,i)}function S(t,e,r){return 0===e&&r===t.length?i.fromByteArray(t):i.fromByteArray(t.slice(e,r))}function k(t,e,r){r=Math.min(t.length,r);for(var i=[],n=e;n<r;){var o,a,s,f,u=t[n],h=null,c=u>239?4:u>223?3:u>191?2:1;if(n+c<=r)switch(c){case 1:u<128&&(h=u);break;case 2:128==(192&(o=t[n+1]))&&(f=(31&u)<<6|63&o)>127&&(h=f);break;case 3:o=t[n+1],a=t[n+2],128==(192&o)&&128==(192&a)&&(f=(15&u)<<12|(63&o)<<6|63&a)>2047&&(f<55296||f>57343)&&(h=f);break;case 4:o=t[n+1],a=t[n+2],s=t[n+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(f=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&f<1114112&&(h=f)}null===h?(h=65533,c=1):h>65535&&(h-=65536,i.push(h>>>10&1023|55296),h=56320|1023&h),i.push(h),n+=c}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var r="",i=0;for(;i<e;)r+=String.fromCharCode.apply(String,t.slice(i,i+=4096));return r}(i)}e.Buffer=f,e.SlowBuffer=function(t){+t!=t&&(t=0);return f.alloc(+t)},e.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=a(),f.poolSize=8192,f._augment=function(t){return t.__proto__=f.prototype,t},f.from=function(t,e,r){return u(null,t,e,r)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(t,e,r){return function(t,e,r,i){return h(e),e<=0?s(t,e):void 0!==r?"string"==typeof i?s(t,e).fill(r,i):s(t,e).fill(r):s(t,e)}(null,t,e,r)},f.allocUnsafe=function(t){return c(null,t)},f.allocUnsafeSlow=function(t){return c(null,t)},f.isBuffer=function(t){return!(null==t||!t._isBuffer)},f.compare=function(t,e){if(!f.isBuffer(t)||!f.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var r=t.length,i=e.length,n=0,o=Math.min(r,i);n<o;++n)if(t[n]!==e[n]){r=t[n],i=e[n];break}return r<i?-1:i<r?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return f.alloc(0);var r;if(void 0===e)for(e=0,r=0;r<t.length;++r)e+=t[r].length;var i=f.allocUnsafe(e),n=0;for(r=0;r<t.length;++r){var a=t[r];if(!f.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(i,n),n+=a.length}return i},f.byteLength=d,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},f.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},f.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},f.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?k(this,0,t):m.apply(this,arguments)},f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===f.compare(this,t)},f.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(t+=" ... ")),"<Buffer "+t+">"},f.prototype.compare=function(t,e,r,i,n){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),e<0||r>t.length||i<0||n>this.length)throw new RangeError("out of range index");if(i>=n&&e>=r)return 0;if(i>=n)return-1;if(e>=r)return 1;if(this===t)return 0;for(var o=(n>>>=0)-(i>>>=0),a=(r>>>=0)-(e>>>=0),s=Math.min(o,a),u=this.slice(i,n),h=t.slice(e,r),c=0;c<s;++c)if(u[c]!==h[c]){o=u[c],a=h[c];break}return o<a?-1:a<o?1:0},f.prototype.includes=function(t,e,r){return-1!==this.indexOf(t,e,r)},f.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},f.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)},f.prototype.write=function(t,e,r,i){if(void 0===e)i="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)i=e,r=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(r)?(r|=0,void 0===i&&(i="utf8")):(i=r,r=void 0)}var n=this.length-e;if((void 0===r||r>n)&&(r=n),t.length>0&&(r<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var o=!1;;)switch(i){case"hex":return v(this,t,e,r);case"utf8":case"utf-8":return w(this,t,e,r);case"ascii":return x(this,t,e,r);case"latin1":case"binary":return A(this,t,e,r);case"base64":return _(this,t,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,t,e,r);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function U(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(127&t[n]);return i}function I(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(t[n]);return i}function T(t,e,r){var i=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>i)&&(r=i);for(var n="",o=e;o<r;++o)n+=M(t[o]);return n}function O(t,e,r){for(var i=t.slice(e,r),n="",o=0;o<i.length;o+=2)n+=String.fromCharCode(i[o]+256*i[o+1]);return n}function P(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function j(t,e,r,i,n,o){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<o)throw new RangeError('"value" argument is out of bounds');if(r+i>t.length)throw new RangeError("Index out of range")}function B(t,e,r,i){e<0&&(e=65535+e+1);for(var n=0,o=Math.min(t.length-r,2);n<o;++n)t[r+n]=(e&255<<8*(i?n:1-n))>>>8*(i?n:1-n)}function L(t,e,r,i){e<0&&(e=4294967295+e+1);for(var n=0,o=Math.min(t.length-r,4);n<o;++n)t[r+n]=e>>>8*(i?n:3-n)&255}function R(t,e,r,i,n,o){if(r+i>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function C(t,e,r,i,o){return o||R(t,0,r,4),n.write(t,e,r,i,23,4),r+4}function z(t,e,r,i,o){return o||R(t,0,r,8),n.write(t,e,r,i,52,8),r+8}f.prototype.slice=function(t,e){var r,i=this.length;if((t=~~t)<0?(t+=i)<0&&(t=0):t>i&&(t=i),(e=void 0===e?i:~~e)<0?(e+=i)<0&&(e=0):e>i&&(e=i),e<t&&(e=t),f.TYPED_ARRAY_SUPPORT)(r=this.subarray(t,e)).__proto__=f.prototype;else{var n=e-t;r=new f(n,void 0);for(var o=0;o<n;++o)r[o]=this[o+t]}return r},f.prototype.readUIntLE=function(t,e,r){t|=0,e|=0,r||P(t,e,this.length);for(var i=this[t],n=1,o=0;++o<e&&(n*=256);)i+=this[t+o]*n;return i},f.prototype.readUIntBE=function(t,e,r){t|=0,e|=0,r||P(t,e,this.length);for(var i=this[t+--e],n=1;e>0&&(n*=256);)i+=this[t+--e]*n;return i},f.prototype.readUInt8=function(t,e){return e||P(t,1,this.length),this[t]},f.prototype.readUInt16LE=function(t,e){return e||P(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUInt16BE=function(t,e){return e||P(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUInt32LE=function(t,e){return e||P(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},f.prototype.readUInt32BE=function(t,e){return e||P(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readIntLE=function(t,e,r){t|=0,e|=0,r||P(t,e,this.length);for(var i=this[t],n=1,o=0;++o<e&&(n*=256);)i+=this[t+o]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*e)),i},f.prototype.readIntBE=function(t,e,r){t|=0,e|=0,r||P(t,e,this.length);for(var i=e,n=1,o=this[t+--i];i>0&&(n*=256);)o+=this[t+--i]*n;return o>=(n*=128)&&(o-=Math.pow(2,8*e)),o},f.prototype.readInt8=function(t,e){return e||P(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},f.prototype.readInt16LE=function(t,e){e||P(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt16BE=function(t,e){e||P(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},f.prototype.readInt32LE=function(t,e){return e||P(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return e||P(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readFloatLE=function(t,e){return e||P(t,4,this.length),n.read(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return e||P(t,4,this.length),n.read(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return e||P(t,8,this.length),n.read(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return e||P(t,8,this.length),n.read(this,t,!1,52,8)},f.prototype.writeUIntLE=function(t,e,r,i){(t=+t,e|=0,r|=0,i)||j(this,t,e,r,Math.pow(2,8*r)-1,0);var n=1,o=0;for(this[e]=255&t;++o<r&&(n*=256);)this[e+o]=t/n&255;return e+r},f.prototype.writeUIntBE=function(t,e,r,i){(t=+t,e|=0,r|=0,i)||j(this,t,e,r,Math.pow(2,8*r)-1,0);var n=r-1,o=1;for(this[e+n]=255&t;--n>=0&&(o*=256);)this[e+n]=t/o&255;return e+r},f.prototype.writeUInt8=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,1,255,0),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},f.prototype.writeUInt16LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):B(this,t,e,!0),e+2},f.prototype.writeUInt16BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):B(this,t,e,!1),e+2},f.prototype.writeUInt32LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):L(this,t,e,!0),e+4},f.prototype.writeUInt32BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},f.prototype.writeIntLE=function(t,e,r,i){if(t=+t,e|=0,!i){var n=Math.pow(2,8*r-1);j(this,t,e,r,n-1,-n)}var o=0,a=1,s=0;for(this[e]=255&t;++o<r&&(a*=256);)t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},f.prototype.writeIntBE=function(t,e,r,i){if(t=+t,e|=0,!i){var n=Math.pow(2,8*r-1);j(this,t,e,r,n-1,-n)}var o=r-1,a=1,s=0;for(this[e+o]=255&t;--o>=0&&(a*=256);)t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+r},f.prototype.writeInt8=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,1,127,-128),f.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},f.prototype.writeInt16LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):B(this,t,e,!0),e+2},f.prototype.writeInt16BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):B(this,t,e,!1),e+2},f.prototype.writeInt32LE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,**********,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):L(this,t,e,!0),e+4},f.prototype.writeInt32BE=function(t,e,r){return t=+t,e|=0,r||j(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),f.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):L(this,t,e,!1),e+4},f.prototype.writeFloatLE=function(t,e,r){return C(this,t,e,!0,r)},f.prototype.writeFloatBE=function(t,e,r){return C(this,t,e,!1,r)},f.prototype.writeDoubleLE=function(t,e,r){return z(this,t,e,!0,r)},f.prototype.writeDoubleBE=function(t,e,r){return z(this,t,e,!1,r)},f.prototype.copy=function(t,e,r,i){if(r||(r=0),i||0===i||(i=this.length),e>=t.length&&(e=t.length),e||(e=0),i>0&&i<r&&(i=r),i===r)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-e<i-r&&(i=t.length-e+r);var n,o=i-r;if(this===t&&r<e&&e<i)for(n=o-1;n>=0;--n)t[n+e]=this[n+r];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(n=0;n<o;++n)t[n+e]=this[n+r];else Uint8Array.prototype.set.call(t,this.subarray(r,r+o),e);return o},f.prototype.fill=function(t,e,r,i){if("string"==typeof t){if("string"==typeof e?(i=e,e=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),1===t.length){var n=t.charCodeAt(0);n<256&&(t=n)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!f.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<r)throw new RangeError("Out of range index");if(r<=e)return this;var o;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(o=e;o<r;++o)this[o]=t;else{var a=f.isBuffer(t)?t:N(new f(t,i).toString()),s=a.length;for(o=0;o<r-e;++o)this[o+e]=a[o%s]}return this};var D=/[^+\/0-9A-Za-z-_]/g;function M(t){return t<16?"0"+t.toString(16):t.toString(16)}function N(t,e){var r;e=e||1/0;for(var i=t.length,n=null,o=[],a=0;a<i;++a){if((r=t.charCodeAt(a))>55295&&r<57344){if(!n){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(a+1===i){(e-=3)>-1&&o.push(239,191,189);continue}n=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(e-=3)>-1&&o.push(239,191,189);if(n=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function Y(t){return i.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function F(t,e,r,i){for(var n=0;n<i&&!(n+r>=e.length||n>=t.length);++n)e[n+r]=t[n];return n}}).call(this,r(1))},function(t,e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"===("undefined"==typeof window?"undefined":r(window))&&(i=window)}t.exports=i},function(t,e){var r,i,n=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(t){i=a}}();var f,u=[],h=!1,c=-1;function p(){h&&f&&(h=!1,f.length?u=f.concat(u):c=-1,u.length&&l())}function l(){if(!h){var t=s(p);h=!0;for(var e=u.length;e;){for(f=u,u=[];++c<e;)f&&f[c].run();c=-1,e=u.length}f=null,h=!1,function(t){if(i===clearTimeout)return clearTimeout(t);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(t);try{i(t)}catch(e){try{return i.call(null,t)}catch(e){return i.call(this,t)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function m(){}n.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];u.push(new d(t,e)),1!==u.length||h||s(l)},d.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=m,n.addListener=m,n.once=m,n.off=m,n.removeListener=m,n.removeAllListeners=m,n.emit=m,n.prependListener=m,n.prependOnceListener=m,n.listeners=function(t){return[]},n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},function(module,exports,__webpack_require__){"use strict";(function(Buffer){function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance")}function _iterableToArray(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}function ownKeys(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach((function(e){_defineProperty(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var _require=__webpack_require__(11),multiByteIndexOf=_require.multiByteIndexOf,stringToBytes=_require.stringToBytes,readUInt64LE=_require.readUInt64LE,tarHeaderChecksumMatches=_require.tarHeaderChecksumMatches,uint8ArrayUtf8ByteString=_require.uint8ArrayUtf8ByteString,supported=__webpack_require__(12),xpiZipFilename=stringToBytes("META-INF/mozilla.rsa"),oxmlContentTypes=stringToBytes("[Content_Types].xml"),oxmlRels=stringToBytes("_rels/.rels"),fileType=function(t){if(!(t instanceof Uint8Array||t instanceof ArrayBuffer||Buffer.isBuffer(t)))throw new TypeError("Expected the `input` argument to be of type `Uint8Array` or `Buffer` or `ArrayBuffer`, got `".concat(_typeof(t),"`"));var e=t instanceof Uint8Array?t:new Uint8Array(t);if(e&&e.length>1){var r=function(t,r){r=_objectSpread({offset:0},r);for(var i=0;i<t.length;i++)if(r.mask){if(t[i]!==(r.mask[i]&e[i+r.offset]))return!1}else if(t[i]!==e[i+r.offset])return!1;return!0},i=function(t,e){return r(stringToBytes(t),e)};if(r([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(r([137,80,78,71,13,10,26,10])){var n=e.findIndex((function(t,r){return r>=33&&73===e[r]&&68===e[r+1]&&65===e[r+2]&&84===e[r+3]})),o=e.subarray(33,n);return o.findIndex((function(t,e){return 97===o[e]&&99===o[e+1]&&84===o[e+2]&&76===o[e+3]}))>=0?{ext:"apng",mime:"image/apng"}:{ext:"png",mime:"image/png"}}if(r([71,73,70]))return{ext:"gif",mime:"image/gif"};if(r([87,69,66,80],{offset:8}))return{ext:"webp",mime:"image/webp"};if(r([70,76,73,70]))return{ext:"flif",mime:"image/flif"};if((r([73,73,42,0])||r([77,77,0,42]))&&r([67,82],{offset:8}))return{ext:"cr2",mime:"image/x-canon-cr2"};if(r([73,73,82,79,8,0,0,0,24]))return{ext:"orf",mime:"image/x-olympus-orf"};if(r([73,73,42,0])&&(r([16,251,134,1],{offset:4})||r([8,0,0,0],{offset:4}))&&r([0,254,0,4,0,1,0,0,0,1,0,0,0,3,1],{offset:9}))return{ext:"arw",mime:"image/x-sony-arw"};if(r([73,73,42,0,8,0,0,0])&&(r([45,0,254,0],{offset:8})||r([39,0,254,0],{offset:8})))return{ext:"dng",mime:"image/x-adobe-dng"};if(r([73,73,42,0])&&r([28,0,254,0],{offset:8}))return{ext:"nef",mime:"image/x-nikon-nef"};if(r([73,73,85,0,24,0,0,0,136,231,116,216]))return{ext:"rw2",mime:"image/x-panasonic-rw2"};if(i("FUJIFILMCCD-RAW"))return{ext:"raf",mime:"image/x-fujifilm-raf"};if(r([73,73,42,0])||r([77,77,0,42]))return{ext:"tif",mime:"image/tiff"};if(r([66,77]))return{ext:"bmp",mime:"image/bmp"};if(r([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(r([56,66,80,83]))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};var a=[80,75,3,4];if(r(a)){if(r([109,105,109,101,116,121,112,101,97,112,112,108,105,99,97,116,105,111,110,47,101,112,117,98,43,122,105,112],{offset:30}))return{ext:"epub",mime:"application/epub+zip"};if(r(xpiZipFilename,{offset:30}))return{ext:"xpi",mime:"application/x-xpinstall"};if(i("mimetypeapplication/vnd.oasis.opendocument.text",{offset:30}))return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};if(i("mimetypeapplication/vnd.oasis.opendocument.spreadsheet",{offset:30}))return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};if(i("mimetypeapplication/vnd.oasis.opendocument.presentation",{offset:30}))return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"};var s,f=0,u=!1;do{var h=f+30;if(u||(u=r(oxmlContentTypes,{offset:h})||r(oxmlRels,{offset:h})),s||(i("word/",{offset:h})?s={ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}:i("ppt/",{offset:h})?s={ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"}:i("xl/",{offset:h})&&(s={ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})),u&&s)return s;f=multiByteIndexOf(e,a,h)}while(f>=0);if(s)return s}if(r([80,75])&&(3===e[2]||5===e[2]||7===e[2])&&(4===e[3]||6===e[3]||8===e[3]))return{ext:"zip",mime:"application/zip"};if(r([48,48,48,48,48,48],{offset:148,mask:[248,248,248,248,248,248]})&&tarHeaderChecksumMatches(e))return{ext:"tar",mime:"application/x-tar"};if(r([82,97,114,33,26,7])&&(0===e[6]||1===e[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(r([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(r([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(r([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(r([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(r([102,114,101,101],{offset:4})||r([109,100,97,116],{offset:4})||r([109,111,111,118],{offset:4})||r([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(r([102,116,121,112],{offset:4})&&0!=(96&e[8])&&0!=(96&e[9])&&0!=(96&e[10])&&0!=(96&e[11])){var c=uint8ArrayUtf8ByteString(e,8,12);switch(c){case"mif1":return{ext:"heic",mime:"image/heif"};case"msf1":return{ext:"heic",mime:"image/heif-sequence"};case"heic":case"heix":return{ext:"heic",mime:"image/heic"};case"hevc":case"hevx":return{ext:"heic",mime:"image/heic-sequence"};case"qt  ":return{ext:"mov",mime:"video/quicktime"};case"M4V ":case"M4VH":case"M4VP":return{ext:"m4v",mime:"video/x-m4v"};case"M4P ":return{ext:"m4p",mime:"video/mp4"};case"M4B ":return{ext:"m4b",mime:"audio/mp4"};case"M4A ":return{ext:"m4a",mime:"audio/x-m4a"};case"F4V ":return{ext:"f4v",mime:"video/mp4"};case"F4P ":return{ext:"f4p",mime:"video/mp4"};case"F4A ":return{ext:"f4a",mime:"audio/mp4"};case"F4B ":return{ext:"f4b",mime:"audio/mp4"};default:return c.startsWith("3g")?c.startsWith("3g2")?{ext:"3g2",mime:"video/3gpp2"}:{ext:"3gp",mime:"video/3gpp"}:{ext:"mp4",mime:"video/mp4"}}}if(r([77,84,104,100]))return{ext:"mid",mime:"audio/midi"};if(r([26,69,223,163])){var p=e.subarray(4,4100),l=p.findIndex((function(t,e,r){return 66===r[e]&&130===r[e+1]}));if(-1!==l){var d=l+3,m=function(t){return _toConsumableArray(t).every((function(t,e){return p[d+e]===t.charCodeAt(0)}))};if(m("matroska"))return{ext:"mkv",mime:"video/x-matroska"};if(m("webm"))return{ext:"webm",mime:"video/webm"}}}if(r([82,73,70,70])){if(r([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(r([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(r([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(r([48,38,178,117,142,102,207,17,166,217])){var g=30;do{var y=readUInt64LE(e,g+16);if(r([145,7,220,183,183,169,207,17,142,230,0,192,12,32,83,101],{offset:g})){if(r([64,158,105,248,77,91,207,17,168,253,0,128,95,92,68,43],{offset:g+24}))return{ext:"wma",mime:"audio/x-ms-wma"};if(r([192,239,25,188,77,91,207,17,168,253,0,128,95,92,68,43],{offset:g+24}))return{ext:"wmv",mime:"video/x-ms-asf"};break}g+=y}while(g+24<=e.length);return{ext:"asf",mime:"application/vnd.ms-asf"}}if(r([0,0,1,186])||r([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};for(var b=0;b<2&&b<e.length-16;b++){if(r([73,68,51],{offset:b})||r([255,226],{offset:b,mask:[255,230]}))return{ext:"mp3",mime:"audio/mpeg"};if(r([255,228],{offset:b,mask:[255,230]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,248],{offset:b,mask:[255,252]}))return{ext:"mp2",mime:"audio/mpeg"};if(r([255,240],{offset:b,mask:[255,252]}))return{ext:"mp4",mime:"audio/mpeg"}}if(r([79,112,117,115,72,101,97,100],{offset:28}))return{ext:"opus",mime:"audio/opus"};if(r([79,103,103,83]))return r([128,116,104,101,111,114,97],{offset:28})?{ext:"ogv",mime:"video/ogg"}:r([1,118,105,100,101,111,0],{offset:28})?{ext:"ogm",mime:"video/ogg"}:r([127,70,76,65,67],{offset:28})?{ext:"oga",mime:"audio/ogg"}:r([83,112,101,101,120,32,32],{offset:28})?{ext:"spx",mime:"audio/ogg"}:r([1,118,111,114,98,105,115],{offset:28})?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"};if(r([102,76,97,67]))return{ext:"flac",mime:"audio/x-flac"};if(r([77,65,67,32]))return{ext:"ape",mime:"audio/ape"};if(r([119,118,112,107]))return{ext:"wv",mime:"audio/wavpack"};if(r([35,33,65,77,82,10]))return{ext:"amr",mime:"audio/amr"};if(r([37,80,68,70]))return{ext:"pdf",mime:"application/pdf"};if(r([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if((67===e[0]||70===e[0])&&r([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(r([123,92,114,116,102]))return{ext:"rtf",mime:"application/rtf"};if(r([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(r([119,79,70,70])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff",mime:"font/woff"};if(r([119,79,70,50])&&(r([0,1,0,0],{offset:4})||r([79,84,84,79],{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(r([76,80],{offset:34})&&(r([0,0,1],{offset:8})||r([1,0,2],{offset:8})||r([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(r([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(r([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(r([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(r([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(r([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(r([37,33]))return{ext:"ps",mime:"application/postscript"};if(r([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(r([83,81,76,105]))return{ext:"sqlite",mime:"application/x-sqlite3"};if(r([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(r([67,114,50,52]))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(r([77,83,67,70])||r([73,83,99,40]))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(r([33,60,97,114,99,104,62,10,100,101,98,105,97,110,45,98,105,110,97,114,121]))return{ext:"deb",mime:"application/x-deb"};if(r([33,60,97,114,99,104,62]))return{ext:"ar",mime:"application/x-unix-archive"};if(r([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(r([31,160])||r([31,157]))return{ext:"Z",mime:"application/x-compress"};if(r([76,90,73,80]))return{ext:"lz",mime:"application/x-lzip"};if(r([208,207,17,224,161,177,26,225,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,62]))return{ext:"msi",mime:"application/x-msi"};if(r([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(r([71],{offset:4})&&(r([71],{offset:192})||r([71],{offset:196})))return{ext:"mts",mime:"video/mp2t"};if(r([66,76,69,78,68,69,82]))return{ext:"blend",mime:"application/x-blender"};if(r([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(r([0,0,0,12,106,80,32,32,13,10,135,10])){if(r([106,112,50,32],{offset:20}))return{ext:"jp2",mime:"image/jp2"};if(r([106,112,120,32],{offset:20}))return{ext:"jpx",mime:"image/jpx"};if(r([106,112,109,32],{offset:20}))return{ext:"jpm",mime:"image/jpm"};if(r([109,106,112,50],{offset:20}))return{ext:"mj2",mime:"image/mj2"}}return r([70,79,82,77])?{ext:"aif",mime:"audio/aiff"}:i("<?xml ")?{ext:"xml",mime:"application/xml"}:r([66,79,79,75,77,79,66,73],{offset:60})?{ext:"mobi",mime:"application/x-mobipocket-ebook"}:r([171,75,84,88,32,49,49,187,13,10,26,10])?{ext:"ktx",mime:"image/ktx"}:r([68,73,67,77],{offset:128})?{ext:"dcm",mime:"application/dicom"}:r([77,80,43])||r([77,80,67,75])?{ext:"mpc",mime:"audio/x-musepack"}:r([66,69,71,73,78,58])?{ext:"ics",mime:"text/calendar"}:r([103,108,84,70,2,0,0,0])?{ext:"glb",mime:"model/gltf-binary"}:r([212,195,178,161])||r([161,178,195,212])?{ext:"pcap",mime:"application/vnd.tcpdump.pcap"}:r([68,83,68,32])?{ext:"dsf",mime:"audio/x-dsf"}:r([76,0,0,0,1,20,2,0,0,0,0,0,192,0,0,0,0,0,0,70])?{ext:"lnk",mime:"application/x.ms.shortcut"}:r([98,111,111,107,0,0,0,0,109,97,114,107,0,0,0,0])?{ext:"alias",mime:"application/x.apple.alias"}:i("Creative Voice File")?{ext:"voc",mime:"audio/x-voc"}:r([11,119])?{ext:"ac3",mime:"audio/vnd.dolby.dd-raw"}:(r([126,16,4])||r([126,24,4]))&&r([48,77,73,69],{offset:4})?{ext:"mie",mime:"application/x-mie"}:r([65,82,82,79,87,49,0,0])?{ext:"arrow",mime:"application/x-apache-arrow"}:r([39,10,0,0,0,0,0,0,0,0,0,0],{offset:2})?{ext:"shp",mime:"application/x-esri-shape"}:void 0}};module.exports=fileType,Object.defineProperty(fileType,"minimumBytes",{value:4100}),fileType.stream=function(readableStream){return new Promise((function(resolve,reject){var stream=eval("require")("stream");readableStream.on("error",reject),readableStream.once("readable",(function(){var t=new stream.PassThrough,e=readableStream.read(module.exports.minimumBytes)||readableStream.read();try{t.fileType=fileType(e)}catch(t){reject(t)}readableStream.unshift(e),stream.pipeline?resolve(stream.pipeline(readableStream,t,(function(){}))):resolve(readableStream.pipe(t))}))}))},Object.defineProperty(fileType,"extensions",{get:function(){return new Set(supported.extensions)}}),Object.defineProperty(fileType,"mimeTypes",{get:function(){return new Set(supported.mimeTypes)}})}).call(this,__webpack_require__(0).Buffer)},function(t,e,r){(function(t){function e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function i(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var n=r(5),o=r(24),a=r(25),s=r(27);t.addEventListener("message",(function(t){var e=t.data;n.dispatchHandlers(e,(function(t){return postMessage(t)}))})),n.setAdapter(function(t){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?e(Object(n),!0).forEach((function(e){i(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({getCore:o,gunzip:a,fetch:function(){}},s))}).call(this,r(1))},function(t,e,r){var i=this;function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t,e,r,i,n,o,a){try{var s=t[o](a),f=s.value}catch(t){return void r(t)}s.done?e(f):Promise.resolve(f).then(i,n)}function u(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var o=t.apply(e,r);function a(t){f(o,i,n,a,s,"next",t)}function s(t){f(o,i,n,a,s,"throw",t)}a(void 0)}))}}function h(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}r(6);var c,p,l=r(3),d=r(13),m=r(14),g="webworker"===r(15)("type"),y=r(17),b=r(21),v=r(23),w=v.log,x=v.setLogging,A=null,_={},E=b,S=function(t,e){var r=t.workerId,i=t.jobId,n=t.payload.options,o=n.corePath,a=n.logging;if(x(a),c)e.resolve({loaded:!0});else{var s=_.getCore(o,e);e.progress({workerId:r,status:"initializing tesseract",progress:0}),s({TesseractProgress:function(t){p.progress({workerId:r,jobId:i,status:"recognizing text",progress:Math.max(0,(t-30)/70)})}}).then((function(t){c=t,e.progress({workerId:r,status:"initialized tesseract",progress:1}),e.resolve({loaded:!0})}))}},k=function(t,e){var r,i=t.workerId,n=t.payload,o=n.method,a=n.args;w("[".concat(i,"]: FS.").concat(o," with args ").concat(a)),e.resolve((r=c.FS)[o].apply(r,h(a)))},U=function(){var t=u(regeneratorRuntime.mark((function t(e,r){var i,n,o,a,s,f,h,p,m,y,b;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=e.workerId,n=e.payload,o=n.langs,a=n.options,s=a.langPath,f=a.dataPath,h=a.cachePath,p=a.cacheMethod,m=a.gzip,y=void 0===m||m,b=function(){var t=u(regeneratorRuntime.mark((function t(e){var n,o,a,u,m,b,v;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n="string"==typeof e?e:e.code,o=["refresh","none"].includes(p)?function(){return Promise.resolve()}:_.readCache,a=null,t.prev=3,t.next=6,o("".concat(h||".","/").concat(n,".traineddata"));case 6:if(void 0===(u=t.sent)){t.next=13;break}w("[".concat(i,"]: Load ").concat(n,".traineddata from cache")),r.progress({workerId:i,status:"loading language traineddata (from cache)",progress:.5}),a=u,t.next=14;break;case 13:throw Error("Not found in cache");case 14:t.next=37;break;case 16:if(t.prev=16,t.t0=t.catch(3),w("[".concat(i,"]: Load ").concat(n,".traineddata from ").concat(s)),"string"!=typeof e){t.next=36;break}if(m=null,(d(s)||s.startsWith("moz-extension://")||s.startsWith("chrome-extension://")||s.startsWith("file://"))&&(m=s),null===m){t.next=31;break}return t.next=25,(g?fetch:_.fetch)("".concat(m,"/").concat(n,".traineddata").concat(y?".gz":""));case 25:return b=t.sent,t.next=28,b.arrayBuffer();case 28:a=t.sent,t.next=34;break;case 31:return t.next=33,_.readCache("".concat(s,"/").concat(n,".traineddata").concat(y?".gz":""));case 33:a=t.sent;case 34:t.next=37;break;case 36:a=e.data;case 37:if(a=new Uint8Array(a),void 0!==(v=l(a))&&"application/gzip"===v.mime&&(a=_.gunzip(a)),c){if(f)try{c.FS.mkdir(f)}catch(t){r.reject(t.toString())}c.FS.writeFile("".concat(f||".","/").concat(n,".traineddata"),a)}if(!["write","refresh",void 0].includes(p)){t.next=44;break}return t.next=44,_.writeCache("".concat(h||".","/").concat(n,".traineddata"),a);case 44:return t.abrupt("return",Promise.resolve(a));case 45:case"end":return t.stop()}}),t,null,[[3,16]])})));return function(e){return t.apply(this,arguments)}}(),r.progress({workerId:i,status:"loading language traineddata",progress:0}),t.prev=3,t.next=6,Promise.all(("string"==typeof o?o.split("+"):o).map(b));case 6:r.progress({workerId:i,status:"loaded language traineddata",progress:1}),r.resolve(o),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(3),g&&t.t0 instanceof DOMException||r.reject(t.t0.toString());case 13:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e,r){return t.apply(this,arguments)}}(),I=function(t,e){var r=t.payload.params;Object.keys(r).filter((function(t){return!t.startsWith("tessjs_")})).forEach((function(t){A.SetVariable(t,r[t])})),E=a({},E,{},r),void 0!==e&&e.resolve(E)},T=function(t,e){var r=t.workerId,i=t.payload,n=i.langs,o=i.oem,a="string"==typeof n?n:n.map((function(t){return"string"==typeof t?t:t.data})).join("+");try{e.progress({workerId:r,status:"initializing api",progress:0}),null!==A&&A.End(),(A=new c.TessBaseAPI).Init(null,a,o),I({payload:{params:E=b}}),e.progress({workerId:r,status:"initialized api",progress:1}),e.resolve()}catch(t){e.reject(t.toString())}},O=function(t,e){var r=t.payload,i=r.image,o=r.options.rectangle;try{var a=y(c,A,i);"object"===n(o)&&A.SetRectangle(o.left,o.top,o.width,o.height),A.Recognize(null),e.resolve(m(c,A,E)),c._free(a)}catch(t){e.reject(t.toString())}},P=function(t,e){var r=t.payload,i=r.title,n=r.textonly,o=new c.TessPDFRenderer("tesseract-ocr","/",n);o.BeginDocument(i),o.AddImage(A),o.EndDocument(),c._free(o),e.resolve(c.FS.readFile("/tesseract-ocr.pdf"))},j=function(t,e){var r=t.payload.image;try{var i=y(c,A,r),n=new c.OSResults;if(A.DetectOS(n)){var o=n.best_result,a=o.orientation_id,s=o.script_id;c._free(i),e.resolve({tesseract_script_id:s,script:n.unicharset.get_script_from_script_id(s),script_confidence:o.sconfidence,orientation_degrees:[0,270,180,90][a],orientation_confidence:o.oconfidence})}else A.End(),c._free(i),e.reject("Failed to detect OS")}catch(t){e.reject(t.toString())}},B=function(t,e){try{null!==A&&A.End(),e.resolve({terminated:!0})}catch(t){e.reject(t.toString())}};e.dispatchHandlers=function(t,e){var r=function(r,i){e(a({},t,{status:r,data:i}))};r.resolve=r.bind(i,"resolve"),r.reject=r.bind(i,"reject"),r.progress=r.bind(i,"progress"),p=r;try{({load:S,FS:k,loadLanguage:U,initialize:T,setParameters:I,recognize:O,getPDF:P,detect:j,terminate:B})[t.action](t,r)}catch(t){r.reject(t.toString())}},e.setAdapter=function(t){_=t}},function(t,e,r){(function(t){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r=function(t){"use strict";var r=Object.prototype,i=r.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",s=n.toStringTag||"@@toStringTag";function f(t,e,r,i){var n=e&&e.prototype instanceof c?e:c,o=Object.create(n.prototype),a=new _(i||[]);return o._invoke=function(t,e,r){var i="suspendedStart";return function(n,o){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===n)throw o;return S()}for(r.method=n,r.arg=o;;){var a=r.delegate;if(a){var s=w(a,r);if(s){if(s===h)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===i)throw i="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i="executing";var f=u(t,e,r);if("normal"===f.type){if(i=r.done?"completed":"suspendedYield",f.arg===h)continue;return{value:f.arg,done:r.done}}"throw"===f.type&&(i="completed",r.method="throw",r.arg=f.arg)}}}(t,r,a),o}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function c(){}function p(){}function l(){}var d={};d[o]=function(){return this};var m=Object.getPrototypeOf,g=m&&m(m(E([])));g&&g!==r&&i.call(g,o)&&(d=g);var y=l.prototype=c.prototype=Object.create(d);function b(t){["next","throw","return"].forEach((function(e){t[e]=function(t){return this._invoke(e,t)}}))}function v(t){var r;this._invoke=function(n,o){function a(){return new Promise((function(r,a){!function r(n,o,a,s){var f=u(t[n],t,o);if("throw"!==f.type){var h=f.arg,c=h.value;return c&&"object"===e(c)&&i.call(c,"__await")?Promise.resolve(c.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):Promise.resolve(c).then((function(t){h.value=t,a(h)}),(function(t){return r("throw",t,a,s)}))}s(f.arg)}(n,o,r,a)}))}return r=r?r.then(a,a):a()}}function w(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,w(t,e),"throw"===e.method))return h;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var i=u(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var n=i.arg;return n?n.done?(e[t.resultName]=n.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):n:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function x(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function E(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,n=function e(){for(;++r<t.length;)if(i.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return n.next=n}}return{next:S}}function S(){return{value:void 0,done:!0}}return p.prototype=y.constructor=l,l.constructor=p,l[s]=p.displayName="GeneratorFunction",t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,l):(t.__proto__=l,s in t||(t[s]="GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},b(v.prototype),v.prototype[a]=function(){return this},t.AsyncIterator=v,t.async=function(e,r,i,n){var o=new v(f(e,r,i,n));return t.isGeneratorFunction(r)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},b(y),y[s]="Generator",y[o]=function(){return this},y.toString=function(){return"[object Generator]"},t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var i=e.pop();if(i in t)return r.value=i,r.done=!1,r}return r.done=!0,r}},t.values=E,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(A),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,i){return a.type="throw",a.arg=t,e.next=r,i&&(e.method="next",e.arg=void 0),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),f=i.call(o,"finallyLoc");if(s&&f){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!f)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),A(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var i=r.completion;if("throw"===i.type){var n=i.arg;A(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:E(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}("object"===e(t)?t.exports:{});try{regeneratorRuntime=r}catch(t){Function("r","regeneratorRuntime = r")(r)}}).call(this,r(7)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e,r){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],i=e[1];return 3*(r+i)/4-i},e.toByteArray=function(t){var e,r,i=u(t),a=i[0],s=i[1],f=new o(function(t,e,r){return 3*(e+r)/4-r}(0,a,s)),h=0,c=s>0?a-4:a;for(r=0;r<c;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],f[h++]=e>>16&255,f[h++]=e>>8&255,f[h++]=255&e;2===s&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,f[h++]=255&e);1===s&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,f[h++]=e>>8&255,f[h++]=255&e);return f},e.fromByteArray=function(t){for(var e,r=t.length,n=r%3,o=[],a=0,s=r-n;a<s;a+=16383)o.push(h(t,a,a+16383>s?s:a+16383));1===n?(e=t[r-1],o.push(i[e>>2]+i[e<<4&63]+"==")):2===n&&(e=(t[r-2]<<8)+t[r-1],o.push(i[e>>10]+i[e>>4&63]+i[e<<2&63]+"="));return o.join("")};for(var i=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,f=a.length;s<f;++s)i[s]=a[s],n[a.charCodeAt(s)]=s;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");return-1===r&&(r=e),[r,r===e?0:4-r%4]}function h(t,e,r){for(var n,o,a=[],s=e;s<r;s+=3)n=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),a.push(i[(o=n)>>18&63]+i[o>>12&63]+i[o>>6&63]+i[63&o]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},function(t,e){e.read=function(t,e,r,i,n){var o,a,s=8*n-i-1,f=(1<<s)-1,u=f>>1,h=-7,c=r?n-1:0,p=r?-1:1,l=t[e+c];for(c+=p,o=l&(1<<-h)-1,l>>=-h,h+=s;h>0;o=256*o+t[e+c],c+=p,h-=8);for(a=o&(1<<-h)-1,o>>=-h,h+=i;h>0;a=256*a+t[e+c],c+=p,h-=8);if(0===o)o=1-u;else{if(o===f)return a?NaN:1/0*(l?-1:1);a+=Math.pow(2,i),o-=u}return(l?-1:1)*a*Math.pow(2,o-i)},e.write=function(t,e,r,i,n,o){var a,s,f,u=8*o-n-1,h=(1<<u)-1,c=h>>1,p=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,l=i?0:o-1,d=i?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=h):(a=Math.floor(Math.log(e)/Math.LN2),e*(f=Math.pow(2,-a))<1&&(a--,f*=2),(e+=a+c>=1?p/f:p*Math.pow(2,1-c))*f>=2&&(a++,f/=2),a+c>=h?(s=0,a=h):a+c>=1?(s=(e*f-1)*Math.pow(2,n),a+=c):(s=e*Math.pow(2,c-1)*Math.pow(2,n),a=0));n>=8;t[r+l]=255&s,l+=d,s/=256,n-=8);for(a=a<<n|s,u+=n;u>0;t[r+l]=255&a,l+=d,a/=256,u-=8);t[r+l-d]|=128*m}},function(t,e){var r={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==r.call(t)}},function(t,e,r){"use strict";(function(t){function r(t){return function(t){if(Array.isArray(t)){for(var e=0,r=new Array(t.length);e<t.length;e++)r[e]=t[e];return r}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}e.stringToBytes=function(t){return r(t).map((function(t){return t.charCodeAt(0)}))};var i=function(t,e,i){return String.fromCharCode.apply(String,r(t.slice(e,i)))};e.readUInt64LE=function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=t[e],i=1,n=0;++n<8;)i*=256,r+=t[e+n]*i;return r},e.tarHeaderChecksumMatches=function(t){if(t.length<512)return!1;for(var e=256,r=0,n=0;n<148;n++){var o=t[n];e+=o,r+=128&o}for(var a=156;a<512;a++){var s=t[a];e+=s,r+=128&s}var f=parseInt(i(t,148,154),8);return f===e||f===e-(r<<1)},e.multiByteIndexOf=function(e,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(t&&t.isBuffer(e))return e.indexOf(t.from(r),i);for(var n=function(t,e,r){for(var i=1;i<e.length;i++)if(e[i]!==t[r+i])return!1;return!0},o=e.indexOf(r[0],i);o>=0;){if(n(e,r,o))return o;o=e.indexOf(r[0],o+1)}return-1},e.uint8ArrayUtf8ByteString=i}).call(this,r(0).Buffer)},function(t,e,r){"use strict";t.exports={extensions:["jpg","png","apng","gif","webp","flif","cr2","orf","arw","dng","nef","rw2","raf","tif","bmp","jxr","psd","zip","tar","rar","gz","bz2","7z","dmg","mp4","mid","mkv","webm","mov","avi","mpg","mp2","mp3","m4a","oga","ogg","ogv","opus","flac","wav","spx","amr","pdf","epub","exe","swf","rtf","wasm","woff","woff2","eot","ttf","otf","ico","flv","ps","xz","sqlite","nes","crx","xpi","cab","deb","ar","rpm","Z","lz","msi","mxf","mts","blend","bpg","docx","pptx","xlsx","3gp","3g2","jp2","jpm","jpx","mj2","aif","qcp","odt","ods","odp","xml","mobi","heic","cur","ktx","ape","wv","wmv","wma","dcm","ics","glb","pcap","dsf","lnk","alias","voc","ac3","m4v","m4p","m4b","f4v","f4p","f4b","f4a","mie","asf","ogm","ogx","mpc","arrow","shp"],mimeTypes:["image/jpeg","image/png","image/gif","image/webp","image/flif","image/x-canon-cr2","image/tiff","image/bmp","image/vnd.ms-photo","image/vnd.adobe.photoshop","application/epub+zip","application/x-xpinstall","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.spreadsheet","application/vnd.oasis.opendocument.presentation","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/zip","application/x-tar","application/x-rar-compressed","application/gzip","application/x-bzip2","application/x-7z-compressed","application/x-apple-diskimage","application/x-apache-arrow","video/mp4","audio/midi","video/x-matroska","video/webm","video/quicktime","video/vnd.avi","audio/vnd.wave","audio/qcelp","audio/x-ms-wma","video/x-ms-asf","application/vnd.ms-asf","video/mpeg","video/3gpp","audio/mpeg","audio/mp4","audio/opus","video/ogg","audio/ogg","application/ogg","audio/x-flac","audio/ape","audio/wavpack","audio/amr","application/pdf","application/x-msdownload","application/x-shockwave-flash","application/rtf","application/wasm","font/woff","font/woff2","application/vnd.ms-fontobject","font/ttf","font/otf","image/x-icon","video/x-flv","application/postscript","application/x-xz","application/x-sqlite3","application/x-nintendo-nes-rom","application/x-google-chrome-extension","application/vnd.ms-cab-compressed","application/x-deb","application/x-unix-archive","application/x-rpm","application/x-compress","application/x-lzip","application/x-msi","application/x-mie","application/mxf","video/mp2t","application/x-blender","image/bpg","image/jp2","image/jpx","image/jpm","image/mj2","audio/aiff","application/xml","application/x-mobipocket-ebook","image/heif","image/heif-sequence","image/heic","image/heic-sequence","image/ktx","application/dicom","audio/x-musepack","text/calendar","model/gltf-binary","application/vnd.tcpdump.pcap","audio/x-dsf","application/x.ms.shortcut","application/x.apple.alias","audio/x-voc","audio/vnd.dolby.dd-raw","audio/x-m4a","image/apng","image/x-olympus-orf","image/x-sony-arw","image/x-adobe-dng","image/x-nikon-nef","image/x-panasonic-rw2","image/x-fujifilm-raf","video/x-m4v","video/3gpp2","application/x-esri-shape"]}},function(t,e){t.exports=function(t){if("string"!=typeof t)return!1;var e=t.match(r);if(!e)return!1;var o=e[1];if(!o)return!1;if(i.test(o)||n.test(o))return!0;return!1};var r=/^(?:\w+:)?\/\/(\S+)$/,i=/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/,n=/^[^\s\.]+\.\S{2,}$/},function(t,e){var r=function(t){var e=t.split("\n");if("  "===e[0].substring(0,2))for(var r=0;r<e.length;r+=1)"  "===e[r].substring(0,2)&&(e[r]=e[r].slice(2));return e.join("\n")};t.exports=function(t,e,i){var n,o,a,s,f,u=i.tessjs_create_hocr,h=i.tessjs_create_tsv,c=i.tessjs_create_box,p=i.tessjs_create_unlv,l=i.tessjs_create_osd,d=e.GetIterator(),m=t.RIL_BLOCK,g=t.RIL_PARA,y=t.RIL_TEXTLINE,b=t.RIL_WORD,v=t.RIL_SYMBOL,w=[],x=function(e,r){return Object.keys(t).filter((function(i){return i.startsWith("".concat(r,"_"))&&t[i]===e})).map((function(t){return t.slice(r.length+1)}))[0]};d.Begin();do{if(d.IsAtBeginningOf(m)){var A=d.BlockPolygon(),_=null;if(t.getPointer(A)>0){var E=A.get_n(),S=A.get_x(),k=A.get_y();_=[];for(var U=0;U<E;U+=1)_.push([S.getValue(U),k.getValue(U)])}n={paragraphs:[],text:d.GetUTF8Text(m),confidence:d.Confidence(m),baseline:d.getBaseline(m),bbox:d.getBoundingBox(m),blocktype:x(d.BlockType(),"PT"),polygon:_},w.push(n)}if(d.IsAtBeginningOf(g)&&(o={lines:[],text:d.GetUTF8Text(g),confidence:d.Confidence(g),baseline:d.getBaseline(g),bbox:d.getBoundingBox(g),is_ltr:!!d.ParagraphIsLtr()},n.paragraphs.push(o)),d.IsAtBeginningOf(y)&&(a={words:[],text:d.GetUTF8Text(y),confidence:d.Confidence(y),baseline:d.getBaseline(y),bbox:d.getBoundingBox(y)},o.lines.push(a)),d.IsAtBeginningOf(b)){var I=d.getWordFontAttributes(),T=d.WordDirection();s={symbols:[],choices:[],text:d.GetUTF8Text(b),confidence:d.Confidence(b),baseline:d.getBaseline(b),bbox:d.getBoundingBox(b),is_numeric:!!d.WordIsNumeric(),in_dictionary:!!d.WordIsFromDictionary(),direction:x(T,"DIR"),language:d.WordRecognitionLanguage(),is_bold:I.is_bold,is_italic:I.is_italic,is_underlined:I.is_underlined,is_monospace:I.is_monospace,is_serif:I.is_serif,is_smallcaps:I.is_smallcaps,font_size:I.pointsize,font_id:I.font_id,font_name:I.font_name};var O=new t.WordChoiceIterator(d);do{s.choices.push({text:O.GetUTF8Text(),confidence:O.Confidence()})}while(O.Next());t.destroy(O),a.words.push(s)}if(d.IsAtBeginningOf(v)){f={choices:[],image:null,text:d.GetUTF8Text(v),confidence:d.Confidence(v),baseline:d.getBaseline(v),bbox:d.getBoundingBox(v),is_superscript:!!d.SymbolIsSuperscript(),is_subscript:!!d.SymbolIsSubscript(),is_dropcap:!!d.SymbolIsDropcap()},s.symbols.push(f);var P=new t.ChoiceIterator(d);do{f.choices.push({text:P.GetUTF8Text(),confidence:P.Confidence()})}while(P.Next())}}while(d.Next(v));return t.destroy(d),{text:e.GetUTF8Text(),hocr:"1"===u?r(e.GetHOCRText()):null,tsv:"1"===h?e.GetTSVText():null,box:"1"===c?e.GetBoxText():null,unlv:"1"===p?e.GetUNLVText():null,osd:"1"===l?e.GetOsdText():null,confidence:e.MeanTextConf(),blocks:w,psm:x(e.GetPageSegMode(),"PSM"),oem:x(e.oem(),"OEM"),version:e.Version()}}},function(t,e,r){(function(e){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var n=r(16);t.exports=function(t){var r={};return"undefined"!=typeof WorkerGlobalScope?r.type="webworker":n()?r.type="electron":"object"===("undefined"==typeof window?"undefined":i(window))?r.type="browser":"object"===(void 0===e?"undefined":i(e))&&(r.type="node"),void 0===t?r:r[t]}}).call(this,r(2))},function(t,e,r){(function(e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t.exports=function(){return"undefined"!=typeof window&&"object"===r(window.process)&&"renderer"===window.process.type||(!(void 0===e||"object"!==r(e.versions)||!e.versions.electron)||"object"===("undefined"==typeof navigator?"undefined":r(navigator))&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron")>=0)}}).call(this,r(2))},function(t,e,r){(function(e){function i(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if(!(Symbol.iterator in Object(t))&&"[object Arguments]"!==Object.prototype.toString.call(t))return;var r=[],i=!0,n=!1,o=void 0;try{for(var a,s=t[Symbol.iterator]();!(i=(a=s.next()).done)&&(r.push(a.value),!e||r.length!==e);i=!0);}catch(t){n=!0,o=t}finally{try{i||null==s.return||s.return()}finally{if(n)throw o}}return r}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function o(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var a=r(18),s=r(3);t.exports=function(t,r,f){var u=e.from(Array.from(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},f,{length:Object.keys(f).length}))),h=s(u),c=0,p=null,l=null,d=0,m=0;if(h&&"image/bmp"===h.mime){var g=a.decode(u);p=t._malloc(g.data.length*Uint8Array.BYTES_PER_ELEMENT),t.HEAPU8.set(g.data,p),d=g.width,m=g.height,c=4}else{var y=t._malloc(u.length*Uint8Array.BYTES_PER_ELEMENT);t.HEAPU8.set(u,y),l=t._pixReadMem(y,u.length),0===t.getValue(l+28,"i32")&&t.setValue(l+28,300,"i32");var b=i(Array(2).fill(0).map((function(e,r){return t.getValue(l+4*r,"i32")})),2);d=b[0],m=b[1]}return null===p?r.SetImage(l):r.SetImage(p,d,m,c,d*c),null===p?l:p}}).call(this,r(0).Buffer)},function(t,e,r){var i=r(19),n=r(20);t.exports={encode:i,decode:n}},function(t,e,r){(function(e){function r(t){this.buffer=t.data,this.width=t.width,this.height=t.height,this.extraBytes=this.width%4,this.rgbSize=this.height*(3*this.width+this.extraBytes),this.headerInfoSize=40,this.data=[],this.flag="BM",this.reserved=0,this.offset=54,this.fileSize=this.rgbSize+this.offset,this.planes=1,this.bitPP=24,this.compress=0,this.hr=0,this.vr=0,this.colors=0,this.importantColors=0}r.prototype.encode=function(){var t=new e(this.offset+this.rgbSize);this.pos=0,t.write(this.flag,this.pos,2),this.pos+=2,t.writeUInt32LE(this.fileSize,this.pos),this.pos+=4,t.writeUInt32LE(this.reserved,this.pos),this.pos+=4,t.writeUInt32LE(this.offset,this.pos),this.pos+=4,t.writeUInt32LE(this.headerInfoSize,this.pos),this.pos+=4,t.writeUInt32LE(this.width,this.pos),this.pos+=4,t.writeInt32LE(-this.height,this.pos),this.pos+=4,t.writeUInt16LE(this.planes,this.pos),this.pos+=2,t.writeUInt16LE(this.bitPP,this.pos),this.pos+=2,t.writeUInt32LE(this.compress,this.pos),this.pos+=4,t.writeUInt32LE(this.rgbSize,this.pos),this.pos+=4,t.writeUInt32LE(this.hr,this.pos),this.pos+=4,t.writeUInt32LE(this.vr,this.pos),this.pos+=4,t.writeUInt32LE(this.colors,this.pos),this.pos+=4,t.writeUInt32LE(this.importantColors,this.pos),this.pos+=4;for(var r=0,i=3*this.width+this.extraBytes,n=0;n<this.height;n++){for(var o=0;o<this.width;o++){var a=this.pos+n*i+3*o;r++,t[a]=this.buffer[r++],t[a+1]=this.buffer[r++],t[a+2]=this.buffer[r++]}if(this.extraBytes>0){var s=this.pos+n*i+3*this.width;t.fill(0,s,s+this.extraBytes)}}return t},t.exports=function(t,e){return void 0===e&&(e=100),{data:new r(t).encode(),width:t.width,height:t.height}}}).call(this,r(0).Buffer)},function(t,e,r){(function(e){function r(t,e){if(this.pos=0,this.buffer=t,this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=this.buffer.toString("utf-8",0,this.pos+=2),"BM"!=this.flag)throw new Error("Invalid BMP File");this.parseHeader(),this.parseRGBA()}r.prototype.parseHeader=function(){if(this.fileSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.reserved=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.offset=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.headerSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.width=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.height=this.buffer.readInt32LE(this.pos),this.pos+=4,this.planes=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.bitPP=this.buffer.readUInt16LE(this.pos),this.pos+=2,this.compress=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.rawSize=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.hr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.vr=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.colors=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.importantColors=this.buffer.readUInt32LE(this.pos),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var r=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);this.palette[e]={red:n,green:i,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},r.prototype.parseRGBA=function(){var t="bit"+this.bitPP,r=this.width*this.height*4;this.data=new e(r),this[t]()},r.prototype.bit1=function(){var t=Math.ceil(this.width/8),e=t%4,r=this.height>=0?this.height-1:-this.height;for(r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,n=0;n<t;n++)for(var o=this.buffer.readUInt8(this.pos++),a=i*this.width*4+8*n*4,s=0;s<8&&8*n+s<this.width;s++){var f=this.palette[o>>7-s&1];this.data[a+4*s]=0,this.data[a+4*s+1]=f.blue,this.data[a+4*s+2]=f.green,this.data[a+4*s+3]=f.red}0!=e&&(this.pos+=4-e)}},r.prototype.bit4=function(){if(2==this.compress){var t=function(t){var r=this.palette[t];this.data[e]=0,this.data[e+1]=r.blue,this.data[e+2]=r.green,this.data[e+3]=r.red,e+=4};this.data.fill(255);for(var e=0,r=this.bottom_up?this.height-1:0,i=!1;e<this.data.length;){var n=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++);if(0==n){if(0==o){this.bottom_up?r--:r++,e=r*this.width*4,i=!1;continue}if(1==o)break;if(2==o){var a=this.buffer.readUInt8(this.pos++),s=this.buffer.readUInt8(this.pos++);this.bottom_up?r-=s:r+=s,e+=s*this.width*4+4*a}else{for(var f=this.buffer.readUInt8(this.pos++),u=0;u<o;u++)t.call(this,i?15&f:(240&f)>>4),1&u&&u+1<o&&(f=this.buffer.readUInt8(this.pos++)),i=!i;1==(o+1>>1&1)&&this.pos++}}else for(u=0;u<n;u++)t.call(this,i?15&o:(240&o)>>4),i=!i}}else{var h=Math.ceil(this.width/2),c=h%4;for(s=this.height-1;s>=0;s--){var p=this.bottom_up?s:this.height-1-s;for(a=0;a<h;a++){o=this.buffer.readUInt8(this.pos++),e=p*this.width*4+2*a*4;var l=o>>4,d=15&o,m=this.palette[l];if(this.data[e]=0,this.data[e+1]=m.blue,this.data[e+2]=m.green,this.data[e+3]=m.red,2*a+1>=this.width)break;m=this.palette[d],this.data[e+4]=0,this.data[e+4+1]=m.blue,this.data[e+4+2]=m.green,this.data[e+4+3]=m.red}0!=c&&(this.pos+=4-c)}}},r.prototype.bit8=function(){if(1==this.compress){var t=function(t){var r=this.palette[t];this.data[e]=0,this.data[e+1]=r.blue,this.data[e+2]=r.green,this.data[e+3]=r.red,e+=4};this.data.fill(255);for(var e=0,r=this.bottom_up?this.height-1:0;e<this.data.length;){var i=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++);if(0==i){if(0==n){this.bottom_up?r--:r++,e=r*this.width*4;continue}if(1==n)break;if(2==n){var o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++);this.bottom_up?r-=a:r+=a,e+=a*this.width*4+4*o}else{for(var s=0;s<n;s++){var f=this.buffer.readUInt8(this.pos++);t.call(this,f)}!0&n&&this.pos++}}else for(s=0;s<i;s++)t.call(this,n)}}else{var u=this.width%4;for(a=this.height-1;a>=0;a--){var h=this.bottom_up?a:this.height-1-a;for(o=0;o<this.width;o++){n=this.buffer.readUInt8(this.pos++),e=h*this.width*4+4*o;if(n<this.palette.length){var c=this.palette[n];this.data[e]=0,this.data[e+1]=c.blue,this.data[e+2]=c.green,this.data[e+3]=c.red}else this.data[e]=0,this.data[e+1]=255,this.data[e+2]=255,this.data[e+3]=255}0!=u&&(this.pos+=4-u)}}},r.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,n=0;n<this.width;n++){var o=this.buffer.readUInt16LE(this.pos);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,f=(o>>10&e)/e*255|0,u=o>>15?255:0,h=i*this.width*4+4*n;this.data[h]=u,this.data[h+1]=a,this.data[h+2]=s,this.data[h+3]=f}this.pos+=t}},r.prototype.bit16=function(){var t=this.width%2*2;this.maskRed=31744,this.maskGreen=992,this.maskBlue=31,this.mask0=0,3==this.compress&&(this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4);for(var e=[0,0,0],r=0;r<16;r++)this.maskRed>>r&1&&e[0]++,this.maskGreen>>r&1&&e[1]++,this.maskBlue>>r&1&&e[2]++;e[1]+=e[0],e[2]+=e[1],e[0]=8-e[0],e[1]-=8,e[2]-=8;for(var i=this.height-1;i>=0;i--){for(var n=this.bottom_up?i:this.height-1-i,o=0;o<this.width;o++){var a=this.buffer.readUInt16LE(this.pos);this.pos+=2;var s=(a&this.maskBlue)<<e[0],f=(a&this.maskGreen)>>e[1],u=(a&this.maskRed)>>e[2],h=n*this.width*4+4*o;this.data[h]=0,this.data[h+1]=s,this.data[h+2]=f,this.data[h+3]=u}this.pos+=t}},r.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var i=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=e*this.width*4+4*r;this.data[a]=0,this.data[a+1]=i,this.data[a+2]=n,this.data[a+3]=o}this.pos+=this.width%4}},r.prototype.bit32=function(){if(3==this.compress){this.maskRed=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskGreen=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.maskBlue=this.buffer.readUInt32LE(this.pos),this.pos+=4,this.mask0=this.buffer.readUInt32LE(this.pos),this.pos+=4;for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){var i=this.buffer.readUInt8(this.pos++),n=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=i,this.data[s+1]=n,this.data[s+2]=o,this.data[s+3]=a}}else for(t=this.height-1;t>=0;t--)for(e=this.bottom_up?t:this.height-1-t,r=0;r<this.width;r++){n=this.buffer.readUInt8(this.pos++),o=this.buffer.readUInt8(this.pos++),a=this.buffer.readUInt8(this.pos++),i=this.buffer.readUInt8(this.pos++),s=e*this.width*4+4*r;this.data[s]=i,this.data[s+1]=n,this.data[s+2]=o,this.data[s+3]=a}},r.prototype.getData=function(){return this.data},t.exports=function(t){return new r(t)}}).call(this,r(0).Buffer)},function(t,e,r){var i=r(22);t.exports={tessedit_pageseg_mode:i.SINGLE_BLOCK,tessedit_char_whitelist:"",tessjs_create_hocr:"1",tessjs_create_tsv:"1",tessjs_create_box:"0",tessjs_create_unlv:"0",tessjs_create_osd:"0"}},function(t,e){t.exports={OSD_ONLY:"0",AUTO_OSD:"1",AUTO_ONLY:"2",AUTO:"3",SINGLE_COLUMN:"4",SINGLE_BLOCK_VERT_TEXT:"5",SINGLE_BLOCK:"6",SINGLE_LINE:"7",SINGLE_WORD:"8",CIRCLE_WORD:"9",SINGLE_CHAR:"10",SPARSE_TEXT:"11",SPARSE_TEXT_OSD:"12"}},function(t,e){var r=this,i=!1;e.logging=i,e.setLogging=function(t){i=t},e.log=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return i?console.log.apply(r,e):null}},function(t,e,r){(function(e){function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}t.exports=function(t,i){if(void 0===e.TesseractCore){if(i.progress({status:"loading tesseract core",progress:0}),e.importScripts(t),void 0!==e.TesseractCoreWASM&&"object"===("undefined"==typeof WebAssembly?"undefined":r(WebAssembly)))e.TesseractCore=e.TesseractCoreWASM;else{if(void 0===e.TesseractCoreASM)throw Error("Failed to load TesseractCore");e.TesseractCore=e.TesseractCoreASM}i.progress({status:"loading tesseract core",progress:1})}return e.TesseractCore}}).call(this,r(1))},function(t,e,r){t.exports=r(26).gunzipSync},function(t,e,r){(function(t,r){/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */
(function(){"use strict";function i(t){throw t}var n=void 0,o=!0,a="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array&&"undefined"!=typeof DataView;function s(t,e){this.index="number"==typeof e?e:0,this.m=0,this.buffer=t instanceof(a?Uint8Array:Array)?t:new(a?Uint8Array:Array)(32768),2*this.buffer.length<=this.index&&i(Error("invalid index")),this.buffer.length<=this.index&&this.f()}s.prototype.f=function(){var t,e=this.buffer,r=e.length,i=new(a?Uint8Array:Array)(r<<1);if(a)i.set(e);else for(t=0;t<r;++t)i[t]=e[t];return this.buffer=i},s.prototype.d=function(t,e,r){var i,n=this.buffer,o=this.index,a=this.m,s=n[o];if(r&&1<e&&(t=8<e?(l[255&t]<<24|l[t>>>8&255]<<16|l[t>>>16&255]<<8|l[t>>>24&255])>>32-e:l[t]>>8-e),8>e+a)s=s<<e|t,a+=e;else for(i=0;i<e;++i)s=s<<1|t>>e-i-1&1,8==++a&&(a=0,n[o++]=l[s],s=0,o===n.length&&(n=this.f()));n[o]=s,this.buffer=n,this.m=a,this.index=o},s.prototype.finish=function(){var t,e=this.buffer,r=this.index;return 0<this.m&&(e[r]<<=8-this.m,e[r]=l[e[r]],r++),a?t=e.subarray(0,r):(e.length=r,t=e),t};var f,u=new(a?Uint8Array:Array)(256);for(f=0;256>f;++f){for(var h=p=f,c=7,p=p>>>1;p;p>>>=1)h<<=1,h|=1&p,--c;u[f]=(h<<c&255)>>>0}var l=u;function d(t,e,r){var i,n="number"==typeof e?e:e=0,o="number"==typeof r?r:t.length;for(i=-1,n=7&o;n--;++e)i=i>>>8^g[255&(i^t[e])];for(n=o>>3;n--;e+=8)i=(i=(i=(i=(i=(i=(i=(i=i>>>8^g[255&(i^t[e])])>>>8^g[255&(i^t[e+1])])>>>8^g[255&(i^t[e+2])])>>>8^g[255&(i^t[e+3])])>>>8^g[255&(i^t[e+4])])>>>8^g[255&(i^t[e+5])])>>>8^g[255&(i^t[e+6])])>>>8^g[255&(i^t[e+7])];return(4294967295^i)>>>0}var m=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],g=a?new Uint32Array(m):m;function y(){}function b(t){this.buffer=new(a?Uint16Array:Array)(2*t),this.length=0}function v(t){var e,r,i,n,o,s,f,u,h,c,p=t.length,l=0,d=Number.POSITIVE_INFINITY;for(u=0;u<p;++u)t[u]>l&&(l=t[u]),t[u]<d&&(d=t[u]);for(e=1<<l,r=new(a?Uint32Array:Array)(e),i=1,n=0,o=2;i<=l;){for(u=0;u<p;++u)if(t[u]===i){for(s=0,f=n,h=0;h<i;++h)s=s<<1|1&f,f>>=1;for(c=i<<16|u,h=s;h<e;h+=o)r[h]=c;++n}++i,n<<=1,o<<=1}return[r,l,d]}function w(t,e){this.k=A,this.F=0,this.input=a&&t instanceof Array?new Uint8Array(t):t,this.b=0,e&&(e.lazy&&(this.F=e.lazy),"number"==typeof e.compressionType&&(this.k=e.compressionType),e.outputBuffer&&(this.a=a&&e.outputBuffer instanceof Array?new Uint8Array(e.outputBuffer):e.outputBuffer),"number"==typeof e.outputIndex&&(this.b=e.outputIndex)),this.a||(this.a=new(a?Uint8Array:Array)(32768))}b.prototype.getParent=function(t){return 2*((t-2)/4|0)},b.prototype.push=function(t,e){var r,i,n,o=this.buffer;for(r=this.length,o[this.length++]=e,o[this.length++]=t;0<r&&(i=this.getParent(r),o[r]>o[i]);)n=o[r],o[r]=o[i],o[i]=n,n=o[r+1],o[r+1]=o[i+1],o[i+1]=n,r=i;return this.length},b.prototype.pop=function(){var t,e,r,i,n,o=this.buffer;for(e=o[0],t=o[1],this.length-=2,o[0]=o[this.length],o[1]=o[this.length+1],n=0;!((i=2*n+2)>=this.length)&&(i+2<this.length&&o[i+2]>o[i]&&(i+=2),o[i]>o[n]);)r=o[n],o[n]=o[i],o[i]=r,r=o[n+1],o[n+1]=o[i+1],o[i+1]=r,n=i;return{index:t,value:e,length:this.length}};var x,A=2,_={NONE:0,L:1,t:A,X:3},E=[];for(x=0;288>x;x++)switch(!0){case 143>=x:E.push([x+48,8]);break;case 255>=x:E.push([x-144+400,9]);break;case 279>=x:E.push([x-256+0,7]);break;case 287>=x:E.push([x-280+192,8]);break;default:i("invalid literal: "+x)}function S(t,e){this.length=t,this.N=e}w.prototype.h=function(){var t,e,r,f,u=this.input;switch(this.k){case 0:for(r=0,f=u.length;r<f;){var h,c,p,l=e=a?u.subarray(r,r+65535):u.slice(r,r+65535),d=(r+=e.length)===f,m=n,g=n,y=this.a,b=this.b;if(a){for(y=new Uint8Array(this.a.buffer);y.length<=b+l.length+5;)y=new Uint8Array(y.length<<1);y.set(this.a)}if(h=d?1:0,y[b++]=0|h,p=65536+~(c=l.length)&65535,y[b++]=255&c,y[b++]=c>>>8&255,y[b++]=255&p,y[b++]=p>>>8&255,a)y.set(l,b),b+=l.length,y=y.subarray(0,b);else{for(m=0,g=l.length;m<g;++m)y[b++]=l[m];y.length=b}this.b=b,this.a=y}break;case 1:var v=new s(a?new Uint8Array(this.a.buffer):this.a,this.b);v.d(1,1,o),v.d(1,2,o);var w,x,_,S=I(this,u);for(w=0,x=S.length;w<x;w++)if(_=S[w],s.prototype.d.apply(v,E[_]),256<_)v.d(S[++w],S[++w],o),v.d(S[++w],5),v.d(S[++w],S[++w],o);else if(256===_)break;this.a=v.finish(),this.b=this.a.length;break;case A:var k,U,T,j,B,L,R,C,z,D,M,N,Y,F,G,q=new s(a?new Uint8Array(this.a.buffer):this.a,this.b),W=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],V=Array(19);for(k=A,q.d(1,1,o),q.d(k,2,o),U=I(this,u),R=P(L=O(this.U,15)),z=P(C=O(this.T,7)),T=286;257<T&&0===L[T-1];T--);for(j=30;1<j&&0===C[j-1];j--);var H,K,X,Z,J,$,Q=T,tt=j,et=new(a?Uint32Array:Array)(Q+tt),rt=new(a?Uint32Array:Array)(316),it=new(a?Uint8Array:Array)(19);for(H=K=0;H<Q;H++)et[K++]=L[H];for(H=0;H<tt;H++)et[K++]=C[H];if(!a)for(H=0,Z=it.length;H<Z;++H)it[H]=0;for(H=J=0,Z=et.length;H<Z;H+=K){for(K=1;H+K<Z&&et[H+K]===et[H];++K);if(X=K,0===et[H])if(3>X)for(;0<X--;)rt[J++]=0,it[0]++;else for(;0<X;)($=138>X?X:138)>X-3&&$<X&&($=X-3),10>=$?(rt[J++]=17,rt[J++]=$-3,it[17]++):(rt[J++]=18,rt[J++]=$-11,it[18]++),X-=$;else if(rt[J++]=et[H],it[et[H]]++,3>--X)for(;0<X--;)rt[J++]=et[H],it[et[H]]++;else for(;0<X;)($=6>X?X:6)>X-3&&$<X&&($=X-3),rt[J++]=16,rt[J++]=$-3,it[16]++,X-=$}for(t=a?rt.subarray(0,J):rt.slice(0,J),D=O(it,7),F=0;19>F;F++)V[F]=D[W[F]];for(B=19;4<B&&0===V[B-1];B--);for(M=P(D),q.d(T-257,5,o),q.d(j-1,5,o),q.d(B-4,4,o),F=0;F<B;F++)q.d(V[F],3,o);for(F=0,G=t.length;F<G;F++)if(N=t[F],q.d(M[N],D[N],o),16<=N){switch(F++,N){case 16:Y=2;break;case 17:Y=3;break;case 18:Y=7;break;default:i("invalid code: "+N)}q.d(t[F],Y,o)}var nt,ot,at,st,ft,ut,ht,ct,pt=[R,L],lt=[z,C];for(ft=pt[0],ut=pt[1],ht=lt[0],ct=lt[1],nt=0,ot=U.length;nt<ot;++nt)if(at=U[nt],q.d(ft[at],ut[at],o),256<at)q.d(U[++nt],U[++nt],o),st=U[++nt],q.d(ht[st],ct[st],o),q.d(U[++nt],U[++nt],o);else if(256===at)break;this.a=q.finish(),this.b=this.a.length;break;default:i("invalid compression type")}return this.a};var k=function(){function t(t){switch(!0){case 3===t:return[257,t-3,0];case 4===t:return[258,t-4,0];case 5===t:return[259,t-5,0];case 6===t:return[260,t-6,0];case 7===t:return[261,t-7,0];case 8===t:return[262,t-8,0];case 9===t:return[263,t-9,0];case 10===t:return[264,t-10,0];case 12>=t:return[265,t-11,1];case 14>=t:return[266,t-13,1];case 16>=t:return[267,t-15,1];case 18>=t:return[268,t-17,1];case 22>=t:return[269,t-19,2];case 26>=t:return[270,t-23,2];case 30>=t:return[271,t-27,2];case 34>=t:return[272,t-31,2];case 42>=t:return[273,t-35,3];case 50>=t:return[274,t-43,3];case 58>=t:return[275,t-51,3];case 66>=t:return[276,t-59,3];case 82>=t:return[277,t-67,4];case 98>=t:return[278,t-83,4];case 114>=t:return[279,t-99,4];case 130>=t:return[280,t-115,4];case 162>=t:return[281,t-131,5];case 194>=t:return[282,t-163,5];case 226>=t:return[283,t-195,5];case 257>=t:return[284,t-227,5];case 258===t:return[285,t-258,0];default:i("invalid length: "+t)}}var e,r,n=[];for(e=3;258>=e;e++)r=t(e),n[e]=r[2]<<24|r[1]<<16|r[0];return n}(),U=a?new Uint32Array(k):k;function I(t,e){function r(t,e){var r,n,o,a,s=t.N,f=[],u=0;switch(r=U[t.length],f[u++]=65535&r,f[u++]=r>>16&255,f[u++]=r>>24,!0){case 1===s:n=[0,s-1,0];break;case 2===s:n=[1,s-2,0];break;case 3===s:n=[2,s-3,0];break;case 4===s:n=[3,s-4,0];break;case 6>=s:n=[4,s-5,1];break;case 8>=s:n=[5,s-7,1];break;case 12>=s:n=[6,s-9,2];break;case 16>=s:n=[7,s-13,2];break;case 24>=s:n=[8,s-17,3];break;case 32>=s:n=[9,s-25,3];break;case 48>=s:n=[10,s-33,4];break;case 64>=s:n=[11,s-49,4];break;case 96>=s:n=[12,s-65,5];break;case 128>=s:n=[13,s-97,5];break;case 192>=s:n=[14,s-129,6];break;case 256>=s:n=[15,s-193,6];break;case 384>=s:n=[16,s-257,7];break;case 512>=s:n=[17,s-385,7];break;case 768>=s:n=[18,s-513,8];break;case 1024>=s:n=[19,s-769,8];break;case 1536>=s:n=[20,s-1025,9];break;case 2048>=s:n=[21,s-1537,9];break;case 3072>=s:n=[22,s-2049,10];break;case 4096>=s:n=[23,s-3073,10];break;case 6144>=s:n=[24,s-4097,11];break;case 8192>=s:n=[25,s-6145,11];break;case 12288>=s:n=[26,s-8193,12];break;case 16384>=s:n=[27,s-12289,12];break;case 24576>=s:n=[28,s-16385,13];break;case 32768>=s:n=[29,s-24577,13];break;default:i("invalid distance")}for(r=n,f[u++]=r[0],f[u++]=r[1],f[u++]=r[2],o=0,a=f.length;o<a;++o)g[y++]=f[o];v[f[0]]++,w[f[3]]++,b=t.length+e-1,l=null}var o,s,f,u,h,c,p,l,d,m={},g=a?new Uint16Array(2*e.length):[],y=0,b=0,v=new(a?Uint32Array:Array)(286),w=new(a?Uint32Array:Array)(30),x=t.F;if(!a){for(f=0;285>=f;)v[f++]=0;for(f=0;29>=f;)w[f++]=0}for(v[256]=1,o=0,s=e.length;o<s;++o){for(f=h=0,u=3;f<u&&o+f!==s;++f)h=h<<8|e[o+f];if(m[h]===n&&(m[h]=[]),c=m[h],!(0<b--)){for(;0<c.length&&32768<o-c[0];)c.shift();if(o+3>=s){for(l&&r(l,-1),f=0,u=s-o;f<u;++f)d=e[o+f],g[y++]=d,++v[d];break}0<c.length?(p=T(e,o,c),l?l.length<p.length?(d=e[o-1],g[y++]=d,++v[d],r(p,0)):r(l,-1):p.length<x?l=p:r(p,0)):l?r(l,-1):(d=e[o],g[y++]=d,++v[d])}c.push(o)}return g[y++]=256,v[256]++,t.U=v,t.T=w,a?g.subarray(0,y):g}function T(t,e,r){var i,n,o,a,s,f,u=0,h=t.length;a=0,f=r.length;t:for(;a<f;a++){if(i=r[f-a-1],o=3,3<u){for(s=u;3<s;s--)if(t[i+s-1]!==t[e+s-1])continue t;o=u}for(;258>o&&e+o<h&&t[i+o]===t[e+o];)++o;if(o>u&&(n=i,u=o),258===o)break}return new S(u,e-n)}function O(t,e){var r,i,n,o,s,f=t.length,u=new b(572),h=new(a?Uint8Array:Array)(f);if(!a)for(o=0;o<f;o++)h[o]=0;for(o=0;o<f;++o)0<t[o]&&u.push(o,t[o]);if(r=Array(u.length/2),i=new(a?Uint32Array:Array)(u.length/2),1===r.length)return h[u.pop().index]=1,h;for(o=0,s=u.length/2;o<s;++o)r[o]=u.pop(),i[o]=r[o].value;for(n=function(t,e,r){function i(t){var r=d[t][m[t]];r===e?(i(t+1),i(t+1)):--p[r],++m[t]}var n,o,s,f,u,h=new(a?Uint16Array:Array)(r),c=new(a?Uint8Array:Array)(r),p=new(a?Uint8Array:Array)(e),l=Array(r),d=Array(r),m=Array(r),g=(1<<r)-e,y=1<<r-1;for(h[r-1]=e,o=0;o<r;++o)g<y?c[o]=0:(c[o]=1,g-=y),g<<=1,h[r-2-o]=(h[r-1-o]/2|0)+e;for(h[0]=c[0],l[0]=Array(h[0]),d[0]=Array(h[0]),o=1;o<r;++o)h[o]>2*h[o-1]+c[o]&&(h[o]=2*h[o-1]+c[o]),l[o]=Array(h[o]),d[o]=Array(h[o]);for(n=0;n<e;++n)p[n]=r;for(s=0;s<h[r-1];++s)l[r-1][s]=t[s],d[r-1][s]=s;for(n=0;n<r;++n)m[n]=0;for(1===c[r-1]&&(--p[0],++m[r-1]),o=r-2;0<=o;--o){for(f=n=0,u=m[o+1],s=0;s<h[o];s++)(f=l[o+1][u]+l[o+1][u+1])>t[n]?(l[o][s]=f,d[o][s]=e,u+=2):(l[o][s]=t[n],d[o][s]=n,++n);m[o]=0,1===c[o]&&i(o)}return p}(i,i.length,e),o=0,s=r.length;o<s;++o)h[r[o].index]=n[o];return h}function P(t){var e,r,i,n,o=new(a?Uint16Array:Array)(t.length),s=[],f=[],u=0;for(e=0,r=t.length;e<r;e++)s[t[e]]=1+(0|s[t[e]]);for(e=1,r=16;e<=r;e++)f[e]=u,u+=0|s[e],u<<=1;for(e=0,r=t.length;e<r;e++)for(u=f[t[e]],f[t[e]]+=1,i=o[e]=0,n=t[e];i<n;i++)o[e]=o[e]<<1|1&u,u>>>=1;return o}function j(t,e){this.input=t,this.b=this.c=0,this.g={},e&&(e.flags&&(this.g=e.flags),"string"==typeof e.filename&&(this.filename=e.filename),"string"==typeof e.comment&&(this.w=e.comment),e.deflateOptions&&(this.l=e.deflateOptions)),this.l||(this.l={})}j.prototype.h=function(){var t,e,r,i,o,s,f,u,h=new(a?Uint8Array:Array)(32768),c=0,p=this.input,l=this.c,m=this.filename,g=this.w;if(h[c++]=31,h[c++]=139,h[c++]=8,t=0,this.g.fname&&(t|=R),this.g.fcomment&&(t|=C),this.g.fhcrc&&(t|=L),h[c++]=t,e=(Date.now?Date.now():+new Date)/1e3|0,h[c++]=255&e,h[c++]=e>>>8&255,h[c++]=e>>>16&255,h[c++]=e>>>24&255,h[c++]=0,h[c++]=B,this.g.fname!==n){for(f=0,u=m.length;f<u;++f)255<(s=m.charCodeAt(f))&&(h[c++]=s>>>8&255),h[c++]=255&s;h[c++]=0}if(this.g.comment){for(f=0,u=g.length;f<u;++f)255<(s=g.charCodeAt(f))&&(h[c++]=s>>>8&255),h[c++]=255&s;h[c++]=0}return this.g.fhcrc&&(r=65535&d(h,0,c),h[c++]=255&r,h[c++]=r>>>8&255),this.l.outputBuffer=h,this.l.outputIndex=c,h=(o=new w(p,this.l)).h(),c=o.b,a&&(c+8>h.buffer.byteLength?(this.a=new Uint8Array(c+8),this.a.set(new Uint8Array(h.buffer)),h=this.a):h=new Uint8Array(h.buffer)),i=d(p,n,n),h[c++]=255&i,h[c++]=i>>>8&255,h[c++]=i>>>16&255,h[c++]=i>>>24&255,u=p.length,h[c++]=255&u,h[c++]=u>>>8&255,h[c++]=u>>>16&255,h[c++]=u>>>24&255,this.c=l,a&&c<h.length&&(this.a=h=h.subarray(0,c)),h};var B=255,L=2,R=8,C=16;function z(t,e){switch(this.o=[],this.p=32768,this.e=this.j=this.c=this.s=0,this.input=a?new Uint8Array(t):t,this.u=!1,this.q=M,this.K=!1,!e&&(e={})||(e.index&&(this.c=e.index),e.bufferSize&&(this.p=e.bufferSize),e.bufferType&&(this.q=e.bufferType),e.resize&&(this.K=e.resize)),this.q){case D:this.b=32768,this.a=new(a?Uint8Array:Array)(32768+this.p+258);break;case M:this.b=0,this.a=new(a?Uint8Array:Array)(this.p),this.f=this.S,this.z=this.O,this.r=this.Q;break;default:i(Error("invalid inflate mode"))}}var D=0,M=1;z.prototype.i=function(){for(;!this.u;){var t=nt(this,3);switch(1&t&&(this.u=o),t>>>=1){case 0:var e=this.input,r=this.c,s=this.a,f=this.b,u=e.length,h=n,c=s.length,p=n;switch(this.e=this.j=0,r+1>=u&&i(Error("invalid uncompressed block header: LEN")),h=e[r++]|e[r++]<<8,r+1>=u&&i(Error("invalid uncompressed block header: NLEN")),h===~(e[r++]|e[r++]<<8)&&i(Error("invalid uncompressed block header: length verify")),r+h>e.length&&i(Error("input buffer is broken")),this.q){case D:for(;f+h>s.length;){if(h-=p=c-f,a)s.set(e.subarray(r,r+p),f),f+=p,r+=p;else for(;p--;)s[f++]=e[r++];this.b=f,s=this.f(),f=this.b}break;case M:for(;f+h>s.length;)s=this.f({B:2});break;default:i(Error("invalid inflate mode"))}if(a)s.set(e.subarray(r,r+h),f),f+=h,r+=h;else for(;h--;)s[f++]=e[r++];this.c=r,this.b=f,this.a=s;break;case 1:this.r(et,it);break;case 2:var l,d,m,g,y=nt(this,5)+257,b=nt(this,5)+1,w=nt(this,4)+4,x=new(a?Uint8Array:Array)(G.length),A=n,_=n,E=n,S=n,k=n;for(k=0;k<w;++k)x[G[k]]=nt(this,3);if(!a)for(k=w,w=x.length;k<w;++k)x[G[k]]=0;for(l=v(x),A=new(a?Uint8Array:Array)(y+b),k=0,g=y+b;k<g;)switch(_=ot(this,l)){case 16:for(S=3+nt(this,2);S--;)A[k++]=E;break;case 17:for(S=3+nt(this,3);S--;)A[k++]=0;E=0;break;case 18:for(S=11+nt(this,7);S--;)A[k++]=0;E=0;break;default:E=A[k++]=_}d=v(a?A.subarray(0,y):A.slice(0,y)),m=v(a?A.subarray(y):A.slice(y)),this.r(d,m);break;default:i(Error("unknown BTYPE: "+t))}}return this.z()};var N,Y,F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],G=a?new Uint16Array(F):F,q=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],W=a?new Uint16Array(q):q,V=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],H=a?new Uint8Array(V):V,K=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],X=a?new Uint16Array(K):K,Z=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],J=a?new Uint8Array(Z):Z,$=new(a?Uint8Array:Array)(288);for(N=0,Y=$.length;N<Y;++N)$[N]=143>=N?8:255>=N?9:279>=N?7:8;var Q,tt,et=v($),rt=new(a?Uint8Array:Array)(30);for(Q=0,tt=rt.length;Q<tt;++Q)rt[Q]=5;var it=v(rt);function nt(t,e){for(var r,n=t.j,o=t.e,a=t.input,s=t.c,f=a.length;o<e;)s>=f&&i(Error("input buffer is broken")),n|=a[s++]<<o,o+=8;return r=n&(1<<e)-1,t.j=n>>>e,t.e=o-e,t.c=s,r}function ot(t,e){for(var r,n,o=t.j,a=t.e,s=t.input,f=t.c,u=s.length,h=e[0],c=e[1];a<c&&!(f>=u);)o|=s[f++]<<a,a+=8;return(n=(r=h[o&(1<<c)-1])>>>16)>a&&i(Error("invalid code length: "+n)),t.j=o>>n,t.e=a-n,t.c=f,65535&r}function at(t){this.input=t,this.c=0,this.G=[],this.R=!1}function st(t){if("string"==typeof t){var e,r,i=t.split("");for(e=0,r=i.length;e<r;e++)i[e]=(255&i[e].charCodeAt(0))>>>0;t=i}for(var n,o=1,a=0,s=t.length,f=0;0<s;){s-=n=1024<s?1024:s;do{a+=o+=t[f++]}while(--n);o%=65521,a%=65521}return(a<<16|o)>>>0}function ft(t,e){var r,n;switch(this.input=t,this.c=0,!e&&(e={})||(e.index&&(this.c=e.index),e.verify&&(this.V=e.verify)),r=t[this.c++],n=t[this.c++],15&r){case ut:this.method=ut;break;default:i(Error("unsupported compression method"))}0!=((r<<8)+n)%31&&i(Error("invalid fcheck flag:"+((r<<8)+n)%31)),32&n&&i(Error("fdict flag is not supported")),this.J=new z(t,{index:this.c,bufferSize:e.bufferSize,bufferType:e.bufferType,resize:e.resize})}z.prototype.r=function(t,e){var r=this.a,i=this.b;this.A=t;for(var n,o,a,s,f=r.length-258;256!==(n=ot(this,t));)if(256>n)i>=f&&(this.b=i,r=this.f(),i=this.b),r[i++]=n;else for(s=W[o=n-257],0<H[o]&&(s+=nt(this,H[o])),n=ot(this,e),a=X[n],0<J[n]&&(a+=nt(this,J[n])),i>=f&&(this.b=i,r=this.f(),i=this.b);s--;)r[i]=r[i++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=i},z.prototype.Q=function(t,e){var r=this.a,i=this.b;this.A=t;for(var n,o,a,s,f=r.length;256!==(n=ot(this,t));)if(256>n)i>=f&&(f=(r=this.f()).length),r[i++]=n;else for(s=W[o=n-257],0<H[o]&&(s+=nt(this,H[o])),n=ot(this,e),a=X[n],0<J[n]&&(a+=nt(this,J[n])),i+s>f&&(f=(r=this.f()).length);s--;)r[i]=r[i++-a];for(;8<=this.e;)this.e-=8,this.c--;this.b=i},z.prototype.f=function(){var t,e,r=new(a?Uint8Array:Array)(this.b-32768),i=this.b-32768,n=this.a;if(a)r.set(n.subarray(32768,r.length));else for(t=0,e=r.length;t<e;++t)r[t]=n[t+32768];if(this.o.push(r),this.s+=r.length,a)n.set(n.subarray(i,i+32768));else for(t=0;32768>t;++t)n[t]=n[i+t];return this.b=32768,n},z.prototype.S=function(t){var e,r,i,n=this.input.length/this.c+1|0,o=this.input,s=this.a;return t&&("number"==typeof t.B&&(n=t.B),"number"==typeof t.M&&(n+=t.M)),2>n?r=(i=(o.length-this.c)/this.A[2]/2*258|0)<s.length?s.length+i:s.length<<1:r=s.length*n,a?(e=new Uint8Array(r)).set(s):e=s,this.a=e},z.prototype.z=function(){var t,e,r,i,n,o=0,s=this.a,f=this.o,u=new(a?Uint8Array:Array)(this.s+(this.b-32768));if(0===f.length)return a?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);for(e=0,r=f.length;e<r;++e)for(i=0,n=(t=f[e]).length;i<n;++i)u[o++]=t[i];for(e=32768,r=this.b;e<r;++e)u[o++]=s[e];return this.o=[],this.buffer=u},z.prototype.O=function(){var t,e=this.b;return a?this.K?(t=new Uint8Array(e)).set(this.a.subarray(0,e)):t=this.a.subarray(0,e):(this.a.length>e&&(this.a.length=e),t=this.a),this.buffer=t},at.prototype.i=function(){for(var t=this.input.length;this.c<t;){var e,r,s=new y,f=n,u=n,h=n,c=n,p=n,l=n,m=n,g=this.input,b=this.c;switch(s.C=g[b++],s.D=g[b++],(31!==s.C||139!==s.D)&&i(Error("invalid file signature:"+s.C+","+s.D)),s.v=g[b++],s.v){case 8:break;default:i(Error("unknown compression method: "+s.v))}if(s.n=g[b++],r=g[b++]|g[b++]<<8|g[b++]<<16|g[b++]<<24,s.$=new Date(1e3*r),s.ba=g[b++],s.aa=g[b++],0<(4&s.n)&&(s.W=g[b++]|g[b++]<<8,b+=s.W),0<(s.n&R)){for(l=[],p=0;0<(c=g[b++]);)l[p++]=String.fromCharCode(c);s.name=l.join("")}if(0<(s.n&C)){for(l=[],p=0;0<(c=g[b++]);)l[p++]=String.fromCharCode(c);s.w=l.join("")}0<(s.n&L)&&(s.P=65535&d(g,0,b),s.P!==(g[b++]|g[b++]<<8)&&i(Error("invalid header crc16"))),f=g[g.length-4]|g[g.length-3]<<8|g[g.length-2]<<16|g[g.length-1]<<24,g.length-b-4-4<512*f&&(h=f),u=new z(g,{index:b,bufferSize:h}),s.data=e=u.i(),b=u.c,s.Y=m=(g[b++]|g[b++]<<8|g[b++]<<16|g[b++]<<24)>>>0,d(e,n,n)!==m&&i(Error("invalid CRC-32 checksum: 0x"+d(e,n,n).toString(16)+" / 0x"+m.toString(16))),s.Z=f=(g[b++]|g[b++]<<8|g[b++]<<16|g[b++]<<24)>>>0,(4294967295&e.length)!==f&&i(Error("invalid input size: "+(4294967295&e.length)+" / "+f)),this.G.push(s),this.c=b}this.R=o;var v,w,x,A=this.G,_=0,E=0;for(v=0,w=A.length;v<w;++v)E+=A[v].data.length;if(a)for(x=new Uint8Array(E),v=0;v<w;++v)x.set(A[v].data,_),_+=A[v].data.length;else{for(x=[],v=0;v<w;++v)x[v]=A[v].data;x=Array.prototype.concat.apply([],x)}return x},ft.prototype.i=function(){var t,e=this.input;return t=this.J.i(),this.c=this.J.c,this.V&&((e[this.c++]<<24|e[this.c++]<<16|e[this.c++]<<8|e[this.c++])>>>0!==st(t)&&i(Error("invalid adler-32 checksum"))),t};var ut=8;function ht(t,e){this.input=t,this.a=new(a?Uint8Array:Array)(32768),this.k=ct.t;var r,i={};for(r in!e&&(e={})||"number"!=typeof e.compressionType||(this.k=e.compressionType),e)i[r]=e[r];i.outputBuffer=this.a,this.I=new w(this.input,i)}var ct=_;function pt(t,e){var r;return r=new ht(t).h(),e||(e={}),e.H?r:gt(r)}function lt(t,e){var r;return t.subarray=t.slice,r=new ft(t).i(),e||(e={}),e.noBuffer?r:gt(r)}function dt(t,e){var r;return t.subarray=t.slice,r=new j(t).h(),e||(e={}),e.H?r:gt(r)}function mt(t,e){var r;return t.subarray=t.slice,r=new at(t).i(),e||(e={}),e.H?r:gt(r)}function gt(t){var e,i,n=new r(t.length);for(e=0,i=t.length;e<i;++e)n[e]=t[e];return n}ht.prototype.h=function(){var t,e,r,n,o,s,f,u=0;switch(f=this.a,t=ut){case ut:e=Math.LOG2E*Math.log(32768)-8;break;default:i(Error("invalid compression method"))}switch(r=e<<4|t,f[u++]=r,t){case ut:switch(this.k){case ct.NONE:o=0;break;case ct.L:o=1;break;case ct.t:o=2;break;default:i(Error("unsupported compression type"))}break;default:i(Error("invalid compression method"))}return n=o<<6|0,f[u++]=n|31-(256*r+n)%31,s=st(this.input),this.I.b=u,u=(f=this.I.h()).length,a&&((f=new Uint8Array(f.buffer)).length<=u+4&&(this.a=new Uint8Array(f.length+4),this.a.set(f),f=this.a),f=f.subarray(0,u+4)),f[u++]=s>>24&255,f[u++]=s>>16&255,f[u++]=s>>8&255,f[u++]=255&s,f},e.deflate=function(e,r,i){t.nextTick((function(){var t,n;try{n=pt(e,i)}catch(e){t=e}r(t,n)}))},e.deflateSync=pt,e.inflate=function(e,r,i){t.nextTick((function(){var t,n;try{n=lt(e,i)}catch(e){t=e}r(t,n)}))},e.inflateSync=lt,e.gzip=function(e,r,i){t.nextTick((function(){var t,n;try{n=dt(e,i)}catch(e){t=e}r(t,n)}))},e.gzipSync=dt,e.gunzip=function(e,r,i){t.nextTick((function(){var t,n;try{n=mt(e,i)}catch(e){t=e}r(t,n)}))},e.gunzipSync=mt}).call(this)}).call(this,r(2),r(0).Buffer)},function(t,e,r){var i=r(28),n=i.set,o=i.get,a=i.del;t.exports={readCache:o,writeCache:n,deleteCache:a,checkCache:function(t){return o(t).then((function(t){return void 0!==t}))}}},function(t,e,r){"use strict";function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function n(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}r.r(e),r.d(e,"Store",(function(){return a})),r.d(e,"get",(function(){return f})),r.d(e,"set",(function(){return u})),r.d(e,"del",(function(){return h})),r.d(e,"clear",(function(){return c})),r.d(e,"keys",(function(){return p}));var o,a=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"keyval-store",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"keyval";i(this,t),this.storeName=r,this._dbp=new Promise((function(t,i){var n=indexedDB.open(e,1);n.onerror=function(){return i(n.error)},n.onsuccess=function(){return t(n.result)},n.onupgradeneeded=function(){n.result.createObjectStore(r)}}))}var e,r,o;return e=t,(r=[{key:"_withIDBStore",value:function(t,e){var r=this;return this._dbp.then((function(i){return new Promise((function(n,o){var a=i.transaction(r.storeName,t);a.oncomplete=function(){return n()},a.onabort=a.onerror=function(){return o(a.error)},e(a.objectStore(r.storeName))}))}))}}])&&n(e.prototype,r),o&&n(e,o),t}();function s(){return o||(o=new a),o}function f(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s();return r._withIDBStore("readonly",(function(r){e=r.get(t)})).then((function(){return e.result}))}function u(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s();return r._withIDBStore("readwrite",(function(r){r.put(e,t)}))}function h(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s();return e._withIDBStore("readwrite",(function(e){e.delete(t)}))}function c(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s();return t._withIDBStore("readwrite",(function(t){t.clear()}))}function p(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s(),e=[];return t._withIDBStore("readonly",(function(t){(t.openKeyCursor||t.openCursor).call(t).onsuccess=function(){this.result&&(e.push(this.result.key),this.result.continue())}})).then((function(){return e}))}}]);
//# sourceMappingURL=worker.min.js.map