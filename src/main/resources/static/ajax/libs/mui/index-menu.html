<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link rel="stylesheet" href="css/mui.min.css">
		<style type="text/css">
			body,
			.mui-content {
				background-color: #333;
				color: #fff;
			}
			
			.title {
				margin: 35px 15px 10px;
			}
			
			.title+.content {
				margin: 10px 15px 35px;
				color: #bbb;
				text-indent: 1em;
				font-size: 14px;
				line-height: 24px;
			}
			
			.mui-table-view {
				margin-bottom: 35px;
			}
		</style>
	</head>

	<body>
		<div class="mui-content">
			<div class="title">侧滑导航</div>
			<div class="content">
				这是首页侧滑导航示例，你可以在这里放置任何内容；<span style="display: none;">关闭侧滑菜单有多种方式： 1.点击本侧滑菜单页之外的任意位置; 2.点击如下红色按钮
				<span id="android-only">；3.Android手机按back键；4.Android手机按menu键。
				</span></span>
				<p style="margin: 10px 15px;">
					<button id="close-btn" type="button" class="mui-btn mui-btn-danger mui-btn-block" style="padding: 5px 20px;">关闭侧滑菜单</button>
				</p>

			</div>
			<div class="title" style="margin-bottom: 25px;">mui典型控件</div>
			<ul class="mui-table-view mui-table-view-chevron mui-table-view-inverted" style="color: #ddd;">
				<li class="mui-table-view-cell">
					<a class="mui-navigate-right" href="examples/pullrefresh_main.html">
						下拉刷新
					</a>
				</li>
				<li class="mui-table-view-cell">
					<a class="mui-navigate-right" href="examples/offcanvas-drag-left.html">
						侧滑导航
					</a>
				</li>
				<li class="mui-table-view-cell">
					<a class="mui-navigate-right" data-title-type="native" href="examples/switches.html">
						开关控件
					</a>
				</li>
				<li class="mui-table-view-cell">
					<a class="mui-navigate-right" data-title-type="native" href="examples/tableviews-with-swipe.html">
						列表左滑菜单
					</a>
				</li>
				<li class="mui-table-view-cell">
					<a class="mui-navigate-right" href="examples/tab-with-viewpagerindicator.html">
						可拖动式选项卡
					</a>
				</li>
			</ul>
		</div>
		<script src="js/mui.min.js"></script>
		<script type="text/javascript" charset="utf-8">
			var aniShow = "slide-in-right";
			//关于backbutton和menubutton两个按键的说明，在iOS平台不存在，故需隐藏
			if(!mui.os.android) {
				var span = document.getElementById("android-only")
				if(span) {
					span.style.display = "none";
				}
			}

			var subWebview = null,
				template = null,
				index = null;
			mui.plusReady(function() {
				//获得主页面webview引用；
				index = plus.webview.currentWebview().opener();
				var _self = plus.webview.currentWebview();
				_self.drag({
					direction: "left",
					moveMode: "followFinger"
				}, {
					view: index,
					moveMode: "follow"
				}, function(e) {});

			})
			mui('.mui-table-view').on('tap', 'a', function() {
				var id = this.getAttribute("href");
				var type = this.getAttribute("open-type");
				var href = this.href;
				
				var webview_style = {
					popGesture: "close",
					statusbar : {
						background: "#f7f7f7"
					}
				}
				
				var extras = {};

				var titleType = this.getAttribute("data-title-type");

				if(titleType == "native") {

					if(!~id.indexOf('pullrefresh.html')) {
						webview_style.bounce = "vertical";
					}

					var webview = plus.webview.create(href, id, webview_style);
					var view = new plus.nativeObj.View("title", {
						top: 0,
						height: "44px",
						width: "100%",
						dock: "top",
						position: "dock"
					});

					view.drawRect("#f7f7f7"); //绘制背景色
					view.drawRect("#cccccc", {
						top: "43px",
						left: "0px"
					}); //绘制底部边线

					var bitmap = new plus.nativeObj.Bitmap("back");
					bitmap.loadBase64Data("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAb1BMVEUAAAAAev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8AAACubimgAAAAI3RSTlMAGfUTGfQTGPMSGPIYGhgaGBsXGxcbFxwXHBccFhwWHRYdHWufDPQAAAABYktHRACIBR1IAAAAB3RJTUUH4QETEBwooeTlkQAAAJVJREFUSMft1EkSgkAQRNFGUXFWHBDBibr/HTUwD5B/48Ig1y+io7u6MqUhf5hsNEY+j5hMgZ/FJ8Xc9ovos3T96utjbfqN/Nb0O/m96Uv5g+mP8ifTn+Ur01/ka9Nf5RvTt/I309/lH6Z/yr9Mn+Q71/MT8B34K/E58Enzv8R/K98HvnF8p3lr8F7izce7lbf3kJ/lDQp9HdBhgg3PAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTAxLTE5VDE2OjI4OjQwKzA4OjAwpTDFwQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wMS0xOVQxNjoyODo0MCswODowMNRtfX0AAAAASUVORK5CYII=");
					view.drawBitmap(bitmap, {}, {
						top: "10px",
						left: "10px",
						width: "24px",
						height: "24px"
					});
					view.drawText(this.innerText.trim(), {}, {
						size: "17px",
						weight: "normal"
					});

					view.setTouchEventRect({
						top: "0px",
						left: "0px",
						width: "44px",
						height: "100%"
					});
					view.interceptTouchEvent(true);
					view.addEventListener("click", function(e) {
						webview.evalJS("mui.back();");
					}, false);
					webview.append(view);

					webview.addEventListener("loaded", function() {
						webview.show(aniShow, 300, null, extras);
					})

				} else {
					//侧滑菜单需动态控制一下zindex值；
					if(~id.indexOf('offcanvas-')) {
						webview_style.zindex = 9998;
						webview_style.popGesture = ~id.indexOf('offcanvas-with-right') ? "close" : "none";
					}

					var webview = plus.webview.create(this.href, id, webview_style, extras);
					webview.addEventListener("loaded", function() {
						webview.show(aniShow, 300);
					});
				}
			});

			/**
			 * 关闭侧滑菜单
			 */
			function close() {
				mui.fire(mui.currentWebview.opener(), "menu:close");
			}

			//点击“关闭侧滑菜单”按钮处理逻辑
			document.getElementById("close-btn").addEventListener("tap", close);

			mui.init({
				swipeBack: false,
				keyEventBind: {
					backbutton: false //关闭back按键监听
				}
			});
		</script>
	</body>

</html>