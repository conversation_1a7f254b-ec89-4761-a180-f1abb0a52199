<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<link rel="stylesheet" href="../css/mui.min.css">
		<script src="../js/mui.min.js"></script>
		<style>
			html,body {
				background-color: #efeff4;
			}
		</style>
		<script>
			mui.init({
				swipeBack:true //启用右滑关闭功能
			});
		</script>
	</head>
	<body>
		<div class="mui-content">
			<header class="mui-bar mui-bar-nav">
				<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
				<h1 class="mui-title">modal（弹出窗口）</h1>
			</header>
			<div class="mui-content">
				<a href="#modal" class="mui-btn mui-btn-primary mui-btn-block">Click</a>
			</div>
			<div id="modal" class="mui-modal">
				<header class="mui-bar mui-bar-nav">
					<a class="mui-icon mui-icon-close mui-pull-right" href="#modal"></a>
					<h1 class="mui-title">Modal</h1>
				</header>
				<div class="mui-content" style="height: 100%;">
					<p class="mui-content-padded">The contents of my modal go here. Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut.</p>
				</div>
			</div>
		</div>
	</body>

</html>