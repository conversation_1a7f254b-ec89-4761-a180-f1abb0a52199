<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css" />-->
	</head>

	<body>

		<style>
			.mui-control-content {
				background-color: white;
				min-height: 215px;
			}
			.mui-control-content .mui-loading {
				margin-top: 50px;
			}
		</style>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title">顶部选项卡-可左右拖动(div)</h1>
		</header>
		<div class="mui-content">
			<div id="slider" class="mui-slider">
				<div id="sliderSegmentedControl" class="mui-slider-indicator mui-segmented-control mui-segmented-control-inverted">
					<a class="mui-control-item" href="#item1mobile">
				待办公文
			</a>
					<a class="mui-control-item" href="#item2mobile">
				已办公文
			</a>
					<a class="mui-control-item" href="#item3mobile">
				全部公文
			</a>
				</div>
				<div id="sliderProgressBar" class="mui-slider-progress-bar mui-col-xs-4"></div>
				<div class="mui-slider-group">
					<div id="item1mobile" class="mui-slider-item mui-control-content mui-active">
						<div id="scroll1" class="mui-scroll-wrapper">
							<div class="mui-scroll">
								<ul class="mui-table-view">
									<li class="mui-table-view-cell">
										第一个选项卡子项-1
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-2
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-3
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-4
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-5
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-6
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-7
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-8
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-9
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-10
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-11
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-12
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-13
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-14
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-15
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-16
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-17
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-18
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-19
									</li>
									<li class="mui-table-view-cell">
										第一个选项卡子项-20
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div id="item2mobile" class="mui-slider-item mui-control-content">
						<div id="scroll2" class="mui-scroll-wrapper">
							<div class="mui-scroll">
								<div class="mui-loading">
									<div class="mui-spinner">
									</div>
								</div>
							</div>
						</div>

					</div>
					<div id="item3mobile" class="mui-slider-item mui-control-content">
						<div id="scroll3" class="mui-scroll-wrapper">
							<div class="mui-scroll">
								<div class="mui-loading">
									<div class="mui-spinner">
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>

			<h5 class="mui-content-padded">Color</h5>
			<div class="mui-card">
				<form class="mui-input-group">
					<div class="mui-input-row mui-radio">
						<label><span class="mui-bg-primary" style="display:block;width:25px;height:25px;"></span>
						</label>
						<input name="radio1" type="radio" checked value="primary">
					</div>
					<div class="mui-input-row mui-radio">
						<label><span class="mui-bg-positive" style="display:block;width:25px;height:25px;"></span>
						</label>
						<input name="radio1" type="radio" value="positive">
					</div>
					<div class="mui-input-row mui-radio">
						<label><span class="mui-bg-negative" style="display:block;width:25px;height:25px;"></span>
						</label>
						<input name="radio1" type="radio" value="negative">
					</div>
				</form>
			</div>

		</div>
		<script src="../js/mui.min.js"></script>
		<script>
			mui.init({
				swipeBack: false
			});
			(function($) {
				$('.mui-scroll-wrapper').scroll({
					indicators: true //是否显示滚动条
				});
				var html2 = '<ul class="mui-table-view"><li class="mui-table-view-cell">第二个选项卡子项-1</li><li class="mui-table-view-cell">第二个选项卡子项-2</li><li class="mui-table-view-cell">第二个选项卡子项-3</li><li class="mui-table-view-cell">第二个选项卡子项-4</li><li class="mui-table-view-cell">第二个选项卡子项-5</li></ul>';
				var html3 = '<ul class="mui-table-view"><li class="mui-table-view-cell">第三个选项卡子项-1</li><li class="mui-table-view-cell">第三个选项卡子项-2</li><li class="mui-table-view-cell">第三个选项卡子项-3</li><li class="mui-table-view-cell">第三个选项卡子项-4</li><li class="mui-table-view-cell">第三个选项卡子项-5</li></ul>';
				var item2 = document.getElementById('item2mobile');
				var item3 = document.getElementById('item3mobile');
				document.getElementById('slider').addEventListener('slide', function(e) {
					if (e.detail.slideNumber === 1) {
						if (item2.querySelector('.mui-loading')) {
							setTimeout(function() {
								item2.querySelector('.mui-scroll').innerHTML = html2;
							}, 500);
						}
					} else if (e.detail.slideNumber === 2) {
						if (item3.querySelector('.mui-loading')) {
							setTimeout(function() {
								item3.querySelector('.mui-scroll').innerHTML = html3;
							}, 500);
						}
					}
				});
				var sliderSegmentedControl = document.getElementById('sliderSegmentedControl');
				$('.mui-input-group').on('change', 'input', function() {
					if (this.checked) {
						sliderSegmentedControl.className = 'mui-slider-indicator mui-segmented-control mui-segmented-control-inverted mui-segmented-control-' + this.value;
						//force repaint
						sliderProgressBar.setAttribute('style', sliderProgressBar.getAttribute('style'));
					}
				});
			})(mui);
		</script>

	</body>

</html>