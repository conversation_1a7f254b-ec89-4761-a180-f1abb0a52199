<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css" />-->
		<style>
			
			.mui-plus .plus{
				display: inline;
			}
			
			.plus{
				display: none;
			}
			
			#topPopover {
				position: fixed;
				top: 16px;
				right: 6px;
			}
			#topPopover .mui-popover-arrow {
				left: auto;
				right: 6px;
			}
			p {
				text-indent: 22px;
			}
			span.mui-icon {
				font-size: 14px;
				color: #007aff;
				margin-left: -15px;
				padding-right: 10px;
			}
			.mui-popover {
				height: 300px;
			}
			.mui-content {
				padding: 10px;
			}
		</style>
	</head>

	<body>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<a id="menu" class="mui-action-menu mui-icon mui-icon-bars mui-pull-right" href="#topPopover"></a>
			<h1 class="mui-title">popover（弹出菜单）</h1>
		</header>
		<footer class="mui-bar mui-bar-footer">
			<a href="#bottomPopover" class="mui-btn mui-btn-link mui-pull-right">菜单</a>
		</footer>
		<div class="mui-content">
			<div class="mui-content-padded">
				<p>popover（弹出菜单）是mobile App中常见的UI组件，在用户点击位置附近弹出悬浮菜单，向用户展示更多信息或提供快捷操作。</p>
				<p>popover最常用的两个位置：顶部导航栏右侧和底部工具栏右侧； 点击本页右上角的
					<span class="mui-icon mui-icon-bars"></span> 图标体验，
					<!--TODO 后续增加原生标题示例-->
					<!--<span class="plus">这是一个<strong>原生标题栏</strong>触发的popover示例，
					<span class="mui-icon mui-icon-bars"></span>在父webview中，点击后通过自定义事件 通知子webview， 子webview再执行popover的显示隐藏逻辑； </span>-->
					接着点击本页面右下角的“菜单”按钮体验。
				</p>
				<p>除了页面顶部导航栏、底部工具栏固定位置之外，其它区域要使用弹出菜单，都要准确计算物理位置，从而实现弹出菜单的绝对定位； mui封装的手势事件中，可以获得点击位置，通过这些位置可实现任意区域的弹出菜单显示，点击如下按钮体验：
				</p>
				<p style="text-indent: 0;">
					<a href="#middlePopover" class="mui-btn mui-btn-primary mui-btn-block mui-btn-outlined" style="padding: 5px 20px;">打开弹出菜单</a>
				</p>
			</div>
		</div>
		<!--右上角弹出菜单-->
		<div id="topPopover" class="mui-popover">
			<div class="mui-popover-arrow"></div>
			<div class="mui-scroll-wrapper">
				<div class="mui-scroll">
					<ul class="mui-table-view">
						<li class="mui-table-view-cell">
							<a href="#">Item1</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item2</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item3</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item4</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item5</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item6</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item7</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item8</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item9</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item10</a>
						</li>
					</ul>
				</div>
			</div>

		</div>
		<div id="middlePopover" class="mui-popover">
			<div class="mui-popover-arrow"></div>
			<div class="mui-scroll-wrapper">
				<div class="mui-scroll">
					<ul class="mui-table-view">
						<li class="mui-table-view-cell"><a href="#">Item1</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item2</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item3</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item4</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item5</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item6</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item7</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item8</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item9</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item10</a>
						</li>
					</ul>
				</div>
			</div>

		</div>
		<!--右下角弹出菜单-->
		<div id="bottomPopover" class="mui-popover mui-popover-bottom">
			<div class="mui-popover-arrow"></div>
			<div class="mui-scroll-wrapper">
				<div class="mui-scroll">
					<ul class="mui-table-view">
						<li class="mui-table-view-cell"><a href="#">Item1</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item2</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item3</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item4</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item5</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item6</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item7</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item8</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item9</a>
						</li>
						<li class="mui-table-view-cell"><a href="#">Item10</a>
						</li>
					</ul>
				</div>
			</div>

		</div>
		<script src="../js/mui.min.js"></script>
		<script>
			mui.init();
			
			mui.plusReady(function () {
//				var view = plus.nativeObj.View.getViewById("title");
//				
//				var bitmap = new plus.nativeObj.Bitmap("back");
//				bitmap.loadBase64Data("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAb1BMVEUAAAAAev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8Aev8AAACubimgAAAAI3RSTlMAGfUTGfQTGPMSGPIYGhgaGBsXGxcbFxwXHBccFhwWHRYdHWufDPQAAAABYktHRACIBR1IAAAAB3RJTUUH4QETEBwooeTlkQAAAJVJREFUSMft1EkSgkAQRNFGUXFWHBDBibr/HTUwD5B/48Ig1y+io7u6MqUhf5hsNEY+j5hMgZ/FJ8Xc9ovos3T96utjbfqN/Nb0O/m96Uv5g+mP8ifTn+Ur01/ka9Nf5RvTt/I309/lH6Z/yr9Mn+Q71/MT8B34K/E58Enzv8R/K98HvnF8p3lr8F7izce7lbf3kJ/lDQp9HdBhgg3PAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTAxLTE5VDE2OjI4OjQwKzA4OjAwpTDFwQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wMS0xOVQxNjoyODo0MCswODowMNRtfX0AAAAASUVORK5CYII=");
//				view.drawBitmap(bitmap, {}, {
//					top: "10px",
//					left: "10px",
//					width: "24px",
//					height: "24px"
//				});
//				
//				view.setTouchEventRect({top:"0px",left:"0px",width:"44px",height:"100%"});
			});
			
			mui('.mui-scroll-wrapper').scroll();
			mui('body').on('shown', '.mui-popover', function(e) {
				//console.log('shown', e.detail.id);//detail为当前popover元素
			});
			mui('body').on('hidden', '.mui-popover', function(e) {
				//console.log('hidden', e.detail.id);//detail为当前popover元素
			});
		</script>
	</body>

</html>