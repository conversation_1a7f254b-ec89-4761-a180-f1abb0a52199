<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!-- <link rel="stylesheet" type="text/css" href="../css/app.css" /> -->
		<style>
			p {
				text-indent: 22px;
				padding: 5px 8px;
			}
			
			html,
			body,
			.mui-content {
				background-color: #fff;
			}
			
			h4 {
				margin-left: 5px;
			}
			
			.qrcode {
				/*position: absolute;*/
				top: 50px;
				/*left: 50%;*/
				width: 100%;
				/*-webkit-transform: translate(-50%, 0);
				transform: translate(-50%, 0);*/
				text-align: center;
			}
			
			.qrcode img {
				margin: 0 auto;
			}
			
		</style>
	</head>

	<body>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title">关于</h1>
		</header>
		<div class="mui-content">
			<div class="mui-content-padded">
				<div class="qrcode">
					<img id="qrcode" src="../images/qrcode.png" width="100%" />
					<a id="shortcut" style="width: 60%;margin: 15px auto;padding: 5px;" class="mui-hidden mui-btn mui-btn-block mui-btn-red">创建桌面图标</a>
				</div>
				<!--<h4 style="margin-top:10px;">mui</h4>-->
				<p>mui是一款小巧高能的前端UI框架，其UI控件富有原生UI样式；
					通过封装多webview、启动原生动画，使得mui成为目前最接近原生体验的前端框架，
					更多详细介绍，请到<a href="http://dev.dcloud.net.cn/mui">mui官网</a>查看</p>
				<p style="text-align: center;color: #ccc;text-indent: 0;">当前版本：<span id="version">3.7.0</span></p>	
			</div>
		</div>
		<script src="../js/mui.min.js"></script>
		<script type="text/javascript" charset="utf-8">
			mui.init({
				gestureConfig: {
					longtap: true
				},
				swipeBack: true //启用右滑关闭功能
			});
			//处理点击事件，需要打开原生浏览器
			mui('body').on('tap', 'a', function(e) {
				var href = this.getAttribute('href');
				if (href) {
					if (window.plus) {
						plus.runtime.openURL(href);
					} else {
						location.href = href;
					}
				}
			});

			var qrcodeEl = document.getElementById("qrcode");
			qrcodeEl.addEventListener('longtap', function() {
				plus.nativeUI.actionSheet({
					cancel: '取消',
					buttons: [{
						title: '保存到相册'
					}]
				}, function(e) {
					var index = e.index;
					if (e.index === 1) {
						plus.gallery.save(qrcodeEl.src, function() {
							mui.toast('保存成功');
						}, function() {
							mui.toast('保存失败，请重试！');
						});
					}
				});
			});
			if (mui.os.android&&mui.os.stream) { //创建快捷方式
				var shortcutElem = document.getElementById("shortcut");
				shortcutElem.classList.remove('mui-hidden');
				shortcutElem.addEventListener('tap', function() {
					plus.navigator.createShortcut({
						name: "hello-mui",
						icon: "images/logo.png"
					});
				});
			}


		</script>
	</body>

</html>