<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<link rel="stylesheet" type="text/css" href="../css/app.css"/>
	</head>
	<body>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title">列表带input类控件</h1>
		</header>
		<div class="mui-content">
			<div class="mui-card">
				<ul class="mui-table-view">
					<li class="mui-table-view-cell">
						card（圆角列表）
						<div id="M_Toggle" class="mui-switch mui-active">
							<div class="mui-switch-handle"></div>
						</div>
					</li>
					<li class="mui-table-view-cell">
						Item 1
						<button type="button" class="mui-btn">
							Button
						</button>
					</li>
					<li class="mui-table-view-cell">
						Item 2
						<button type="button" class="mui-btn mui-btn-primary">
							Button
						</button>
					</li>
					<li class="mui-table-view-cell">
						Item 3
						<div class="mui-switch mui-active">
							<div class="mui-switch-handle"></div>
						</div>
					</li>
					<li class="mui-table-view-cell">
						Item 4
						<div class="mui-switch mui-switch-blue mui-switch-mini mui-active">
							<div class="mui-switch-handle"></div>
						</div>
					</li>
					<li class="mui-table-view-cell mui-radio mui-left">
						<input name="radio" type="radio">Item 5
					</li>
					<li class="mui-table-view-cell mui-radio mui-left">
						<input name="radio" type="radio">Item 6
					</li>
					<li class="mui-table-view-cell mui-checkbox mui-left">
						<input name="checkbox" type="checkbox">Item 7
					</li>
					<li class="mui-table-view-cell mui-checkbox mui-left">
						<input name="checkbox" type="checkbox">Item 8
					</li>
				</ul>
			</div>
		</div>
	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack:true //启用右滑关闭功能
		});
		
		//圆角列表开关处理
		document.getElementById("M_Toggle").addEventListener('toggle',function (e) {
			var isActive = e.detail.isActive;
			var table = document.querySelector('.mui-table-view');
			var card = document.querySelector('.mui-card');
			if (isActive) {
				card.appendChild(table);
				card.style.display = '';
			} else {
				var content = document.querySelector('.mui-content');
				content.insertBefore(table, card);
				card.style.display = 'none';
			}
		});
	</script>
</html>