<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<link rel="stylesheet" type="text/css" href="../css/app.css"/>
		<style>
			.mui-content-padded{
			  	margin: 10px;
			  }
		</style>
	</head>

	<body>
		<header class="mui-bar mui-bar-nav">
		    <a class="mui-action-back mui-icon mui-icon-back mui-pull-left"></a>
		    <h1 class="mui-title">块级按钮</h1>
		</header>
		<div class="mui-content">
		    <div class="mui-content-padded">
		        <button type="button" class="mui-btn mui-btn-block">Block button</button>
		        <button type="button" class="mui-btn mui-btn-primary mui-btn-block">Block button</button>
		        <button type="button" class="mui-btn mui-btn-success mui-btn-block">Block button</button>
		        <button type="button" class="mui-btn mui-btn-warning mui-btn-block">Block button</button>
		        <button type="button" class="mui-btn mui-btn-danger mui-btn-block">Block button</button>
				<button type="button" class="mui-btn mui-btn-royal mui-btn-block">Block button</button>
		        <button type="button" class="mui-btn mui-btn-block mui-btn-outlined">Block button</button>
		        <button type="button" class="mui-btn mui-btn-primary mui-btn-block mui-btn-outlined">Block button</button>
		        <button type="button" class="mui-btn mui-btn-success mui-btn-block mui-btn-outlined">Block button</button>
		        <button type="button" class="mui-btn mui-btn-warning mui-btn-block mui-btn-outlined">Block button</button>
		        <button type="button" class="mui-btn mui-btn-danger mui-btn-block mui-btn-outlined">Block button</button>
		        <button type="button" class="mui-btn mui-btn-royal mui-btn-block mui-btn-outlined">Block button</button>
		    </div>
		</div>
	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack:true //启用右滑关闭功能
		});
	</script>
</html>