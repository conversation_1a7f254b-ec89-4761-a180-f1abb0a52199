import * as i0 from "@angular/core";
import * as i1 from "./swiper.component";
import * as i2 from "./swiper-slide.directive";
import * as i3 from "@angular/common";
export declare class SwiperModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<SwiperModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<SwiperModule, [typeof i1.SwiperComponent, typeof i2.SwiperSlideDirective], [typeof i3.CommonModule], [typeof i1.SwiperComponent, typeof i2.SwiperSlideDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<SwiperModule>;
}
