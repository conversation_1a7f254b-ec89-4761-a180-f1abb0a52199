import { isObject, extend } from './utils';
import { paramsList } from './params-list';
// @ts-ignore
import Swiper from 'swiper';
export const allowedParams = paramsList.map((key) => key.replace(/_/, ''));
export function getParams(obj = {}) {
    const params = {
        on: {},
    };
    // const events = {};
    const passedParams = {};
    extend(params, Swiper.defaults);
    extend(params, Swiper.extendedDefaults);
    params._emitClasses = true;
    params.init = false;
    const rest = {};
    const allowedParams = paramsList.map((key) => key.replace(/_/, ''));
    Object.keys(obj).forEach((key) => {
        const _key = key.replace(/^_/, '');
        if (allowedParams.indexOf(_key) >= 0) {
            if (isObject(obj[key])) {
                params[_key] = {};
                passedParams[_key] = {};
                extend(params[_key], obj[key]);
                extend(passedParams[_key], obj[key]);
            }
            else {
                params[_key] = obj[key];
                passedParams[_key] = obj[key];
            }
        }
        // else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {
        //   events[`${_key[2].toLowerCase()}${key.substr(3)}`] = obj[key];
        // }
        else {
            rest[_key] = obj[key];
        }
    });
    ['navigation', 'pagination', 'scrollbar'].forEach((key) => {
        if (params[key] === true)
            params[key] = {};
        if (params[key] === false)
            delete params[key];
    });
    return { params, passedParams, rest };
}
//# sourceMappingURL=data:application/json;base64,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