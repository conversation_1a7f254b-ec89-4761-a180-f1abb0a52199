/* simple-uploader v3.0.0 | (c) Mycolorway Design | MIT License */
!function(e,t){"object"==typeof module&&module.exports?module.exports=t(require("jquery"),require("simple-module")):e.SimpleUploader=t(e.j<PERSON><PERSON>y,e.SimpleModule)}(this,function(e,t){var r=require=function e(t,r,n){function o(u,l){if(!r[u]){if(!t[u]){var s="function"==typeof require&&require;if(!l&&s)return s(u,!0);if(i)return i(u,!0);var a=new Error("Cannot find module '"+u+"'");throw a.code="MODULE_NOT_FOUND",a}var p=r[u]={exports:{}};t[u][0].call(p.exports,function(e){var r=t[u][1][e];return o(r?r:e)},p,p.exports,e,t,r,n)}return r[u].exports}for(var i="function"==typeof require&&require,u=0;u<n.length;u++)o(n[u]);return o}({"simple-uploader":[function(r,n,o){var i,u=function(e,t){function r(){this.constructor=e}for(var n in t)l.call(t,n)&&(e[n]=t[n]);return r.prototype=t.prototype,e.prototype=new r,e.__super__=t.prototype,e},l={}.hasOwnProperty;i=function(t){function r(t){r.__super__.constructor.apply(this,arguments),this.opts=e.extend({},r.opts,t),this._locales=e.extend({},r.locales,this.opts.locales),this.files=[],this.queue=[],this.uploading=!1,this.id=++r.count,this._bind()}return u(r,t),r.count=0,r.opts={url:"",params:null,fileKey:"upload_file",connectionCount:3,locales:null},r.locales={leaveConfirm:"Are you sure you want to leave?"},r.prototype._bind=function(){return this.on("uploadcomplete",function(t){return function(r,n){return t.files.splice(e.inArray(n,t.files),1),t.queue.length>0&&t.files.length<t.opts.connectionCount?t.upload(t.queue.shift()):0===t.files.length?t.uploading=!1:void 0}}(this)),e(window).on("beforeunload.uploader-"+this.id,function(e){return function(t){if(e.uploading)return t.originalEvent.returnValue=e._locales.leaveConfirm,e._locales.leaveConfirm}}(this))},r.prototype.generateId=function(){var e;return e=0,function(){return e+=1}}(),r.prototype.upload=function(t,r){var n,o,i,u;if(null==r&&(r={}),null!=t){if(e.isArray(t)||t instanceof FileList)for(o=0,u=t.length;o<u;o++)n=t[o],this.upload(n,r);else e(t).is("input:file")?(i=e(t).attr("name"),i&&(r.fileKey=i),this.upload(e.makeArray(e(t)[0].files),r)):t.id&&t.obj||(t=this.getFile(t));if(t&&t.obj){if(e.extend(t,r),this.files.length>=this.opts.connectionCount)return void this.queue.push(t);if(this.trigger("beforeupload",[t])!==!1)return this.files.push(t),this._xhrUpload(t),this.uploading=!0}}},r.prototype.getFile=function(e){var t,r,n;return e instanceof window.File||e instanceof window.Blob?(t=null!=(r=e.fileName)?r:e.name,{id:this.generateId(),url:this.opts.url,params:this.opts.params,fileKey:this.opts.fileKey,name:t,size:null!=(n=e.fileSize)?n:e.size,ext:t?t.split(".").pop().toLowerCase():"",obj:e}):null},r.prototype._xhrUpload=function(t){var r,n,o,i;if(r=new FormData,r.append(t.fileKey,t.obj),r.append("original_filename",t.name),t.params){o=t.params;for(n in o)i=o[n],r.append(n,i)}return t.xhr=e.ajax({url:t.url,data:r,processData:!1,contentType:!1,type:"POST",headers:{"X-File-Name":encodeURIComponent(t.name)},xhr:function(){var t;return t=e.ajaxSettings.xhr(),t&&(t.upload.onprogress=function(e){return function(t){return e.progress(t)}}(this)),t},progress:function(e){return function(r){if(r.lengthComputable)return e.trigger("uploadprogress",[t,r.loaded,r.total])}}(this),error:function(e){return function(r,n,o){return e.trigger("uploaderror",[t,r,n])}}(this),success:function(r){return function(n){return r.trigger("uploadprogress",[t,t.size,t.size]),r.trigger("uploadsuccess",[t,n]),e(document).trigger("uploadsuccess",[t,n,r])}}(this),complete:function(e){return function(r,n){return e.trigger("uploadcomplete",[t,r.responseText])}}(this)})},r.prototype.cancel=function(t){var r,n,o,i;if(!t.id)for(i=this.files,n=0,o=i.length;n<o;n++)if(r=i[n],r.id===1*t){t=r;break}return this.files.splice(e.inArray(t,this.files),1),this.trigger("uploadcancel",[t]),t.xhr&&t.xhr.abort(),t.xhr=null},r.prototype.readImageFile=function(t,r){var n,o;if(e.isFunction(r))return o=new Image,o.onload=function(){return r(o)},o.onerror=function(){return r(!1)},window.FileReader&&FileReader.prototype.readAsDataURL&&/^image/.test(t.type)?(n=new FileReader,n.onload=function(e){return o.src=e.target.result},n.readAsDataURL(t)):r(!1)},r.prototype.destroy=function(){var t,r,n,o;for(this.queue.length=0,o=this.files,r=0,n=o.length;r<n;r++)t=o[r],this.cancel(t);return e(window).off(".uploader-"+this.id),e(document).off(".uploader-"+this.id)},r}(t),n.exports=i},{}]},{},[]);return r("simple-uploader")});