/* simple-module v3.0.3 | (c) Mycolorway Design | MIT License */
!function(e,t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):e.SimpleModule=t(e.jQuery)}(this,function(e){var t=function r(e,t,n){function o(u,l){if(!t[u]){if(!e[u]){var p="function"==typeof require&&require;if(!l&&p)return p(u,!0);if(i)return i(u,!0);var f=new Error("Cannot find module '"+u+"'");throw f.code="MODULE_NOT_FOUND",f}var s=t[u]={exports:{}};e[u][0].call(s.exports,function(t){var r=e[u][1][t];return o(r?r:t)},s,s.exports,r,e,t,n)}return t[u].exports}for(var i="function"==typeof require&&require,u=0;u<n.length;u++)o(n[u]);return o}({1:[function(t,r,n){var o,i=[].slice;o=function(){function t(r){this.opts=e.extend({},t.opts,r),this.opts.plugins.forEach(function(e){return function(r){return e.plugins[r]=new t.plugins[r](e)}}(this))}return t.extend=function(e){var t,r,n;if(!e||"object"!=typeof e)throw new Error("SimpleModule.extend: param should be an object");for(t in e)n=e[t],"included"!==t&&"extended"!==t&&(this[t]=n);return null!=(r=e.extended)&&r.call(this),this},t.include=function(e){var t,r,n;if(!e||"object"!=typeof e)throw new Error("SimpleModule.include: param should be an object");for(t in e)n=e[t],"included"!==t&&"extended"!==t&&(this.prototype[t]=n);return null!=(r=e.included)&&r.call(this),this},t.plugins={},t.plugin=function(e,t){if(!e||"string"!=typeof e)throw new Error("SimpleModule.plugin: first param should be a string");if("function"!=typeof t)throw new Error("SimpleModule.plugin: second param should be a class");return this.plugins[e]=t,this},t.opts={plugins:[]},t.prototype.plugins={},t.prototype.on=function(){var t,r;return t=1<=arguments.length?i.call(arguments,0):[],(r=e(this)).on.apply(r,t)},t.prototype.off=function(){var t,r;return t=1<=arguments.length?i.call(arguments,0):[],(r=e(this)).off.apply(r,t)},t.prototype.trigger=function(){var t,r;return t=1<=arguments.length?i.call(arguments,0):[],(r=e(this)).triggerHandler.apply(r,t)},t.prototype.one=function(){var t,r;return t=1<=arguments.length?i.call(arguments,0):[],(r=e(this)).one.apply(r,t)},t}(),r.exports=o},{}]},{},[1]);return t(1)});