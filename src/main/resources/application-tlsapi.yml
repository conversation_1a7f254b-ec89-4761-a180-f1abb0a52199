tlsapi:
  certificate:
    win-path: /Users/<USER>/Downloads/certificate.pfx
    linux-path: /Users/<USER>/Documents/newcertificate/certificate.pfx
    password: hjdz@2025
  api:
    verifytuple:
      baseurl: https://apimmtls.gm.com/api-0638/verifytuples
      verifyid-post-get: /v1/tuples/verification/requests
      verifydata-get: /v1/tuples/verification/requests/{requestId}
      verify-post: /v1/tuples/verification/requests/{requestId}/data
    downloadtuples:
      baseurl: https://apimmtls.gm.com/api-0728/downloadtuples
      dowload-post: /v1/tuples/requests
      dowload-data-get: /v1/tuples/download/{requestId}

# ZF (采埃孚) 接口配置
zf:
  netty:
    # 连接配置
    connection:
      host: zf.easysec.com.cn  # 替换为实际的ZF服务器地址
      port: 10001
      ssl: true
      connect-timeout: 30000
      read-timeout: 60000
      write-timeout: 30000
    # 连接池配置
    pool:
      max-connections: 50
      max-connections-per-route: 20
      connection-idle-timeout: 300000
      keep-alive-timeout: 60000
    # 重试配置
    retry:
      max-attempts: 3
      retry-delay: 1000
      backoff-multiplier: 2.0
  # ZF API接口配置
  api:
    # 认证接口
    auth:
      baseurl: https://zf-api.example.com/auth
      login: /v1/login
      refresh: /v1/refresh
      logout: /v1/logout
    # 数据接口
    data:
      baseurl: https://zf-api.example.com/api
      vehicle-data: /v1/vehicles/{vehicleId}/data
      diagnostic: /v1/vehicles/{vehicleId}/diagnostic
      telemetry: /v1/vehicles/{vehicleId}/telemetry
    # 控制接口
    control:
      baseurl: https://zf-api.example.com/control
      remote-start: /v1/vehicles/{vehicleId}/start
      remote-stop: /v1/vehicles/{vehicleId}/stop
      lock-unlock: /v1/vehicles/{vehicleId}/lock
  # 证书配置
  certificate:
    client-cert-path: /path/to/zf-client-cert.pfx
    client-cert-password: your-cert-password
    trust-store-path: /path/to/zf-truststore.jks
    trust-store-password: your-truststore-password