package com.nst.nstsecurity.controller;

import com.nst.nstsecurity.common.framework.controller.BaseController;
import com.nst.nstsecurity.common.framework.util.AjaxResult;
import com.nst.nstsecurity.domain.UserDomain;
import com.nst.nstsecurity.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

@Controller
public class LoginController extends BaseController {
    @Autowired
    private IUserService userService;

    @GetMapping("/")
    public String login(){
        return "login";
    }

    @PostMapping("/login")
    public AjaxResult ajaxLogin(String username, String password){
        System.out.println("username:" + username + " password:" + password);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(username, password);
        UserDomain userEntityByName = userService.findByUsername(username);
        return null;
    }
}
