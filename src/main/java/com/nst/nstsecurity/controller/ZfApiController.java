package com.nst.nstsecurity.controller;

import com.nst.nstsecurity.common.zf.dto.*;
import com.nst.nstsecurity.common.zf.service.ZfApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * ZF API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/zf")
public class ZfApiController {

    @Autowired
    private ZfApiService zfApiService;

    /**
     * ZF登录
     */
    @PostMapping("/login")
    public CompletableFuture<ResponseEntity<ZfAuthResponse>> login(@RequestBody ZfLoginRequest loginRequest) {
        log.info("收到ZF登录请求: username={}", loginRequest.getUsername());
        
        return zfApiService.login(loginRequest)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.badRequest().body(response);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("ZF登录异常", throwable);
                    ZfAuthResponse errorResponse = new ZfAuthResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage("登录服务异常: " + throwable.getMessage());
                    return ResponseEntity.internalServerError().body(errorResponse);
                });
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public CompletableFuture<ResponseEntity<ZfAuthResponse>> refreshToken(@RequestParam String refreshToken) {
        log.info("收到ZF令牌刷新请求");
        
        return zfApiService.refreshToken(refreshToken)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.badRequest().body(response);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("ZF令牌刷新异常", throwable);
                    ZfAuthResponse errorResponse = new ZfAuthResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage("令牌刷新服务异常: " + throwable.getMessage());
                    return ResponseEntity.internalServerError().body(errorResponse);
                });
    }

    /**
     * 获取车辆数据
     */
    @GetMapping("/vehicles/{vehicleId}/data")
    public CompletableFuture<ResponseEntity<ZfVehicleDataResponse>> getVehicleData(@PathVariable String vehicleId) {
        log.info("收到获取车辆数据请求: vehicleId={}", vehicleId);
        
        return zfApiService.getVehicleData(vehicleId)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.badRequest().body(response);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("获取车辆数据异常: vehicleId={}", vehicleId, throwable);
                    ZfVehicleDataResponse errorResponse = new ZfVehicleDataResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage("获取车辆数据服务异常: " + throwable.getMessage());
                    return ResponseEntity.internalServerError().body(errorResponse);
                });
    }

    /**
     * 获取车辆诊断信息
     */
    @GetMapping("/vehicles/{vehicleId}/diagnostic")
    public CompletableFuture<ResponseEntity<ZfDiagnosticResponse>> getVehicleDiagnostic(@PathVariable String vehicleId) {
        log.info("收到获取车辆诊断信息请求: vehicleId={}", vehicleId);
        
        return zfApiService.getVehicleDiagnostic(vehicleId)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.badRequest().body(response);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("获取车辆诊断信息异常: vehicleId={}", vehicleId, throwable);
                    ZfDiagnosticResponse errorResponse = new ZfDiagnosticResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage("获取车辆诊断信息服务异常: " + throwable.getMessage());
                    return ResponseEntity.internalServerError().body(errorResponse);
                });
    }

    /**
     * 远程启动车辆
     */
    @PostMapping("/vehicles/{vehicleId}/start")
    public CompletableFuture<ResponseEntity<ZfControlResponse>> remoteStartVehicle(
            @PathVariable String vehicleId, 
            @RequestBody ZfRemoteStartRequest startRequest) {
        
        log.info("收到远程启动车辆请求: vehicleId={}", vehicleId);
        startRequest.setVehicleId(vehicleId);
        
        return zfApiService.remoteStartVehicle(vehicleId, startRequest)
                .thenApply(response -> {
                    if (response.isSuccess()) {
                        return ResponseEntity.ok(response);
                    } else {
                        return ResponseEntity.badRequest().body(response);
                    }
                })
                .exceptionally(throwable -> {
                    log.error("远程启动车辆异常: vehicleId={}", vehicleId, throwable);
                    ZfControlResponse errorResponse = new ZfControlResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage("远程启动车辆服务异常: " + throwable.getMessage());
                    return ResponseEntity.internalServerError().body(errorResponse);
                });
    }

    /**
     * 登出
     */
    @PostMapping("/logout")
    public CompletableFuture<ResponseEntity<String>> logout() {
        log.info("收到ZF登出请求");
        
        return zfApiService.logout()
                .thenApply(success -> {
                    if (success) {
                        return ResponseEntity.ok("登出成功");
                    } else {
                        return ResponseEntity.badRequest().body("登出失败");
                    }
                })
                .exceptionally(throwable -> {
                    log.error("ZF登出异常", throwable);
                    return ResponseEntity.internalServerError().body("登出服务异常: " + throwable.getMessage());
                });
    }
}
