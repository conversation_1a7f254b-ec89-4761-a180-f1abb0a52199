package com.nst.nstsecurity.common.zf.client;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * ZF HTTP客户端响应处理器
 */
@Slf4j
public class ZfHttpClientHandler extends SimpleChannelInboundHandler<FullHttpResponse> {

    private final CompletableFuture<String> responseFuture;

    public ZfHttpClientHandler(CompletableFuture<String> responseFuture) {
        this.responseFuture = responseFuture;
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpResponse response) throws Exception {
        try {
            HttpResponseStatus status = response.status();
            String content = response.content().toString(CharsetUtil.UTF_8);
            
            log.debug("收到响应: 状态码={}, 内容长度={}", status.code(), content.length());
            
            if (status.code() >= 200 && status.code() < 300) {
                // 成功响应
                responseFuture.complete(content);
            } else {
                // 错误响应
                String errorMsg = String.format("HTTP请求失败: 状态码=%d, 响应内容=%s", status.code(), content);
                responseFuture.completeExceptionally(new RuntimeException(errorMsg));
            }
        } catch (Exception e) {
            log.error("处理响应时发生错误", e);
            responseFuture.completeExceptionally(e);
        } finally {
            // 关闭连接
            ctx.close();
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("HTTP客户端处理器发生异常", cause);
        responseFuture.completeExceptionally(cause);
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        if (!responseFuture.isDone()) {
            responseFuture.completeExceptionally(new RuntimeException("连接意外关闭"));
        }
        super.channelInactive(ctx);
    }
}
