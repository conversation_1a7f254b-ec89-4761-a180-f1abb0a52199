package com.nst.nstsecurity.common.zf.retry;

import com.nst.nstsecurity.common.zf.config.ZfProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * ZF重试处理器
 */
@Slf4j
@Component
public class ZfRetryHandler {

    @Autowired
    private ZfProperties zfProperties;

    /**
     * 执行带重试的操作
     */
    public <T> CompletableFuture<T> executeWithRetry(Supplier<CompletableFuture<T>> operation, String operationName) {
        return executeWithRetry(operation, operationName, 0);
    }

    /**
     * 递归执行重试逻辑
     */
    private <T> CompletableFuture<T> executeWithRetry(Supplier<CompletableFuture<T>> operation, String operationName, int attempt) {
        return operation.get()
                .exceptionally(throwable -> {
                    if (attempt < zfProperties.getNetty().getRetry().getMaxAttempts() - 1) {
                        log.warn("操作 '{}' 第{}次尝试失败，将在{}ms后重试: {}", 
                                operationName, attempt + 1, calculateDelay(attempt), throwable.getMessage());
                        
                        // 计算延迟时间
                        long delay = calculateDelay(attempt);
                        
                        // 延迟后重试
                        return CompletableFuture.delayedExecutor(delay, TimeUnit.MILLISECONDS)
                                .execute(() -> executeWithRetry(operation, operationName, attempt + 1));
                    } else {
                        log.error("操作 '{}' 在{}次尝试后最终失败", operationName, attempt + 1, throwable);
                        throw new RuntimeException("操作失败，已达到最大重试次数: " + throwable.getMessage(), throwable);
                    }
                })
                .thenCompose(result -> {
                    if (result instanceof CompletableFuture) {
                        return (CompletableFuture<T>) result;
                    } else {
                        return CompletableFuture.completedFuture(result);
                    }
                });
    }

    /**
     * 计算重试延迟时间（指数退避）
     */
    private long calculateDelay(int attempt) {
        long baseDelay = zfProperties.getNetty().getRetry().getRetryDelay();
        double multiplier = zfProperties.getNetty().getRetry().getBackoffMultiplier();
        
        return (long) (baseDelay * Math.pow(multiplier, attempt));
    }

    /**
     * 判断异常是否可重试
     */
    public boolean isRetryableException(Throwable throwable) {
        // 网络相关异常通常可以重试
        if (throwable instanceof java.net.ConnectException ||
            throwable instanceof java.net.SocketTimeoutException ||
            throwable instanceof java.net.UnknownHostException ||
            throwable instanceof java.io.IOException) {
            return true;
        }
        
        // HTTP 5xx 错误可以重试
        String message = throwable.getMessage();
        if (message != null && message.contains("HTTP请求失败: 状态码=5")) {
            return true;
        }
        
        // 其他情况不重试
        return false;
    }

    /**
     * 执行带条件重试的操作
     */
    public <T> CompletableFuture<T> executeWithConditionalRetry(Supplier<CompletableFuture<T>> operation, String operationName) {
        return executeWithConditionalRetry(operation, operationName, 0);
    }

    /**
     * 递归执行条件重试逻辑
     */
    private <T> CompletableFuture<T> executeWithConditionalRetry(Supplier<CompletableFuture<T>> operation, String operationName, int attempt) {
        return operation.get()
                .exceptionally(throwable -> {
                    if (attempt < zfProperties.getNetty().getRetry().getMaxAttempts() - 1 && isRetryableException(throwable)) {
                        log.warn("操作 '{}' 第{}次尝试失败（可重试异常），将在{}ms后重试: {}", 
                                operationName, attempt + 1, calculateDelay(attempt), throwable.getMessage());
                        
                        long delay = calculateDelay(attempt);
                        
                        return CompletableFuture.delayedExecutor(delay, TimeUnit.MILLISECONDS)
                                .execute(() -> executeWithConditionalRetry(operation, operationName, attempt + 1));
                    } else {
                        if (!isRetryableException(throwable)) {
                            log.error("操作 '{}' 失败（不可重试异常）", operationName, throwable);
                        } else {
                            log.error("操作 '{}' 在{}次尝试后最终失败", operationName, attempt + 1, throwable);
                        }
                        throw new RuntimeException("操作失败: " + throwable.getMessage(), throwable);
                    }
                })
                .thenCompose(result -> {
                    if (result instanceof CompletableFuture) {
                        return (CompletableFuture<T>) result;
                    } else {
                        return CompletableFuture.completedFuture(result);
                    }
                });
    }
}
