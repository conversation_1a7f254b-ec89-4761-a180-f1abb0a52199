package com.nst.nstsecurity.common.zf.ssl;

import com.nst.nstsecurity.common.zf.config.ZfProperties;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.FileInputStream;
import java.security.KeyStore;

/**
 * ZF SSL上下文构建器
 */
@Slf4j
@Component
public class ZfSslContextBuilder {

    /**
     * 构建SSL上下文
     */
    public SslContext buildSslContext(ZfProperties.CertificateConfig certConfig) throws Exception {
        SslContextBuilder sslContextBuilder = SslContextBuilder.forClient();

        // 配置客户端证书（双向认证）
        if (certConfig.getClientCertPath() != null && !certConfig.getClientCertPath().isEmpty()) {
            KeyManagerFactory keyManagerFactory = createKeyManagerFactory(
                certConfig.getClientCertPath(), 
                certConfig.getClientCertPassword()
            );
            sslContextBuilder.keyManager(keyManagerFactory);
            log.info("已配置客户端证书: {}", certConfig.getClientCertPath());
        }

        // 配置信任存储
        if (certConfig.getTrustStorePath() != null && !certConfig.getTrustStorePath().isEmpty()) {
            TrustManagerFactory trustManagerFactory = createTrustManagerFactory(
                certConfig.getTrustStorePath(), 
                certConfig.getTrustStorePassword()
            );
            sslContextBuilder.trustManager(trustManagerFactory);
            log.info("已配置信任存储: {}", certConfig.getTrustStorePath());
        } else {
            // 开发环境可以使用不安全的信任管理器（生产环境不推荐）
            log.warn("未配置信任存储，使用不安全的信任管理器（仅用于开发环境）");
            sslContextBuilder.trustManager(InsecureTrustManagerFactory.INSTANCE);
        }

        return sslContextBuilder.build();
    }

    /**
     * 创建密钥管理器工厂
     */
    private KeyManagerFactory createKeyManagerFactory(String certPath, String password) throws Exception {
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (FileInputStream fis = new FileInputStream(certPath)) {
            keyStore.load(fis, password.toCharArray());
        }

        KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(
            KeyManagerFactory.getDefaultAlgorithm()
        );
        keyManagerFactory.init(keyStore, password.toCharArray());
        
        return keyManagerFactory;
    }

    /**
     * 创建信任管理器工厂
     */
    private TrustManagerFactory createTrustManagerFactory(String trustStorePath, String password) throws Exception {
        KeyStore trustStore = KeyStore.getInstance("JKS");
        try (FileInputStream fis = new FileInputStream(trustStorePath)) {
            trustStore.load(fis, password.toCharArray());
        }

        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(
            TrustManagerFactory.getDefaultAlgorithm()
        );
        trustManagerFactory.init(trustStore);
        
        return trustManagerFactory;
    }
}
