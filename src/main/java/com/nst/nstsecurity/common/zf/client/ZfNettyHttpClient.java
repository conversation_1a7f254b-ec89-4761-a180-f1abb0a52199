package com.nst.nstsecurity.common.zf.client;

import com.nst.nstsecurity.common.zf.config.ZfProperties;
import com.nst.nstsecurity.common.zf.ssl.ZfSslContextBuilder;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.*;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * ZF Netty HTTP客户端
 */
@Slf4j
@Component
public class ZfNettyHttpClient {

    @Autowired
    private ZfProperties zfProperties;

    @Autowired
    private ZfSslContextBuilder sslContextBuilder;

    private EventLoopGroup workerGroup;
    private SslContext sslContext;

    @PostConstruct
    public void init() throws Exception {
        this.workerGroup = new NioEventLoopGroup();
        
        // 初始化SSL上下文
        if (zfProperties.getNetty().getConnection().isSsl()) {
            this.sslContext = sslContextBuilder.buildSslContext(zfProperties.getCertificate());
            log.info("SSL上下文初始化完成");
        }
        
        log.info("ZF Netty HTTP客户端初始化完成");
    }

    @PreDestroy
    public void destroy() {
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
            log.info("ZF Netty HTTP客户端已关闭");
        }
    }

    /**
     * 发送GET请求
     */
    public CompletableFuture<String> sendGetRequest(String url) {
        return sendRequest(HttpMethod.GET, url, null, null);
    }

    /**
     * 发送GET请求（带头部）
     */
    public CompletableFuture<String> sendGetRequest(String url, HttpHeaders headers) {
        return sendRequest(HttpMethod.GET, url, null, headers);
    }

    /**
     * 发送POST请求
     */
    public CompletableFuture<String> sendPostRequest(String url, String body) {
        return sendRequest(HttpMethod.POST, url, body, null);
    }

    /**
     * 发送POST请求（带头部）
     */
    public CompletableFuture<String> sendPostRequest(String url, String body, HttpHeaders headers) {
        return sendRequest(HttpMethod.POST, url, body, headers);
    }

    /**
     * 发送PUT请求
     */
    public CompletableFuture<String> sendPutRequest(String url, String body) {
        return sendRequest(HttpMethod.PUT, url, body, null);
    }

    /**
     * 发送PUT请求（带头部）
     */
    public CompletableFuture<String> sendPutRequest(String url, String body, HttpHeaders headers) {
        return sendRequest(HttpMethod.PUT, url, body, headers);
    }

    /**
     * 发送DELETE请求
     */
    public CompletableFuture<String> sendDeleteRequest(String url) {
        return sendRequest(HttpMethod.DELETE, url, null, null);
    }

    /**
     * 发送DELETE请求（带头部）
     */
    public CompletableFuture<String> sendDeleteRequest(String url, HttpHeaders headers) {
        return sendRequest(HttpMethod.DELETE, url, null, headers);
    }

    /**
     * 通用请求方法
     */
    private CompletableFuture<String> sendRequest(HttpMethod method, String url, String body, HttpHeaders customHeaders) {
        CompletableFuture<String> future = new CompletableFuture<>();
        
        try {
            URI uri = new URI(url);
            String host = uri.getHost();
            int port = uri.getPort() == -1 ? (uri.getScheme().equals("https") ? 443 : 80) : uri.getPort();
            
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(workerGroup)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, zfProperties.getNetty().getConnection().getConnectTimeout())
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // SSL处理器
                            if (sslContext != null) {
                                SslHandler sslHandler = sslContext.newHandler(ch.alloc(), host, port);
                                pipeline.addLast("ssl", sslHandler);
                            }
                            
                            // 超时处理器
                            pipeline.addLast("readTimeout", new ReadTimeoutHandler(
                                zfProperties.getNetty().getConnection().getReadTimeout(), TimeUnit.MILLISECONDS));
                            pipeline.addLast("writeTimeout", new WriteTimeoutHandler(
                                zfProperties.getNetty().getConnection().getWriteTimeout(), TimeUnit.MILLISECONDS));
                            
                            // HTTP编解码器
                            pipeline.addLast("httpCodec", new HttpClientCodec());
                            pipeline.addLast("aggregator", new HttpObjectAggregator(1048576)); // 1MB
                            
                            // 业务处理器
                            pipeline.addLast("handler", new ZfHttpClientHandler(future));
                        }
                    });

            // 连接服务器
            ChannelFuture connectFuture = bootstrap.connect(host, port);
            connectFuture.addListener((ChannelFutureListener) channelFuture -> {
                if (channelFuture.isSuccess()) {
                    // 构建HTTP请求
                    FullHttpRequest request = buildHttpRequest(method, uri, body, customHeaders);

                    // 发送请求
                    channelFuture.channel().writeAndFlush(request);
                    log.debug("发送{}请求到: {}", method, url);
                } else {
                    future.completeExceptionally(new RuntimeException("连接失败: " + channelFuture.cause().getMessage()));
                }
            });
            
        } catch (Exception e) {
            future.completeExceptionally(e);
        }
        
        return future;
    }

    /**
     * 构建HTTP请求
     */
    private FullHttpRequest buildHttpRequest(HttpMethod method, URI uri, String body, HttpHeaders customHeaders) {
        String path = uri.getPath();
        if (uri.getQuery() != null) {
            path += "?" + uri.getQuery();
        }

        FullHttpRequest request;
        if (body != null) {
            request = new DefaultFullHttpRequest(
                HttpVersion.HTTP_1_1, method, path,
                Unpooled.copiedBuffer(body, CharsetUtil.UTF_8)
            );
            request.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
            request.headers().set(HttpHeaderNames.CONTENT_LENGTH, request.content().readableBytes());
        } else {
            request = new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, method, path);
        }

        // 设置通用头部
        request.headers().set(HttpHeaderNames.HOST, uri.getHost());
        request.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);
        request.headers().set(HttpHeaderNames.USER_AGENT, "ZF-Netty-Client/1.0");

        // 添加自定义头部
        if (customHeaders != null) {
            for (String name : customHeaders.names()) {
                request.headers().set(name, customHeaders.get(name));
            }
        }

        return request;
    }
}
