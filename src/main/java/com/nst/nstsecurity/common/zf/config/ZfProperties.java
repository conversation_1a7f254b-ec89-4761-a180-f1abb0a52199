package com.nst.nstsecurity.common.zf.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * ZF (采埃孚) 配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "zf")
public class ZfProperties {

    private NettyConfig netty = new NettyConfig();
    private ApiConfig api = new ApiConfig();
    private CertificateConfig certificate = new CertificateConfig();

    @Data
    public static class NettyConfig {
        private ConnectionConfig connection = new ConnectionConfig();
        private PoolConfig pool = new PoolConfig();
        private RetryConfig retry = new RetryConfig();

        @Data
        public static class ConnectionConfig {
            private String host = "zf-api.example.com";
            private int port = 443;
            private boolean ssl = true;
            private int connectTimeout = 30000;
            private int readTimeout = 60000;
            private int writeTimeout = 30000;
        }

        @Data
        public static class PoolConfig {
            private int maxConnections = 50;
            private int maxConnectionsPerRoute = 20;
            private long connectionIdleTimeout = 300000;
            private long keepAliveTimeout = 60000;
        }

        @Data
        public static class RetryConfig {
            private int maxAttempts = 3;
            private long retryDelay = 1000;
            private double backoffMultiplier = 2.0;
        }
    }

    @Data
    public static class ApiConfig {
        private AuthConfig auth = new AuthConfig();
        private DataConfig data = new DataConfig();
        private ControlConfig control = new ControlConfig();

        @Data
        public static class AuthConfig {
            private String baseurl = "https://zf-api.example.com/auth";
            private String login = "/v1/login";
            private String refresh = "/v1/refresh";
            private String logout = "/v1/logout";
        }

        @Data
        public static class DataConfig {
            private String baseurl = "https://zf-api.example.com/api";
            private String vehicleData = "/v1/vehicles/{vehicleId}/data";
            private String diagnostic = "/v1/vehicles/{vehicleId}/diagnostic";
            private String telemetry = "/v1/vehicles/{vehicleId}/telemetry";
        }

        @Data
        public static class ControlConfig {
            private String baseurl = "https://zf-api.example.com/control";
            private String remoteStart = "/v1/vehicles/{vehicleId}/start";
            private String remoteStop = "/v1/vehicles/{vehicleId}/stop";
            private String lockUnlock = "/v1/vehicles/{vehicleId}/lock";
        }
    }

    @Data
    public static class CertificateConfig {
        private String clientCertPath;
        private String clientCertPassword;
        private String trustStorePath;
        private String trustStorePassword;
    }
}
