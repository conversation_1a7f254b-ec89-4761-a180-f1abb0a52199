package com.nst.nstsecurity.common.zf.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nst.nstsecurity.common.zf.client.ZfNettyHttpClient;
import com.nst.nstsecurity.common.zf.config.ZfProperties;
import com.nst.nstsecurity.common.zf.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * ZF API服务类
 */
@Slf4j
@Service
public class ZfApiService {

    @Autowired
    private ZfNettyHttpClient httpClient;

    @Autowired
    private ZfProperties zfProperties;

    private String accessToken;

    /**
     * 用户认证登录
     */
    public CompletableFuture<ZfAuthResponse> login(ZfLoginRequest loginRequest) {
        String url = zfProperties.getApi().getAuth().getBaseurl() + zfProperties.getApi().getAuth().getLogin();
        String requestBody = JSON.toJSONString(loginRequest);
        
        log.info("发起ZF登录请求: {}", url);
        
        return httpClient.sendPostRequest(url, requestBody)
                .thenApply(response -> {
                    ZfAuthResponse authResponse = JSON.parseObject(response, ZfAuthResponse.class);
                    if (authResponse.isSuccess()) {
                        this.accessToken = authResponse.getAccessToken();
                        log.info("ZF登录成功，获取到访问令牌");
                    }
                    return authResponse;
                })
                .exceptionally(throwable -> {
                    log.error("ZF登录失败", throwable);
                    ZfAuthResponse errorResponse = new ZfAuthResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(throwable.getMessage());
                    return errorResponse;
                });
    }

    /**
     * 刷新访问令牌
     */
    public CompletableFuture<ZfAuthResponse> refreshToken(String refreshToken) {
        String url = zfProperties.getApi().getAuth().getBaseurl() + zfProperties.getApi().getAuth().getRefresh();
        
        JSONObject requestBody = new JSONObject();
        requestBody.put("refreshToken", refreshToken);
        
        log.info("发起ZF令牌刷新请求: {}", url);
        
        return httpClient.sendPostRequest(url, requestBody.toJSONString())
                .thenApply(response -> {
                    ZfAuthResponse authResponse = JSON.parseObject(response, ZfAuthResponse.class);
                    if (authResponse.isSuccess()) {
                        this.accessToken = authResponse.getAccessToken();
                        log.info("ZF令牌刷新成功");
                    }
                    return authResponse;
                })
                .exceptionally(throwable -> {
                    log.error("ZF令牌刷新失败", throwable);
                    ZfAuthResponse errorResponse = new ZfAuthResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(throwable.getMessage());
                    return errorResponse;
                });
    }

    /**
     * 获取车辆数据
     */
    public CompletableFuture<ZfVehicleDataResponse> getVehicleData(String vehicleId) {
        String url = zfProperties.getApi().getData().getBaseurl() + 
                    zfProperties.getApi().getData().getVehicleData().replace("{vehicleId}", vehicleId);
        
        log.info("获取车辆数据: vehicleId={}, url={}", vehicleId, url);
        
        return sendAuthenticatedRequest(url, null, "GET")
                .thenApply(response -> JSON.parseObject(response, ZfVehicleDataResponse.class))
                .exceptionally(throwable -> {
                    log.error("获取车辆数据失败: vehicleId={}", vehicleId, throwable);
                    ZfVehicleDataResponse errorResponse = new ZfVehicleDataResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(throwable.getMessage());
                    return errorResponse;
                });
    }

    /**
     * 获取车辆诊断信息
     */
    public CompletableFuture<ZfDiagnosticResponse> getVehicleDiagnostic(String vehicleId) {
        String url = zfProperties.getApi().getData().getBaseurl() + 
                    zfProperties.getApi().getData().getDiagnostic().replace("{vehicleId}", vehicleId);
        
        log.info("获取车辆诊断信息: vehicleId={}, url={}", vehicleId, url);
        
        return sendAuthenticatedRequest(url, null, "GET")
                .thenApply(response -> JSON.parseObject(response, ZfDiagnosticResponse.class))
                .exceptionally(throwable -> {
                    log.error("获取车辆诊断信息失败: vehicleId={}", vehicleId, throwable);
                    ZfDiagnosticResponse errorResponse = new ZfDiagnosticResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(throwable.getMessage());
                    return errorResponse;
                });
    }

    /**
     * 远程启动车辆
     */
    public CompletableFuture<ZfControlResponse> remoteStartVehicle(String vehicleId, ZfRemoteStartRequest startRequest) {
        String url = zfProperties.getApi().getControl().getBaseurl() + 
                    zfProperties.getApi().getControl().getRemoteStart().replace("{vehicleId}", vehicleId);
        
        String requestBody = JSON.toJSONString(startRequest);
        log.info("远程启动车辆: vehicleId={}, url={}", vehicleId, url);
        
        return sendAuthenticatedRequest(url, requestBody, "POST")
                .thenApply(response -> JSON.parseObject(response, ZfControlResponse.class))
                .exceptionally(throwable -> {
                    log.error("远程启动车辆失败: vehicleId={}", vehicleId, throwable);
                    ZfControlResponse errorResponse = new ZfControlResponse();
                    errorResponse.setSuccess(false);
                    errorResponse.setErrorMessage(throwable.getMessage());
                    return errorResponse;
                });
    }

    /**
     * 发送带认证的请求
     */
    private CompletableFuture<String> sendAuthenticatedRequest(String url, String body, String method) {
        // 构建认证头部
        io.netty.handler.codec.http.HttpHeaders headers = new io.netty.handler.codec.http.DefaultHttpHeaders();
        if (accessToken != null && !accessToken.isEmpty()) {
            headers.set("Authorization", "Bearer " + accessToken);
        }

        switch (method.toUpperCase()) {
            case "GET":
                return httpClient.sendGetRequest(url, headers);
            case "POST":
                return httpClient.sendPostRequest(url, body, headers);
            case "PUT":
                return httpClient.sendPutRequest(url, body, headers);
            case "DELETE":
                return httpClient.sendDeleteRequest(url, headers);
            default:
                return CompletableFuture.failedFuture(new IllegalArgumentException("不支持的HTTP方法: " + method));
        }
    }

    /**
     * 登出
     */
    public CompletableFuture<Boolean> logout() {
        String url = zfProperties.getApi().getAuth().getBaseurl() + zfProperties.getApi().getAuth().getLogout();
        
        log.info("发起ZF登出请求: {}", url);
        
        return sendAuthenticatedRequest(url, null, "POST")
                .thenApply(response -> {
                    this.accessToken = null;
                    log.info("ZF登出成功");
                    return true;
                })
                .exceptionally(throwable -> {
                    log.error("ZF登出失败", throwable);
                    return false;
                });
    }
}
