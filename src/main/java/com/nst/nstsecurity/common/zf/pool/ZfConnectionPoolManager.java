package com.nst.nstsecurity.common.zf.pool;

import com.nst.nstsecurity.common.zf.config.ZfProperties;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.pool.*;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;
import java.util.concurrent.TimeUnit;

/**
 * ZF连接池管理器
 */
@Slf4j
@Component
public class ZfConnectionPoolManager {

    @Autowired
    private ZfProperties zfProperties;

    private EventLoopGroup eventLoopGroup;
    private ChannelPool channelPool;

    @PostConstruct
    public void init() {
        this.eventLoopGroup = new NioEventLoopGroup();
        
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(eventLoopGroup)
                .channel(NioSocketChannel.class)
                .remoteAddress(new InetSocketAddress(
                    zfProperties.getNetty().getConnection().getHost(),
                    zfProperties.getNetty().getConnection().getPort()
                ));

        // 创建连接池
        this.channelPool = new SimpleChannelPool(
            bootstrap,
            new ZfChannelPoolHandler(),
            ChannelHealthChecker.ACTIVE,
            true, // releaseHealthCheck
            true  // lastRecentUsed
        ) {
            @Override
            protected ChannelFuture connectChannel(Bootstrap bs) {
                return bs.connect();
            }
        };

        log.info("ZF连接池管理器初始化完成: host={}, port={}, maxConnections={}", 
                zfProperties.getNetty().getConnection().getHost(),
                zfProperties.getNetty().getConnection().getPort(),
                zfProperties.getNetty().getPool().getMaxConnections());
    }

    @PreDestroy
    public void destroy() {
        if (channelPool != null) {
            channelPool.close();
        }
        if (eventLoopGroup != null) {
            eventLoopGroup.shutdownGracefully(0, 5, TimeUnit.SECONDS);
        }
        log.info("ZF连接池管理器已关闭");
    }

    /**
     * 获取连接
     */
    public Future<Channel> acquireChannel() {
        return channelPool.acquire();
    }

    /**
     * 释放连接
     */
    public Future<Void> releaseChannel(Channel channel) {
        return channelPool.release(channel);
    }

    /**
     * 获取连接（带Promise）
     */
    public Future<Channel> acquireChannel(Promise<Channel> promise) {
        return channelPool.acquire(promise);
    }

    /**
     * 释放连接（带Promise）
     */
    public Future<Void> releaseChannel(Channel channel, Promise<Void> promise) {
        return channelPool.release(channel, promise);
    }

    /**
     * 获取连接池统计信息
     */
    public String getPoolStats() {
        // 这里可以添加连接池统计信息的实现
        return String.format("ZF连接池状态 - 配置最大连接数: %d", 
                zfProperties.getNetty().getPool().getMaxConnections());
    }
}
