package com.nst.nstsecurity.common.zf.pool;

import io.netty.channel.Channel;
import io.netty.channel.pool.ChannelPoolHandler;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpClientCodec;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * ZF连接池处理器
 */
@Slf4j
public class ZfChannelPoolHandler implements ChannelPoolHandler {

    @Override
    public void channelReleased(Channel ch) throws Exception {
        log.debug("连接已释放回连接池: {}", ch.id());
        // 清理连接状态，为下次使用做准备
        ch.pipeline().remove("aggregator");
        ch.pipeline().addLast("aggregator", new HttpObjectAggregator(1048576));
    }

    @Override
    public void channelAcquired(Channel ch) throws Exception {
        log.debug("从连接池获取连接: {}", ch.id());
    }

    @Override
    public void channelCreated(Channel ch) throws Exception {
        log.debug("创建新连接: {}", ch.id());
        
        SocketChannel socketChannel = (SocketChannel) ch;
        
        // 配置连接管道
        socketChannel.pipeline()
                // 空闲状态处理器
                .addLast("idleStateHandler", new IdleStateHandler(60, 30, 0, TimeUnit.SECONDS))
                // HTTP编解码器
                .addLast("httpCodec", new HttpClientCodec())
                // HTTP对象聚合器
                .addLast("aggregator", new HttpObjectAggregator(1048576)); // 1MB
    }
}
