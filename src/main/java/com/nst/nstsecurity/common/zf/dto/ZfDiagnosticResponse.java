package com.nst.nstsecurity.common.zf.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * ZF诊断响应
 */
@Data
public class ZfDiagnosticResponse {
    private boolean success;
    private String vehicleId;
    private List<DiagnosticCode> diagnosticCodes;
    private Map<String, Object> systemStatus;
    private String timestamp;
    private String errorMessage;
    private String errorCode;

    @Data
    public static class DiagnosticCode {
        private String code;
        private String description;
        private String severity;
        private String system;
        private String timestamp;
    }
}
