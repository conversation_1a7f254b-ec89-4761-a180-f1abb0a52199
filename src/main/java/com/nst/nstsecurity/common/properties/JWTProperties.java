package com.nst.nstsecurity.common.properties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "jwt")
public class JWTProperties {

    @Value("${secret}")
    public static String secret;

    @Value("${tokenHeader}")
    public static String tokenHeader;

    @Value("${tokenPrefix}")
    public static String tokenPrefix;

    @Value("${expiration}")
    public static Integer expiration;

    @Value("${antMatchers}")
    public static String antMatchers;


    public void setExpiration(Integer expiration) {
        this.expiration = expiration * 1000;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public void setTokenHeader(String tokenHeader) {
        this.tokenHeader = tokenHeader;
    }

    public void setTokenPrefix(String tokenPrefix) {
        this.tokenPrefix = tokenPrefix + " ";
    }

    public void setAntMatchers(String antMatchers) {
        this.antMatchers = antMatchers;
    }

}
