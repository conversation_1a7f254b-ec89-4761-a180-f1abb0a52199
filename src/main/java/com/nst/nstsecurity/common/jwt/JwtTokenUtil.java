package com.nst.nstsecurity.common.jwt;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.nst.nstsecurity.common.framework.util.StringUtils;
import com.nst.nstsecurity.common.properties.JWTProperties;
import com.nst.nstsecurity.common.security.AuthUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Clock;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClock;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class JwtTokenUtil implements Serializable {
    private static final long serialVersionUID = 1L;
    private static Clock clock = DefaultClock.INSTANCE;
    private Map<String,String> tokenMap = new ConcurrentHashMap<>();

    public static String createAccessToken(AuthUser authUser) {
        final Date createdDate = clock.now();
        String token = Jwts.builder().setId(
                authUser.getId().toString()).setSubject(authUser.getUsername())
                .setIssuedAt(new Date())
                .setIssuer("HJDZ")
                .setExpiration(generateExpirationDate(createdDate))
               .signWith(SignatureAlgorithm.HS256, JWTProperties.secret)
                .claim("authorities", JSON.toJSONString(authUser.getAuthorities())).compact();
        return JWTProperties.tokenPrefix + token;
    }

    public static AuthUser parseAccessToken(String token) {
        AuthUser authUser = null;
        if(StringUtils.isNotEmpty(token)) {
            token = token.substring(JWTProperties.tokenPrefix.length());
            Claims claims = Jwts.parser().setSigningKey(JWTProperties.secret).parseClaimsJws(token).getBody();
            authUser = new AuthUser();
            authUser.setId(Long.parseLong(claims.getId()));
            authUser.setUsername(claims.getSubject());
            Set<GrantedAuthority> authorities = new HashSet<GrantedAuthority>();
            String authority = claims.get("authorities", String.class);
            if(StringUtils.isNotEmpty(authority)) {
                List<Map<String,String>> authoritiesList = JSON.parseObject(authority, new TypeReference<List<Map<String,String>>>(){});
                for(Map<String,String> role : authoritiesList) {
                    if(!role.isEmpty()){
                        authorities.add(new SimpleGrantedAuthority(role.get("authority")));
                    }
                }
            }
            authUser.setAuthorities(authorities);
        }
        return authUser;
    }

    private static Date generateExpirationDate(Date createdDate) {
        return new Date(createdDate.getTime() + JWTProperties.expiration);
    }
}

