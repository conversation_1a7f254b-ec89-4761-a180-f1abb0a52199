package com.nst.nstsecurity.common.security.handler;

import com.nst.nstsecurity.common.framework.util.AjaxResult;
import com.nst.nstsecurity.common.framework.util.ResponseUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class AuthenticationNotLoginHandler implements AuthenticationEntryPoint {
    @Override
    public void commence(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, AuthenticationException e) throws IOException, ServletException {
        AjaxResult ajaxResult = new AjaxResult(AjaxResult.Type.ERROR,"未登陆",e.getMessage());
        ResponseUtils.responseJson(httpServletResponse, ajaxResult);
    }
}
