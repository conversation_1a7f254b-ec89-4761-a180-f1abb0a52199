package com.nst.nstsecurity.common.security.handler;

import com.nst.nstsecurity.common.framework.util.AjaxResult;
import com.nst.nstsecurity.common.framework.util.ResponseUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class AuthenticationLoginOutSuccessHandler implements LogoutSuccessHandler {
    @Override
    public void onLogoutSuccess(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Authentication authentication) throws IOException, ServletException {
        AjaxResult ajaxResult = new AjaxResult(AjaxResult.Type.SUCCESS,"登出成功",null);
        ResponseUtils.responseJson(httpServletResponse, ajaxResult);
    }
}
