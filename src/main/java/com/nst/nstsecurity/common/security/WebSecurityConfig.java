package com.nst.nstsecurity.common.security;

import com.nst.nstsecurity.common.jwt.JwtAuthenticationTokenFilter;
import com.nst.nstsecurity.common.properties.JWTProperties;
import com.nst.nstsecurity.common.security.handler.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.access.expression.DefaultWebSecurityExpressionHandler;

@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
    /**
     * 无权限处理类
     */
    @Autowired
    private AuthenticationDeniedHandler authenticationDeniedHandler;

    /**
     * 用户未登录处理类
     */
    @Autowired
    private AuthenticationNotLoginHandler authenticationNotLoginHandler;

    /**
     * 用户登录成功处理类
     */
    @Autowired
    private AuthenticationLoginSuccessHandler authenticationLoginSuccessHandler;

    /**
     * 用户登录失败处理类
     */
    @Autowired
    private AuthenticationLoginFailureHandler authenticationLoginFailureHandler;

    /**
     * 用户登出成功处理类
     */
    @Autowired
    private AuthenticationLoginOutSuccessHandler authenticationLoginOutSuccessHandler;

    /**
     * 用户登录验证
     */
    @Autowired
    private UserAuthenticationProvider userAuthenticationProvider;

    /**
     * 用户权限注解
     */
    @Autowired
    private UserPermissionEvaluator userPermissionEvaluator;
    /**
     * 加密方式
     *
     * @return
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
    /**
     * 注入自定义PermissionEvaluator
     *
     * @return
     */
    @Bean
    public DefaultWebSecurityExpressionHandler userSecurityExpressionHandler() {
        DefaultWebSecurityExpressionHandler handler = new DefaultWebSecurityExpressionHandler();
        handler.setPermissionEvaluator(userPermissionEvaluator);
        return handler;
    }
    /**
     * 用户登录验证
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(userAuthenticationProvider);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests() // 权限配置
                .antMatchers(JWTProperties.antMatchers.split(",")).permitAll()// 获取白名单（不进行权限验证）
                //.mvcMatchers(JWTProperties.antMatchers.split(",")).permitAll()
                .anyRequest().authenticated() // 其他的需要登陆后才能访问
                .and().httpBasic().authenticationEntryPoint(authenticationNotLoginHandler) // 配置未登录处理类
                .and().formLogin().loginPage("/").loginProcessingUrl("/login/submit")// 配置登录URL
                .successHandler(authenticationLoginSuccessHandler) // 配置登录成功处理类
                .failureHandler(authenticationLoginFailureHandler) // 配置登录失败处理类
                .and().logout().logoutUrl("/logout/submit")// 配置登出地址
                .logoutSuccessHandler(authenticationLoginOutSuccessHandler) // 配置用户登出处理类
                .and().exceptionHandling().accessDeniedHandler(authenticationDeniedHandler)// 配置没有权限处理类
                .and().cors()// 开启跨域
                .and().csrf().disable(); // 禁用跨站请求伪造防护
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS); // 禁用session（使用Token认证）
        http.headers().cacheControl(); // 禁用缓存
        http.headers().contentTypeOptions().disable(); // 禁用Content-Type选项,用于静态资源访问
        http.addFilter(new JwtAuthenticationTokenFilter(authenticationManager())); //// 添加JWT过滤器
    }
}
