package com.nst.nstsecurity.common.tlsHttps;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

import java.security.KeyStore;

public class HttpClientConnectionManagerHandler {

    private static HttpClientConnectionManagerHandler INSTANCE = null;
    public static HttpClientConnectionManagerHandler getInstance() {
        if (INSTANCE == null) INSTANCE = new HttpClientConnectionManagerHandler();
        return INSTANCE;
    }

    private HttpClientConnectionManagerHandler() {
    }

    private PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = null;
    private CloseableHttpClient httpClient = null;

    public PoolingHttpClientConnectionManager getPoolingHttpClientConnectionManager(KeyStore keyStore, String keyPassword) throws Exception {
        ConnectionSocketFactory plainsf = PlainConnectionSocketFactory.getSocketFactory();
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                SSLUtil.getInstance().getSslContext(keyStore,keyPassword)
        );

        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory> create().register("http", plainsf).register("https", sslsf).build();

        poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager(registry);
        poolingHttpClientConnectionManager.setMaxTotal(200); // 最大连接数
        poolingHttpClientConnectionManager.setDefaultMaxPerRoute(20); // 每个路由的最大连接数
        poolingHttpClientConnectionManager.setValidateAfterInactivity(3000); // 空闲连接验证时间

        return poolingHttpClientConnectionManager;
    }

    public static RequestConfig getRequestConfig() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(5000) // 连接超时时间
                .setSocketTimeout(10000) // 读取超时时间
                .setConnectionRequestTimeout(3000).build(); // 从连接池获取连接的超时时间
        return requestConfig;
    }

    public void initHttpClient(KeyStore keyStore, String keyPassword) throws Exception{
        synchronized (HttpClientConnectionManagerHandler.class) {
            if(httpClient == null) {
                HttpClientBuilder builder = HttpClients.custom();
                builder.setConnectionManager(getPoolingHttpClientConnectionManager(keyStore,keyPassword)).setConnectionManagerShared(true);
                builder.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy());
                builder.setDefaultRequestConfig(getRequestConfig());
                builder.setRetryHandler(new CustomRetryHandler());
                builder.addInterceptorFirst(new RequestInterceptor());
                httpClient = builder.build();
            }
        }
    }

    public CloseableHttpClient getHttpClient(KeyStore keyStore, String keyPassword) throws Exception{
        if(httpClient == null) {
            initHttpClient(keyStore,keyPassword);
        }
        return httpClient;
    }

//    public TimerTask closeHttpClient() {
//        TimerTask timerTask = new TimerTask() {
//            @Override
//            public void run() {
//                if(poolingHttpClientConnectionManager != null) {
//                    poolingHttpClientConnectionManager.closeExpiredConnections();
//                    poolingHttpClientConnectionManager.closeIdleConnections(30, TimeUnit.SECONDS);
//                }
//            }
//        };
//        return timerTask;
//    }
}
