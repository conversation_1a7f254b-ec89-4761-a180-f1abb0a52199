package com.nst.nstsecurity.common.tlsHttps;

import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.protocol.HttpContext;

import java.io.IOException;

public class RequestInterceptor implements HttpRequestInterceptor {
    @Override
    public void process(HttpRequest httpRequest, HttpContext httpContext) throws HttpException, IOException {
        System.out.println("-----request interceptor-----");
        httpRequest.setHeader("Content-Type", "application/json");
    }
}
