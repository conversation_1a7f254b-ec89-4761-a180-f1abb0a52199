package com.nst.nstsecurity.common.tlsHttps;

import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.protocol.HttpContext;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

public class CustomRetryHandler implements HttpRequestRetryHandler {
    @Override
    public boolean retryRequest(IOException exception, int executionCount, HttpContext httpContext) {
        if(executionCount >= 5) {
            return false;
        }
        if(exception instanceof NoHttpResponseException) {
            // 服务器无响应
            return true;
        }
        if(exception instanceof SSLHandshakeException) {
            // SSL握手异常
            return true;
        }
        if(exception instanceof InterruptedIOException) {
            // 连接超时
            return true;
        }
        if(exception instanceof UnknownHostException) {
            // 服务器地址未知
            return true;
        }
        if(exception instanceof ConnectTimeoutException) {
            // 连接超时
            return true;
        }
        if(exception instanceof SSLException) {
            // SSL异常
            return true;
        }
        if(exception instanceof SocketException) {
            // Socket异常
            return true;
        }
        if(exception instanceof SocketTimeoutException) {
            // Socket超时
            return true;
        }

        HttpClientContext clientContext = HttpClientContext.adapt(httpContext);

        HttpRequest request = clientContext.getRequest();
        if (!(request instanceof HttpEntityEnclosingRequest)) {
            // 如果请求是幂等的，就再次尝试
            return true;
        }

        return false;
    }
}
