package com.nst.nstsecurity.zf;

import com.nst.nstsecurity.common.zf.dto.*;
import com.nst.nstsecurity.common.zf.service.ZfApiService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * ZF API服务测试类
 */
@SpringBootTest
public class ZfApiServiceTest {

    @Autowired
    private ZfApiService zfApiService;

    @Test
    public void testLogin() throws Exception {
        ZfLoginRequest loginRequest = new ZfLoginRequest();
        loginRequest.setUsername("test_user");
        loginRequest.setPassword("test_password");
        loginRequest.setClientId("test_client_id");
        loginRequest.setClientSecret("test_client_secret");

        CompletableFuture<ZfAuthResponse> future = zfApiService.login(loginRequest);
        ZfAuthResponse response = future.get(30, TimeUnit.SECONDS);

        System.out.println("登录响应: " + response);
        // 这里可以添加断言来验证响应
    }

    @Test
    public void testGetVehicleData() throws Exception {
        // 首先需要登录获取token
        ZfLoginRequest loginRequest = new ZfLoginRequest();
        loginRequest.setUsername("test_user");
        loginRequest.setPassword("test_password");
        loginRequest.setClientId("test_client_id");
        loginRequest.setClientSecret("test_client_secret");

        CompletableFuture<ZfAuthResponse> loginFuture = zfApiService.login(loginRequest);
        ZfAuthResponse loginResponse = loginFuture.get(30, TimeUnit.SECONDS);

        if (loginResponse.isSuccess()) {
            // 获取车辆数据
            String vehicleId = "TEST_VEHICLE_001";
            CompletableFuture<ZfVehicleDataResponse> dataFuture = zfApiService.getVehicleData(vehicleId);
            ZfVehicleDataResponse dataResponse = dataFuture.get(30, TimeUnit.SECONDS);

            System.out.println("车辆数据响应: " + dataResponse);
        }
    }

    @Test
    public void testRemoteStartVehicle() throws Exception {
        // 首先需要登录获取token
        ZfLoginRequest loginRequest = new ZfLoginRequest();
        loginRequest.setUsername("test_user");
        loginRequest.setPassword("test_password");
        loginRequest.setClientId("test_client_id");
        loginRequest.setClientSecret("test_client_secret");

        CompletableFuture<ZfAuthResponse> loginFuture = zfApiService.login(loginRequest);
        ZfAuthResponse loginResponse = loginFuture.get(30, TimeUnit.SECONDS);

        if (loginResponse.isSuccess()) {
            // 远程启动车辆
            String vehicleId = "TEST_VEHICLE_001";
            ZfRemoteStartRequest startRequest = new ZfRemoteStartRequest();
            startRequest.setVehicleId(vehicleId);
            startRequest.setDuration(15); // 15分钟
            startRequest.setClimateControl(true);
            startRequest.setTargetTemperature(22.0);
            startRequest.setAuthCode("AUTH_CODE_123");

            CompletableFuture<ZfControlResponse> controlFuture = zfApiService.remoteStartVehicle(vehicleId, startRequest);
            ZfControlResponse controlResponse = controlFuture.get(30, TimeUnit.SECONDS);

            System.out.println("远程启动响应: " + controlResponse);
        }
    }
}
